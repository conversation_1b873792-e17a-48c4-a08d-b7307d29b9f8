apiVersion: apps/v1
kind: Deployment
metadata:
  name: backend
  namespace: 2048-app
  labels: { app: backend }
spec:
  replicas: 2
  selector:
    matchLabels: { app: backend }
  template:
    metadata:
      labels: { app: backend }
    spec:
      imagePullSecrets: [{ name: regcred }]
      terminationGracePeriodSeconds: 30
      containers:
        - name: 2048-backend
          image: registry.gitlab.com/trantrongbinh/vj-2048/backend:latest
          ports:
            - { containerPort: 8001, name: http }
          envFrom:
            - secretRef: { name: backend-secrets }
          # startupProbe: # cho backend warm-up thoải mái trước khi liveness/ready
          #   httpGet: { path: /healthz, port: http } # đảm báo có /healthz trong nginx config
          #   failureThreshold: 30      # tối đa ~150s (30 * 5s)
          #   periodSeconds: 5
          # readinessProbe: # sẵn sàng nhận traffic khi /healthz trả 200
          #   httpGet: { path: /healthz, port: http }
          #   initialDelaySeconds: 5
          #   periodSeconds: 5
          #   timeoutSeconds: 2
          #   failureThreshold: 3
          # livenessProbe: # liveness để K8s tự khởi động lại nếu app “đơ”
          #   httpGet: { path: /healthz, port: http }
          #   initialDelaySeconds: 30
          #   periodSeconds: 10
          #   timeoutSeconds: 2
          #   failureThreshold: 3
          # Supervisord + Nginx: dùng tcpSocket probe
          readinessProbe:
            tcpSocket: { port: http }
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 2
          livenessProbe:
            tcpSocket: { port: http }
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 2
          resources:
            requests: { cpu: "400m", memory: "768Mi" }
            limits:   { cpu: "2", memory: "2Gi" }
---
apiVersion: v1
kind: Service
metadata:
  name: backend
  namespace: 2048-app
spec:
  selector: { app: backend }
  ports:
    - name: http
      port: 80
      targetPort: 8001
