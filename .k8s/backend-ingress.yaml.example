apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: backend
  namespace: 2048-app
spec:
  ingressClassName: traefik
  rules:
    - host: api.myapp.test      # ĐỔI host ở đây
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service: { name: backend, port: { number: 80 } }

# ở trên là tls ở nginx rồi reverse proxy vào Traefik NodePort còn nếu muốn quản lý tls trong cụm k8s thì dùng cấu hình sau:

# apiVersion: networking.k8s.io/v1
# kind: Ingress
# metadata:
#   name: backend
#   namespace: 2048-app
#   annotations:
#     cert-manager.io/cluster-issuer: letsencrypt # có thể dùng tự ký bời cụm: selfsigned - chỉ dùng dev vì ko đc trình duyệt tin cậy bằng letsencrypt
# spec:
#   ingressClassName: traefik
#   tls:
#     - hosts: ["api.myapp.test"] # ĐỔI host ở đây
#       secretName: be-tls
#   rules:
#     - host: api.myapp.test # ĐỔI host ở đây
#       http:
#         paths:
#           - path: /
#             pathType: Prefix
#             backend: { service: { name: backend, port: { number: 80 } } }
