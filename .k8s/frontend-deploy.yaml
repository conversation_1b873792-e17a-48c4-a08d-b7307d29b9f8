apiVersion: apps/v1
kind: Deployment
metadata:
  name: frontend
  namespace: 2048-app
  labels: { app: frontend }
spec:
  replicas: 2
  selector: { matchLabels: { app: frontend } }
  template:
    metadata: { labels: { app: frontend } }
    spec:
      imagePullSecrets: [{ name: regcred }]
      securityContext:
        runAsNonRoot: true
      containers:
        - name: 2048-frontend
          image: registry.gitlab.com/trantrongbinh/vj-2048/frontend:latest
          ports:
            - { containerPort: 3000, name: http }
          envFrom:
            - secretRef: { name: frontend-secrets }
          securityContext:
            runAsNonRoot: true # yêu cầu container không chạy bằng root
            runAsUser: 1001    # thêm UID user như trong Dockerfile
            runAsGroup: 1001   # thêm GID số (khớp group nodejs)
            allowPrivilegeEscalation: false
          readinessProbe:
            httpGet: { path: "/", port: http }
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 2
          livenessProbe:
            httpGet: { path: "/", port: http }
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 2
          resources:
            requests: { cpu: "150m", memory: "256Mi" }
            limits:   { cpu: "600m", memory: "512Mi" }
---
apiVersion: v1
kind: Service
metadata:
  name: frontend
  namespace: 2048-app
spec:
  selector: { app: frontend }
  ports:
    - name: http
      port: 80
      targetPort: 3000
