apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: frontend
  namespace: 2048-app
spec:
  ingressClassName: traefik
  rules:
    - host: myapp.test # Đổi host ở đây
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service: { name: frontend, port: { number: 80 } }

# ở trên là tls ở nginx rồi reverse proxy vào Traefik NodePort còn nếu muốn quản lý tls trong cụm k8s thì dùng cấu hình sau:

# apiVersion: networking.k8s.io/v1
# kind: Ingress
# metadata:
#   name: frontend
#   namespace: 2048-app
#   annotations:
#     cert-manager.io/cluster-issuer: selfsigned
# spec:
#   ingressClassName: traefik
#   tls:
#     - hosts: ["myapp.test"] # Đổi host ở đây
#       secretName: fe-tls
#   rules:
#     - host: myapp.test # Đổi host ở đây
#       http:
#         paths:
#           - path: /
#             pathType: Prefix
#             backend: { service: { name: frontend, port: { number: 80 } } }
