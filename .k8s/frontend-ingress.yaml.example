apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: frontend
  namespace: 2048-app
  annotations:
    # Sticky cookie ở Traefik cho FE để 1 phiên gắn 1 pod
    traefik.ingress.kubernetes.io/service.sticky.cookie: "true"
    traefik.ingress.kubernetes.io/service.sticky.cookie.name: "route"
    traefik.ingress.kubernetes.io/service.sticky.cookie.secure: "true"
    traefik.ingress.kubernetes.io/service.sticky.cookie.httpOnly: "true"
    traefik.ingress.kubernetes.io/service.sticky.cookie.sameSite: "lax"
spec:
  ingressClassName: traefik
  rules:
    - host: myapp.test # Đổi host ở đây
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service: { name: frontend, port: { number: 80 } }

# ở trên là tls ở nginx rồi reverse proxy vào Traefik NodePort còn nếu muốn quản lý tls trong cụm k8s thì dùng cấu hình sau:

# apiVersion: networking.k8s.io/v1
# kind: Ingress
# metadata:
#   name: frontend
#   namespace: 2048-app
#   annotations:
#     cert-manager.io/cluster-issuer: selfsigned
#     # Sticky cookie ở Traefik cho FE để 1 phiên gắn 1 pod
#     traefik.ingress.kubernetes.io/service.sticky.cookie: "true"
#     traefik.ingress.kubernetes.io/service.sticky.cookie.name: "route"
#     traefik.ingress.kubernetes.io/service.sticky.cookie.secure: "true"
#     traefik.ingress.kubernetes.io/service.sticky.cookie.httpOnly: "true"
#     traefik.ingress.kubernetes.io/service.sticky.cookie.sameSite: "lax"
# spec:
#   ingressClassName: traefik
#   tls:
#     - hosts: ["myapp.test"] # Đổi host ở đây
#       secretName: fe-tls
#   rules:
#     - host: myapp.test # Đổi host ở đây
#       http:
#         paths:
#           - path: /
#             pathType: Prefix
#             backend: { service: { name: frontend, port: { number: 80 } } }
