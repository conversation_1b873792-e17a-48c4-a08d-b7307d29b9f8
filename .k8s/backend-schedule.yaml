apiVersion: batch/v1
kind: CronJob
metadata:
  name: backend-schedule
  namespace: 2048-app
spec:
  schedule: "* * * * *"
  jobTemplate:
    spec:
      template:
        spec:
          restartPolicy: OnFailure
          imagePullSecrets: [{ name: regcred }]
          containers:
            - name: scheduler
              image: registry.gitlab.com/trantrongbinh/vj-2048/backend:latest
              envFrom: [{ secretRef: { name: backend-secrets } }]
              command: ["sh","-c","php artisan schedule:run"]
