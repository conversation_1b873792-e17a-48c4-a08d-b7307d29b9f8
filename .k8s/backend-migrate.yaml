apiVersion: batch/v1
kind: Job
metadata:
  name: backend-migrate
  namespace: 2048-app
spec:
  template:
    spec:
      restartPolicy: Never
      imagePullSecrets: [{ name: regcred }]
      containers:
        - name: migrate
          image: registry.gitlab.com/trantrongbinh/vj-2048/backend:latest
          envFrom: [{ secretRef: { name: backend-secrets } }]
          command: ["sh","-c","php artisan migrate --force"]
