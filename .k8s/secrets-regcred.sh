#!/usr/bin/env bash

# Script tạo imagePullSecret regcred (tên đặt tùy ý - registry credentials) kéo ảnh từ GitLab Container Registry
set -euo pipefail
: "${GITLAB_USER:?Missing}"; : "${GITLAB_TOKEN:?Missing}"
kubectl -n 2048-app create secret docker-registry regcred \
  --docker-server=registry.gitlab.com \
  --docker-username="$GITLAB_USER" \
  --docker-password="$GITLAB_TOKEN" \
  --docker-email="<EMAIL>" \
  --dry-run=client -o yaml | kubectl apply -f -
