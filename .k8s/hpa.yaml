apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata: { name: frontend-hpa, namespace: 2048-app }
spec:
  scaleTargetRef: { apiVersion: apps/v1, kind: Deployment, name: frontend }
  minReplicas: 2
  maxReplicas: 10
  metrics:
    - type: Resource
      resource: { name: cpu, target: { type: Utilization, averageUtilization: 60 } }
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata: { name: backend-hpa, namespace: 2048-app }
spec:
  scaleTargetRef: { apiVersion: apps/v1, kind: Deployment, name: backend }
  minReplicas: 2
  maxReplicas: 10
  metrics:
    - type: Resource
      resource: { name: cpu, target: { type: Utilization, averageUtilization: 60 } }
