apiVersion: apps/v1
kind: Deployment
metadata:
  name: backend-queue
  namespace: 2048-app
  labels: { app: backend-queue }
spec:
  replicas: 1
  selector: { matchLabels: { app: backend-queue } }
  template:
    metadata: { labels: { app: backend-queue } }
    spec:
      imagePullSecrets: [{ name: regcred }]
      containers:
        - name: worker
          image: registry.gitlab.com/trantrongbinh/vj-2048/backend:latest
          envFrom: [{ secretRef: { name: backend-secrets } }]
          command: ["sh","-c","php artisan queue:work --sleep=1 --tries=3 --memory=256"]
          resources:
            requests: { cpu: "50m", memory: "128Mi" }
            limits:   { cpu: "300m", memory: "256Mi" }
