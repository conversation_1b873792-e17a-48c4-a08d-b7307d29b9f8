apiVersion: v1
kind: Service
metadata:
  name: redis-master
  namespace: 2048-app
spec:
  selector: { app: redis, role: master }
  ports:
    - name: redis
      port: 6379
      targetPort: 6379
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: redis-master
  namespace: 2048-app
spec:
  serviceName: redis-master
  replicas: 1
  selector:
    matchLabels: { app: redis, role: master }
  template:
    metadata:
      labels: { app: redis, role: master }
    spec:
      containers:
        - name: redis
          image: redis:7.2-alpine
          args: ["--requirepass", "$(REDIS_PASSWORD)"]
          env:
            - name: REDIS_PASSWORD
              valueFrom: { secretKeyRef: { name: backend-secrets, key: REDIS_PASSWORD } }
          ports: [{ containerPort: 6379, name: redis }]
          volumeMounts:
            - name: data
              mountPath: /data
          resources:
            requests: { cpu: "100m", memory: "128Mi" }
            limits:   { cpu: "500m", memory: "512Mi" }
  volumeClaimTemplates:
    - metadata: { name: data }
      spec:
        accessModes: ["ReadWriteOnce"]
        resources: { requests: { storage: 2Gi } }
        storageClassName: "local-path"
