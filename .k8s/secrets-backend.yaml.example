apiVersion: v1
kind: Secret
metadata:
  name: backend-secrets
  namespace: 2048-app
type: Opaque
stringData:
  APP_ENV: "production"
  APP_KEY: <PLACEHOLDER>
  APP_DEBUG: "false"
  DEBUG_LOG_QUERIES: "false"

  APP_URL: <PLACEHOLDER>
  FRONTEND_URL: <PLACEHOLDER>
  FRONTEND_INTERNAL_URL: <PLACEHOLDER>

  SANCTUM_STATEFUL_DOMAINS: <PLACEHOLDER>
  USER_INFO_COOKIE_NAME: "_u_info_"

  DB_CONNECTION: "mysql"
  DB_HOST: <PLACEHOLDER>
  DB_PORT: "3307"
  DB_DATABASE: <PLACEHOLDER>
  DB_USERNAME: <PLACEHOLDER>
  DB_PASSWORD: <PLACEHOLDER>

  SESSION_DRIVER: "cookie"
  SESSION_LIFETIME: "120"
  SESSION_ENCRYPT: "false"
  SESSION_PATH: "/"
  SESSION_DOMAIN: <PLACEHOLDER>

  REDIS_CLIENT: "phpredis"
  REDIS_HOST: "redis-master.2048-app.svc.cluster.local"
  REDIS_PASSWORD: <PLACEHOLDER>
  REDIS_PORT: "6379"

  CHROMIUM_BINARIES: '/usr/bin/chromium' # OR '/usr/bin/chromium-browser'
