image: docker:27.5

services:
  - docker:27.5-dind

workflow:
  rules:
    - changes:
      - .docker/**/* # ** để đệ quy nếu nhiều folder lồng nhau
      - server/app/**/*
      - server/bootstrap/**/*
      - server/config/*
      - server/public/**/*
      - server/resources/**/*
      - server/routes/*
      - server/package.json
      - server/composer.json
      - server/vite.config.js
      - client/**/*
      when: always
    - when: never

stages:
  - build
  - release

before_script:
  - docker version
  # các biến được gitlab khai báo: https://docs.gitlab.com/ee/ci/variables/
  - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY # xem thêm về biến: https://docs.gitlab.com/ee/ci/variables/predefined_variables.html

build-develop:
  stage: build
  only:
    - develop
  script:
    - docker pull $CI_REGISTRY_IMAGE/backend:develop || true
    - docker build -f .docker/backend.prod.Dockerfile --cache-from $CI_REGISTRY_IMAGE/backend:develop --tag $CI_REGISTRY_IMAGE/backend:develop --target backend .
    - docker push $CI_REGISTRY_IMAGE/backend:develop

    - docker pull $CI_REGISTRY_IMAGE/frontend:develop || true
    - docker build -f .docker/frontend.prod.Dockerfile --cache-from $CI_REGISTRY_IMAGE/frontend:develop --tag $CI_REGISTRY_IMAGE/frontend:develop --target app .
    - docker push $CI_REGISTRY_IMAGE/frontend:develop

# release-branch:
#   variables:
#     GIT_STRATEGY: none
#   stage: release
#   script:
#     - docker pull $CI_REGISTRY_IMAGE/backend:$CI_COMMIT_SHA
#     - docker tag $CI_REGISTRY_IMAGE/backend:$CI_COMMIT_SHA $CI_REGISTRY_IMAGE/backend:$CI_COMMIT_REF_NAME
#     - docker push $CI_REGISTRY_IMAGE/backend:$CI_COMMIT_REF_NAME

#     - docker pull $CI_REGISTRY_IMAGE/frontend:$CI_COMMIT_SHA
#     - docker tag $CI_REGISTRY_IMAGE/frontend:$CI_COMMIT_SHA $CI_REGISTRY_IMAGE/frontend:$CI_COMMIT_REF_NAME
#     - docker push $CI_REGISTRY_IMAGE/frontend:$CI_COMMIT_REF_NAME

build-main:
  stage: build
  only:
    - main
  script:
    - docker pull $CI_REGISTRY_IMAGE/backend:latest || true
    - docker build -f .docker/backend.prod.Dockerfile --cache-from $CI_REGISTRY_IMAGE/backend:latest --tag $CI_REGISTRY_IMAGE/backend:$CI_COMMIT_SHA --target backend .
    - docker push $CI_REGISTRY_IMAGE/backend:$CI_COMMIT_SHA

    - docker pull $CI_REGISTRY_IMAGE/frontend:latest || true
    - docker build -f .docker/frontend.prod.Dockerfile --cache-from $CI_REGISTRY_IMAGE/frontend:latest --tag $CI_REGISTRY_IMAGE/frontend:$CI_COMMIT_SHA --target app .
    - docker push $CI_REGISTRY_IMAGE/frontend:$CI_COMMIT_SHA

release-latest:
  variables:
    GIT_STRATEGY: none
  stage: release
  only:
    - main
  script:
    - docker pull $CI_REGISTRY_IMAGE/backend:$CI_COMMIT_SHA
    - docker tag  $CI_REGISTRY_IMAGE/backend:$CI_COMMIT_SHA $CI_REGISTRY_IMAGE/backend:latest
    - docker push $CI_REGISTRY_IMAGE/backend:latest

    - docker pull $CI_REGISTRY_IMAGE/frontend:$CI_COMMIT_SHA
    - docker tag  $CI_REGISTRY_IMAGE/frontend:$CI_COMMIT_SHA $CI_REGISTRY_IMAGE/frontend:latest
    - docker push $CI_REGISTRY_IMAGE/frontend:latest
