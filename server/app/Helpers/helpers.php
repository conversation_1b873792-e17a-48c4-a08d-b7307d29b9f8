<?php

use Illuminate\Support\Str;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Facades\Storage;
use App\Services\Media\ImageService;
use App\Services\Media\FileService;
use Mews\Purifier\Facades\Purifier;

/**
 * Add css class when link is currently active
 *
 * @param array|string $path Single path or array of paths
 * @param string $activeClass Default is 'active'
 * @return string
 */
function active_link($name, $activeClass = 'active')
{
    return request()->route()->named($name) ? $activeClass : '';
}

function active_link_by_path($path, $activeClass = 'active')
{
    return Request::url() == $path ? $activeClass : '';
}

/**
 * Check if an input has error, e.g. validation errors from Laravel
 *
 * @param array|string $fields Input fields name
 * @param \Illuminate\Support\MessageBag $errors
 * @param string $errorCssClass   Css class when field has error
 *
 * @return string
 */
function has_error($fields, $errors, $errorCssClass = 'has-error')
{
    return $errors->hasAny($fields) ? $errorCssClass : '';
}

/**
 * Return a Carbon instance.
 */
function carbon(string $parseString = '', string $tz = null): Carbon
{
    return new Carbon($parseString, $tz);
}

/**
 * Return a formatted Carbon date.
 */
function humanize_date(Carbon $date, string $format = 'd/m/Y H:i'): string
{
    return $date->format($format);
}

function validateDate($date, $format = 'd/m/Y')
{
    $d = DateTime::createFromFormat($format, $date);
    // The Y ( 4 digits year ) returns TRUE for any integer with any number of digits so changing the comparison from == to === fixes the issue.
    return $d && $d->format($format) === $date;
}

function formatDateString($dateString, $format = 'd/m/Y')
{
    return date($format, strtotime($dateString));
}

function phutToString($so_phut)
{
    // Chuyển đổi phút thành giây
    $so_giay = $so_phut * 60;

    // Tính giờ, phút và giây
    $gio = floor($so_giay / 3600);
    $phut = floor(($so_giay % 3600) / 60);
    $giay = $so_giay % 60;

    // Định dạng lại chuỗi giờ, phút và giây để có dạng "00:00:00"
    $gio_str = str_pad($gio, 2, '0', STR_PAD_LEFT);
    $phut_str = str_pad($phut, 2, '0', STR_PAD_LEFT);
    $giay_str = str_pad($giay, 2, '0', STR_PAD_LEFT);

    // In ra định dạng "00:00:00"
    return "$gio_str:$phut_str:$giay_str";
}

function stringToPhut($timeString)
{
    $timeParts = explode(":", $timeString);

    if (count($timeParts) == 3) {
        // Handle "00:00:60" format (hours:minutes:seconds)
        $hours = (int)$timeParts[0];
        $minutes = (int)$timeParts[1];
        $seconds = (int)$timeParts[2];

        // Calculate the total minutes
        $totalMinutes = ($hours * 60) + $minutes + ($seconds / 60);

        return $totalMinutes;
    } elseif (count($timeParts) == 2) {
        // Handle "00:60" format (minutes:seconds)
        $hours = 0;
        $minutes = (int)$timeParts[0];
        $seconds = (int)$timeParts[1];

        // Calculate the total minutes
        $totalMinutes = $minutes + ($seconds / 60);

        return $totalMinutes;
    } elseif (count($timeParts) == 1) {
        // Handle "60" format (minutes)
        return (int)$timeParts[0];
    } else {
        return false;
    }
}

function selected_select2_values($selectedValues)
{
    return is_array($selectedValues) ? implode(',', $selectedValues) : $selectedValues;
}

function selected_select_value($selectedKey, $selectedValue)
{
    return request()->get($selectedKey) == $selectedValue ? 'selected' : '';
}

function uploaded_image_url($uploadedPath)
{
    return $uploadedPath ? app()->make(ImageService::class)->uploadedImageUrl($uploadedPath) : null;
}

function uploaded_resized_image_url($uploadedPath, $size)
{
    return $uploadedPath ? app()->make(ImageService::class)->resizedImageUrl($uploadedPath, $size) : null;
}

function uploaded_file_url($uploadedPath)
{
    return $uploadedPath ? app()->make(FileService::class)->uploadedFileUrl($uploadedPath): null;
}

function asset_image($path = '')
{
    if ($path) {
        $path = str_contains($path, 'http') ? $path : ('/storage/' . $path);
    }

    return $path;
}

function route_slug_id($arr = []) {
    return remove_utf8_bom(implode('-', $arr));
}

function decodeSlug($fatSlug)
{
    preg_match('/^(.+)-([0-9]+)$/', $fatSlug, $parts);

    return [
        'slug' => $parts[1] ?? null,
        'id' => $parts[2] ?? null,
    ];
}

function replace_base_url($url, $replacements)
{
    $patterns = '/^https?:\/\/[^\/]+/i';

    return preg_replace($patterns, $replacements, $url);
}

function removeEmptyTags($html_replace)
{
    $pattern = "/<[^\/>]*>([\s]?)*<\/[^>]*>/";

    return preg_replace($pattern, '', $html_replace);
}

function get_between($input, $start, $end)
{
    $substr = substr($input, strpos($input, $start), (strlen($input) - strpos($input, $end) - strlen($end))*(-1));

    return $substr;
}

function remove_font_family($content, $replacements = '', $removeMore = false)
{
    $content = preg_replace('/cursor:.+?url(?:.+?\)).+?;/', '', $content);

    if ($removeMore) {
        // remove comment html
        $content = preg_replace('/(?=<!--)([\s\S]*?)-->/', '', $content);
        // remove some style atribute
        $content = preg_replace('/(font-family|line-height|font-size).+?;/', $replacements, $content);
    } else {
        $content = preg_replace('/font-family.+?;/', $replacements, $content);
    }

    return $content;
}

// for object
function buildTreeFromObjects($items)
{
    $children = [];

    foreach ($items as $item)
        $children[$item->parent_id][] = $item;

    foreach ($items as $item) if (isset($children[$item->id]))
        $item->childs = $children[$item->id];

    return $children[0];
}

// or array version
function buildTreeFromArray(array &$elements, $parentId = 0) {
    $branch = array();

    foreach ($elements as $element) {
        if ($element['parent_id'] == $parentId) {
            $children = buildTreeFromArray($elements, $element['id']);
            if ($children) {
                $element['children'] = $children;
            }
            $branch[$element['id']] = $element;
            unset($elements[$element['id']]);
        }
    }
    return $branch;
}

function time_elapsed_string($time)
{
    $time = time() - strtotime($time); // to get the time since that moment
    $time = ($time < 1) ? 1 : $time;
    $tokens = array(
        31536000 => 'năm',
        2592000 => 'tháng',
        604800 => 'tuần',
        86400 => 'ngày',
        3600 => 'giờ',
        60 => 'phút',
        1 => 'giây'
    );

    foreach ($tokens as $unit => $text) {
        if ($time < $unit) continue;
        $numberOfUnits = floor($time / $unit);
        return $numberOfUnits . ' ' . $text . ' trước';
    }
}

/**
 * @param $n
 * @return string
 * Converts a number into a short version, eg: 1000 -> 1k
 */
function number_format_short( $n, $precision = 1 )
{
    if ($n < 900) {
        // 0 - 900
        $n_format = number_format($n, $precision);
        $suffix = '';
    } else if ($n < 900000) {
        // 0.9k-850k
        $n_format = number_format($n / 1000, $precision);
        $suffix = ' K';
    } else if ($n < 900000000) {
        // 0.9m-850m
        $n_format = number_format($n / 1000000, $precision);
        $suffix = ' M';
    } else if ($n < 900000000000) {
        // 0.9b-850b
        $n_format = number_format($n / 1000000000, $precision);
        $suffix = ' B';
    } else {
        // 0.9t+
        $n_format = number_format($n / 1000000000000, $precision);
        $suffix = ' T';
    }
    // Remove unecessary zeroes after decimal. "1.0" -> "1"; "1.00" -> "1"
    // Intentionally does not affect partials, eg "1.50" -> "1.50"
    if ( $precision > 0 ) {
        $dotzero = '.' . str_repeat( '0', $precision );
        $n_format = str_replace( $dotzero, '', $n_format );
    }

    return $n_format . $suffix;
}

if (!function_exists('dateRange')) {
    function dateRange($first, $last, $step = '+1 day', $format = 'Y-m-d')
    {
        $dates = array();
        $current = strtotime($first);
        $last = strtotime($last);

        while ($current <= $last) {

            $dates[] = date($format, $current);
            $current = strtotime($step, $current);
        }

        return $dates;
    }
}

if (!function_exists('monthRange')) {
    function monthRange($first, $last, $desc = null, $step = '+1 month', $format = 'Y-m')
    {
        $dates = [];
        $current = strtotime($first);
        $last = strtotime($last);

        while ($current <= $last) {
            $dates[] = date($format, $current);
            $current = strtotime($step, $current);
        }

        if ($desc == 'desc') {
            $dates = collect($dates)->sortDesc()->toArray();
        }

        return $dates;
    }
}

function getDateRangePicker($date, $format)
{
    if (!empty($date)) {
        $beginDate = Carbon::make(explode(' - ', $date)[0])->format($format);
        $endDate = Carbon::make(explode(' - ', $date)[1])->format($format);
    } else {
        $now = Carbon::now();
        $beginDate = $now->startOfMonth()->format($format);
        $endDate = $now->endOfMonth()->format($format);
    }

    return [$beginDate, $endDate];
}

function html_clean($content = '')
{
    return preg_replace('/[:\.]+/mu', '.', preg_replace('/[\s\n\t\r\\\]+/mu', ' ', html_entity_decode(strip_tags($content))));
}

function text_clean($str = '')
{
    return preg_replace('/([:\.])+/mu', '$1', preg_replace('/[\s\n\t\r\\\]+/mu', ' ', $str));
}

function text_limit($str = '', $limit = 100, $suffix = '...')
{
    return  Str::limit($str, $limit, $suffix);
}

function convert_to_slug($content, $limit = null, $suffix = null)
{
    $str = strip_tags($content);
    $slug = Str::slug(preg_replace('/\W+/', ' ', Str::ascii($str)));

    if ($limit) {
        $slug = Str::limit($slug, $limit, '');
    }

    if ($suffix) {
        $slug = $slug . '-' . $suffix;
    }

    return rtrim($slug, '-');
}

function ratingCount($count, $max = 50) {
    return $count > 0 ? $count : round($max * 0.2, 0); // default: 20% of max
}

if(!function_exists('my_debug')) {
    function my_debug($var, $is_die = true) {
        echo '<pre>' . print_r($var, true) . '</pre>';
        if ($is_die) {
            die();
        }
    }
}

function str_to_slug($str, $limit = 70)
{
    $slug = Str::limit(Str::slug(preg_replace('/\W+/', ' ', Str::ascii($str))), $limit, '');

    return trim($slug, '-');
}

function replaceStr($str, $from, $to)
{
    if ($from && $to) {
        $str = str_replace($from, $to, $str);
    }

    return $str;
}

function cleanStringMath($text) {
    $matchArr = array(
        '\Rightarrow' => '',
        '\sqrt' => '',
        '\left' => '',
        '\right' => '',
        '\begin' => '',
        '\end' => '',
        '\frac' => '',
        '{array}' => '',
        '\forall' => '',
        '\in' => '',
        '\mathbb' => '',
        '\Leftrightarrow' => '',
        '{l}' => '',
        '\ge' => '',
        '\le' => '',
    );

    return str_replace(array_keys($matchArr), array_values($matchArr), $text);
}

function remove_utf8_bom($text)
{
    $bom = pack('H*','EFBBBF');
    $text = preg_replace("/^$bom/", '', $text);

    return $text;
}

function sortByKeyValue($data, $sortKey, $sort_flag = 'ASC')
{
    if (empty($data) or empty($sortKey)) return $data;

    switch ($sort_flag) {
        case 'ASC':
            usort($data, function ($first, $second) use ($sortKey) {
                return $first[$sortKey] > $second[$sortKey];
            });
            break;
        case 'DESC':
            usort($data, function ($first, $second) use ($sortKey) {
                return $first[$sortKey] < $second[$sortKey];
            });
            break;
        default:
            break;
    }

    return $data;
}

function removeUl($html)
{
    return str_replace(['<ul>', '</ul>'], '', $html);
}

function handleContentForEditor($html)
{
    $html = str_replace('contenteditable', 'contentedit', $html);
    $html = str_replace('Hiển thị đ&aacute;p &aacute;n', 'Hướng dẫn giải', $html);
    $pattems = ['vj-answer-text', 'vj-template-answer'];

    return str_replace($pattems, '', $html);
}

// Code đếm số dòng trong văn bản
function count_paragraph($insertion, $paragraph_id, $content) {
    $closing_p = '</p>';
    $paragraphs = explode($closing_p, $content);

    foreach ($paragraphs as $index => $paragraph) {
        if (trim($paragraph)) {
            $paragraphs[$index] .= $closing_p;
        }

        if ($paragraph_id == $index + 1) {
            $paragraphs[$index] .= $insertion;
        }
    }

    return implode('', $paragraphs);
}
function count_paragraphs($insertions, $paragraph_ids, $content) {
    $closing_p = '</p>';
    $paragraphs = explode($closing_p, $content);

    foreach ($paragraphs as $index => $paragraph) {
        if (trim($paragraph)) {
            $paragraphs[$index] .= $closing_p;
        }

        if (in_array($index + 1, $paragraph_ids)) {
            $paragraphs[$index] .= ($insertions[array_search($index + 1, $paragraph_ids)] ?? '');
        }
    }

    return implode('', $paragraphs);
}

function pkcs7Pad($data, $blockSize) {
    $padSize = $blockSize - (strlen($data) % $blockSize);

    return $data . str_repeat(chr($padSize), $padSize);
}

function encryptAES($input, $key) {
    $blockSize = 16; // AES block size
    $iv = openssl_random_pseudo_bytes($blockSize);
    $paddedInput = pkcs7Pad($input, $blockSize);
    $encrypted = openssl_encrypt($paddedInput, 'aes-256-cbc', $key, OPENSSL_RAW_DATA, $iv);
    return base64_encode($iv . $encrypted);
}

function numberToLetter($number) {
    $letters = ['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z'];

    if ($number > 0 && $number < 27) {
        return $letters[$number-1];
    }

    if ($number > 26 && $number < 53) {
        return (isset($letters[$number - 26 -1]) ? str_repeat($letters[$number - 26 -1], 2) : $number);
    }

    return (isset($letters[$number - 52 -1]) ? str_repeat($letters[$number - 52 -1], 3) : $number);
}

function getVideoYoutubeId($video_url) {
    preg_match('/^.*(?:(?:youtu\.be\/|v\/|vi\/|u\/\w\/|embed\/)|(?:(?:watch)?\?v(?:i)?=|\&v(?:i)?=))([^#\&\?]*).*/', $video_url, $matches);

    return ($matches[1] ?? null);
}

function diskFilePath($disk, $filepath)
{
    return config('filesystems.disks.' . $disk . '.root') . '/' . $filepath;
}

function flatten($array) {
    $result = [];
    foreach ($array as $item) {
        if (is_array($item)) {
            $result[] = array_filter($item, function($array) {
                return ! is_array($array);
            });
            $result = array_merge($result, flatten($item));
        }
    }
    return array_filter($result);
}

function purifyContentHasMath($html)
{
    $pattern = '/<math.*?>.*?<\/math>/s';

    preg_match_all($pattern, $html, $matches);

    $placeholders = [];

    foreach ($matches[0] as $index => $match) {
        $placeholder = "__MATH_PLACEHOLDER_{$index}__";
        $placeholders[$placeholder] = $match;
        $html = str_replace($match, $placeholder, $html);
    }

    $clean_html = Purifier::clean($html, 'simple_html');

    foreach ($placeholders as $placeholder => $original) {
        $clean_html = str_replace($placeholder, $original, $clean_html);
    }

    return $clean_html;
}

function isBase64(string $data): bool
{
    if (str_starts_with($data, 'data:image')) {
        $data = substr($data, strpos($data, ',') + 1);
    }

    $decoded = base64_decode($data, true);

    return $decoded !== false && base64_encode($decoded) === $data;
}

function normalizeText($text): string
{
    if (!is_string($text)) {
        return '';
    }

    // Trim whitespace
    $normalized = trim($text);

    // Convert to lowercase
    $normalized = mb_strtolower($normalized, 'UTF-8');

    // Loại bỏ dấu tiếng Việt (optional - có thể bật/tắt tùy yêu cầu)
    // $normalized = $this->removeVietnameseAccents($normalized);

    // Loại bỏ multiple spaces
    $normalized = preg_replace('/\s+/', ' ', $normalized);

    return $normalized;
}

/**
 * Loại bỏ dấu tiếng Việt (optional)
 */
function removeVietnameseAccents($str): string
{
    $accents = [
        'à', 'á', 'ạ', 'ả', 'ã', 'â', 'ầ', 'ấ', 'ậ', 'ẩ', 'ẫ', 'ă', 'ằ', 'ắ', 'ặ', 'ẳ', 'ẵ',
        'è', 'é', 'ẹ', 'ẻ', 'ẽ', 'ê', 'ề', 'ế', 'ệ', 'ể', 'ễ',
        'ì', 'í', 'ị', 'ỉ', 'ĩ',
        'ò', 'ó', 'ọ', 'ỏ', 'õ', 'ô', 'ồ', 'ố', 'ộ', 'ổ', 'ỗ', 'ơ', 'ờ', 'ớ', 'ợ', 'ở', 'ỡ',
        'ù', 'ú', 'ụ', 'ủ', 'ũ', 'ư', 'ừ', 'ứ', 'ự', 'ử', 'ữ',
        'ỳ', 'ý', 'ỵ', 'ỷ', 'ỹ',
        'đ'
    ];

    $noAccents = [
        'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a',
        'e', 'e', 'e', 'e', 'e', 'e', 'e', 'e', 'e', 'e', 'e',
        'i', 'i', 'i', 'i', 'i',
        'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o',
        'u', 'u', 'u', 'u', 'u', 'u', 'u', 'u', 'u', 'u', 'u',
        'y', 'y', 'y', 'y', 'y',
        'd'
    ];

    return str_replace($accents, $noAccents, $str);
}
