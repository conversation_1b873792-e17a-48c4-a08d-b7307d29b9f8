<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Event;
use Illuminate\Auth\Events\Login;
use Illuminate\Auth\Events\Logout;
use App\Listeners\LoginListener;
use App\Listeners\LogoutListener;
use App\Models\Classroom;
use App\Policies\ClassroomPolicy;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\URL;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        Event::listen(
            Login::class,
            LoginListener::class,
        );

        Event::listen(
            Logout::class,
            LogoutListener::class,
        );

        // policy
        Gate::policy(Classroom::class, ClassroomPolicy::class);

        // Override unauthorized response
        Gate::after(function ($user, $ability, $result, $arguments) {
            if ($result === false) {
                throw new AuthorizationException('Bạn không có quyền thực hiện hành động này.');
            }
        });

        if (app()->environment('production')) {
            URL::forceScheme('https');
        }

        if (config('app.debug_log_queries')) {
            DB::listen(function ($query) {
                Log::channel('queries')->debug($query->sql, [
                    'time' => $query->time . ' ms', // milisecond
                    'bindings' => $query->bindings,
                ]);
            });
        }
    }
}
