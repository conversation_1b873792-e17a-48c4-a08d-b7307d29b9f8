<?php

namespace App\Services;

use App\Concern\CallApiTrait;
use App\Models\Question;
use App\Models\TableContent;
use App\Services\Media\ImageService;
use Exception;
use Mews\Purifier\Facades\Purifier;

class TableContentService {
    use CallApiTrait;

    protected $excelService;

    public function __construct(ExcelService $excelService)
    {
        $this->excelService = $excelService;
    }

    public function handleImportFile($request, $quiz): array
    {
        try {
            $response = $this->callApiHandleFile($request);

            $res = json_decode($response, true);

            if (isset($res['error'])) {
                $errMessage = is_string($res['error']) ? $res['error'] : "Không đọc được file";
                throw new \Exception($errMessage);
            }

            if (empty($res['data'])) {
                throw new \Exception('Không đọc được nội dung file');
            }

            $importQuestions = $this->importQuestionsFromFile($res, $quiz, $request->file_type);

            return [
                'status' => 200,
                'data' => $importQuestions,
                'imported' => true,
                'message' => 'Import đề thi thành công',
            ];
        } catch (\Exception $e) {
            return [
                'status' => 500,
                'data' => [],
                'imported' => false,
                'message' => $e->getMessage(),
            ];
        }
    }

    public function callApiHandleFile($request): string
    {
        if ($request->file_type == 'excel') {
            return $this->handleExcel($request->file('file'));
        }

        return $this->makeRequest(
            config('app.tool_api_domain'),
            'POST',
            '/api/file/upload',
            [],
            [
                'type' => 'cau_hoi',
                'question_flag' => $request->question_flag,
                'reason_flag' => $request->reason_flag,
                'content_common' => $request->content_common,
            ],
            [
                'file' => $request->file('file')
            ]
        );
    }

    public function handleExcel($file): string
    {
        try {
            $this->excelService->validateExcelFile($file);

            $columns = [
                'question' => 'A',
                'type' => 'B',
                'answer1' => 'C',
                'answer2' => 'D',
                'answer3' => 'E',
                'answer4' => 'F',
                'answer5' => 'G',
                'correct' => 'H',
                'timeLimit' => 'I',
                'explain' => 'J'
            ];

            $result = $this->excelService->processExcelFile($file, function($rowData, $row) {
                return $this->processQuestionRow($rowData, $row);
            }, [
                'columns' => $columns,
                'required_columns' => ['question'],
                'start_row' => 3,
                'max_empty_rows' => 10
            ]);

            if (!empty($result['errors'])) {
                throw new Exception('File có lỗi tại các dòng: ' . implode(', ', array_column($result['errors'], 'row')));
            }

            if (empty($result['results'])) {
                throw new Exception('Không tìm thấy câu hỏi hợp lệ trong file Excel');
            }

            return json_encode([
                'data' => $result['results'],
                'images' => []
            ]);
        } catch (\Exception $e) {
            return json_encode([
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * @throws Exception
     */
    private function processQuestionRow(array $rowData, int $row): ?array
    {
        if (empty($rowData['question'])) {
            return null;
        }

        // Parse question type
        $questionType = $this->parseQuestionType($rowData['type'] ?? '');

        // Tạo danh sách đáp án
        $options = [];
        foreach (['answer1', 'answer2', 'answer3', 'answer4', 'answer5'] as $answerKey) {
            if (!empty($rowData[$answerKey])) {
                $options[] = [
                    'content' => $rowData[$answerKey],
                    'answer' => 'N'
                ];
            }
        }

        if (empty($options)) {
            throw new Exception("Câu hỏi tại dòng {$row} không có đáp án");
        }

        // Xác định đáp án đúng
        /*if (!empty($rowData['correct'])) {
            $correctIndex = $this->excelService->parseCorrectAnswer($rowData['correct'], count($options));
            if ($correctIndex !== null && isset($options[$correctIndex])) {
                $options[$correctIndex]['answer'] = 'Y';
            }
        }*/

        // Xác định đáp án đúng (hỗ trợ nhiều đáp án)
        $correctIndices = [];
        if (!empty($rowData['correct'])) {
            $correctIndices = $this->excelService->parseMultipleCorrectAnswers($rowData['correct'], count($options), $row);

            // Đánh dấu các đáp án đúng
            foreach ($correctIndices as $index) {
                if (isset($options[$index])) {
                    $options[$index]['answer'] = 'Y';
                }
            }
        }

        return [
            'content' => $rowData['question'],
            'options' => $options,
            'answers' => $correctIndices,
            'reason' => $rowData['explain'] ?? '',
            'timeLimit' => $rowData['timeLimit'] ?? 0,
            'type' => $questionType,
        ];
    }

    private function parseQuestionType(string $type): string
    {
        $type = trim($type);

        $typeMapping = [
            'Multiple Choice' => Question::TYPE_QUIZX,
            'Fill-in-the-Blank' => Question::TYPE_BLANK,
            'test' => Question::TYPE_QUIZX,
        ];

        return $typeMapping[$type] ?? Question::TYPE_QUIZX;
    }

    public function importQuestionsFromFile($res, $tableContent, $fileType): array
    {
        $importQuestions = [];
        $data = $res['data'] ?? []; // data->part->question
        $images = $res['images'] ?? [];

        $maxIndex = Question::where('table_content_id', $tableContent->id)->max('index') ?: 0;

        if ($fileType == 'excel') {
            foreach ($data as $questionData) {
                if (empty($questionData['content'])) {
                    continue;
                }

                $importQuestions[] = $this->createQuestion($questionData, $tableContent, ++$maxIndex, $images, $fileType);
            }

            return $importQuestions;
        }

        // file word
        foreach ($data as $part) {
            foreach ($part['question'] as $questionData) {
                if (empty($questionData['content'])) {
                    continue;
                }

                if ($questionData['type'] === "test") {
                    $importQuestions[] = $this->createQuestion($questionData, $tableContent, ++$maxIndex, $images);
                }
            }
        }

        return $importQuestions;
    }

    public function createQuestion($questionData, $tableContent, $index, $images, $fileType = 'word')
    {
        $prefixFolder = "quiz/$tableContent->id";

        $qContent = $this->handleImageInContent($questionData['content'], $images, $prefixFolder);
        $formattedData = $this->formatContentJson($questionData, $qContent, $images, $prefixFolder);

        $titleText = Purifier::clean($questionData['content'], 'none_html');
        $answersText = '';
        foreach ($questionData['options'] as $option) {
            $answersText .= ' ' . strip_tags($option['content']);
        }

        $title = $titleText . $answersText;
        $seoTitle = text_limit($title, config('web.title_limit'), '');

        return Question::create([
            'title' => $seoTitle,
            'slug' => convert_to_slug($seoTitle),
            'content' => $qContent,
            'table_content_id' => $tableContent->id,
            'content_json' => $formattedData,
            'editor_id' => auth()->id(),
            'explain' => $this->handleImageInContent($questionData['reason'], $images, $prefixFolder),
            'status' => Question::ACTIVE,
            'type' => $this->parseQuestionType($questionData['type']),
            'index' => $index,
            'is_updated' => Question::UPDATED,
            'import_origin' => "file_$fileType",
            'origin_id' => 0,
            'seo_title' => $seoTitle,
            'seo_description' => $title,
            'grade_id' => $tableContent->grade_id,
            'subject_id' => $tableContent->subject_id,
        ]);
    }

    public function formatContentJson($apiQuestion, $qContent, $images, $prefixFolder): array
    {
        $options = [];
        $correctAnswerIndex = [];

        foreach ($apiQuestion['options'] as $index => $option) {
            $options[] = [
                'content' => $this->handleImageInContent($option['content'], $images, $prefixFolder),
                'isCorrect' => $option['answer'] == 'Y',
                'origin_label' => $option['label'] ?? '',
            ];

            if ($option['answer'] == 'Y') {
                $correctAnswerIndex[] = $index;
            }
        }

        return [
            'content' => $qContent,
            'options' => $options,
            'answer' => $correctAnswerIndex,
            'points' => 1,
            'timeLimit' => $apiQuestion['timeLimit'] ?? 0,
        ];
    }

    public function handleImageInContent($content, $images, $prefixFolder): string
    {
        if (count($images) === 0) {
            return $content;
        }

        [$dirname, $newDirname] = $this->getDirnameAndNewDirname($images, $prefixFolder);

        return remove_font_family(replaceStr($content, $dirname, $newDirname), '', true);
    }

    public function getDirnameAndNewDirname($images, $prefixFolder): array
    {
        $dirname = '';
        $newDirname = '';

        if (count($images) > 0) {
            $baseUrl = config('app.url');

            $imageService = app()->make(ImageService::class);

            foreach ($images as $image) {
                if (!$dirname) {
                    $dirname = dirname($image);
                }

                $path = $imageService->saveByUrl($prefixFolder, $image);

                if (!$newDirname) {
                    $newDirname = $baseUrl . '/storage/' . dirname($path);
                }
            }
        }

        return [$dirname, $newDirname];
    }

    public function createTableContent($title, $importOrigin)
    {
        return TableContent::create([
            'title' => $title,
            'subtitle' => $title,
            'editor_id' => auth()->id(),
            'type' => TableContent::TYPE_EXAM_QUIZ,
            'import_origin' => $importOrigin,
            'origin_id' => 0,
            'seo_title' => $title,
        ]);
    }

    public function getQuizRelated($quiz)
    {
        return TableContent::selectColumns()
            ->with(['grade:id,title', 'subject:id,title'])
            ->withCount('questions')
            ->where('type', TableContent::TYPE_EXAM_QUIZ)
            ->when($quiz->toc_id, fn($q) => $q->where('toc_id', $quiz->toc_id))
            ->when($quiz->course_id, fn($q) => $q->where('course_id', $quiz->course_id))
            ->when($quiz->grade_id, fn($q) => $q->where('grade_id', $quiz->grade_id))
            ->when($quiz->subject_id, fn($q) => $q->where('subject_id', $quiz->subject_id))
            ->where('id', '<>', $quiz->id)
            ->orderByDesc('id')
            ->limit(8)
            ->get();
    }
}
