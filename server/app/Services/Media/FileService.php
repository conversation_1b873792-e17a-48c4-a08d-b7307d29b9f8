<?php

namespace App\Services\Media;

use Exception;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;

class FileService
{
    private function storage()
    {
        return Storage::disk(config('media.file.storage_disk'));
    }

    private function logger()
    {
        return Log::channel('file_service');
    }

    public function folder($prefixFolder)
    {
        $location = config('media.file.base_folder');

        if ($prefixFolder) {
            $location .= $prefixFolder;
        }

        // if (auth()->id()) {
        //     $location .= auth()->id();
        // }

        $this->storage()->makeDirectory($location);

        return $location;
    }

    public function deleteFiles($oldFilesPath)
    {
        try {
            $this->storage()->delete($oldFilesPath);
        } catch (Exception $e) {
            $this->logger()->error($e);
        }
    }

    public function saveHtmlFile($folder, string $content, $fileName)
    {
        try {
            $fullPath = $folder . $fileName;

            $isSaved = $this->storage()->put($fullPath, $content);

            if (!$isSaved) {
                return null;
            }

            return $fullPath;
        } catch (Exception $e) {
            report($e);
            return null;
        }
    }

    private function constructFileName($fileName, $extension)
    {
        $fileName = Str::slug($fileName, '-') . '-' . time();

        return strtolower("{$fileName}.{$extension}");
    }

    public function upload($prefixFolder, $file, $filename = null, $oldFilesPath = [])
    {
        try {
            $folder = $this->folder($prefixFolder);
            $filename = $this->constructFileName($filename ?? pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME), $file->getClientOriginalExtension());
            $storedFilePath = $folder . '/' . $filename;

            $isUploaded = $this->storage()->put($storedFilePath, $file->get());

            if (!$isUploaded) {
                return null;
            }

            if ($oldFilesPath) {
                $this->deleteFiles($oldFilesPath);
            }

            return $storedFilePath;
        } catch (Exception $e) {
            $this->logger()->error($e);
            return null;
        }
    }

    public function uploadedFileUrl($uploadedPath)
    {
        return $this->storage()->url($uploadedPath);
    }

    public function fileExists($path)
    {
        return $this->storage()->exists($path);
    }

    public function getUrlPath($path)
    {
        return $this->storage()->path($path);
    }

    public function download($filePath, $fileName, $headers = ['Content-Type' => 'application/octet-stream'])
    {
        if (!$filePath || !$this->storage()->exists($filePath)) {
            $contents = 'Tệp đang trong thời gian biên soạn, chúng tôi sẽ cập nhật lại ngay sau khi biên soạn xong!';
            return response()->stream(function () use ($contents) {
                echo $contents;
            }, 200, [
                'Cache-Control'         => 'must-revalidate, post-check=0, pre-check=0',
                'Content-Type'          => 'application/octet-stream',
                'Content-Disposition'   => 'attachment; filename="Download.txt"',
            ]);
        }

        $fileExtension = File::extension($filePath);

        return $this->storage()->download($filePath, "$fileName.$fileExtension", $headers);
    }
}
