<?php

namespace App\Services\Media;

use App\Concern\CallApiTrait;
use CURLFile;
use Exception;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Spatie\Image\Image;
use Spatie\ImageOptimizer\OptimizerChain;
use Spatie\ImageOptimizer\Optimizers\Jpegoptim;
use Spatie\ImageOptimizer\Optimizers\Pngquant;

class ImageService
{
    use CallApiTrait;

    private function storage()
    {
        return Storage::disk(config('media.image.storage_disk'));
    }

    private function logger()
    {
        return Log::channel('images_service');
    }

    public function folder($prefixFolder)
    {
        $folder = config('media.image.base_folder') . $prefixFolder;

        $this->storage()->makeDirectory($folder);

        return $folder;
    }

    protected function resizedImagePath($originalFilePath, $width, $height)
    {
        $lastPositionOfDot = strrpos($originalFilePath, '.');
        if ($lastPositionOfDot === false) {
            return sprintf('%s_%sx%s', $originalFilePath, $width, $height);
        }

        $extension = substr($originalFilePath, $lastPositionOfDot);
        $startName = substr($originalFilePath, 0, $lastPositionOfDot);

        return sprintf('%s_%sx%s%s', $startName, $width, $height, $extension);
    }

    protected function deleteOldFiles($oldFiles)
    {
        try {
            $this->storage()->delete($oldFiles);
        } catch (Exception $e) {
            $this->logger()->error($e);
        }
    }

    protected function resizes($storedFilePath, $resizes)
    {
        try {
            $image = Image::load($this->storage()->path($storedFilePath));

            foreach ($resizes as $size) {
                $resizedPath = $this->resizedImagePath($storedFilePath, $size[0], $size[1]);

                (clone $image)
                    ->resize($size[0], $size[1])
                    ->save($this->storage()->path($resizedPath));
            }
        } catch (Exception $e) {
            $this->logger()->error($e);
        }
    }

    protected function deleteOldResizedFiles($oldFiles, $resizes)
    {
        try {
            foreach ($resizes as $size) {
                $oldResizedFiles = $this->resizedImagePath($oldFiles, $size[0], $size[1]);

                $this->storage()->delete($oldResizedFiles);
            }
        } catch (Exception $e) {
            $this->logger()->error($e);
        }
    }

    private function constructFileName($fileName, $extension)
    {
        $fileName = Str::slug($fileName, '-') . '-' . time();

        return strtolower("{$fileName}.{$extension}");
    }

    protected function optimizeImage($imagePath)
    {
        try {
            $optimizerChain = (new OptimizerChain)
                ->addOptimizer(new Jpegoptim([
                    '--strip-all',
                    '--all-progressive',
                ]))
                ->addOptimizer(new Pngquant([
                    '--force',
                ]));

            $optimizerChain->optimize($this->storage()->path($imagePath));

            return true;
        } catch (Exception $e) {
            return false;
        }
    }

    public function upload($prefixFolder, $file, $resizes = [[50, 50]], $oldFiles = [])
    {
        try {
            $folder = $this->folder($prefixFolder);
            $extension = $file->getClientOriginalExtension();
            $canOptimize = true;

            if (strtolower($extension) === 'svg') {
                $image = file_get_contents($file->getRealPath());
                $canOptimize = false;
            } else {
                $image = $file->get();
            }

            $fileName = $this->constructFileName(pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME), $extension);
            $storedFilePath = $folder . '/' . $fileName;

            $isUploaded = $this->storage()->put($storedFilePath, $image);

            if (!$isUploaded) {
                return null;
            }

            if ($canOptimize) $this->optimizeImage($storedFilePath);

            if ($resizes) {
                $this->resizes($storedFilePath, $resizes);
            }

            if ($oldFiles) {
                $this->deleteOldFiles($oldFiles);
            }

            return $storedFilePath;
        } catch (Exception $e) {
            $this->logger()->error($e);
            return null;
        }
    }

    public function uploadBase64($prefixFolder, $base64, $resizes = [], $oldFiles = [])
    {
        try {
            $extension = explode('/', explode(':', substr($base64, 0, strpos($base64, ';')))[1])[1];

            $image = substr($base64, strpos($base64, ',') + 1);
            $image = str_replace(' ', '+', $image);

            $imageName = Str::random(10).'_' .time().'.'.$extension;
            $folder = $this->folder($prefixFolder);

            $storedFilePath = $folder . '/' . $imageName;

            $isUploaded = $this->storage()->put($storedFilePath, base64_decode($image));

            if (!$isUploaded) {
                return null;
            }

            $this->optimizeImage($storedFilePath);

            if ($resizes) {
                $this->resizes($storedFilePath, $resizes);
            }

            return $storedFilePath;
        } catch (Exception $e) {
            $this->logger()->error($e);
            return null;
        }
    }

    public function uploadToRemoteServer($prefixFolder, $imageData, $resizes = [], $oldFiles = [])
    {
        try {
            // Prepare form data for remote server
            $formParams = [
                'folder' => $prefixFolder,
            ];

            // Add resizes if provided
            if (!empty($resizes)) {
                $formParams['resizes'] = json_encode($resizes);
            }

            // Add old files for deletion if provided
            if (!empty($oldFiles)) {
                $formParams['old_files'] = json_encode($oldFiles);
            }

            $files = [];

            // Detect input type and prepare accordingly
            if (is_string($imageData) && preg_match('/^data:image\/([a-zA-Z0-9]+);base64,/', $imageData)) {
                // Handle base64 string
                $formParams['image_data'] = $imageData;
                $headers = [
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/x-www-form-urlencoded'
                ];
            } elseif (is_string($imageData) && file_exists($imageData)) {
                // Handle file path
                $files['image'] = new CURLFile($imageData, mime_content_type($imageData), basename($imageData));
                $headers = [
                    'Accept' => 'application/json',
                ];
            } else {
                $this->logger()->error('Invalid image data provided: neither valid base64 nor existing file path');
                return null;
            }

            // Make request to remote server
            $response = $this->makeRequest(
                config('app.upload_api_domain'),
                'POST',
                '/api/media/upload-image',
                [], // query params
                $formParams,
                $files, // files
                $headers,
            );

            // Parse response
            $responseData = json_decode($response, true);

            // Return the uploaded path from remote server
            return $responseData['img_url'] ?? null;
        } catch (Exception $e) {
            $this->logger()->error('Error uploading to remote server: ' . $e->getMessage());
            return null;
        }
    }

    public function uploadedImageUrl($uploadedPath)
    {
        return $this->storage()->url($uploadedPath);
    }

    public function relativeFileUrl($uploadedPath)
    {
        return diskFilePath(config('media.image.storage_disk'), $uploadedPath); // diskFilePath define helpers function
    }

    public function resizedImageUrl($uploadedPath, $size)
    {
        $imagePath = $this->resizedImagePath($uploadedPath, $size[0], $size[1]);

        return $this->storage()->url($imagePath);
    }

    public function move($fromPath, $toPath)
    {
        $this->storage()->move($fromPath, $toPath);

        return $this->storage()->path($toPath);
    }

    public function images($folder, $page = 1)
    {
        if (!$this->storage()->exists($folder)) {
            return [
                'images' => collect([]),
                'hasMore' => false,
            ];
        }

        $limit = 50;

        $images = collect(File::files($this->storage()->path($folder)))
            ->sortBy(function ($file) {
                return $file->getFilename();
            })
            ->slice(($page - 1) * $limit, $limit)
            ->map(function ($file) use ($folder) {
                $path = $folder . '/' . $file->getFilename();

                return [
                    'path' => $path,
                    'url' => $this->storage()->url($path),
                ];
            })
            ->values();

        return [
            'images' => $images,
            'hasMore' => $images->count() >= $limit,
        ];
    }

    public function delete($uploadedPaths)
    {
        $this->deleteOldFiles($uploadedPaths);
    }

    public function saveByUrl($prefix_folder, $image_url)
    {
        $folder = $this->folder($prefix_folder);
        $contents = file_get_contents($image_url);
        $imagename = substr($image_url, strrpos($image_url, '/') + 1);
        $storedFilePath = $folder . '/' . $imagename;

        $this->storage()->put($storedFilePath, $contents);

        return $storedFilePath;
    }

    public function uploadFromPath($prefixFolder, $filePath)
    {
        if (!file_exists($filePath)) {
            return null;
        }

        $folder = $this->folder($prefixFolder);
        $extension = pathinfo($filePath, PATHINFO_EXTENSION);
        $filename = uniqid() . '.' . $extension;
        $storedFilePath = $folder . '/' . $filename;

        $this->storage()->put($storedFilePath, file_get_contents($filePath));

        return $storedFilePath;
    }

    /**
     * Save image from Google Forms URL and return local path
     *
     * @param string $imageUrl Google Forms image URL
     * @param string $prefixFolder Folder to save image
     * @return string|null Local image path or null if failed
     */
    public function saveImageFromUrl(string $imageUrl, string $prefixFolder, string $prefixFile = ''): ?string
    {
        try {
            // Use cURL with proper headers for Google images
            $ch = curl_init();
            curl_setopt_array($ch, [
                CURLOPT_URL => $imageUrl,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_USERAGENT => 'Mozilla/5.0 (compatible; GoogleFormImporter/1.0)',
                CURLOPT_HTTPHEADER => [
                    'Accept: image/*,*/*;q=0.8',
                    'Accept-Language: en-US,en;q=0.5',
                ],
                CURLOPT_SSL_VERIFYPEER => true,
            ]);

            $imageData = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
            curl_close($ch);

            if ($httpCode !== 200 || !$imageData) {
                Log::warning('Failed to download Google Forms image', [
                    'url' => $imageUrl,
                    'http_code' => $httpCode
                ]);
                return null;
            }

            // Determine file extension from content type
            $extension = $this->getExtensionFromContentType($contentType);
            if (!$extension) {
                return null;
            }

            // Save image using ImageService
            $folder = $this->folder($prefixFolder);
            $filename = uniqid($prefixFile) . '.' . $extension;
            $storedFilePath = $folder . '/' . $filename;

            $storage = Storage::disk(config('media.image.storage_disk'));
            $isUploaded = $storage->put($storedFilePath, $imageData);

            if (!$isUploaded) {
                return null;
            }

            // Optimize image if possible
            if (in_array($extension, ['jpg', 'jpeg', 'png'])) {
                $this->optimizeImage($storedFilePath);
            }

            return $storedFilePath;
        } catch (Exception $e) {
            Log::error('Error saving Google Forms image', [
                'url' => $imageUrl,
                'error' => $e->getMessage()
            ]);

            return null;
        }
    }

    /**
     * Get file extension from content type
     *
     * @param string $contentType
     * @return string|null
     */
    private function getExtensionFromContentType(string $contentType): ?string
    {
        $extensions = [
            'image/jpeg' => 'jpg',
            'image/jpg' => 'jpg',
            'image/png' => 'png',
            'image/gif' => 'gif',
            'image/webp' => 'webp',
            'image/svg+xml' => 'svg',
        ];

        return $extensions[$contentType] ?? null;
    }
}
