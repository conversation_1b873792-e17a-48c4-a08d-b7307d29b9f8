<?php

namespace App\Services;

use Exception;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use App\Models\User;
use App\Models\Classroom;
use Illuminate\Support\Facades\Hash;

class StudentExcelService
{
    /**
     * Read students data from an Excel file
     *
     * @param string $filePath
     * @return array
     * @throws Exception
     */
    public function readStudentsFromExcel(string $filePath): array
    {
        try {
            $spreadsheet = IOFactory::load($filePath);
            $worksheet = $spreadsheet->getActiveSheet();
            $highestRow = $worksheet->getHighestRow();

            $students = [];
            $errors = [];

            $rowsWithData = 0;
            $emptyCount = 0;

            // Bắt đầu từ dòng 2 (bỏ qua header)
            for ($row = 2; $row <= $highestRow; $row++) {
                $email = trim($worksheet->getCell('C' . $row)->getValue() ?? '');

                if ($email == '') {
                    $emptyCount++;
                    if ($emptyCount >= 50) {
                        break; // Gặp quá nhiều dòng trống => dừng sớm
                    }
                } else {
                    $rowsWithData++;
                    $emptyCount = 0;
                }

                $name = trim($worksheet->getCell('B' . $row)->getValue() ?? '');
                $phone = trim($worksheet->getCell('D' . $row)->getValue() ?? '');
                $identNumber = trim($worksheet->getCell('E' . $row)->getValue() ?? '');
                $password = trim($worksheet->getCell('F' . $row)->getValue() ?? '');

                // Bỏ qua dòng trống
                if (empty($name) && empty($email) && empty($identNumber)) {
                    continue;
                }

                // Validate dữ liệu
                $rowErrors = [];
                if (empty($name)) {
                    $rowErrors[] = 'Tên không được để trống';
                }
                if (empty($email)) {
                    $rowErrors[] = 'Email không được để trống';
                } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                    $rowErrors[] = 'Email không hợp lệ';
                }

                if (!empty($rowErrors)) {
                    $errors[] = [
                        'row' => $row,
                        'name' => $name,
                        'errors' => $rowErrors
                    ];
                    continue;
                }

                $students[] = [
                    'name' => $name,
                    'email' => $email,
                    'phone' => $phone,
                    'ident_number' => $identNumber,
                    'password' => $password,
                    'row' => $row
                ];
            }

            return [
                'students' => $students,
                'errors' => $errors,
                'rows_with_data' => $rowsWithData,
            ];

        } catch (Exception $e) {
            throw new Exception('Không thể đọc file Excel: ' . $e->getMessage());
        }
    }

    /**
     * Validate students for duplicates
     *
     * @param array $students
     * @param Classroom $classroom
     * @return array
     */
    public function validateStudents(array $students, Classroom $classroom): array
    {
        $errors = [];

        // Lấy danh sách email và số báo danh đã tồn tại trong lớp
        $existingEmails = $classroom->users()->pluck('users.email')->toArray();
        $existingIdentNumbers = $classroom->users()
            ->whereNotNull('classroom_user.ident_number')
            ->pluck('classroom_user.ident_number')
            ->toArray();

        foreach ($students as $student) {
            $studentErrors = [];

            // Kiểm tra email trùng
            if (in_array($student['email'], $existingEmails)) {
                $studentErrors[] = 'Email đã tồn tại trong hệ thống';
            }

            // Kiểm tra số báo danh trùng
            if (!empty($student['ident_number']) && in_array($student['ident_number'], $existingIdentNumbers)) {
                $studentErrors[] = 'Số báo danh đã tồn tại trong hệ thống';
            }

            if (!empty($studentErrors)) {
                $errors[] = [
                    'name' => $student['name'],
                    'row' => $student['row'],
                    'errors' => $studentErrors
                ];
            }
        }

        return $errors;
    }

    /**
     * Import students to classroom
     *
     * @param array $students
     * @param Classroom $classroom
     * @return int
     */
    public function importStudents(array $students, Classroom $classroom): int
    {
        $importedCount = 0;

        foreach ($students as $studentData) {
            // Tìm hoặc tạo user
            $user = User::where('email', $studentData['email'])->first();
            $passDefault = '12345678';

            if (!$user) {
                $user = User::create([
                    'name' => $studentData['name'],
                    'email' => $studentData['email'],
                    'phone' => $studentData['phone'],
                    'password' => Hash::make($studentData['password'] ?: $passDefault),
                    'status' => User::ACTIVE
                ]);
            }

            // Kiểm tra xem user đã có trong lớp chưa
            $existingUser = $classroom->users()->where('users.id', $user->id)->first();

            if (!$existingUser) {
                // Thêm vào lớp học
                $classroom->users()->attach([
                    $user->id => [
                        'name' => $studentData['name'] ?? $user->name,
                        'ident_number' => $studentData['ident_number'] ?? null,
                        'temp_password' => $studentData['password'] ?: $passDefault,
                    ]
                ]);

                $importedCount++;
            }
        }

        return $importedCount;
    }

    /**
     * Stream export students to Excel without saving to disk
     *
     * @param Classroom $classroom
     * @return void
     * @throws Exception
     */
    public function streamExportStudents(Classroom $classroom): void
    {
        // Load the template file
        $templatePath = public_path('sample/list-student_example.xlsx');

        if (!file_exists($templatePath)) {
            throw new Exception('Template file not found: ' . $templatePath);
        }

        $spreadsheet = IOFactory::load($templatePath);
        $worksheet = $spreadsheet->getActiveSheet();

        // Kiểm tra có học sinh không, Trả về file template nếu không có học sinh
        $studentsCount = $classroom->users()->count();

        // Set title
        $worksheet->setTitle($studentsCount ? 'Danh sách học sinh': 'Mẫu danh sách học sinh');

        if ($studentsCount) {
            $this->handleSheet($worksheet, $classroom);
        }

        // Stream the file directly to output
        $writer = new Xlsx($spreadsheet);
        $writer->save('php://output');

        // Cleanup memory
        $spreadsheet->disconnectWorksheets();
        unset($spreadsheet);
    }

    private function handleSheet($worksheet, $classroom): void
    {
        // Clear any existing sample data (keep only header row)
        $highestRow = $worksheet->getHighestRow();
        if ($highestRow > 1) {
            $worksheet->removeRow(2, $highestRow - 1);
        }

        // Get students data in chunks to handle large datasets
        $chunkSize = 1000;
        $offset = 0;
        $row = 2;
        $stt = 1;

        do {
            $students = $classroom->users()
                ->select([
                    'users.email',
                    'users.phone',
                    'classroom_user.name',
                    'classroom_user.ident_number',
                    'classroom_user.temp_password',
                    'classroom_user.created_at'
                ])
                ->offset($offset)
                ->limit($chunkSize)
                ->get();

            // Fill data for this chunk
            foreach ($students as $student) {
                // Copy formatting from row 2 (if it exists) or use template formatting
                if ($row > 2) {
                    $worksheet->insertNewRowBefore($row);
                    // Copy formatting from the previous row
                    $worksheet->duplicateStyle($worksheet->getStyle('A' . ($row - 1)), 'A' . $row . ':H' . $row);
                }

                $worksheet->setCellValue('A' . $row, $stt); // STT
                $worksheet->setCellValue('B' . $row, $student->name); // Họ và tên
                $worksheet->setCellValue('C' . $row, $student->email); // Email
                $worksheet->setCellValue('D' . $row, $student->phone ?: ''); // Phone
                $worksheet->setCellValue('E' . $row, $student->ident_number ?: ''); // Số báo danh
                $worksheet->setCellValue('F' . $row, $student->temp_password ?: ''); // Mật khẩu
                $worksheet->setCellValue('G' . $row, $classroom->title ?: ''); // Lớp
                $worksheet->setCellValue('H' . $row, $student->created_at ? $student->created_at->format('d/m/Y') : ''); // Ngày tham gia

                // Căn giữa tất cả các cột trừ cột Họ và tên (B) và Email (C)
                $worksheet->getStyle('A' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER); // STT
                $worksheet->getStyle('B' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT); // Họ và tên
                $worksheet->getStyle('C' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT); // Email
                $worksheet->getStyle('D' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER); // Phone
                $worksheet->getStyle('E' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER); // Số báo danh
                $worksheet->getStyle('F' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER); // Mật khẩu
                $worksheet->getStyle('G' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER); // Lớp
                $worksheet->getStyle('H' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER); // Ngày tham gia
                $row++;
                $stt++;
            }

            $offset += $chunkSize;

        } while ($students->count() === $chunkSize);
    }
}
