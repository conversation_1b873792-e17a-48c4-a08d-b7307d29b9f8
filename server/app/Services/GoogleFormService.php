<?php

namespace App\Services;

use App\Models\Question;
use App\Models\TableContent;
use App\Services\Media\ImageService;
use Exception;
use Illuminate\Support\Facades\Log;

class GoogleFormService
{
    protected ImageService $imageService;

    public function __construct(ImageService $imageService)
    {
        $this->imageService = $imageService;
    }

    /**
     * Fetch Google Form data from Google Forms API
     *
     * @param string $formId The Google Form ID
     * @param string $accessToken OAuth2 access token
     * @return array Google Form data
     * @throws Exception When API call fails or returns error
     */
    public function fetchGoogleForm(string $formId, string $accessToken): array
    {
        $url = "https://forms.googleapis.com/v1/forms/{$formId}";

        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HTTPHEADER => [
                "Authorization: Bearer {$accessToken}",
                "Content-Type: application/json"
            ],
            CURLOPT_TIMEOUT => 30,
            CURLOPT_SSL_VERIFYPEER => true,
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        curl_close($ch);

        if ($curlError) {
            Log::error('Google Forms API cURL error', ['error' => $curlError, 'form_id' => $formId]);
            throw new Exception("Network error: {$curlError}");
        }

        if ($httpCode === 401) {
            throw new Exception('Invalid Credentials');
        }

        if ($httpCode === 404) {
            throw new Exception('Not Found');
        }

        if ($httpCode !== 200) {
            Log::error('Google Forms API error', ['http_code' => $httpCode, 'response' => $response]);
            throw new Exception("Google Forms API error: HTTP {$httpCode}");
        }

        $formData = json_decode($response, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception('Invalid JSON response from Google Forms API');
        }

        return $formData;
    }

    /**
     * Parse Google Form data and convert to system question format
     *
     * @param array $formData Google Form data from API
     * @return array Parsed questions in system format
     */
    public function parseGoogleFormQuestions(array $formData, $quizId): array
    {
        $items = $formData['items'] ?? [];
        $parsedQuestions = [];

        foreach ($items as $item) {
            $parsedQuestion = $this->parseSingleQuestion($item, $quizId);

            if ($parsedQuestion) {
                $parsedQuestions[] = $parsedQuestion;
            }
        }

        return $parsedQuestions;
    }

    private function parseSingleQuestion(array $item, $quizId): ?array
    {
        if (!isset($item['questionItem'])) {
            return null;
        }

        $questionItem = $item['questionItem'];
        $question = $questionItem['question'] ?? [];

        // Only process multiple choice questions
        if (isset($question['choiceQuestion'])) {
            return $this->parseChoiceQuestion($questionItem, $item['title'] ?? '', $quizId);
        }

        return null;
    }

    /**
     * Parse a choice question from Google Form
     *
     * @param array $questionItem Question data from Google Form
     * @return array|null Parsed question data or null if invalid
     */
    private function parseChoiceQuestion(array $questionItem, $qTitle, $quizId): ?array
    {
        $question = $questionItem['question'] ?? [];

        $options = $question['choiceQuestion']['options'] ?? [];
        $grading = $question['grading'] ?? [];
        $correctAnswers = $grading['correctAnswers']['answers'] ?? [];
        $pointValue = $grading['pointValue'] ?? 1;

        if (empty($qTitle) || empty($options)) {
            return null;
        }

        $qContent = $qTitle;

        // Add question image if exists
        if (isset($questionItem['image']['contentUri'])) {
            $qImage = $questionItem['image']['contentUri'];
            $qContent = $this->handleContent($qContent, $qImage, $quizId);
        }

        // Get correct answer values
        $correctValues = array_column($correctAnswers, 'value');

        // Format options for system
        $formattedOptions = [];
        $answerIndices = [];

        foreach ($options as $index => $option) {
            $optionValue = $option['value'] ?? '';
            $isCorrect = in_array($optionValue, $correctValues);

            $formattedOptions[] = [
                'content' => $optionValue,
                'isCorrect' => $isCorrect,
                'origin_label' => chr(65 + $index),
            ];

            if (isset($option['image']['contentUri'])) {
                $optionImage = $option['image']['contentUri'];

                $formattedOptions[$index]['image'] = $optionImage;
                $formattedOptions[$index]['content'] = $this->handleContent($optionValue, $optionImage, $quizId);
            }

            if ($isCorrect) {
                $answerIndices[] = $index;
            }
        }

        return [
            'title' => $qTitle,
            'content' => $qContent,
            'options' => $formattedOptions,
            'content_json' => [
                'content' => $qContent,
                'options' => $formattedOptions,
                'answer' => $answerIndices,
                'points' => $pointValue,
                'timeLimit' => 0,
            ]
        ];
    }

    /**
     * Import parsed questions to a quiz
     *
     * @param array $parsedQuestions Parsed questions from parseGoogleFormQuestions
     * @param TableContent $tableContent Target quiz to import questions to
     * @return array Imported questions
     */
    public function importQuestionsToQuiz(array $parsedQuestions, TableContent $tableContent): array
    {
        $importedQuestions = [];
        $maxIndex = Question::where('table_content_id', $tableContent->id)->max('index') ?: 0;

        foreach ($parsedQuestions as $questionData) {
            $importedQuestions[] = $this->createQuestion($questionData, $tableContent, ++$maxIndex);
        }

        return $importedQuestions;
    }

    /**
     * Create a Question record in database
     *
     * @param array $questionData Parsed question data
     * @param TableContent $tableContent Target quiz
     * @param int $index Question index
     * @return Question Created question
     */
    private function createQuestion(array $questionData, TableContent $tableContent, int $index): Question
    {
        $title = $questionData['title'];
        $seoTitle = text_limit($title, config('web.title_limit'), '');

        return Question::create([
            'title' => $seoTitle,
            'slug' => convert_to_slug($seoTitle),
            'content' => $questionData['content'],
            'table_content_id' => $tableContent->id,
            'content_json' => $questionData['content_json'],
            'editor_id' => auth()->id(),
            'status' => Question::ACTIVE,
            'type' => Question::TYPE_QUIZX,
            'index' => $index,
            'is_updated' => Question::UPDATED,
            'import_origin' => 'google_form',
            'origin_id' => 0,
            'seo_title' => $seoTitle,
            'seo_description' => $title,
            'grade_id' => $tableContent->grade_id,
            'subject_id' => $tableContent->subject_id,
        ]);
    }

    public function handleContent($content, $imgUrl, $quizId): string
    {
        $newImageUrl = $this->imageService->uploadToRemoteServer("2048/g_form", $imgUrl, "quiz_" . $quizId . "_");

        return $content . '<br><img src="' . $newImageUrl . '" alt="Question Image" style="max-width: 100%; height: auto;">';
    }

    public function getNewImageUrl($image, $prefixFolder, $prefixFile): string
    {
        $newImagePath = $this->imageService->saveImageFromUrl($image, $prefixFolder, $prefixFile);

        return $this->imageService->uploadedImageUrl($newImagePath);
    }
}
