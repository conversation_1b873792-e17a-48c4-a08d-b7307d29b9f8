<?php

namespace App\Services;

use App\Models\Grade;
use App\Models\Subject;
use App\Models\Book;
use App\Models\Course;
use App\Models\Toc;
use App\Models\TableContent;
use App\Models\Question;
use Illuminate\Support\Str;
use Mews\Purifier\Facades\Purifier;
use DB;

class HandleData
{
    public function importGradesFromKhoaHoc()
    {
        $gradesKH = DB::connection('khoahoc_mysql')
            ->table('classlevel')
            ->where('type', 1)
            ->get();

        foreach ($gradesKH as $grade) {
            $type = \App\Models\Grade::PRIMARY_SCHOOL;

            switch ($grade->group) {
                case 'secondary':
                    $type = \App\Models\Grade::JUNIOR_SCHOOL;
                    break;

                case 'high':
                    $type = \App\Models\Grade::HIGH_SCHOOL;
                    break;
            }

            DB::table('grades')->insert(
                [
                    'id' => $grade->id,
                    'title' => $grade->name,
                    'slug' => Str::slug($grade->name),
                    'status' => \App\Models\Grade::ACTIVE,
                    'type' => $type,
                    'index' => $grade->id,
                    'editor_id' => 1,
                    'seo_title' => $grade->seo_title,
                    'seo_keywords' => $grade->seo_keywords,
                    'seo_description' => $grade->seo_description,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]
            );
        }
    }

    public function importSubjectsFromKhoaHoc()
    {
        $subjectsKH = DB::connection('khoahoc_mysql')
            ->table('subject')
            ->get();

        foreach ($subjectsKH as $subject) {
            DB::table('subjects')->insert(
                [
                    'id' => $subject->id,
                    'title' => $subject->name,
                    'slug' => Str::slug($subject->name),
                    'status' => \App\Models\Subject::ACTIVE,
                    'editor_id' => 1,
                    'index' => $subject->id,
                    'seo_title' => $subject->seo_title,
                    'seo_keywords' => $subject->seo_keywords,
                    'seo_description' => $subject->seo_description,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]
            );
        }
    }

    public function importBooksFromKhoaHoc()
    {
        $booksKH = DB::connection('khoahoc_mysql')
            ->table('books')
            ->get();

        foreach ($booksKH as $book) {
            DB::table('books')->insert(
                [
                    'id' => $book->id,
                    'name' => $book->name,
                    'slug' => Str::slug($book->name),
                    'created_at' => now(),
                    'updated_at' => now(),
                ]
            );
        }
    }

    public function importLessonsFromKhoaHoc()
    {
        $chaptersKH = DB::connection('khoahoc_mysql')
            ->table('season')
            ->get();

        foreach ($chaptersKH as $chapterKH) {
            $grade = Grade::find($chapterKH->class_id);
            $subject = Subject::find($chapterKH->subject_id);
            $book = Book::find($chapterKH->book_id);

            if ($grade && $subject) {
                $course = Course::firstOrCreate(
                    [
                        'grade_id' =>  $grade->id,
                        'subject_id' =>  $subject->id,
                        'book_id' =>  $book ? $book->id : null,
                        'type' => Course::TYPE_VIP,
                    ],
                    [
                        'title' => "$subject->title $grade->title" . ($book ? " $book->name" : ''),
                        'subtitle' => "$subject->title $grade->title" . ($book ? " $book->name" : ''),
                        'slug' => Str::slug("$subject->title $grade->title" . ($book ? " $book->name" : '')),
                        'seo_title' => "$subject->title $grade->title" . ($book ? " $book->name" : ''),
                        'editor_id' => 1,
                        'status' => Course::ACTIVE,
                    ]
                );

                $lessonsKH = DB::connection('khoahoc_mysql')
                    ->table('lessons')
                    ->where('season_id', $chapterKH->id)
                    ->get();

                $chapterTitle = preg_replace('/^(?:Chương|Bài)\s*\d+\s*:?\s*/i', '', $chapterKH->name);

                $chapter = Toc::create([
                    'title' => $chapterTitle,
                    'slug' => Str::slug($chapterTitle),
                    'status' => 1,
                    'index' => $chapterKH->index,
                    'course_id' => $course->id,
                    'grade_id' => $grade->id,
                    'subject_id' => $subject->id,
                    'editor_id' => 1,
                    'origin_id' =>  $chapterKH->id,
                    'import_origin' =>  'kh_chapter',
                ]);

                foreach ($lessonsKH as $lessonKH) {
                    $title = preg_replace('/^(?:Chương|Bài)\s*\d+\s*:?\s*/i', '', $lessonKH->name);

                    Toc::create([
                        'title' => $title,
                        'slug' => Str::slug($title),
                        'status' => 1,
                        'index' => $lessonKH->index,
                        'course_id' => $course->id,
                        'grade_id' => $grade->id,
                        'subject_id' => $subject->id,
                        'editor_id' => 1,
                        'parent_id' =>  $chapter->id,
                        'origin_id' =>  $lessonKH->id,
                        'import_origin' =>  'kh_lesson',
                    ]);
                }
            }
        }
    }

    private function normalizeForExtraction(string $html): string
    {
        $s = trim($html);
        $s = preg_replace("/\r\n?/", "\n", $s);      // newline
        $s = preg_replace("/[ \t]+/u", " ", $s);     // nhiều space/tab → 1 space
        $s = preg_replace("/\n{2,}/u", "\n", $s);    // nhiều newline → 1
        $s = preg_replace('/^[ \t]+/mu', ' ', $s);   // normalize space đầu dòng

        // Replace various non-breaking/spaces (NBSP, zero-width, etc.) -> regular space
        //    \x{00A0} NBSP, \x{200B} zero-width space, \p{Zs} other separators
        $s = preg_replace('/[\x{00A0}\x{200B}\p{Zs}]+/u', ' ', $s);

        // Remove leading/trailing <br> or wrapper block tags (p, div, span)
        $s = preg_replace('#^(?:\s*<(?:br|p|div|span)\b[^>]*>\s*)+#i', '', $s);
        $s = preg_replace('#(?:\s*</(?:p|div|span)[^>]*>\s*|\s*<br\s*/?>\s*)+$#i', '', $s);

        // Collapse multiple spaces to single space (so \s in regex behaves predictably)
        $s = preg_replace('/\s+/u', ' ', $s);

        return trim($s);
    }

    private function extractOptionLabel(string $rawHtml): array
    {
        $content = $this->normalizeForExtraction($rawHtml);

        if ($content === '') {
            return [];
        }

        // Lấy pattern delimiter (ví dụ: [.\);] | dash | linebreak )
        $delim = '(?:[.\);]|\R+)';

        /*
        Pattern ý tưởng (anchor ^ để chỉ ở đầu chuỗi):
        - ^\s* (các khoảng trắng đầu)
        - (?:<[^>]+>\s*)*     => có thể có một hoặc nhiều thẻ mở ở đầu, ví dụ <p><strong>
        - (?:                 => phần label có thể ở hai dạng:
            ([A-H])           => trực tiếp A..H
            |                 => hoặc
            (?:<[^>]+>\s*([A-H])\s*(?:<\/[^>]+>\s*)+) => chữ cái nằm trong 1+ tag (ví dụ <strong>A</strong> hoặc <span><strong>A</strong></span>)
        )
        - (?:\s*(?:<\/[^>]+>\s*)*) => có thể có closing tags/khoảng trắng ngay sau
        - \.?                   => optional dot
        - (?:\s*DELIM)?         => optional delimiter after label
        - \s*                   => drop trailing spaces before content
        */
        $pattern = '/^
            \s*
            (?:<[^>]+>\s*)*
            (?:
                ([A-H])
                |
                (?:<[^>]+>\s*([A-H])\s*(?:<\/[^>]+>\s*)+)
            )
            (?:\s*(?:<\/[^>]+>\s*)*)
            (?=\.|,|;|\)|\s*<)              # CHỈ chấp nhận .,; ) hoặc thẻ HTML sau label
            \.?
            (?:\s*' . $delim . ')?
            \s*
        /imux';

        if (!preg_match($pattern, $content, $m, PREG_OFFSET_CAPTURE)) {
            // không tìm thấy label ở đầu
            return [];
        }

        // Nhận chữ cái label từ nhóm bắt được (nhóm 1 hoặc 2)
        $letter = null;
        if (!empty($m[1][0])) {
            $letter = strtoupper($m[1][0]);
            $matchEnd = $m[0][1] + strlen($m[0][0]); // vị trí kết thúc match (byte offset)
        } elseif (!empty($m[2][0])) {
            $letter = strtoupper($m[2][0]);
            $matchEnd = $m[0][1] + strlen($m[0][0]);
        } else {
            return []; // dự phòng
        }

        // Lấy phần còn lại (content) bắt đầu từ vị trí matchEnd
        $rest = substr($content, $matchEnd);

        // Dọn tiếp: collapse whitespace, trim
        $rest = preg_replace('/\s+/u', ' ', $rest);
        $rest = trim($rest);

        // Loại bỏ dấu phân cách thừa ở đầu content
        $rest = preg_replace(
            '/^(?:\s*<[^>]+>\s*)*[.,;\)\s]+/u',
            '',
            $rest
        );

        return [
            'label' => $letter,
            'content' => purifyContentHasMath($rest), // sửa lại cấu trúc html nếu có sai
        ];
    }

    public function importQuizsFromKhoaHoc()
    {
        $maxId = TableContent::where('import_origin', 'kh_curriculum')
            ->max('origin_id') ?: 0;
        $curriculumsKH = DB::connection('khoahoc_mysql')
            ->table('course_curriculum_items')
            ->where('id', '>', $maxId)
            ->where('type', 'test')
            ->where('status', 'active')
            ->limit(10000)
            ->get();

        foreach ($curriculumsKH as $curriculum) {
            $questionsKH = DB::connection('khoahoc_mysql')
                ->table('questions')
                ->where('curriculum_item', $curriculum->id)
                ->get();

            if ($questionsKH->count() == 0) {
                continue;
            }

            $courseKH = DB::connection('khoahoc_mysql')
                ->table('course')
                ->where('id', $curriculum->course_id)
                ->first();

            if ($courseKH) {
                $grade = Grade::find($courseKH->classlevel);
                $subject = Subject::find($courseKH->subject);
                $toc = null;

                if ($courseKH->lesson_id) {
                    $toc = Toc::where('import_origin', 'kh_lesson')
                        ->where('origin_id', $courseKH->lesson_id)
                        ->first();
                } else if ($courseKH->season_id) {
                    $toc = Toc::where('import_origin', 'kh_chapter')
                        ->where('origin_id', $courseKH->season_id)
                        ->first();
                }

                if ($grade && $subject && $toc) {
                    $tableContent = TableContent::firstOrCreate(
                        [
                            'origin_id' =>  $curriculum->id,
                            'import_origin' =>  'kh_curriculum',
                            'toc_id' => $toc->id,
                            'type' => TableContent::TYPE_EXAM_QUIZ,
                            'vip' => TableContent::VIP,
                        ],
                        [
                            'title' => $curriculum->name,
                            'slug' => Str::slug($curriculum->name),
                            'description' => $curriculum->description,
                            'status' => 1,
                            'index' => $curriculum->index,
                            'course_id' => $toc->course_id,
                            'grade_id' => $grade->id,
                            'subject_id' => $subject->id,
                            'editor_id' => 1,
                        ]
                    );

                    if ($tableContent) {
                        foreach ($questionsKH as $questionKH) {
                            $content_json = [];

                            try {
                                $answersKH = DB::connection('khoahoc_mysql')
                                    ->table('answers')
                                    ->where('question', $questionKH->id)
                                    ->get();

                                if ($answersKH->count() > 0) {
                                    $content = '';
                                    $options = [];
                                    $answer = [];

                                    foreach ($answersKH as $key => $answerKH) {
                                        $contentAnswerKH = purifyContentHasMath($answerKH->content);
                                        $content .= ('<li' . ($answerKH->answer == 'Y' ? (' class="correctAnswer"') : '') . '>' . $contentAnswerKH . '</li>');
                                        $extractData = $this->extractOptionLabel($contentAnswerKH);
                                        $newData = [];

                                        if (!empty($extractData['content'])) {
                                            $newData['content'] = $extractData['content'];
                                        } else {
                                            $newData['content'] = $contentAnswerKH;
                                        }

                                        if (!empty($extractData['label'])) {
                                            $newData['origin_label'] = $extractData['label'];
                                        }

                                        $newData['isCorrect'] = $answerKH->answer == 'Y';

                                        if (!empty($newData)) {
                                            $options[] = $newData;
                                        }

                                        if ($answerKH->answer == 'Y') {
                                            $answer[] = $key;
                                        }
                                    }

                                    $content_json = [];
                                    $contentQuestionKH = purifyContentHasMath($questionKH->content);

                                    if (!empty($options)) {
                                        $content_json['content'] = $contentQuestionKH;
                                        $content_json['options'] = $options;
                                        $content_json['answer'] = $answer;
                                    }

                                    $content = $contentQuestionKH . '<ol class="quiz-list">' . $content . '</ol>';
                                    $explain = $questionKH->reason ?
                                        purifyContentHasMath($questionKH->reason) : '';

                                    $question = Question::create([
                                        'title' => $questionKH->title,
                                        'slug' => $questionKH->slug,
                                        'content' => $content,
                                        'explain' => $explain,
                                        'content_json' => $content_json ?? null,
                                        'grade_id' => $tableContent->grade_id,
                                        'subject_id' => $tableContent->subject_id,
                                        'table_content_id' => $tableContent->id,
                                        'editor_id' => 1,
                                        'status' => Question::ACTIVE,
                                        'type' => Question::TYPE_QUIZX,
                                        'seo_title' => $questionKH->title,
                                        'seo_description' => $content ? Purifier::clean($content, 'none_html') : $questionKH->title,
                                        'index' => $questionKH->index,
                                        'import_origin' => 'kh_question',
                                        'origin_id' => $questionKH->id,
                                    ]);
                                }
                            } catch (\Exception $e) {
                                report($e);
                                continue;
                            }
                        }
                    }
                }
            }
        }
    }
}
