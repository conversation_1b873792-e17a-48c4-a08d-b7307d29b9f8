<?php

namespace App\Services;

use Exception;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class ExcelService
{
    /**
     * Load Excel file and return spreadsheet object
     *
     * @param string|\Illuminate\Http\UploadedFile $file
     * @return Spreadsheet
     * @throws Exception
     */
    public function loadExcelFile($file): Spreadsheet
    {
        try {
            $filePath = is_string($file) ? $file : $file->getPathname();

            if (!file_exists($filePath)) {
                throw new Exception('File Excel không tồn tại');
            }

            return IOFactory::load($filePath);
        } catch (Exception $e) {
            throw new Exception('Không thể đọc file Excel: ' . $e->getMessage());
        }
    }

    /**
     * Get active worksheet from spreadsheet
     *
     * @param Spreadsheet $spreadsheet
     * @return Worksheet
     */
    public function getActiveWorksheet(Spreadsheet $spreadsheet): Worksheet
    {
        return $spreadsheet->getActiveSheet();
    }

    /**
     * Get cell value safely with trimming
     *
     * @param Worksheet $worksheet
     * @param string $cell
     * @return string
     */
    public function getCellValue(Worksheet $worksheet, string $cell): string
    {
        return trim($worksheet->getCell($cell)->getValue() ?? '');
    }

    /**
     * Read row data from worksheet
     *
     * @param Worksheet $worksheet
     * @param int $row
     * @param array $columns
     * @return array
     */
    public function readRowData(Worksheet $worksheet, int $row, array $columns): array
    {
        $data = [];
        foreach ($columns as $key => $column) {
            $data[$key] = $this->getCellValue($worksheet, $column . $row);
        }
        return $data;
    }

    /**
     * Check if row is empty based on specified columns
     *
     * @param array $rowData
     * @param array $requiredColumns
     * @return bool
     */
    public function isRowEmpty(array $rowData, array $requiredColumns = []): bool
    {
        if (empty($requiredColumns)) {
            return empty(array_filter($rowData));
        }

        foreach ($requiredColumns as $column) {
            if (!empty($rowData[$column])) {
                return false;
            }
        }
        return true;
    }

    /**
     * Process Excel file with callback for each row
     *
     * @param string|\Illuminate\Http\UploadedFile $file
     * @param callable $rowProcessor
     * @param array $options
     * @return array
     * @throws Exception
     */
    public function processExcelFile($file, callable $rowProcessor, array $options = []): array
    {
        $spreadsheet = $this->loadExcelFile($file);
        $worksheet = $this->getActiveWorksheet($spreadsheet);

        $startRow = $options['start_row'] ?? 2;
        $maxEmptyRows = $options['max_empty_rows'] ?? 50;
        $columns = $options['columns'] ?? [];
        $requiredColumns = $options['required_columns'] ?? [];

        $highestRow = $worksheet->getHighestRow();
        $results = [];
        $errors = [];
        $emptyCount = 0;
        $processedCount = 0;

        // Bắt đầu từ dòng 2 (bỏ qua header)
        for ($row = $startRow; $row <= $highestRow; $row++) {
            $rowData = $this->readRowData($worksheet, $row, $columns);

            if ($this->isRowEmpty($rowData, $requiredColumns)) {
                $emptyCount++;
                if ($emptyCount >= $maxEmptyRows) {
                    break;
                }
                continue;
            }

            $emptyCount = 0;
            $processedCount++;

            try {
                $result = $rowProcessor($rowData, $row);
                if ($result !== null) {
                    $results[] = $result;
                }
            } catch (Exception $e) {
                $errors[] = [
                    'row' => $row,
                    'error' => $e->getMessage(),
                    'data' => $rowData
                ];
            }
        }

        return [
            'results' => $results,
            'errors' => $errors,
            'processed_count' => $processedCount,
            'total_rows' => $highestRow - $startRow + 1
        ];
    }

    /**
     * Validate Excel file basic requirements
     *
     * @param string|\Illuminate\Http\UploadedFile $file
     * @param array $options
     * @return void
     * @throws Exception
     */
    public function validateExcelFile($file, array $options = []): void
    {
        if (is_object($file) && !$file->isValid()) {
            throw new Exception('File Excel không hợp lệ');
        }

        $spreadsheet = $this->loadExcelFile($file);
        $worksheet = $this->getActiveWorksheet($spreadsheet);

        $minRows = $options['min_rows'] ?? 2;
        $highestRow = $worksheet->getHighestRow();

        if ($highestRow < $minRows) {
            throw new Exception('File Excel không có dữ liệu hoặc chỉ có header');
        }
    }

    /**
     * Parse correct answer format (A/B/C/D or 1/2/3/4)
     *
     * @param string $correctAnswer
     * @param int $maxOptions
     * @return int|null
     */
    public function parseCorrectAnswer(string $correctAnswer, int $maxOptions = 4): ?int
    {
        $correctAnswer = strtoupper(trim($correctAnswer));

        // Xử lý định dạng A, B, C, D
        if (preg_match('/^[A-Z]$/', $correctAnswer)) {
            $index = ord($correctAnswer) - ord('A');
            return ($index >= 0 && $index < $maxOptions) ? $index : null;
        }

        // Xử lý định dạng số 1, 2, 3, 4
        if (is_numeric($correctAnswer)) {
            $index = (int)$correctAnswer - 1;
            return ($index >= 0 && $index < $maxOptions) ? $index : null;
        }

        return null;
    }

    /**
     * Parse multiple correct answers format (A/B/C/D or 1/2/3/4)
     *
     * @param string $correctAnswers
     * @param int $maxOptions
     * @param int $row
     * @return array
     * @throws Exception
     */
    public function parseMultipleCorrectAnswers(string $correctAnswers, int $maxOptions, int $row): array
    {
        $correctAnswers = trim($correctAnswers);
        $indices = [];

        // Tách các đáp án bằng dấu phẩy
        $answers = array_map('trim', explode(',', $correctAnswers));

        foreach ($answers as $answer) {
            $answer = strtoupper($answer);
            $index = null;

            // Xử lý định dạng chữ cái (A, B, C, D, E)
            if (preg_match('/^[A-Z]$/', $answer)) {
                $index = ord($answer) - ord('A');
            }
            // Xử lý định dạng số (1, 2, 3, 4, 5)
            elseif (is_numeric($answer)) {
                $index = (int)$answer - 1;
            }

            // Validate index
            if ($index !== null && $index >= 0 && $index < $maxOptions) {
                $indices[] = $index;
            } else {
                throw new Exception("Đáp án đúng '{$answer}' tại dòng {$row} không hợp lệ (vượt quá số lượng đáp án có sẵn)");
            }
        }

        if (empty($indices)) {
            throw new Exception("Không thể parse đáp án đúng '{$correctAnswers}' tại dòng {$row}");
        }

        return array_unique($indices);
    }
}
