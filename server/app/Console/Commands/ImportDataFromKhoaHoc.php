<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\HandleData;

class ImportDataFromKhoaHoc extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:import-data-from-khoa-hoc {table}';

    protected $handleData;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(HandleData $handleData)
    {
        parent::__construct();

        $this->handleData = $handleData;
    }

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import data from khoahoc';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $argumentArr = $this->argument();

        switch ($argumentArr['table']) {
            case 'grades':
                $this->handleData->importGradesFromKhoaHoc();
                break;
            case 'subjects':
                $this->handleData->importSubjectsFromKhoaHoc();
                break;
            case 'books':
                $this->handleData->importBooksFromKhoaHoc();
                break;
            case 'lessons':
                $this->handleData->importLessonsFromKhoaHoc();
                break;
            case 'quizs':
                $this->handleData->importQuizsFromKhoaHoc();
                break;
        }

        print_r('End import: ' . $argumentArr['table']);
        print_r("\n");
        exit(0);
    }
}
