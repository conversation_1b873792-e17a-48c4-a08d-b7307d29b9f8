<?php

namespace App\Console\Commands;

use App\Models\TableContent;
use App\Services\Media\ImageService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Spatie\Browsershot\Browsershot;
use Spatie\Image\Image;
use Spatie\ImageOptimizer\OptimizerChain;
use Spatie\ImageOptimizer\Optimizers\Jpegoptim;
use Spatie\ImageOptimizer\Optimizers\Pngquant;

class GenerateTableContentBanners extends Command
{
    protected $signature = 'generate:table-content-banners';

    protected ImageService $imageService;

    public function __construct(ImageService $imageService)
    {
        parent::__construct();

        $this->imageService = $imageService;
    }

    public function handle()
    {
        $this->info("Bắt đầu tạo banner cho TableContent");
        $count = 0;

        $folderQuiz = 'uploads/banner/quiz';
        $bannerDirectory = Storage::path('public/' . $folderQuiz);

        if (!File::isDirectory($bannerDirectory)) {
            File::makeDirectory($bannerDirectory, 0755, true);
        }

        TableContent::whereNull('banner')
            ->where('vip', TableContent::VIP)
            ->withCount('questions')
            ->having('questions_count', '>', 0)
            ->with(['questions' => function($query) {
                $query->limit(10);
            }])
            ->chunkById(500, function($tableContents) use (&$count, $bannerDirectory, $folderQuiz) {
                $this->output->progressStart($tableContents->count());

                foreach ($tableContents as $tableContent) {
                    try {
                        if ($tableContent->questions->isEmpty()) {
                            $this->output->progressAdvance();
                            continue;
                        }

                        $filename =  'q' . $tableContent->id . '.jpg';
                        $fullPath = $bannerDirectory . '/' . $filename;

                        $previewUrl = env('FRONTEND_INTERNAL_URL') . "/print/quiz/{$tableContent->id}";

                        // $options = [
                        //   '--allow-running-insecure-content', // https://source.chromium.org/search?q=lang:cpp+symbol:kAllowRunningInsecureContent&ss=chromium
                        //   '--autoplay-policy=user-gesture-required', // https://source.chromium.org/search?q=lang:cpp+symbol:kAutoplayPolicy&ss=chromium
                        //   '--disable-component-update', // https://source.chromium.org/search?q=lang:cpp+symbol:kDisableComponentUpdate&ss=chromium
                        //   '--disable-domain-reliability', // https://source.chromium.org/search?q=lang:cpp+symbol:kDisableDomainReliability&ss=chromium
                        //   '--disable-features=AudioServiceOutOfProcess,IsolateOrigins,site-per-process', // https://source.chromium.org/search?q=file:content_features.cc&ss=chromium
                        //   '--disable-print-preview', // https://source.chromium.org/search?q=lang:cpp+symbol:kDisablePrintPreview&ss=chromium
                        //   '--disable-setuid-sandbox', // https://source.chromium.org/search?q=lang:cpp+symbol:kDisableSetuidSandbox&ss=chromium
                        //   '--disable-site-isolation-trials', // https://source.chromium.org/search?q=lang:cpp+symbol:kDisableSiteIsolation&ss=chromium
                        //   '--disable-speech-api', // https://source.chromium.org/search?q=lang:cpp+symbol:kDisableSpeechAPI&ss=chromium
                        //   '--disable-web-security', // https://source.chromium.org/search?q=lang:cpp+symbol:kDisableWebSecurity&ss=chromium
                        //   '--disk-cache-size=33554432', // https://source.chromium.org/search?q=lang:cpp+symbol:kDiskCacheSize&ss=chromium
                        //   '--enable-features=SharedArrayBuffer', // https://source.chromium.org/search?q=file:content_features.cc&ss=chromium
                        //   '--hide-scrollbars', // https://source.chromium.org/search?q=lang:cpp+symbol:kHideScrollbars&ss=chromium
                        //   '--ignore-gpu-blocklist', // https://source.chromium.org/search?q=lang:cpp+symbol:kIgnoreGpuBlocklist&ss=chromium
                        //   '--in-process-gpu', // https://source.chromium.org/search?q=lang:cpp+symbol:kInProcessGPU&ss=chromium
                        //   '--mute-audio', // https://source.chromium.org/search?q=lang:cpp+symbol:kMuteAudio&ss=chromium
                        //   '--no-default-browser-check', // https://source.chromium.org/search?q=lang:cpp+symbol:kNoDefaultBrowserCheck&ss=chromium
                        //   '--no-pings', // https://source.chromium.org/search?q=lang:cpp+symbol:kNoPings&ss=chromium
                        //   '--no-sandbox', // https://source.chromium.org/search?q=lang:cpp+symbol:kNoSandbox&ss=chromium
                        //   '--no-zygote', // https://source.chromium.org/search?q=lang:cpp+symbol:kNoZygote&ss=chromium
                        //   '--use-gl=swiftshader', // https://source.chromium.org/search?q=lang:cpp+symbol:kUseGl&ss=chromium
                        //   '--window-size=1920,1080', // https://source.chromium.org/search?q=lang:cpp+symbol:kWindowSize&ss=chromium
                        // ];

                        Browsershot::url($previewUrl)
                            ->setChromePath(env('CHROMIUM_BINARIES', '/usr/bin/chromium'))
                            ->setOption('args', [
                                '--headless=new',
                                '--no-sandbox',
                                '--disable-setuid-sandbox',
                                '--disable-dev-shm-usage',
                                '--disable-gpu'
                            ])
                            ->windowSize(800, 1200)
                            ->format('jpg')
                            ->timeout(120)
                            ->setOption('protocolTimeout', 120000)
                            ->save($fullPath);

                        Image::load($fullPath)
                            ->width(400)
                            ->save($fullPath);

                        $optimizerChain = (new OptimizerChain)
                            ->addOptimizer(new Jpegoptim([
                                '--strip-all',
                                '--all-progressive',
                            ]))
                            ->addOptimizer(new Pngquant([
                                '--force',
                            ]));
                        $optimizerChain->optimize($fullPath);

                        // saveBannerToRemoteServer
                        $remotePath = $this->saveBannerToRemoteServer($fullPath, $tableContent);

                        $bannerPath = $folderQuiz . '/' . $filename;
                        $tableContent->update([
                            'banner' => $remotePath ?? $bannerPath
                        ]);

                        $count++;

                        if ($remotePath) {
                            // Delete local file after successful upload
                            File::delete($fullPath);
                        }
                    } catch (\Exception $e) {
                        Log::error("Lỗi khi tạo banner cho TableContent #{$tableContent->id}: {$e->getMessage()}");
                        $this->error($e->getMessage());
                    }

                    $this->output->progressAdvance();
                }

                $this->output->progressFinish();
            });

        $this->info("Hoàn thành tạo banner cho $count TableContent.");
        return 0;
    }

    private function saveBannerToRemoteServer($fullPath, $tableContent)
    {
        // Read file and convert to base64, then upload to remote server
        try {
            if (!File::exists($fullPath)) {
                Log::error("File không tồn tại: {$fullPath}");
                $this->output->progressAdvance();

                return null;
            }

            // Upload to remote server
            return $this->imageService->uploadToRemoteServer('2048/banner/quiz', $fullPath);
        } catch (\Exception $e) {
            Log::error("Lỗi khi xử lý ảnh cho TableContent #{$tableContent->id}: {$e->getMessage()}");
            return null;
        }
    }
}
