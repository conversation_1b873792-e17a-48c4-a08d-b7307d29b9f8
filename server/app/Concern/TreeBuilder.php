<?php

namespace App\Concern;

trait TreeBuilder
{
    /** @var integer|boolean */
    protected $showDepth = true;

    /** @var array */
    protected $exceptKeys = [];

    protected function transform(array $item): array
    {
        return $item;
    }

    public function hideDepth(): self
    {
        $this->showDepth = false;
        return $this;
    }

    public function setExceptKeys($keys): self
    {
        $this->exceptKeys = is_array($keys) ? $keys : [$keys];
        return $this;
    }

    protected function buildItem(array $item): array
    {
        $node = $this->transform($item);
        foreach ($this->exceptKeys as $key) {
            unset($node[$key]);
        }
        return $node;
    }

    /**
     * Nhóm items theo parent_id, chỉ duyệt 1 lần → O(n)
     *
     * @param array<array> $items
     * @return array<int|null, array<array>>
     */
    protected function groupByParent(array $items): array
    {
        $map = [];
        foreach ($items as $it) {
            $map[$it['parent_id']][] = $it;
        }
        return $map;
    }

    /**
     * <PERSON><PERSON> quy build cây + thu luôn descendant_ids → O(n)
     *
     * @param array<int|null, array<array>> $map
     * @param mixed                         $parentId
     * @param int                           $depth
     * @return array                        Mỗi node gồm các key:
     *                                      - [fields...]
     *                                      - children: array
     *                                      - descendant_ids: array
     *                                      - depth (nếu showDepth=true)
     */
    protected function buildTreeFromMap(array $map, $parentId = null, int $depth = 1): array
    {
        $tree = [];
        foreach ($map[$parentId] ?? [] as $item) {
            // 1. Build children của item này
            $children = $this->buildTreeFromMap($map, $item['id'], $depth + 1);

            // 2. Gom tất cả descendant_ids từ các children
            $descIds = [];
            foreach ($children as $c) {
                $descIds[] = $c['id'];
                if (!empty($c['descendant_ids'])) {
                    $descIds = array_merge($descIds, $c['descendant_ids']);
                }
            }

            // 3. Build node hiện tại
            $node = $this->buildItem($item);
            if ($this->showDepth) {
                $node['depth'] = $depth;
            }
            $node['children']        = $children;
            $node['descendant_ids']  = $descIds;

            $tree[] = $node;
        }

        return $tree;
    }

    /**
     * Lấy cây bắt đầu từ parentId (không bao gồm root), O(n)
     *
     * @param array<array> $items
     * @param mixed        $parentId
     * @return array
     */
    public function getTree(array $items, $parentId = null): array
    {
        $map = $this->groupByParent($items);
        return $this->buildTreeFromMap($map, $parentId, 1);
    }

    /**
     * Lấy cây có bao gồm node $parent, đồng thời có descendant_ids
     *
     * @param array<array> $items
     * @param array        $parent        ['id'=>..., 'parent_id'=>...]
     * @return array       Mảng chứa đúng 1 node gốc
     */
    public function getTreeWithRoot(array $items, array $parent): array
    {
        // 1. Nhóm map
        $map = $this->groupByParent($items);

        // 2. Build root
        $root = $this->buildItem($parent);
        if ($this->showDepth) {
            $root['depth'] = 1;
        }

        // 3. Build children + descendant_ids
        $children = $this->buildTreeFromMap($map, $parent['id'], 2);
        $root['children']       = $children;

        // 4. Gom descendant_ids cho root
        $rootDesc = [];
        foreach ($children as $c) {
            $rootDesc[] = $c['id'];
            if (!empty($c['descendant_ids'])) {
                $rootDesc = array_merge($rootDesc, $c['descendant_ids']);
            }
        }
        $root['descendant_ids'] = $rootDesc;

        // 5. Trả về mảng chứa root node
        return [ $root ];
    }

    /**
     * Lấy 1 item đơn lẻ
     */
    public function getItem(array $item): array
    {
        return $this->buildItem($item);
    }

    /**
     * Lấy nhiều items (transform + exceptKeys)
     */
    public function getItems(array $items): array
    {
        return array_map(fn($it) => $this->buildItem($it), $items);
    }
}
