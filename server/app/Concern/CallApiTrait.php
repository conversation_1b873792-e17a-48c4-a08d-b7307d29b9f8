<?php

namespace App\Concern;

use GuzzleHttp\Client;

trait CallApiTrait
{
    /**
     * Send a request to any service
     * @return string
     */
    public function makeRequest($baseUri, $method, $requestUrl, $queryParams = [], $formParams = [], $files = [], $headers = [])
    {
        $hasFile = false;
        $client = new Client([
            'base_uri' => $baseUri,
        ]);

        $bodyType = 'form_params';

        if (count($files) > 0) {
            $hasFile = true;
            $bodyType = 'multipart';
            $multipart = [];

            foreach ($formParams as $name => $contents) {
                $multipart[] = [
                    'name' => $name,
                    'contents' => $contents
                ];
            }

            foreach ($files as $name => $file) {
                if ($file instanceof \Illuminate\Http\UploadedFile) {
                    // Handle Laravel UploadedFile
                    $multipart[] = [
                        'name' => $name,
                        'filename' => $file->getClientOriginalName(),
                        'Mime-Type' => $file->getMimeType(),
                        'contents' => fopen($file->getPathname(), 'r'),
                    ];
                } elseif ($file instanceof \CURLFile) {
                    // Handle CURLFile
                    $multipart[] = [
                        'name' => $name,
                        'filename' => $file->getFilename() ?: basename($file->getFilename()),
                        'Mime-Type'=> $file->getMimeType() ?: mime_content_type($file->getFilename()) ?: 'application/octet-stream',
                        'contents' => fopen($file->getFilename(), 'r'),
                    ];
                } elseif (is_string($file) && file_exists($file)) {
                    // Handle file path string (fallback)
                    $multipart[] = [
                        'name' => $name,
                        'filename' => basename($file),
                        'Mime-Type'=> mime_content_type($file) ?: 'application/octet-stream',
                        'contents' => fopen($file, 'r'),
                    ];
                } else {
                    // Skip invalid file types
                    continue;
                }
            }
        }

        $response = $client->request($method, $requestUrl, [
            'query' => $queryParams,
            $bodyType => $hasFile ? $multipart : $formParams,
            'headers' => $headers,
            'verify' => false,
        ]);

        $response = $response->getBody()->getContents();

        return $response;
    }
}
