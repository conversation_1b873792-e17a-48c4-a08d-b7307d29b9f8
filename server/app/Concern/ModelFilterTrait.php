<?php

namespace App\Concern;

use Illuminate\Pagination\LengthAwarePaginator;

trait ModelFilterTrait
{
    protected function getSortDirection($conditions)
    {
        if (!isset($conditions['sortDir'])) {
            return 'ASC';
        }

        $direction = $conditions['sortDir'];
        $validDirections = ['ASC', 'DESC'];
        if (!in_array(strtoupper($direction), $validDirections)) {
            return 'ASC';
        }

        return $direction;
    }

    protected function sortModels($query, $sortable, $conditions)
    {
        if (isset($conditions['sortBy']) && in_array($conditions['sortBy'], $sortable)) {
            $query->orderBy($conditions['sortBy'], $this->getSortDirection($conditions));
        }
    }

    /**
     * Apply filters to model query
     *
     * @param \Illuminate\Database\Eloquent\Model|mixed $query
     * @param array $searchable Array of filter column and callback
     * @param array $sortable Array of column that can be used to sort
     * @param array $defaultSort Array of [column, sort direction] used as default sort
     * @param array $conditions Array of filter column and filter value
     * @param null|int $limit Pass 0 to get all, otherwise doing pagination
     *
     * @return LengthAwarePaginator
     */
    public function filterModels(
        $query,
        $searchable,
        $sortable,
        $defaultSort,
        $conditions,
        $limit = null,
        $columns = ['*']
    ) {
        foreach ($conditions as $key => $value) {
            if (!isset($searchable[$key]) || $value === null) {
                continue;
            }

            $callback = $searchable[$key];

            call_user_func($callback, $query, $value);
        }

        $this->sortModels($query, $sortable, $conditions);

        $query->orderBy($defaultSort[0], $defaultSort[1]);

        $limit = $limit === null ? config('admin.pagination.per_page') : $limit;

        if ($limit !== 0) {
            return $query->paginate($limit, $columns);
        }

        $results = $query->get($columns);
        $total = $results->count();

        // Make output be the same as method ->paginate() above
        return new LengthAwarePaginator($results, $total, $total ?: 1, 1);
    }
}
