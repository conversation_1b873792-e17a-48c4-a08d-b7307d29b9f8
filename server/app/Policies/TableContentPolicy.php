<?php

namespace App\Policies;

use App\Models\User;
use App\Models\TableContent;
use Illuminate\Auth\Access\HandlesAuthorization;

class TableContentPolicy
{
    use HandlesAuthorization;

    public function before(User $user)
    {
        if ($user->canBeAdministrators()) {
            return true;
        }
    }

    public function edit(User $user, TableContent $tableContent)
    {
        return $user->id === $tableContent->editor_id;
    }

    public function delete(User $user, TableContent $tableContent)
    {
        return $user->id === $tableContent->editor_id;
    }

    public function import(User $user, TableContent $tableContent)
    {
        return $user->id === $tableContent->editor_id;
    }
}
