<?php

namespace App\Policies;

use App\Models\User;
use App\Models\Toc;
use Illuminate\Auth\Access\HandlesAuthorization;

class TocPolicy
{
    use HandlesAuthorization;

    public function before(User $user)
    {
        if ($user->canBeAdministrators()) {
            return true;
        }
    }

    public function edit(User $user, Toc $toc)
    {
        return $user->id === $toc->editor_id;
    }
}