<?php

namespace App\Policies;

use App\Models\User;
use App\Models\Course;
use Illuminate\Auth\Access\HandlesAuthorization;

class CoursePolicy
{
    use HandlesAuthorization;

    public function before(User $user)
    {
        if ($user->canBeAdministrators()) {
            return true;
        }
    }

    public function edit(User $user, Course $course)
    {
        return $user->id === $course->editor_id;
    }
}