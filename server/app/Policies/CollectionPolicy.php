<?php

namespace App\Policies;

use App\Models\Collection;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class CollectionPolicy
{
    use HandlesAuthorization;

    /**
     * Perform pre-authorization checks.
     *
     * @param  \App\Models\User  $user
     * @return bool|null
     */
    public function before(User $user)
    {
        if ($user->canBeAdministrators()) {
            return true;
        }
    }

    /**
     * Determine whether the user can edit the collection.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\Collection  $collection
     * @return bool
     */
    public function edit(User $user, Collection $collection)
    {
        return $user->id === $collection->user_id;
    }
}