<?php

namespace App\Policies;

use App\Models\Classroom;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class ClassroomPolicy
{
    use HandlesAuthorization;

    public function before(User $user)
    {
        // if ($user->canBeAdministrators()) {
        //     return true;
        // }
    }

    public function edit(User $user, Classroom $classroom)
    {
        return $user->id === $classroom->author_id;
    }

    public function delete(User $user, Classroom $classroom)
    {
        return $user->id === $classroom->author_id;
    }

    public function preview(User $user, Classroom $classroom)
    {
        return $classroom->users()->where('users.id', $user->id)->exists();
    }

    public function inClassRoom(User $user, Classroom $classroom, User $student)
    {
        return $classroom->classroomUsers()
            ->where('user_id', $student->id)
            ->exists();
    }
}
