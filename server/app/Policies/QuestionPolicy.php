<?php

namespace App\Policies;

use App\Models\User;
use App\Models\Question;
use Illuminate\Auth\Access\HandlesAuthorization;

class QuestionPolicy
{
    use HandlesAuthorization;

    public function before(User $user)
    {
        if ($user->canBeAdministrators()) {
            return true;
        }
    }

    public function edit(User $user, Question $question)
    {
        return $user->id === $question->editor_id;
    }

    public function delete(User $user, Question $question): bool
    {
        return $user->id === $question->editor_id;
    }
}
