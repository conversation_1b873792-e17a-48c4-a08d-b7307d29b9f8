<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;

class ValidateFileExtension implements Rule
{
    protected $types;

    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct($types = null)
    {
        $this->types = $types;
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        if ($this->types) {
            $extension = $value->getClientOriginalExtension();
            $types = $this->types;
            $arrayTypes = explode(',', $types);

            if (!in_array($extension, $arrayTypes)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return 'File không đúng định dạng!';
    }
}
