<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;

class ValidateImage implements Rule
{
    protected string $pattern;
    protected array $allowedExtensions;

    public function __construct()
    {
        $this->allowedExtensions = array_map('strtolower', array_map('trim', explode(',', config('media.image.types'))));
        $this->pattern = '/^data:image\/(' . implode('|', $this->allowedExtensions) . ');base64,/i';
    }

    public function passes($attribute, $value): bool
    {
        // Check base64 image
        if (is_string($value) && preg_match($this->pattern, $value)) {
            return true;
        }

        // Check URL with valid image extension
        if (filter_var($value, FILTER_VALIDATE_URL)) {
            $path = parse_url($value, PHP_URL_PATH);
            $extension = strtolower(pathinfo($path, PATHINFO_EXTENSION));

            return in_array($extension, $this->allowedExtensions, true);
        }

        return false;
    }

    public function message(): string
    {
        return 'Trường :attribute phải là ảnh hợp lệ (base64 hoặc URL có đuôi hợp lệ).';
    }
}
