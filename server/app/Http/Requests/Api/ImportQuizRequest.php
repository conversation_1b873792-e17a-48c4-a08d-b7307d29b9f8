<?php

namespace App\Http\Requests\Api;

class ImportQuizRequest extends BaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'file' => [
                'required',
                'mimes:doc,docx,xls,xlsx',
                'max:10240' // 10MB
            ]
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'file' => 'File Excel',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'file.required' => 'Vui lòng chọn file word hoặc excel .',
            'file.mimes' => 'File phải có định dạng .docx, .doc hoặc .xlsx, .xls.',
            'file.max' => 'File không được vượt quá 10MB.',
        ];
    }
}
