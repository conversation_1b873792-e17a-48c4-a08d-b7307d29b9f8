<?php

namespace App\Http\Requests\Api;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Contracts\Validation\Validator;

abstract class BaseRequest extends FormRequest
{
    public function validatedData()
    {
        $inputKeys = array_keys($this->rules());

        $keys = [];
        foreach ($inputKeys as $key) {
            // array validation: person.name, photos.*, person.*.name
            if (preg_match('#(\w+)\.#', $key, $match)) {
                $key = $match[1];
                if (!in_array($key, $keys)) {
                    $keys[] = $key;
                }
            } else {
                $keys[] = $key;
            }
        }

        return $this->all($keys);
    }

    // public function failedValidation(Validator $validator)
    // {
    //     throw new HttpResponseException(response()->json([
    //         'status'   => 422,
    //         'message'   => 'Validation errors',
    //         'errors'      => $validator->errors()
    //     ]));
    // }
}
