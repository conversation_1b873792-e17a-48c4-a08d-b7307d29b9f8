<?php

namespace App\Http\Requests\Api;

use App\Rules\ValidateImage;

class CourseRequest extends BaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'title' => 'required|string|min:3|max:255',
            'grade_id' => 'required|exists:grades,id',
            'subject_id' => 'required|exists:subjects,id',
            'banner_base64' => ['nullable', new ValidateImage],
            'remove_banner' => 'nullable',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'grade_id' => 'Lớp',
            'subject_id' => 'Môn',
            'banner_base64' => 'Ảnh',
        ];
    }
}
