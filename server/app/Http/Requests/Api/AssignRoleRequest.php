<?php

namespace App\Http\Requests\Api;

use Illuminate\Foundation\Http\FormRequest;

class AssignRoleRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'role' => 'required|string|in:student,teacher,admin,editor',
            'age' => 'nullable|integer|min:5|max:100',
            'subject_ids' => 'nullable|array|min:1',
            'subject_ids.*' => 'integer|exists:subjects,id',
            'grade_ids' => 'nullable|array|min:1',
            'grade_ids.*' => 'integer|exists:grades,id',
        ];
    }
}
