<?php

namespace App\Http\Requests\Api;

class JoinClassroomRequest extends BaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'class_code' => 'required|min:6|max:8',
        ];
    }

    public function attributes(): array
    {
        return [
            'class_code' => 'Mã lớp',
        ];
    }

    public function messages(): array
    {
        return [
            'class_code.min' => ':attribute cần ít nhất :min ký tự',
            'class_code.max' => ':attribute không được vượt quá :max ký tự',
        ];
    }
}
