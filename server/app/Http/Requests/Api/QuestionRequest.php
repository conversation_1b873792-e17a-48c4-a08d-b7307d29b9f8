<?php

namespace App\Http\Requests\Api;

use App\Models\Question;
use Illuminate\Validation\Rule;

class QuestionRequest extends BaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rules = [
            'content_json' => 'required|array',
            'content' => 'nullable',
            'explain' => 'nullable',
            'static_content' => 'nullable',
            'type' => ['required', Rule::in(array_keys(Question::allTypes()))],
        ];

        if ($this->input('type') === Question::TYPE_QUIZX) {
            $rules = array_merge($rules, [
                'content_json.content' => 'required',
                'content_json.answer' => 'required|array|min:1',
                'content_json.options' => 'required|array|min:2',
                'content_json.options.*.content' => 'required|string',
                'content_json.options.*.isCorrect' => 'required|boolean',
            ]);
        }

        if (in_array($this->input('type'), [Question::TYPE_BLANK, Question::TYPE_DRAG_DROP, Question::TYPE_DROPDOWN])) {
            $rules = array_merge($rules, [
                'content_json.content' => 'required',
                'content_json.answer' => 'required|array|min:1',
                'content_json.options' => 'required|array|min:1',
                'content_json.options.*.content' => 'required|string',
            ]);
        }

        return $rules;
    }

    /**
     * Get the validation messages that apply to the request.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'content_json.content.required' => 'Bạn chưa nhập nội dung câu hỏi.',
            'content_json.options.required' => 'Câu hỏi cần tối thiểu 2 lựa chọn.',
            'content_json.options.*.content.required' => 'Bạn chưa nhập đáp án.',
            'content_json.options.min' => 'Câu hỏi cần tối thiểu 2 lựa chọn.',
        ];
    }
}
