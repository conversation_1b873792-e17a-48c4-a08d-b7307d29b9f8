<?php

namespace App\Http\Requests\Api;

use App\Rules\ValidateImage;
use Illuminate\Foundation\Http\FormRequest;

class UserProfileRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'min:3', 'max:255'],
            'email' => 'required|email|unique:users,email,' . auth()->user()->id,
            'phone' => ['nullable', 'numeric', 'digits:10', 'regex:/^((09)|(03)|(07)|(08)|(05))[0-9]{8}$/'],
            'avatar' => ['nullable', new ValidateImage],
        ];
    }
}
