<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CreateTocRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true; // Đã xác thực qua middleware auth:sanctum
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'title' => 'required|string|min:3|max:500',
            'course_id' => 'required|integer|exists:courses,id',
            'toc_id' => 'nullable|integer|exists:tocs,id',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'title.required' => 'Tiêu đề là bắt buộc',
            'title.min' => 'Tiêu đề phải có ít nhất :min ký tự',
            'title.max' => 'Tiêu đề không được vượt quá :max ký tự',
            'course_id.required' => 'ID khóa học là bắt buộc',
            'course_id.exists' => 'Khóa học không tồn tại',
            'toc_id.exists' => 'Thư mục cha không tồn tại',
        ];
    }
}
