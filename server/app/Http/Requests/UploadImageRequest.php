<?php

namespace App\Http\Requests;

use App\Rules\ValidateFileExtension;
use Illuminate\Foundation\Http\FormRequest;

class UploadImageRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'image' => [
                'required',
                'mimes:' . config('media.image.types'),
                'max:' . config('media.image.max_size'),
                new ValidateFileExtension(config('media.image.types'))
            ],
        ];
    }
}
