<?php

namespace App\Http\Requests;

use App\Rules\ValidateFileExtension;
use Illuminate\Foundation\Http\FormRequest;

class UploadMediaRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'media' => [
                'required',
                'file',
                'mimes:' . config('media.file.media_types'),
                'max:' . config('media.file.max_size'),
                new ValidateFileExtension(config('media.file.media_types'))
            ],
        ];
    }
}
