<?php

namespace App\Http\Controllers\Api;

use App\Models\Subject;
use App\Models\TableContent;
use App\Http\Resources\SubjectResource;
use App\Http\Resources\TableContentResource;
use Illuminate\Http\Request;

class SubjectController extends BaseController
{
    public function subjects(Request $request)
    {
        $hasQuiz = $request->quiz === 'true' ? true : false;
        $page = $request->page ?: 1;
        $limit = $hasQuiz ? 2 : 10;

        $subjectsQuery = Subject::select([
                'id',
                'title',
                'slug',
                'banner',
                'status',
                'index',
            ])
            ->where('status', Subject::ACTIVE)
            ->withCount(['tableContents' => function ($query) {
                $query->where('type', TableContent::TYPE_EXAM_QUIZ);
            }])
            ->orderBy('table_contents_count', 'DESC')
            ->offset(($page - 1) * $limit)
            ->limit($limit)
            ->having('table_contents_count', '>', 0);

        if ($hasQuiz) {
            $subjectsQuery->with(['tableContents' => function ($query) {
                $query->select([
                    'id',
                    'title',
                    'slug',
                    'banner',
                    'grade_id',
                    'subject_id',
                    'view',
                    'editor_id',
                    'type',
                    'created_at',
                ])
                ->where('type', TableContent::TYPE_EXAM_QUIZ)
                ->with('grade:id,title')
                ->withCount('questions')
                ->orderBy('view','desc')
                ->limit(10);
            }]);
        }

        $subjects = $subjectsQuery->get();

        return SubjectResource::collection($subjects);
    }

    public function getQuizsOfSubject(Subject $subject, Request $request)
    {
        $page = (int) ($request->query('page', 1));
        $limit = (int) ($request->query('limit', 10));

        $quizzes = TableContent::select([
                'id',
                'title',
                'slug',
                'banner',
                'grade_id',
                'subject_id',
                'view',
                'type',
                'editor_id',
                'created_at'
            ])
            ->where('type', TableContent::TYPE_EXAM_QUIZ)
            ->where('subject_id', $subject->id)
            ->with(['grade:id,title', 'subject:id,title'])
            ->withCount('questions')
            ->orderBy('id', 'DESC')
            ->offset(($page - 1) * $limit)
            ->limit($limit)
            ->get();

        return $this->jsonResponse('OK', [
            'data' => [
                'subject' => new SubjectResource($subject),
                'quizzes' => TableContentResource::collection($quizzes),
            ]
        ]);
    }

    public function getSubjectBySlug(Subject $subject)
    {
        return new SubjectResource($subject);
    }
}
