<?php

namespace App\Http\Controllers\Api;

use App\Models\Grade;
use App\Models\Subject;
use App\Models\Course;
use App\Models\Toc;
use App\Models\TableContent;
use App\Models\Classroom;
use App\Models\ClassroomTableContent;
use App\Models\QuizResult;
use App\Http\Resources\GradeResource;
use App\Http\Resources\SubjectResource;
use App\Http\Resources\CourseResource;
use App\Http\Resources\TocResource;
use App\Http\Resources\TableContentResource;
use App\Http\Resources\ClassroomTableContentResource;
use App\Http\Resources\ClassRoomResource;
use App\Http\Resources\QuizResultResource;
use App\Concern\TreeBuilder;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;

class HomeController extends BaseController
{
    use TreeBuilder;

    public function recentActivity(Request $request)
    {
        $page = (int) ($request->query('page', 1));
        $limit = (int) ($request->query('limit', 10));
        $status = (int) ($request->query('status'));

        return $this->jsonResponse('Success', [
            'data' => [
                'completedQuiz' => $this->completedQuiz($page, $limit, $status),
                'myAssignments' => $this->myAssignments(),
                'classrooms' => $this->classrooms(),
            ]
        ]);
    }

    public function completedQuiz($page = 1, $limit = 10, $status = null)
    {
        $quizResults = QuizResult::select([
            'id',
            'table_content_id',
            'classroom_table_content_id',
            'total_correct',
            'total_wrong',
            'status',
            'type',
            'total',
            'timer',
            'user_id',
            'created_at'
        ])
            ->where('user_id', auth()->id())
            ->whereHas('tableContent')
            ->when($status, function ($query) use ($status) {
                if ($status == 1) {
                    return $query->where('status', '!=', 2);
                } elseif ($status == 2) {
                    return $query->where('status', 2);
                }
            })
            ->with(['tableContent' => function ($query) {
                $query->select([
                    'id',
                    'title',
                    'banner',
                    'type',
                    'grade_id',
                    'subject_id'
                ])->with(['grade:id,title', 'subject:id,title'])
                    ->withCount('questions');
            }])
            ->orderBy('id', 'DESC')
            ->offset(($page - 1) * $limit)
            ->limit($limit)
            ->get();

        return QuizResultResource::collection($quizResults);
    }

    public function myAssignments()
    {
        $userId = auth()->id();

        $assignments = ClassroomTableContent::select([
            'classroom_table_content.id',
            'classroom_table_content.start_time',
            'classroom_table_content.end_time',
            'classroom_table_content.table_content_id',
            'classroom_table_content.classroom_id',
            'classroom_table_content.show_answer',
            'classroom_table_content.code',
            'table_contents.title',
            'table_contents.subtitle',
            'table_contents.type',
        ])
            ->join('table_contents', function ($join) {
                $join->on('table_contents.id', '=', 'classroom_table_content.table_content_id');
            })
            ->join('classroom_user', function ($join) use ($userId) {
                $join->on('classroom_table_content.classroom_id', '=', 'classroom_user.classroom_id')
                    ->where('classroom_user.user_id', $userId);
            })
            ->with('classroom.author')
            ->withCount(['quizResults' => function ($query) use ($userId) {
                $query->where('user_id', $userId)->where('status', QuizResult::STATUS_DONE);
            }])
            ->orderBy('id', 'DESC')
            ->limit(10)
            ->get();

        return ClassroomTableContentResource::collection($assignments);
    }

    public function classrooms()
    {
        $classrooms = Classroom::where('author_id', auth()->id())
            ->with('grade')
            ->withCount(['classroomUsers', 'classroomTableContents'])
            ->orderBy('id', 'DESC')
            ->limit(10)
            ->get();

        return ClassRoomResource::collection($classrooms);
    }

    public function sidebarWorksheets(Request $request)
    {
        $grades = Grade::select([
            'id',
            'title',
            'slug',
            'status',
            'index',
        ])
        ->where('status', Grade::ACTIVE)
        ->whereHas('courses', function ($query) use ($request) {
            $query->where('type', Course::TYPE_VIP)
                ->where('status', Course::ACTIVE);

            if ($request->subject) {
                $query->where('subject_id', $request->subject);
            }
        })
        ->whereHas('tableContents', function ($query) use ($request) {
            $query->where('vip', TableContent::VIP)
                ->where('status', TableContent::ACTIVE);

            if ($request->subject) {
                $query->where('subject_id', $request->subject);
            }
        })
        ->get();
        $subjects = Subject::select([
            'id',
            'title',
            'slug',
            'status',
            'index',
        ])
        ->where('status', Subject::ACTIVE)
        ->whereHas('courses', function ($query) use ($request) {
            $query->where('type', Course::TYPE_VIP)
                ->where('status', Course::ACTIVE);

            if ($request->grade) {
                $query->where('grade_id', $request->grade);
            }
        })
        ->whereHas('tableContents', function ($query) use ($request) {
            $query->where('vip', TableContent::VIP)
                ->where('status', TableContent::ACTIVE);
        })
        ->get();

        if ($request->subject) {
            $subject = $subjects->where('id', $request->subject)->first();

            if ($subject) {
                $subject->load(['courses' => function ($query) use ($request) {
                    $query->select([
                            'id',
                            'title',
                            'subtitle',
                            'slug',
                            'grade_id',
                            'subject_id',
                            'book_id',
                            'index',
                        ])
                        ->where('type', Course::TYPE_VIP)
                        ->where('status', Course::ACTIVE);

                    if ($request->grade) {
                        $query->where('grade_id', $request->grade);
                    }
                }]);

                if ($subject->courses->count() == 1 || $request->course) {
                    $course = $request->course ? $subject->courses->where('id', $request->course)->first()
                        : $subject->courses->first();
                    if ($course) $course->load(['tocs' => function ($query) {
                                    $query->select([
                                        'id',
                                        'title',
                                        'subtitle',
                                        'slug',
                                        'course_id',
                                        'grade_id',
                                        'subject_id',
                                        'index',
                                        'parent_id'
                                    ])
                                    ->where('status', Course::ACTIVE)
                                    ->orderBy('index');
                                }]);
                }
            }
        }

        return $this->jsonResponse('OK', [
            'data' => [
                'grades' => GradeResource::collection($grades),
                'subjects' => SubjectResource::collection($subjects),
            ]
        ]);
    }

    public function searchWorksheets(Request $request)
    {
        $page = (int) ($request->page ?: 1);
        $slimit = 5;
        $qlimit = (int) ($request->limit ?: 24);

        $grade = null;
        $subject = null;
        $toc = null;
        $course = null;
        $quizzes = collect([]);

        if ($request->sgrade) {
            $grade = Grade::where('slug', $request->sgrade)->first();
        }

        if ($request->ssubject) {
            $subject = Subject::where('slug', $request->ssubject)->first();
        }

        if ($request->toc) {
            $toc = Toc::select([
                        'id',
                        'title',
                        'subtitle',
                        'slug',
                        'course_id',
                        'grade_id',
                        'subject_id',
                        'index',
                        'parent_id'
                    ])
                    ->where('id', $request->toc)
                    ->with([
                        'grade:id,title,slug',
                        'subject:id,title,slug',
                        'course:id,title,slug'
                    ])
                    ->first();

            if ($toc) {
                $toc = $toc->toArray();
                $allToc = Toc::select([
                        'id',
                        'title',
                        'subtitle',
                        'slug',
                        'course_id',
                        'grade_id',
                        'subject_id',
                        'index',
                        'parent_id'
                    ])
                    ->where('status', Toc::ACTIVE)
                    ->where('course_id', $toc['course_id'])
                    ->whereHas('tableContents', function ($query) {
                        $query->where('vip', TableContent::VIP)
                            ->where('status', TableContent::ACTIVE);
                    })
                    ->orderBy('index')
                    ->get();
                $tocTree = $this->getTreeWithRoot($allToc->toArray(), $toc);

                if (!empty($tocTree[0])) {
                    $toc = $tocTree[0];
                }
            }
        }

        if ($request->course) {
            $course = Course::with([
                'tocs' => function ($query) {
                    $query->select([
                        'id',
                        'title',
                        'subtitle',
                        'slug',
                        'course_id',
                        'grade_id',
                        'subject_id',
                        'index',
                        'parent_id'
                    ])
                    ->where('status', Toc::ACTIVE)
                    ->whereNull('parent_id')
                    ->orderBy('index');
                },
                'grade:id,title,slug',
                'subject:id,title,slug',
            ])
            ->find($request->course);
        }

        $subjectsQuery = Subject::select([
                'id',
                'title',
                'slug',
                'banner',
                'status',
                'index',
            ])
            ->where('status', Subject::ACTIVE)
            ->whereHas('courses', function ($query) use ($grade, $subject) {
                $query->where('type', Course::TYPE_VIP)
                    ->where('status', Course::ACTIVE);

                if ($grade) {
                    $query->where('grade_id', $grade->id);
                }

                if ($subject) {
                    $query->where('subject_id', $subject->id);
                }
            })
            ->whereHas('tableContents', function ($query) use ($grade, $subject) {
                $query->where('vip', TableContent::VIP)
                    ->where('status', TableContent::ACTIVE);

                if ($grade) {
                    $query->where('grade_id', $grade->id);
                }

                if ($subject) {
                    $query->where('subject_id', $subject->id);
                }
            })
            ->offset(($page - 1) * $slimit)
            ->limit($slimit);

        if ($subject || $course || $toc) {
            $quizzes = TableContent::selectColumns()
                ->where('vip', TableContent::VIP)
                ->where('type', TableContent::TYPE_EXAM_QUIZ)
                ->when($grade, fn($q) =>
                    $q->where('grade_id', $grade->id)
                )
                ->when($subject, fn($q) =>
                    $q->where('subject_id', $subject->id)
                )
                ->when($course, fn($q) =>
                    $q->where('course_id', $course->id)
                )
                ->when($toc, function($q) use ($toc) {
                    return !empty($toc['descendant_ids']) ? $q->whereIn('toc_id', $toc['descendant_ids']) : $q->where('toc_id', $toc['id']);
                })
                ->with([
                    'grade:id,title',
                    'subject:id,title',
                    'editor:id,name,avatar'
                ])
                ->withCount('questions')
                ->offset(($page - 1) * $qlimit)
                ->limit($qlimit)
                ->orderByDesc('id')
                ->get();

            $subject_id = $toc ? $toc['subject_id'] : ($course ? $course->subject_id : ($subject ? $subject->id : null));

            $subjectsQuery->when($subject_id, fn($q) =>
                $q->where('id', $subject_id)
            );
        }

        $subjects = $subjectsQuery->get();

        if (!$course && !$toc) {
            $subjects->load([
                'courses' => function ($query) use ($grade, $subject) {
                    $query->select([
                        'id',
                        'title',
                        'subtitle',
                        'slug',
                        'grade_id',
                        'subject_id',
                        'book_id',
                    ])
                    ->where('type', Course::TYPE_VIP)
                    ->where('status', Course::ACTIVE)
                    ->whereHas('tableContents', function ($query) use ($grade, $subject) {
                        $query->where('vip', TableContent::VIP)
                            ->where('status', TableContent::ACTIVE);

                        if ($grade) {
                            $query->where('grade_id', $grade->id);
                        }

                        if ($subject) {
                            $query->where('subject_id', $subject->id);
                        }
                    })
                    ->with([
                        'tocs' => function ($query) use ($grade, $subject) {
                            $query->select([
                                'id',
                                'title',
                                'subtitle',
                                'slug',
                                'course_id',
                                'grade_id',
                                'subject_id',
                                'index',
                                'parent_id'
                            ])
                            ->where('status', Toc::ACTIVE)
                            ->whereHas('tableContents', function ($query) use ($grade, $subject) {
                                $query->where('vip', TableContent::VIP)
                                    ->where('status', TableContent::ACTIVE);

                                if ($grade) {
                                    $query->where('grade_id', $grade->id);
                                }

                                if ($subject) {
                                    $query->where('subject_id', $subject->id);
                                }
                            })
                            ->whereNull('parent_id')
                            ->orderBy('index');
                        },
                    ]);

                    if ($grade) {
                        $query->where('grade_id', $grade->id);
                    }

                    if ($subject) {
                        $query->where('subject_id', $subject->id);
                    }
                }
            ]);

            if (!$subject) {
                $subjects->load([
                    'tableContents' => function ($query) use ($grade, $subject) {
                        $query->selectColumns()
                            ->where('vip', TableContent::VIP)
                            ->where('type', TableContent::TYPE_EXAM_QUIZ)
                            ->with([
                                'grade:id,title',
                                'subject:id,title',
                                'editor:id,name,avatar'
                            ])
                            ->withCount('questions')
                            ->orderBy('view','desc')
                            ->limit(6);

                        if ($grade) {
                            $query->where('grade_id', $grade->id);
                        }

                        if ($subject) {
                            $query->where('subject_id', $subject->id);
                        }
                    }
                ]);
            }
        }

        $data = [
            'subjects' => SubjectResource::collection($subjects),
        ];

        if ($grade) {
            $data['grade'] = new GradeResource($grade);
        }

        if ($subject) {
            $data['subject'] = new SubjectResource($subject);
        }

        if ($course) {
            $data['course'] = new CourseResource($course);
        }

        if ($toc) {
            $data['toc'] = $toc; // đã là mảng rồi
        }

        if ($quizzes->count() > 0) {
            $data['quizzes'] = TableContentResource::collection($quizzes);
        }

        return $this->jsonResponse('OK', [
            'data' => $data
        ]);
    }
}
