<?php

namespace App\Http\Controllers\Api;

use App\Http\Requests\Api\QuestionRequest;
use App\Models\Question;
use App\Models\TableContent;
use Illuminate\Http\Request;
use App\Http\Resources\QuestionResource;
use Illuminate\Support\Facades\Gate;
use Mews\Purifier\Facades\Purifier;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class QuestionController extends BaseController
{
    public function store(QuestionRequest $request, TableContent $quiz)
    {
        Gate::authorize('edit', $quiz);

        $content_text = $request->content ? Purifier::clean($request->content, 'none_html') : '';
        $title = text_limit($content_text, config('web.title_limit'), '');

        $maxIndex = Question::where('table_content_id', $quiz->id)
            ->max('index') ?: 0;

        $question = Question::create(
            [
                'title' => $title,
                'seo_title' => $title,
                'seo_description' => $content_text,
                'grade_id' => $quiz->grade_id,
                'subject_id' => $quiz->subject_id,
                'editor_id' => auth()->id(),
                'is_updated' => Question::UPDATED,
                'table_content_id' => $quiz->id,
                'index' => ++$maxIndex,
            ] + $request->all()
        );

        return new QuestionResource($question);
    }

    public function update(QuestionRequest $request, TableContent $quiz, Question $question)
    {
        Gate::authorize('edit', $quiz);
        Gate::authorize('edit', $question);

        $content_text = $request->content ? Purifier::clean($request->content, 'none_html') : '';
        $title = text_limit($content_text, config('web.title_limit'), '');

        $question->update(
            [
                'title' => $title,
                'seo_title' => $title,
                'seo_description' => $content_text,
                'grade_id' => $quiz->grade_id,
                'subject_id' => $quiz->subject_id,
                'editor_id' => auth()->id(),
                'is_updated' => Question::UPDATED,
            ] + $request->all()
        );

        return new QuestionResource($question);
    }

    public function destroy(TableContent $quiz, Question $question)
    {
        Gate::authorize('delete', $question);

        if ($question->table_content_id !== $quiz->id) {
            return $this->jsonResponse('Câu hỏi không nằm trong quiz này!', [], 422);
        }

        $question->delete();

        return $this->jsonResponse('Xóa thành công!');
    }
}
