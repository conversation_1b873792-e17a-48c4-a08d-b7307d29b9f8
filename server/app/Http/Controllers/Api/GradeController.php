<?php

namespace App\Http\Controllers\Api;

use App\Models\Grade;
use App\Models\Subject;
use App\Models\Course;
use App\Models\TableContent;
use App\Http\Resources\GradeResource;
use App\Http\Resources\SubjectResource;
use App\Http\Resources\CourseResource;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;

class GradeController extends BaseController
{
    public function grades()
    {
        $grades = Grade::select([
                'grades.id',
                'grades.title',
                'grades.slug',
                'grades.status',
                'grades.index',
            ])
            ->where('status', Grade::ACTIVE)
            ->whereHas('courses', function ($query) {
                $query->where('type', Course::TYPE_VIP)
                    ->where('status', Course::ACTIVE);
            })
            ->with(['subjects' => function($query) {
                $query->select([
                        'subjects.id',
                        'subjects.title',
                        'subjects.slug',
                        'subjects.status',
                        'subjects.index',
                    ])
                    ->where('subjects.status', Subject::ACTIVE);
            }])
            ->get();

        return GradeResource::collection($grades);
    }

    public function getGradeBySlug(Grade $grade)
    {
        return new GradeResource($grade);
    }
}
