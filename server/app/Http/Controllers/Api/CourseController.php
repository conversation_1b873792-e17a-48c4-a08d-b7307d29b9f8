<?php

namespace App\Http\Controllers\Api;

use App\Models\Course;
use App\Models\Toc;
use App\Models\TableContent;
use App\Http\Resources\CourseResource;
use App\Http\Resources\TableContentResource;
use App\Http\Resources\TocResource;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use App\Http\Requests\Api\CourseRequest;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;
use App\Services\Media\ImageService;
use App\Http\Requests\Api\UpdatePositionsRequest;
use Illuminate\Support\Facades\Gate;
class CourseController extends BaseController
{
    protected $imageService;

    public function __construct(ImageService $imageService)
    {
        $this->imageService = $imageService;
    }

    public function getCourse(Course $course)
    {
        return new CourseResource($course);
    }

    public function courses(Request $request)
    {
        $coursesQuery = Course::select([
                'id',
                'title',
                'subtitle',
                'banner',
                'slug',
                'grade_id',
                'subject_id',
                'book_id',
                'editor_id',
            ]);

        if (!$request->input('all')) {
            $coursesQuery->where('status', Course::ACTIVE)
                ->whereHas('tableContents');
        }

        $coursesQuery->orderBy('id', 'DESC');

        $coursesQuery->when($request->boolean('vip'), fn($q) =>
            $q->where('type', Course::TYPE_VIP)
        );

        $coursesQuery->when($request->filled('grade'), fn($q) =>
            $q->where('grade_id', $request->grade)
        );

        $coursesQuery->when($request->filled('subject'), fn($q) =>
            $q->where('subject_id', $request->subject)
        );

        $limit = (int) $request->query('limit', $request->boolean('all') ? 0 : 10);
        $page  = max(1, (int) $request->query('page', 1));

        if ($limit > 0) {
            $offset = ($page - 1) * $limit;
            $coursesQuery->skip($offset)->take($limit);
        }

        $courses = $coursesQuery->with([
            'grade:id,title',
            'subject:id,title',
            'editor:id,name,email'
        ])->get();

        return CourseResource::collection($courses);
    }

    public function getQuizsOfCourse(Course $course, Request $request)
    {
        $page = (int) ($request->query('page', 1));
        $limit = (int) ($request->query('limit', 10));

        $quizzes = TableContent::select([
            'id',
            'title',
            'banner',
            'slug',
            'grade_id',
            'subject_id',
            'type',
            'editor_id',
            'created_at'
        ])
        ->where('vip', TableContent::VIP)
        ->where('type', TableContent::TYPE_EXAM_QUIZ)
        ->where('course_id', $course->id)
        ->with(['grade:id,title', 'subject:id,title'])
        ->withCount('questions')
        ->offset(($page - 1) * $limit)
        ->limit($limit)
        ->orderByDesc('id')
        ->get();

        $course->load([
            'tocs' => function ($query) {
                $query->select([
                    'id',
                    'title',
                    'subtitle',
                    'slug',
                    'course_id',
                    'grade_id',
                    'subject_id',
                    'index',
                    'parent_id'
                ])
                ->where('status', Toc::ACTIVE)
                ->whereNull('parent_id')
                ->orderBy('index');
            }
        ]);

        return $this->jsonResponse('OK', [
            'data' => [
                'course' => new CourseResource($course),
                'quizzes' => TableContentResource::collection($quizzes),
            ]
        ]);
    }

    public function getTocsOfCourse(Course $course, Request $request)
    {
        $course->load([
            'tocs' => function($query) {
                $query->orderBy('index');
            },
            'tocs.tableContents' => function($query) {
                $query->orderBy('index');
            },
            'tableContents' => function($query) {
                $query->whereNull('toc_id')
                      ->orderBy('index');
            }
        ]);

        return new CourseResource($course);
    }

    public function updatePositions(UpdatePositionsRequest $request, Course $course)
    {
        Gate::authorize('edit', $course);
        DB::beginTransaction();

        try {
            $positions = $request->positions;

            foreach ($positions as $position) {
                $id = $position['id'];

                if (strpos($id, 'q-') === 0) {
                    $tableContentId = (int)str_replace('q-', '', $id);
                    $tableContent = TableContent::findOrFail($tableContentId);
                    $tableContent->toc_id = $position['toc_id'] ?? null;
                    $tableContent->index = $position['index'];
                    $tableContent->save();
                } else {
                    $toc = Toc::findOrFail($id);
                    $toc->parent_id = $position['toc_id'] ?? null;
                    $toc->index = $position['index'];
                    $toc->save();
                }
            }

            DB::commit();
            return $this->jsonResponse('Cập nhật vị trí thành công!');
        } catch (\Exception $e) {
            report($e);
            DB::rollBack();

            return $this->jsonResponse('Cập nhật vị trí thất bại!', [], 500);
        }
    }

    public function update(CourseRequest $request, Course $course)
    {
        Gate::authorize('edit', $course);

        DB::beginTransaction();

        try {
            $bannerPath = $course->banner;

            if ($request->banner_base64 && isBase64($request->banner_base64)) {
                $bannerPath = $this->imageService->uploadToRemoteServer('2048/course/' . auth()->id(), $request->banner_base64);
            } elseif ($request->remove_banner) {
                $bannerPath = null;
            }

            $course->update($request->only(
                'title',
                'grade_id',
                'subject_id'
            ) + [
                'banner' => $bannerPath,
            ]);

            $course->load(['grade:id,title', 'subject:id,title', 'editor:id,name']);

            DB::commit();

            return new CourseResource($course);
        } catch (\Exception $e) {
            report($e);
            DB::rollBack();

            return $this->jsonResponse('Cập nhật khóa học thất bại!', [], 500);
        }
    }

    public function destroy(Course $course)
    {
        Gate::authorize('edit', $course);

        DB::beginTransaction();

        try {
            TableContent::where('course_id', $course->id)
                ->update([
                    'course_id' => null,
                    'toc_id' => null
                ]);

            Toc::where('course_id', $course->id)->delete();

            $course->delete();

            DB::commit();

            return $this->jsonResponse('Khóa học đã được xóa thành công', [], 200);
        } catch (\Exception $e) {
            DB::rollBack();
            report($e);
            return $this->jsonResponse('Không thể xóa khóa học: ' . $e->getMessage(), [], 500);
        }
    }
}
