<?php

namespace App\Http\Controllers\Api;

use App\Models\QuizResult;
use App\Models\TableContent;
use App\Models\Question;
use App\Models\ClassroomUser;
use App\Http\Resources\QuizResultResource;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Log;

class QuizResultController extends BaseController
{
    public function soloJoin(Request $request)
    {
        $quiz = $request->quizId ? TableContent::withCount('questions')->find($request->quizId) : null;

        if (!$quiz) {
            return $this->jsonResponse('Quiz not found.', [], 404);
        }

        // $assignmentId = $request->assignmentId;

        // if ($assignmentId && auth()->check()) {
        //     $exists = ClassroomUser::where('user_id', auth()->id())
        //         ->join('classroom_table_content', function ($join) use ($assignmentId) {
        //             $join->on('classroom_table_content.classroom_id', '=', 'classroom_user.classroom_id')
        //                 ->where('classroom_table_content.id', $assignmentId);
        //         })
        //         ->exists();

        //     if (!$exists) {
        //         $assignmentId = null;
        //     }
        // }

        if ($quizResultId = $request->quizResultId) {
            $quizResult = QuizResult::find($quizResultId);

            if ($quizResult && $quizResult->status < QuizResult::STATUS_DONE) {
                // Nếu đang làm chưa xong thì cập nhật
                $quizResult->update([
                    'user_id' => auth()->id(),
                    'total' => $quiz->questions_count,
                    'table_content_id' => $quiz->id,
                    'classroom_table_content_id' => $request->assignmentId,
                    'type' => in_array($request->gameType, [
                        QuizResult::TYPE_SOLO,
                        QuizResult::TYPE_FLASHCARD
                    ]) ? $request->gameType : QuizResult::TYPE_SOLO,
                ]);

                return new QuizResultResource($quizResult);
            }
        }

        $quizResult = QuizResult::create([
            'user_id' => auth()->id(), // nếu chưa đăng nhập thì là null
            'total' => $quiz->questions_count,
            'table_content_id' => $quiz->id,
            'data_log' => null,
            'classroom_table_content_id' => $request->assignmentId,
            'status' => QuizResult::STATUS_CREATE,
            'type' => in_array($request->gameType, [
                QuizResult::TYPE_SOLO,
                QuizResult::TYPE_FLASHCARD
            ]) ? $request->gameType : QuizResult::TYPE_SOLO,
        ]);

        return new QuizResultResource($quizResult);
    }

    public function quizResultInfo(QuizResult $quizResult)
    {
        $canPlay = false;
        $userId = null;
        $assignmentId = $quizResult->assignmentId;

        if (auth()->check()) {
            $userId = auth()->id();

            if ($quizResult->user_id == $userId) {
                $canPlay = true;
            } elseif ($assignmentId) {
                $exists = ClassroomUser::where('user_id', $userId)
                    ->join('classroom_table_content', function ($join) use ($assignmentId) {
                        $join->on('classroom_table_content.classroom_id', '=', 'classroom_user.classroom_id')
                            ->where('classroom_table_content.id', $assignmentId);
                    })
                    ->exists();

                if (!$exists) {
                    $assignmentId = null;
                }
            }
        }

        if (!$canPlay && $quizResult->status == QuizResult::STATUS_DOING) {
            $newQuizResult = QuizResult::create([
                'user_id' => $userId,
                'total' => $quizResult->total,
                'table_content_id' => $quizResult->table_content_id,
                'data_log' => null,
                'classroom_table_content_id' => $assignmentId,
                'status' => QuizResult::STATUS_CREATE,
                'type' => $quizResult->type,
            ]);

            return new QuizResultResource($newQuizResult);
        }

        if ($quizResult->status == QuizResult::STATUS_CREATE) {
            $quizResult->update([
                'status' => QuizResult::STATUS_DOING,
                'started_at' => now(),
            ]);
        } elseif ($quizResult->status == QuizResult::STATUS_DOING) {
            $quizResult->increment('replay');
        }

        $quizResult->load([
            'tableContent.questions:id,type,content,content_json,table_content_id,index,explain'
        ]);

        return new QuizResultResource($quizResult);
    }

    public function soloProceed(QuizResult $quizResult, Request $request)
    {
        $canPlay = true;

        if (auth()->check()) {
            $userId = auth()->id();

            if ($quizResult->user_id != $userId) {
                $canPlay = false;
            }
        } else {
            $statusCode = 401;
        }

        if ($canPlay && $quizResult->status == QuizResult::STATUS_DONE) {
            $canPlay = false;
        }

        if (!$canPlay) {
            return $this->jsonResponse('Không thể thao tác.', [
                'data' => [
                    'newjoin' => true,
                    'quiz_id' => $quizResult->table_content_id,
                ]
            ], $statusCode ?? 500);
        }

        $question = $request->questionId ? Question::where('table_content_id', $quizResult->table_content_id)
            ->where('id', $request->questionId)
            ->first() : null;

        if (!$question) {
            return $this->jsonResponse('Question not found.', [], 404);
        }

        $data_log = json_decode($quizResult->data_log ?? '{}', true);
        $save_data = [];
        $contentJson = $question->content_json;

        switch ($question->type) {
            case Question::TYPE_QUIZX:
                $result = null;
                $isCorrect = false;

                if (is_array($request->answer)) {
                    if (count($request->answer) > 0) {
                        $answer_user = $request->answer;
                        sort($answer_user);

                        $answer_question = $contentJson['answer'];
                        sort($answer_question);

                        $answer_user_str = implode(':', $answer_user);
                        $answer_question_str = implode(':', $answer_question);

                        if ($answer_user_str == $answer_question_str) {
                            $result = 1;
                            $isCorrect = true;
                        } else {
                            $result = 0;
                        }
                    } else if ($quizResult->type === QuizResult::TYPE_FLASHCARD) {
                        $isCorrect = true;
                    }
                }

                $save_data = [
                    'answer' => $request->answer,
                    'result' => $result,
                    'isCorrect' => $isCorrect,
                    'question' => [
                        'answer' => $contentJson['answer'],
                    ]
                ];
                break;
            case Question::TYPE_BLANK:
            case Question::TYPE_DRAG_DROP:
            case Question::TYPE_DROPDOWN:
                $result = null;
                $isCorrect = false;
                $detailedResults = []; // Lưu kết quả chi tiết từng chỗ trống

                if (is_array($request->answer)) {
                    if (count($request->answer) > 0) {
                        $userAnswers = $request->answer; // Format: [{"targetId": 1, "optionId": [1], "type": "option"} hoặc {"targetId": 1, "text": "user input", "type": "input"}]
                        $correctAnswers = $contentJson['answer'];

                        $isCorrect = true;
                        $totalBlanks = count($correctAnswers);
                        $correctBlanks = 0;

                        // Kiểm tra từng chỗ trống
                        foreach ($correctAnswers as $correctAnswer) {
                            $targetId = $correctAnswer['targetId'];
                            $correctOptionIds = $correctAnswer['optionId'];

                            // Tìm câu trả lời của user cho chỗ trống này
                            $userAnswer = collect($userAnswers)->firstWhere('targetId', $targetId);

                            $blankResult = [
                                'targetId' => $targetId,
                                'isCorrect' => false,
                                'userAnswer' => $userAnswer,
                                'correctAnswer' => $correctAnswer
                            ];

                            if (!$userAnswer) {
                                $isCorrect = false;
                                $detailedResults[] = $blankResult;
                                continue;
                            }

                            // Xử lý theo type của user answer
                            if (isset($userAnswer['type']) && $userAnswer['type'] === 'input') {
                                // User tự nhập text
                                $blankResult['isCorrect'] = $this->checkInputAnswer(
                                    $userAnswer['text'] ?? '',
                                    $correctOptionIds,
                                    $contentJson['options'] ?? []
                                );
                            } else {
                                // User chọn option
                                $blankResult['isCorrect'] = $this->checkOptionAnswer(
                                    $userAnswer['optionId'] ?? [],
                                    $correctOptionIds
                                );
                            }

                            if ($blankResult['isCorrect']) {
                                $correctBlanks++;
                            } else {
                                $isCorrect = false;
                            }

                            $detailedResults[] = $blankResult;
                        }

                        $result = $isCorrect ? 1 : 0;
                    } else if ($quizResult->type === QuizResult::TYPE_FLASHCARD) {
                        $isCorrect = true;
                    }
                }

                $save_data = [
                    'answer' => $request->answer,
                    'result' => $result,
                    'isCorrect' => $isCorrect,
                    'detailedResults' => $detailedResults ?? [], // Thêm kết quả chi tiết
                    'correctBlanks' => $correctBlanks ?? 0, // Số lượng chỗ trống đúng
                    'totalBlanks' => $totalBlanks ?? 0, // Tổng số lượng chỗ trống
                    'question' => [
                        'answer' => $contentJson['answer'],
                    ]
                ];
                break;
        }

        $save_data['timer'] = $request->timer;
        $save_data['question_duration'] = $request->question_duration ?? 0;
        $save_data['answered_at'] = now()->toISOString();
        $save_data['qtype'] = $question->type;

        $data_log[$question->id] = $save_data;
        $total_correct = 0;
        $total_wrong = 0;

        foreach ($data_log as $log) {
            if (isset($log['isCorrect'])) {
                $log['isCorrect'] ? $total_correct++ : $total_wrong++;
            }
        }

        $quizResult->update([
            'status' => QuizResult::STATUS_DOING,
            'total_correct' => $total_correct,
            'total_wrong' => $total_wrong,
            'not_doing' => $quizResult->total - $total_correct - $total_wrong,
            'data_log' => json_encode($data_log),
        ]);

        return $this->jsonResponse('OK', ['data' => $save_data]);
    }

    public function soloEnd(QuizResult $quizResult, Request $request)
    {
        $canPlay = true;

        if ($quizResult->status == QuizResult::STATUS_DONE) {
            $canPlay = false;
        } else {
            if (auth()->check()) {
                $userId = auth()->id();

                if ($quizResult->user_id != $userId) {
                    $canPlay = false;
                }
            }
        }

        if (!$canPlay) {
            return $this->jsonResponse('Không thể thao tác.', [
                'data' => [
                    'newjoin' => true,
                    'quiz_id' => $quizResult->table_content_id,
                ]
            ]);
        }

        $quizResult->update([
            'status' => QuizResult::STATUS_DONE,
            'timer' => $request->timer,
            'total_duration' => $request->total_duration,
            'completed_at' => now(),
        ]);

        return new QuizResultResource($quizResult);
    }

    /**
     * Kiểm tra đáp án tự nhập
     */
    private function checkInputAnswer($userText, $correctOptionIds, $options): bool
    {
        $userText = normalizeText($userText);

        // So sánh với tất cả đáp án đúng cho target này
        foreach ($correctOptionIds as $correctOptionId) {
            // Tìm option text từ content_json
            $correctOption = collect($options)->firstWhere('id', $correctOptionId);
            if ($correctOption) {
                $correctText = normalizeText($correctOption['content']);
                if ($userText === $correctText) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Kiểm tra đáp án chọn option
     */
    private function checkOptionAnswer($userOptionIds, $correctOptionIds): bool
    {
        if (!is_array($userOptionIds)) {
            return false;
        }

        sort($userOptionIds);
        sort($correctOptionIds);

        return $userOptionIds === $correctOptionIds;
    }

    public function show(QuizResult $quizResult)
    {
        // Kiểm tra quyền truy cập
        if (auth()->check()) {
            $userId = auth()->id();

            // Chỉ cho phép user sở hữu hoặc user trong classroom (nếu có assignment)
            if ($quizResult->user_id !== $userId) {
                if ($quizResult->classroom_table_content_id) {
                    $exists = ClassroomUser::where('user_id', $userId)
                        ->join('classroom_table_content', function ($join) use ($quizResult) {
                            $join->on('classroom_table_content.classroom_id', '=', 'classroom_user.classroom_id')
                                ->where('classroom_table_content.id', $quizResult->classroom_table_content_id);
                        })
                        ->exists();

                    if (!$exists) {
                        return $this->jsonResponse('Không có quyền truy cập.', [], 403);
                    }
                } else {
                    return $this->jsonResponse('Không có quyền truy cập.', [], 403);
                }
            }
        } else {
            // Nếu chưa đăng nhập, chỉ cho phép truy cập quiz result không có user_id
            if ($quizResult->user_id !== null) {
                return $this->jsonResponse('Không có quyền truy cập.', [], 401);
            }
        }

        return new QuizResultResource($quizResult);
    }
}
