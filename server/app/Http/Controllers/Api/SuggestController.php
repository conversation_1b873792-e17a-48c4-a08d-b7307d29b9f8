<?php

namespace App\Http\Controllers\Api;

use App\Models\Grade;
use App\Models\Subject;
use App\Models\Book;
use Illuminate\Http\Request;
use App\Http\Resources\SuggestResource;
use App\Models\TableContent;

class SuggestController extends BaseController
{
    public function suggest(Request $request)
    {
        $optionData = [];

        if ($option = $request->input('option')) {
            $optionArr = explode(',', $option);

            foreach ($optionArr as $op) {
                switch ($op) {
                    case 'grades':
                        $optionData['grades'] = $this->grades()['data'] ?? [];
                        break;
                    case 'subjects':
                        $optionData['subjects'] = $this->subjects()['data'] ?? [];
                        break;
                    case 'books':
                        $optionData['books'] = $this->books()['data'] ?? [];
                        break;
                    case 'myQuizzes':
                        $optionData['myQuizzes'] = $this->myQuizzes()['data'] ?? [];
                        break;
                }
            }
        }

        return $this->jsonResponse('Success', ['data' => $optionData]);
    }

    public function grades()
    {
        $grades = Grade::select('id as value', 'title as label')
            ->where('status', Grade::ACTIVE)
            ->orderBy('id', 'DESC')
            ->get();

        return SuggestResource::collection($grades)
            ->response()
            ->getData(true);
    }

    public function subjects()
    {
        $subjects = Subject::select('id as value', 'title as label')
            ->where('status', Subject::ACTIVE)
            ->orderBy('id', 'DESC')
            ->get();

        return SuggestResource::collection($subjects)
            ->response()
            ->getData(true);
    }

    public function books()
    {
        $books = Book::select('id as value', 'name as label')
            ->orderBy('id', 'DESC')
            ->get();

        return SuggestResource::collection($books)
            ->response()
            ->getData(true);
    }
}
