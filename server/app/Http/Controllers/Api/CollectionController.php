<?php

namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use App\Models\Collection;
use App\Models\TableContent;
use App\Http\Resources\CollectionResource;
use App\Http\Resources\TableContentResource;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Log;
use App\Http\Requests\Api\StoreCollectionRequest;

class CollectionController extends BaseController
{
    public function index(Request $request)
    {
        $collections = Collection::where('user_id', auth()->id())->withCount([
            'tableContents' => function ($que) use ($request) {
                if ($request->quizId) {
                    $que->where('collectionable_id', $request->quizId)
                        ->where('collectionable_type', TableContent::class);
                }
            }
        ])->get();

        return CollectionResource::collection($collections);
    }

    public function store(StoreCollectionRequest $request)
    {
        $collection = Collection::create([
            'user_id' => auth()->id(),
            'title' => $request->title,
        ]);

        return new CollectionResource($collection);
    }

    public function show(Request $request, Collection $collection)
    {
        Gate::authorize('edit', $collection);

        if ($request->page) {
            $page = $request->get('page', 1);
            $limit = $request->get('limit', 10);
            $orderBy = $request->get('orderBy', 'latest');

            $collection->load(['user:id,name']);

            $query = $collection->tableContents()
                ->select([
                    'table_contents.id',
                    'table_contents.title',
                    'table_contents.slug',
                    'table_contents.banner',
                    'table_contents.grade_id',
                    'table_contents.subject_id',
                    'table_contents.type',
                    'table_contents.editor_id',
                    'table_contents.created_at'
                ])
                ->with(['grade:id,title', 'subject:id,title', 'editor:id,name'])
                ->withCount('questions');

            switch ($orderBy) {
                case 'latest':
                    $query->orderBy('table_contents.id', 'DESC');
                    break;
                case 'oldest':
                    $query->orderBy('table_contents.id', 'ASC');
                    break;
                case 'abc':
                    $query->orderBy('table_contents.title', 'ASC');
                    break;
                default:
                    $query->orderBy('table_contents.id', 'DESC');
                    break;
            }

            $tableContents = $query
                ->offset(($page - 1) * $limit)
                ->limit($limit)
                ->get();

            $collection->setRelation('tableContents', $tableContents);
        }

        return new CollectionResource($collection);
    }

    public function update(StoreCollectionRequest $request, Collection $collection)
    {
        Gate::authorize('edit', $collection);

        $collection->update([
            'title' => $request->title,
        ]);

        return new CollectionResource($collection);
    }

    public function destroy(Collection $collection)
    {
        Gate::authorize('edit', $collection);

        $collection->delete();

        return $this->jsonResponse('Bộ sưu tập đã được xóa thành công');
    }

    public function addQuiz(Collection $collection, TableContent $quiz)
    {
        Gate::authorize('edit', $collection);

        $collection->tableContents()->sync($quiz->id);

        return $this->jsonResponse('Đã thêm nội dung vào bộ sưu tập');
    }

    public function removeQuiz(Collection $collection, TableContent $quiz)
    {
        Gate::authorize('edit', $collection);

        $collection->tableContents()->detach($quiz->id);

        return $this->jsonResponse('Đã xóa nội dung khỏi bộ sưu tập');
    }
}
