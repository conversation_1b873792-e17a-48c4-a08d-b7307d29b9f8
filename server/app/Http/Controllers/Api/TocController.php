<?php

namespace App\Http\Controllers\Api;

use App\Models\Toc;
use App\Models\TableContent;
use App\Http\Resources\TocResource;
use App\Http\Resources\TableContentResource;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use App\Http\Requests\Api\UpdateTitleRequest;
use App\Http\Requests\CreateTocRequest;
use App\Models\Course;
use Illuminate\Support\Facades\Gate;

class TocController extends BaseController
{
    public function getToc(Toc $toc)
    {
        return new TocResource($toc);
    }

    public function getQuizsOfToc(Toc $toc, Request $request)
    {
        $page = (int) ($request->query('page', 1));
        $limit = (int) ($request->query('limit', 10));

        $quizzes = TableContent::select([
            'id',
            'title',
            'banner',
            'slug',
            'grade_id',
            'subject_id',
            'editor_id',
            'type',
            'created_at'
        ])
        ->where('vip', TableContent::VIP)
        ->where('type', TableContent::TYPE_EXAM_QUIZ)
        ->where('toc_id', $toc->id)
        ->with([
            'grade:id,title',
            'subject:id,title',
        ])
        ->withCount('questions')
        ->offset(($page - 1) * $limit)
        ->limit($limit)
        ->orderByDesc('id')
        ->get();

        $toc->load([
            'course' => function ($query) {
                $query->select([
                    'id',
                    'title',
                    'subtitle',
                    'slug',
                    'grade_id',
                    'subject_id',
                    'book_id',
                ]);
            },
            'children' => function ($query) {
                $query->select([
                    'id',
                    'title',
                    'subtitle',
                    'slug',
                    'course_id',
                    'grade_id',
                    'subject_id',
                    'index',
                    'parent_id'
                ])
                ->where('status', Toc::ACTIVE)
                ->orderBy('index');
            }
        ]);

        return $this->jsonResponse('OK', [
            'data' => [
                'toc' => new TocResource($toc),
                'quizzes' => TableContentResource::collection($quizzes),
            ]
        ]);
    }

    public function updateTitle(UpdateTitleRequest $request, Toc $toc)
    {
        Gate::authorize('edit', $toc);

        $toc->title = $request->title;
        $toc->slug = Str::slug($request->title);
        $toc->save();

        return new TocResource($toc);
    }

    public function destroy(Toc $toc)
    {
        Gate::authorize('edit', $toc);

        $tocIds = $this->getAllChildTocs($toc->id);
        $tocIds[] = $toc->id;

        TableContent::whereIn('toc_id', $tocIds)
            ->update([
                'toc_id' => null,
                'course_id' => null
            ]);

        Toc::whereIn('id', $tocIds)->delete();

        return response()->json(['message' => 'Đã xóa thư mục thành công']);
    }

    private function getAllChildTocs($parentId)
    {
        $childIds = [];
        $children = Toc::where('parent_id', $parentId)->get(['id']);

        foreach ($children as $child) {
            $childIds[] = $child->id;
            $childIds = array_merge($childIds, $this->getAllChildTocs($child->id));
        }

        return $childIds;
    }

    public function create(CreateTocRequest $request)
    {
        $validated = $request->validated();

        $course = Course::findOrFail($validated['course_id']);

        $toc = new Toc();
        $toc->course_id = $validated['course_id'];
        $toc->title = $validated['title'];
        $toc->slug = Str::slug($validated['title']);

        $toc->grade_id = $course->grade_id;
        $toc->subject_id = $course->subject_id;

        $toc->status = Toc::ACTIVE;

        $toc->editor_id = auth()->id();

        if (!empty($validated['toc_id'])) {
            $toc->parent_id = $validated['toc_id'];
        }

        $maxIndex = Toc::where('course_id', $validated['course_id'])
            ->where(function($query) use ($validated) {
                if (!empty($validated['toc_id'])) {
                    $query->where('parent_id', $validated['toc_id']);
                } else {
                    $query->whereNull('parent_id');
                }
            })
            ->max('index');

        $toc->index = $maxIndex !== null ? $maxIndex + 1 : 0;

        $toc->save();

        return new TocResource($toc);
    }
}
