<?php

namespace App\Http\Controllers;

use App\Services\Media\FileService;
use App\Http\Requests\UploadMediaRequest;

class MediaController extends Controller
{
    protected $fileService;

    public function __construct(FileService $fileService) {
        $this->fileService = $fileService;
    }

    public function upload(UploadMediaRequest $request)
    {
        $uploadedPath = $this->fileService->upload($request->folder ? ($request->folder . '/') : 'media/', $request->media);
        $url = $this->fileService->uploadedFileUrl($uploadedPath);

        return response()->json([
            'uploaded' => true,
            'path' => $uploadedPath,
            'url' => $url,
            'link' => $url,
            'name' => basename($uploadedPath),
            'ext' => strtolower(pathinfo($uploadedPath, PATHINFO_EXTENSION)),
        ]);
    }
}
