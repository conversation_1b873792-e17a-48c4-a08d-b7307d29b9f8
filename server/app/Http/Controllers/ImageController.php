<?php

namespace App\Http\Controllers;

use App\Services\Media\ImageService;
use App\Http\Requests\UploadImageRequest;
use App\Http\Requests\UploadImageBase64Request;
use Illuminate\Validation\ValidationException;

class ImageController extends Controller
{
    protected $imageService;

    public function __construct(ImageService $imageService)
    {
        $this->imageService = $imageService;
    }

    public function upload(UploadImageRequest $request)
    {
        $prefixFolder = $request->folder ?: auth()->id();

        if (config('app.upload_api_domain')) {
            $uploadedPath = $this->imageService->uploadToRemoteServer($prefixFolder, $request->image);
            $url = $uploadedPath;
        } else {
            $uploadedPath = $this->imageService->upload($prefixFolder, $request->image);
            $url = $this->imageService->uploadedImageUrl($uploadedPath);
        }

        return response()->json([
            'uploaded' => true,
            'path' => $uploadedPath,
            'url' => $url,
            'link' => $url,
        ]);
    }

    public function uploadBase64(UploadImageBase64Request $request)
    {
        $uploadedPath = $this->imageService->uploadBase64($request->folder ?: auth()->id(), $request->image);
        $url = $this->imageService->uploadedImageUrl($uploadedPath);

        return response()->json([
            'uploaded' => true,
            'path' => $uploadedPath,
            'url' => $url,
            'link' => $url,
        ]);
    }
}
