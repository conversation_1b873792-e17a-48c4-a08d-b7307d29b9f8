<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Provider;
use Illuminate\Http\Request;
use Auth;
use Socialite;
use Illuminate\Support\Facades\DB;
use Lara<PERSON>\Socialite\Contracts\User as ProviderUser;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Date;
use Illuminate\Support\Facades\Log;
use Google\Client as GoogleClient;

class AuthenticatedSocialController22 extends Controller
{
    /**
     * Redirect the user to the provider authentication page.
     *
     * @param string $provider
     * @return \Illuminate\Http\Response
     */
    public function redirectToProvider($provider)
    {
        $redirect = request()->query('redirect', '/dashboard');
        $state = base64_encode(json_encode(['redirect' => $redirect]));

        $redirectUrl = Socialite::driver($provider)
            ->stateless()
            ->with(['state' => $state])
            ->redirect()
            ->getTargetUrl();

        return response()->json(['url' => $redirectUrl]);
    }

    /**
     * Obtain the user information from provider.
     *
     * @param string $provider
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function handleProviderCallback($provider, Request $request)
    {
        $providerAccount = Socialite::driver($provider)->stateless()->user();
        $email = $providerAccount->getEmail() ?: "{$providerAccount->id}@{$provider}.com";
        $providerId = $providerAccount->getId();

        $providerData = [
            'provider_id' => $providerId,
            'email' => $email,
            'name' => $providerAccount->getName() ?? $providerAccount->getNickname(),
            'nickname' => $providerAccount->getNickname(),
            'avatar' => $providerAccount->getAvatar(),
            'token' => $providerAccount->token,
            'secret' => $providerAccount->tokenSecret ?? null,
            'refresh_token' => $providerAccount->refreshToken ?? null,
        ];

        $account = Provider::where('provider', $provider)
            ->where('provider_id', $providerId)
            ->first();

        $user = $account ? $account->user : $this->createsUser($provider, $providerData, $email);

        if (!$account) {
            $this->createProviderAccount($user, $provider, $providerData);
        } else {
            $this->updateProviderAccount($user, $account, $provider, $providerAccount);
        }

        Auth::login($user, true);
        $request->session()->regenerate();

        $backUrl = $this->getRedirectUrl($request);

        return redirect($backUrl);
    }

    /**
     * Create a new user from a social provider user.
     *
     * @param string $provider
     * @param array $providerData
     * @param string $email
     * @return User
     */
    public function createsUser(string $provider, array $providerData, string $email)
    {
        return DB::transaction(function () use ($provider, $providerData, $email) {
            return User::firstOrCreate(
                ['email' => $email],
                [
                    'name' => $providerData['name'],
                    'avatar' => $providerData['avatar'],
                    'password' => Hash::make($providerData['provider_id']),
                    'status' => User::ACTIVE,
                    'email_verified_at' => Date::now(),
                ]
            );
        });
    }

    /**
     * Create a provider account for the user.
     *
     * @param User $user
     * @param string $provider
     * @param array $providerData
     * @return Provider
     */
    protected function createProviderAccount(User $user, string $provider, array $providerData)
    {
        $providerData['user_id'] = $user->id;
        $providerData['provider'] = strtolower($provider);

        return Provider::forceCreate($providerData);
    }

    /**
     * Update the provider account for the user.
     *
     * @param \App\Models\User $user
     * @param \App\Models\Provider $providerAccount
     * @param string $provider
     * @param \Laravel\Socialite\Contracts\User $providerUser
     * @return \App\Models\Provider
     */
    public function updateProviderAccount($user, Provider $providerAccount, string $provider, ProviderUser $providerUser)
    {
        $providerAccount->forceFill([
            'provider' => strtolower($provider),
            'provider_id' => $providerUser->getId(),
            'name' => $providerUser->getName(),
            'nickname' => $providerUser->getNickname(),
            'email' => $providerUser->getEmail(),
            'avatar' => $providerUser->getAvatar(),
            'token' => $providerUser->token,
            'secret' => $providerUser->tokenSecret ?? null,
            'refresh_token' => $providerUser->refreshToken ?? null,
            'expires_at' => property_exists($providerUser, 'expiresIn') ? now()->addSeconds($providerUser->expiresIn) : null,
        ])->save();

        return $providerAccount;
    }

    protected function getRedirectUrl($request, $redirect = '/dashboard'): string
    {
        if ($state = $request->query('state')) {
            $decoded = json_decode(base64_decode($state), true);
            if (isset($decoded['redirect']) && !str_contains($decoded['redirect'], 'login')) {
                $redirect = $decoded['redirect'];
            }
        }

        return $this->normalizeUrl($redirect, env('FRONTEND_URL'));
    }

    public function normalizeUrl($url, $base): string
    {
        $url = trim($url);
        $url = str_starts_with($url, 'http') ? $url : rtrim($base, '/') . '/' . ltrim($url, '/');
        $p = parse_url($url);

        return "{$p['scheme']}://{$p['host']}" . (isset($p['port']) ? ":{$p['port']}" : '') . ($p['path'] ?? '/');
    }

    public function authenticateGoogleOneTap(Request $request): \Illuminate\Http\JsonResponse
    {
        try {
            $credential = $request->input('credential');

            if (!$credential) {
                return response()->json([
                    'success' => false,
                    'message' => 'Credential không hợp lệ',
                ], 400);
            }

            // Xác thực token với Google API
            $client = new GoogleClient(['client_id' => config('services.google.client_id')]);
            $payload = $client->verifyIdToken($credential);

            if (!$payload) {
                return response()->json([
                    'success' => false,
                    'message' => 'Token không hợp lệ',
                ], 400);
            }

            // Lấy thông tin người dùng từ payload
            $googleId = $payload['sub'];
            $email = $payload['email'];

            $data = [
                'provider_id' => $googleId,
                'email' => $email,
                'name' => $payload['name'] ?? ($payload['given_name'] . ' ' . $payload['family_name']),
                'avatar' => $payload['picture'] ?? null,
                'token' => $payload['jti'] ?? null,
                'expires_at' => date('Y-m-d H:i:s', $payload['exp']),
            ];

            // Tìm hoặc tạo user
            $user = $this->findOrCreateUser($data, 'google', $googleId, $email);

            // Đăng nhập
            Auth::login($user, true);
            $request->session()->regenerate();

            return response()->json([
                'success' => true,
                'message' => 'Đăng nhập thành công',
                'data' => $user
            ]);
        } catch (\Exception $e) {
            Log::error('Google One Tap error: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Đã xảy ra lỗi khi xác thực: ' . $e->getMessage(),
            ], 500);
        }
    }

    public function findOrCreateUser($data, $provider, $providerId, $email)
    {
        $account = Provider::where('provider', $provider)
            ->where('provider_id', $providerId)
            ->first();

        $user = $account ? $account->user : $this->createsUser($provider, $data, $email);

        if (!$account) {
            $this->createProviderAccount($user, $provider, $data);
        }

        return $user;
    }
}
