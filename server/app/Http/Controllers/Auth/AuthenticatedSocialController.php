<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Provider;
use Illuminate\Http\Request;
use Auth;
use Socialite;
use Illuminate\Support\Facades\DB;
use Lara<PERSON>\Socialite\Contracts\User as ProviderUser;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Date;
use Illuminate\Support\Facades\Log;

class AuthenticatedSocialController extends Controller
{
    /**
     * Redirect the user to the provider authentication page.
     *
     * @param string $provider
     * @return \Illuminate\Http\Response
     */
    public function redirectToProvider($provider)
    {
        $redirect = request()->query('redirect', '/dashboard');
        $state = base64_encode(json_encode(['redirect' => $redirect]));

        $redirectUrl = Socialite::driver($provider)
            ->stateless()
            ->with(['state' => $state])
            ->redirect()
            ->getTargetUrl();

        return response()->json(['url' => $redirectUrl]);
    }

    /**
     * Obtain the user information from provider.
     *
     * @param string $provider
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function handleProviderCallback($provider, Request $request)
    {
        $providerAccount = Socialite::driver($provider)->stateless()->user();
        $email = $providerAccount->getEmail() ?: "{$providerAccount->id}@{$provider}.com";

        $account = Provider::where('provider', $provider)
            ->where('provider_id', $providerAccount->id)
            ->first();

        $user = $account ? $account->user : $this->createsUser($provider, $providerAccount, $email);

        if (!$account) {
            $this->createProviderAccount($user, $provider, $providerAccount, $email);
        } else {
            $this->updateProviderAccount($user, $account, $provider, $providerAccount);
        }

        Auth::login($user, true);
        $request->session()->regenerate();

        $backUrl = $this->getRedirectUrl($request);

        return redirect($backUrl);
    }

    /**
     * Create a new user from a social provider user.
     *
     * @param string $provider
     * @param \Laravel\Socialite\Contracts\User $providerUser
     * @param string $email
     * @return \App\Models\User
     */
    public function createsUser(string $provider, ProviderUser $providerUser, string $email)
    {
        return DB::transaction(function () use ($provider, $providerUser, $email) {
            return User::firstOrCreate(
                ['email' => $email],
                [
                    'name' => $providerUser->getName() ?? $providerUser->getNickname(),
                    'avatar' => $providerUser->getAvatar(),
                    'password' => Hash::make($providerUser->getId()),
                    'status' => User::ACTIVE,
                    'email_verified_at' => Date::now(),
                ]
            );
        });
    }

    /**
     * Create a provider account for the user.
     *
     * @param \App\Models\User $user
     * @param string $provider
     * @param \Laravel\Socialite\Contracts\User $providerUser
     * @param string $email
     * @return \App\Models\Provider
     */
    protected function createProviderAccount($user, string $provider, ProviderUser $providerUser, string $email)
    {
        return Provider::forceCreate([
            'user_id' => $user->id,
            'provider' => strtolower($provider),
            'provider_id' => $providerUser->getId(),
            'name' => $providerUser->getName(),
            'nickname' => $providerUser->getNickname(),
            'email' => $email,
            'avatar' => $providerUser->getAvatar(),
            'token' => $providerUser->token,
            'secret' => $providerUser->tokenSecret ?? null,
            'refresh_token' => $providerUser->refreshToken ?? null,
        ]);
    }

    /**
     * Update the provider account for the user.
     *
     * @param \App\Models\User $user
     * @param \App\Models\Provider $providerAccount
     * @param string $provider
     * @param \Laravel\Socialite\Contracts\User $providerUser
     * @return \App\Models\Provider
     */
    public function updateProviderAccount($user, Provider $providerAccount, string $provider, ProviderUser $providerUser)
    {
        $providerAccount->forceFill([
            'provider' => strtolower($provider),
            'provider_id' => $providerUser->getId(),
            'name' => $providerUser->getName(),
            'nickname' => $providerUser->getNickname(),
            'email' => $providerUser->getEmail(),
            'avatar' => $providerUser->getAvatar(),
            'token' => $providerUser->token,
            'secret' => $providerUser->tokenSecret ?? null,
            'refresh_token' => $providerUser->refreshToken ?? null,
            'expires_at' => property_exists($providerUser, 'expiresIn') ? now()->addSeconds($providerUser->expiresIn) : null,
        ])->save();

        return $providerAccount;
    }

    protected function getRedirectUrl($request, $redirect = '/dashboard'): string
    {
        if ($state = $request->query('state')) {
            $decoded = json_decode(base64_decode($state), true);
            if (isset($decoded['redirect']) && !str_contains($decoded['redirect'], 'login')) {
                $redirect = $decoded['redirect'];
            }
        }

        return $this->normalizeUrl($redirect, env('FRONTEND_URL'));
    }

    public function normalizeUrl($url, $base): string
    {
        $url = trim($url);
        $url = str_starts_with($url, 'http') ? $url : rtrim($base, '/') . '/' . ltrim($url, '/');
        $p = parse_url($url);

        return "{$p['scheme']}://{$p['host']}" . (isset($p['port']) ? ":{$p['port']}" : '') . ($p['path'] ?? '/');
    }
}
