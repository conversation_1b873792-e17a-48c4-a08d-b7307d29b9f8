<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\LoginRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\View\View;
use Illuminate\Support\Facades\Cookie;
use App\Http\Resources\UserResource;

class AuthenticatedSessionController extends Controller
{
    /**
     * Display the login view.
     */
    public function create(): View
    {
        return view('auth.login');
    }

    /**
     * Handle an incoming authentication request.
     */
    public function store(LoginRequest $request)
    {
        $request->authenticate();

        $request->session()->regenerate();

        if ($request->expectsJson()) {
            $cookie = $request->user()->cookieUserApi();

            return (new UserResource(Auth::user()))
                ->toResponse($request)
                ->withCookie($cookie);
        }

        return redirect()
            ->intended(route('dashboard', absolute: false));
    }

    /**
     * Destroy an authenticated session.
     */
    public function destroy(Request $request)
    {
        $request->user()->tokens()->delete();

        Auth::guard('web')->logout();

        $request->session()->invalidate();

        $request->session()->regenerateToken();

        // Xóa cookie for 'authenticated'
        $cookie = Cookie::forget(config('app.user_cookie_name'));

        if ($request->expectsJson()) {
            return response()
                ->noContent()
                ->withCookie($cookie);
        }

        return redirect('/');
    }
}
