<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use <PERSON><PERSON>\Sanctum\PersonalAccessToken;

class SanctumAuthentication
{
    public function handle(Request $request, Closure $next, ...$abilities)
    {
        if ($token_plain = $request->bearerToken()) {
            $accessToken = PersonalAccessToken::findToken($token_plain);
            // $user = Auth::guard('sanctum')->user();

            if ($accessToken) {
                $user = $accessToken->tokenable;

                if (method_exists($user, 'withAccessToken')) {
                    $user->withAccessToken($accessToken);
                }

                // (Tùy chọn) yêu cầu abilities: ->middleware('sanctum.api:read,write')
                foreach ($abilities as $ab) {
                    if (!$user->tokenCan($ab)) {
                        abort(403, 'Insufficient token abilities.');
                    }
                }

                if ($user) {
                    // Đăng nhập cho vòng đời request (không tạo session)
                    Auth::setUser($user);
                    $request->setUserResolver(fn () => $user);
                }
            }
        }

        return $next($request);
    }
}
