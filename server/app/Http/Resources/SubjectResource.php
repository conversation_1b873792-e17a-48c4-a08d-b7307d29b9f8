<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class SubjectResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'slug' => $this->slug,
            'index' => $this->index,
            'banner' => $this->when($this->banner, asset_image($this->banner)),
            'grades' => $this->whenLoaded('grades', fn () =>
                GradeResource::collection($this->grades)
            ),
            'courses' => $this->whenLoaded('courses', fn () =>
                CourseResource::collection($this->courses)
            ),
            'quiz' => $this->whenLoaded('tableContents', fn () =>
                TableContentResource::collection($this->tableContents)
            ),
        ];
    }
}
