<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class ClassRoomResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'color' => $this->color,
            'code' => $this->code,
            'grade_id' => $this->grade_id,
            'created_at' => $this->created_at,
            'quizzes_count' => $this->when($this->quizzes_count, $this->quizzes_count),
            'users_count' => $this->when($this->users_count, $this->users_count),
            'classroom_user_count' => $this->when(isset($this->classroom_users_count), $this->classroom_users_count),
            'classroom_quiz' => $this->whenLoaded('classroomTableContents', fn() => ClassroomTableContentResource::collection($this->classroomTableContents)),
            'classroom_quiz_count' => $this->when(isset($this->classroom_table_contents_count), $this->classroom_table_contents_count),
            'author' => $this->whenLoaded('author', fn() => new UserResource($this->author)),
            'grade' => $this->whenLoaded('grade', fn() => new GradeResource($this->grade)),
            'users' => $this->whenLoaded('users', fn() => UserResource::collection($this->users)),
            'completion_rate' => $this->when(isset($this->completion_rate), $this->completion_rate),
        ];
    }
}
