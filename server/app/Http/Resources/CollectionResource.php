<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class CollectionResource extends JsonResource
{
    /**
     * Transform the resource collection into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'user_id' => $this->user_id,
            'user' => $this->whenLoaded('user', fn() => new UserResource($this->user)),
            'table_contents_count' => $this->when(isset($this->table_contents_count), $this->table_contents_count),
            'table_contents' => $this->whenLoaded('tableContents', fn() => TableContentResource::collection($this->tableContents)),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
