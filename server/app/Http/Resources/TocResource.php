<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class TocResource extends JsonResource
{
    /**
     * Transform the resource collection into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'subtitle' => $this->subtitle,
            'slug' => $this->slug,
            'course_id' => $this->course_id,
            'grade_id' => $this->grade_id,
            'subject_id' => $this->subject_id,
            'index' => $this->index,
            'parent_id' => $this->parent_id,
            'grade' => $this->whenLoaded('grade', fn () => new GradeResource($this->grade)),
            'subject' => $this->whenLoaded('subject', fn () => new SubjectResource($this->subject)),
            'course' => $this->whenLoaded('course', fn () => new CourseResource($this->course)),
            'children' => $this->whenLoaded('children', fn () => TocResource::collection($this->children)),
            'tableContents' => $this->whenLoaded('tableContents', fn () => TableContentResource::collection($this->tableContents)),
        ];
    }
}
