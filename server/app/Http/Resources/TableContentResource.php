<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class TableContentResource extends JsonResource
{
    /**
     * Transform the resource collection into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $questionsCount = $this->questions_count ?? $this->when(
            $this->relationLoaded('questions'), // Ki<PERSON><PERSON> tra xem questions có được load không
            fn() => $this->questions->count(), // Nế<PERSON> có, lấy số lượng questions
            // fn () => $this->questions()->count() // Nế<PERSON> không, thực hiện query để lấy số lượng questions
            0
        );

        return [
            'id' => $this->id,
            'title' => $this->title,
            'subtitle' => $this->subtitle,
            'slug' => $this->slug,
            'banner' => $this->when($this->banner, asset_image($this->banner)),
            'grade_id' => $this->grade_id,
            'subject_id' => $this->subject_id,
            'course_id' => $this->course_id,
            'toc_id' => $this->toc_id,
            'editor_id' => $this->when($this->editor_id, $this->editor_id),
            'status' => $this->status,
            'created_at' => $this->created_at,
            'updated_at' => $this->when($this->updated_at, $this->updated_at),
            'grade' => $this->whenLoaded('grade', fn() => new GradeResource($this->grade)),
            'subject' => $this->whenLoaded('subject', fn() => new SubjectResource($this->subject)),
            'quizResults' => $this->whenLoaded('quizResults', fn() => QuizResultResource::collection($this->quizResults)),
            'editor' => $this->whenLoaded('editor', fn() => new UserResource($this->editor)),
            'questions_count' => $questionsCount,
            'questions' => $this->whenLoaded('questions', fn() => QuestionResource::collection($this->questions)),
            'parent_id' => $this->parent_id,
            'index' => $this->index,
            'type' => $this->type,
            'type_text' => __('common.quiz_type.' . $this->type),
            'view' => $this->view,
            'played' => $this->when($this->quiz_results_count, $this->quiz_results_count),
            'my_played' => $this->when($this->my_quiz_results_count, $this->my_quiz_results_count),
            'related' => $this->when($this->related, fn() => TableContentResource::collection($this->related)),
            'classroom_quiz' => $this->whenLoaded('classroomTableContents', fn() => ClassroomTableContentResource::collection($this->classroomTableContents)),
            'collections_count' => $this->when($this->collections_count, $this->collections_count),
        ];
    }
}
