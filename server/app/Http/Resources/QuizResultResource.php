<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class QuizResultResource extends JsonResource
{
    /**
     * Transform the resource collection into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $data = [
            'id' => $this->id,
            'quiz_id' => $this->table_content_id,
            'assignment_id' => $this->classroom_table_content_id,
            'total' => $this->total ?: 0,
            'total_correct' => $this->total_correct ?: 0,
            'total_wrong' => $this->total_wrong ?: 0,
            'accuracy' => $this->total > 0 ? round(($this->total_correct * 100) / $this->total) : 0,
            'not_doing' => $this->not_doing ?: 0,
            'timer' => $this->timer ?: 0,
            'total_duration' => $this->total_duration ?: 0,
            'started_at' => $this->started_at,
            'completed_at' => $this->completed_at,
            'status' => $this->status,
            'type' => $this->type,
            'data_log' => $this->when($this->data_log, $this->data_log),
            'quiz' => $this->whenLoaded('tableContent', fn () => new TableContentResource($this->tableContent)),
            'created_at' => $this->when($this->created_at, $this->created_at),
            'user_id' => $this->user_id,
            'user' => $this->whenLoaded('user', fn () => new UserResource($this->user)),
        ];

        return $data;
    }
}
