<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class GradeResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'slug' => $this->slug,
            'index' => $this->index,
            'classrooms' => $this->whenLoaded('classrooms', fn () =>
                ClassRoomResource::collection($this->classrooms)
            ),
            'subjects' => $this->whenLoaded('subjects', fn () =>
                SubjectResource::collection($this->subjects)
            ),
        ];
    }
}
