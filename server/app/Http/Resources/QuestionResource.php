<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use App\Models\Question;
use Illuminate\Support\Arr;

class QuestionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $hide = $request->is('api/play-quiz/*/info');
        $contentJson = $this->content_json ?? [];

        $content = Arr::get($contentJson, 'content', '');
        $rawOptions = Arr::get($contentJson, 'options', []);

        if ($hide) {
            $answers = [];
            $options = [];
            $kind = count(Arr::get($contentJson, 'answer', [])) > 1 ? 'MSQ' : 'MCQ';

            switch ($this->type) {
                case Question::TYPE_QUIZX:
                    $options = array_map(function($item) {
                        return ['content' => $item['content']];
                    }, $rawOptions);
                    break;
                case Question::TYPE_BLANK || Question::TYPE_DRAG_DROP || Question::TYPE_DROPDOWN:
                    // Xáo trộn các đáp án
                    $options = collect($rawOptions)->shuffle()->values()->all();
                    break;
                default:
                    $options = [];
            }
        } else {
            $answers = Arr::get($contentJson, 'answer', []);
            $options = $rawOptions;
        }

        return [
            'id' => $this->id,
            'quiz_id' => $this->table_content_id,
            // 'title' => $this->title,
            // 'content' => $this->content,
             'explain' => $this->explain,
            // 'static_content' => $this->static_content,
            'type' => $this->type,
            'content_json' => [
                'content' => $content,
                'options' => $options,
                'answer'  => $answers,
                'points'  => Arr::get($contentJson, 'points', 1),
                'timeLimit'  => Arr::get($contentJson, 'timeLimit', 0),
                'kind' => $kind ?? null,
            ],
        ];
    }
}
