<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class CourseResource extends JsonResource
{
    /**
     * Transform the resource collection into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'subtitle' => $this->subtitle,
            'slug' => $this->slug,
            'banner' => $this->when($this->banner, asset_image($this->banner)),
            'grade_id' => $this->grade_id,
            'subject_id' => $this->subject_id,
            'book_id' => $this->book_id,
            'editor_id' => $this->when($this->editor_id, $this->editor_id),
            'created_at' => $this->when($this->created_at, $this->created_at),
            'grade' => $this->whenLoaded('grade', fn () => new GradeResource($this->grade)),
            'subject' => $this->whenLoaded('subject', fn () => new SubjectResource($this->subject)),
            'book' => $this->whenLoaded('book', fn () => new BookResource($this->book)),
            'editor' => $this->whenLoaded('editor', fn () => new UserResource($this->editor)),
            'tocs' => $this->whenLoaded('tocs', fn () => TocResource::collection($this->tocs)),
            'tableContents' => $this->whenLoaded('tableContents', fn () => TableContentResource::collection($this->tableContents)),
        ];
    }
}
