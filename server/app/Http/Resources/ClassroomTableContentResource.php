<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class ClassroomTableContentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'title' => $this->when($this->title, $this->title),
            'subtitle' => $this->when($this->subtitle, $this->subtitle),
            'quiz_id' => $this->table_content_id,
            'start_day' => $this->start_time ? strtotime(humanize_date(carbon($this->start_time), 'Y-m-d')) : 0,
            'start_time' => $this->start_time,
            'end_time' => $this->end_time,
            'is_expired' => $this->end_time ? carbon($this->end_time)->isPast() : false,
            'status' => $this->setStatusAssignment($this),
            'code' => $this->code,
            'show_answer' => $this->when($this->show_answer, $this->show_answer),
            'quiz_results_count' => $this->when($this->quiz_results_count, $this->quiz_results_count),
            'correct_percentage' => $this->when($this->correct_percentage, $this->correct_percentage),
            'last_correct_percentage' => $this->when($this->last_correct_percentage, $this->last_correct_percentage),
            'first_correct_percentage' => $this->when($this->first_correct_percentage, $this->first_correct_percentage),
            'classroom' => $this->whenLoaded('classroom', fn () => new ClassRoomResource($this->classroom)),
            'classrooms' => $this->whenLoaded('classrooms', fn () => ClassRoomResource::collection($this->classrooms)),
            'quiz' => $this->whenLoaded('tableContent', fn () => new TableContentResource($this->tableContent)),
            'quiz_results' => $this->whenLoaded('quizResults', fn () => QuizResultResource::collection($this->quizResults)),
        ];
    }

    public function setStatusAssignment($item)
    {
        if ($item->start_time && carbon($item->start_time)->isFuture()) {
            return 0; // Chưa bắt đầu
        }

        if ($item->end_time && carbon($item->end_time)->isPast()) {
            return 2; // Đã kết thúc
        }

        return 1; // Đang diễn ra
    }
}
