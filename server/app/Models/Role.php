<?php

namespace App\Models;

class Role extends BaseModel
{
    const ROLE_ADMIN = 'admin';
    const ROLE_EDITOR = 'editor';
    const ROLE_TEACHER = 'teacher';
    const ROLE_STUDENT = 'student';

    const DISPLAY_NAME = [
        self::ROLE_ADMIN => 'Quản trị viên',
        self::ROLE_EDITOR => 'Biên tập viên',
        self::ROLE_TEACHER => 'Giáo viên',
        self::ROLE_STUDENT => 'Học sinh',
    ];

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['name'];
}
