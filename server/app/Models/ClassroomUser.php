<?php

namespace App\Models;

class ClassroomUser extends BaseModel
{
    const STUDENT = 0;

    protected $table = 'classroom_user';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'classroom_id',
        'user_id',
        'name',
        'role',
        'ident_number',
        'temp_password',
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function classroom()
    {
        return $this->belongsTo(Classroom::class, 'classroom_id');
    }
}
