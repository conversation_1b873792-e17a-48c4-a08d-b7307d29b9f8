<?php

namespace App\Models;

class ClassroomTableContent extends BaseModel
{
    protected $table = 'classroom_table_content';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'classroom_id',
        'table_content_id',
        'show_answer',
        'start_time',
        'end_time',
    ];

    public function classroom()
    {
        return $this->belongsTo(Classroom::class, 'classroom_id');
    }

    public function classrooms()
    {
        return $this->hasManyThrough(
            Classroom::class,
            ClassroomTableContent::class,
            'table_content_id',
            'id',
            'table_content_id',
            'classroom_id'
        )
        ->distinct();
    }

    public function tableContent()
    {
        return $this->belongsTo(TableContent::class, 'table_content_id');
    }

    public function quizResults()
    {
        return $this->hasMany(QuizResult::class, 'classroom_table_content_id');
    }
}
