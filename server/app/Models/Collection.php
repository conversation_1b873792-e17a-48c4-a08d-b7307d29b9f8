<?php

namespace App\Models;

class Collection extends BaseModel
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'title',
        'user_id',
    ];

    /**
     * Get the user that owns the collection.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the table contents associated with the collection.
     */
    public function tableContents()
    {
        return $this->morphedByMany(TableContent::class, 'collectionable', 'collectionables')
            ->withTimestamps();
    }
}
