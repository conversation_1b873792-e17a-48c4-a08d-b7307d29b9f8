<?php

namespace App\Models;

use Illuminate\Support\Str;

class Book extends BaseModel
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'id',
        'name',
        'slug',
    ];

    /**
     * Set the name and the readable slug.
     *
     * @param string $value
     */
    public function setNameAttribute($value)
    {
        $this->attributes['name'] = $value;
        $this->setUniqueSlug($value);
    }

    public function setUniqueSlug($value)
    {
        $slug = Str::slug(preg_replace('/\W+/', ' ', Str::ascii($value)));

        if (static::whereSlug($slug)->where('id', '<>', $this->id)->exists()) {
            $this->setUniqueSlug($slug . '-' . Str::random(5));

            return;
        }

        $this->attributes['slug'] = $slug;
    }

    public function grades()
    {
        return $this->belongsToMany(
            Grade::class,
            'book_grade',
            'book_id',
            'grade_id'
        )->orderBy('id')
        ->withTimestamps();
    }

    public function courses()
    {
        return $this->hasMany(Course::class);
    }
}
