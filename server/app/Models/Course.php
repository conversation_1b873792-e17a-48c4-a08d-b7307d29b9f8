<?php

namespace App\Models;

use Illuminate\Support\Str;

class Course extends BaseModel
{
    const DISABLE = 0, ACTIVE = 1;
    const TYPE_DEFAULT = 0; // user create
    const TYPE_VIP = 1; // admin create
    const DISPLAY_DEFAULT = 0;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'title',
        'subtitle',
        'slug',
        'description',
        'banner',
        'grade_id',
        'subject_id',
        'book_id',
        'editor_id',
        'index',
        'status',
        'type',
        'view',
        'display',
        'related_courses',
        'seo_title',
        'seo_keywords',
        'seo_description',
    ];

    protected $casts = [
        'related_courses' => 'array',
    ];

    /**
     * Set the title and the readable slug.
     *
     * @param string $value
     */
    public function setTitleAttribute($value)
    {
        $this->attributes['title'] = $value;

        $this->setUniqueSlug($value);
    }

    /**
     * Get the unique slug.
     *
     * @return param $extra
     */
    public function getUniqueSlug()
    {
        return $this->slug;
    }

    /**
     * Set the unique slug.
     *
     * @param $value
     * @param $extra
     */
    public function setUniqueSlug($value)
    {
        $slug = str_to_slug($value);

        $this->attributes['slug'] = $slug;
    }

    /**
     * Set the title and the readable slug.
     *
     * @param string $value
     */
    // public function setSlugAttribute($value)
    // {
    //     $this->attributes['slug'] = str_to_slug($value);
    // }

    public function grade()
    {
        return $this->belongsTo(Grade::class);
    }

    public function subject()
    {
        return $this->belongsTo(Subject::class);
    }

    public function book()
    {
        return $this->belongsTo(Book::class);
    }

    public function editor()
    {
        return $this->belongsTo(User::class, 'editor_id');
    }

    public function tocs()
    {
        return $this->hasMany(Toc::class);
    }

    public function tableContents()
    {
        return $this->hasMany(TableContent::class);
    }

    public function related()
    {
        return $this->belongsToMany(
            Course::class,
            'related_courses',
            'course_id',
            'related_id'
        );
    }

    public function relatives()
    {
        return $this->belongsToMany(
            Course::class,
            'related_courses',
            'related_id',
            'course_id'
        );
    }
}
