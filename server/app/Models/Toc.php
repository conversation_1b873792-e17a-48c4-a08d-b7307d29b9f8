<?php

namespace App\Models;

use Illuminate\Support\Str;

class Toc extends BaseModel
{
    const DISABLE = 0;
    const ACTIVE = 1;

    protected $fillable = [
        'title',
        'subtitle',
        'slug',
        'course_id',
        'grade_id',
        'subject_id',
        'editor_id',
        'index',
        'status',
        'seo_title',
        'seo_description',
        'parent_id',
        'import_origin',
        'origin_id',
    ];

    /**
     * Set the title and the readable slug.
     *
     * @param string $value
     */
    public function setTitleAttribute($value)
    {
        $this->attributes['title'] = $value;

        $this->setUniqueSlug($value);
    }

    /**
     * Get the unique slug.
     *
     * @return param $extra
     */
    public function getUniqueSlug()
    {
        return $this->slug;
    }

    /**
     * Set the unique slug.
     *
     * @param $value
     * @param $extra
     */
    public function setUniqueSlug($value)
    {
        $slug = str_to_slug($value);

        $this->attributes['slug'] = $slug;
    }

    /**
     * Relation to the parent.
     *
     * @return BelongsTo
     */
    public function parent()
    {
        return $this->belongsTo(Toc::class, 'parent_id');
    }

    /**
     * Relation to children.
     *
     * @return HasMany
     */
    public function children()
    {
        return $this->hasMany(Toc::class, 'parent_id');
    }

    /**
     * Recursive relations
     */
    public function allChildren()
    {
        return $this->children()->with('allChildren');
    }

    public function course()
    {
        return $this->belongsTo(Course::class);
    }

    public function grade()
    {
        return $this->belongsTo(Grade::class);
    }

    public function subject()
    {
        return $this->belongsTo(Subject::class);
    }

    public function editor()
    {
        return $this->belongsTo(User::class, 'editor_id');
    }

    public function tableContents()
    {
        return $this->hasMany(TableContent::class);
    }
}
