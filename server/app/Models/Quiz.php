<?php

namespace App\Models;

class Quiz extends BaseModel
{
    protected $fillable = [
        'table_content_id',
        'content',
        'related_questions',
        'related_tablecontents',
        'type',
        'index',
        'parent_id',
    ];

    protected $casts = [
        'related_questions' => 'array',
        'related_tablecontents' => 'array',
    ];

    public function tableContent()
    {
        return $this->belongsTo(TableContent::class);
    }

    /**
     * Relation to the parent.
     *
     * @return BelongsTo
     */
    public function parent()
    {
        return $this->belongsTo(Quiz::class, 'parent_id');
    }

    /**
     * Relation to children.
     *
     * @return HasMany
     */
    public function children()
    {
        return $this->hasMany(Quiz::class, 'parent_id');
    }

    /**
     * Recursive relations
     */
    public function allChildren()
    {
        return $this->children()->with('allChildren');
    }
}
