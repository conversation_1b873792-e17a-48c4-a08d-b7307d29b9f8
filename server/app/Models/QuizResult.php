<?php

namespace App\Models;

class QuizResult extends BaseModel
{
    const STATUS_CREATE = 0;
    const STATUS_DOING = 1;
    const STATUS_DONE = 2;

    const TYPE_SOLO = 'solo';
    const TYPE_FLASHCARD = 'flashcard';

    protected $fillable = [
        'user_id',
        'course_id',
        'classroom_table_content_id',
        'table_content_id',
        'total_correct',
        'total_wrong',
        'not_doing',
        'total',
        'timer',
        'total_duration',
        'started_at',
        'completed_at',
        'replay',
        'status',
        'data_log',
        'type',
    ];

    protected $casts = [
        'results' => 'array',
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function course()
    {
        return $this->belongsTo(Course::class);
    }

    public function tableContent()
    {
        return $this->belongsTo(TableContent::class);
    }
}
