<?php

namespace App\Models;

use Illuminate\Support\Str;

class Subject extends BaseModel
{
    const DISABLE = 0;
    const ACTIVE = 1;

    protected $fillable = [
        'title',
        'slug',
        'banner',
        'status',
        'index',
        'editor_id',
        'seo_title',
        'seo_keywords',
        'seo_description',
    ];

    /**
     * Set the title and the readable slug.
     *
     * @param string $value
     */
    public function setTitleAttribute($value)
    {
        $this->attributes['title'] = $value;

        $this->setUniqueSlug($value);
    }

    /**
     * Set the unique slug.
     *
     * @param $value
     * @param $extra
     */
    public function setUniqueSlug($value)
    {
        $slug = Str::slug($value);

        if (static::whereSlug($slug)->where('id', '<>', $this->id)->exists()) {
            $this->setUniqueSlug($slug . '-' . Str::random(5));

            return;
        }

        $this->attributes['slug'] = $slug;
    }

    public function books()
    {
        return $this->belongsToMany(
            Book::class,
            'courses',
            'subject_id',
            'book_id'
        )->distinct('book_id');
    }

    public function grades()
    {
        return $this->belongsToMany(
            Grade::class,
            'courses',
            'subject_id',
            'grade_id'
        )->distinct('grade_id');
    }

    public function courses()
    {
        return $this->hasMany(Course::class);
    }

    public function tableContents()
    {
        return $this->hasMany(TableContent::class);
    }
}
