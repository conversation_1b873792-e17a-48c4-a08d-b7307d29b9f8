<?php

namespace App\Models;

use Illuminate\Support\Str;

class Grade extends BaseModel
{
    const DISABLE = 0;
    const ACTIVE = 1;

    const PRIMARY_SCHOOL = 1;
    const JUNIOR_SCHOOL = 2;
    const HIGH_SCHOOL = 3;

    protected $fillable = [
        'title',
        'slug',
        'banner',
        'status',
        'type',
        'index',
        'editor_id',
        'color',
        'text_icon',
        'seo_title',
        'seo_keywords',
        'seo_description',
    ];

    /**
     * Set the title and the readable slug.
     *
     * @param string $value
     */
    public function setTitleAttribute($value)
    {
        $this->attributes['title'] = $value;
        $this->setUniqueSlug($value);
    }

    /**
     * Set the unique slug.
     *
     * @param $value
     * @param $extra
     */
    public function setUniqueSlug($value)
    {
        $slug = Str::slug($value);

        if (static::whereSlug($slug)->where('id', '<>', $this->id)->exists()) {
            $this->setUniqueSlug($slug . '-' . Str::random(5));

            return;
        }

        $this->attributes['slug'] = $slug;
    }

    public static function allTypes()
    {
        return [
            self::PRIMARY_SCHOOL => 'Tiểu học',
            self::JUNIOR_SCHOOL => 'Trung học cơ sở',
            self::HIGH_SCHOOL => 'Trung học phổ thông',
        ];
    }

    public function subjects()
    {
        return $this->belongsToMany(
            Subject::class,
            'courses',
            'grade_id',
            'subject_id'
        )->distinct('subject_id');
    }

    public function courses()
    {
        return $this->hasMany(Course::class);
    }

    public function tableContents()
    {
        return $this->hasMany(TableContent::class);
    }

    public function books()
    {
        return $this->belongsToMany(
            Book::class,
            'book_grade',
            'grade_id',
            'book_id'
        )->withTimestamps();
    }

    public function questions()
    {
        return $this->hasMany(Question::class);
    }

    public function classrooms()
    {
        return $this->hasMany(Classroom::class);
    }
}
