<?php

namespace App\Models;

use Illuminate\Support\Str;

class Question extends BaseModel
{
    const DISABLE = 0;
    const ACTIVE = 1;

    const LEVEL_DEFAULT = 0; // nhận biết
    const LEVEL_NB = 1; // nhận biết
    const LEVEL_TH = 2; // thông hiểu
    const LEVEL_VD = 3; // vận dụng
    const LEVEL_VDC = 4; // vận dùng cao

    const NOT_UPDATED = 0;
    const UPDATED = 1;
    const CLONED = 2;

    const TYPE_QUIZX = 'QUIZX';
    const TYPE_BLANK = 'BLANK';
    const TYPE_DRAG_DROP = 'DRAG_DROP';
    const TYPE_DROPDOWN = 'DROPDOWN';
    const TYPE_PARAGRAPH = 'PARAGRAPH';

    protected $fillable = [
        'grade_id',
        'subject_id',
        'table_content_id',
        'editor_id',
        'title',
        'slug',
        'content',
        'explain',
        'content_json',
        'static_content',
        'index',
        'status',
        'type',
        'level',
        'view',
        'seo_title',
        'seo_keywords',
        'seo_description',
        'import_origin',
        'origin_id',
        'is_updated'
    ];

    protected $casts = [
        'content_json' => 'array',
    ];

    public static function allTypes()
    {
        return [
            self::TYPE_QUIZX => 'Trắc nghiệm',
            self::TYPE_BLANK => 'Điền từ',
            self::TYPE_DRAG_DROP => 'Kéo và thả',
            self::TYPE_DROPDOWN => 'Danh sách thả xuống',
            self::TYPE_PARAGRAPH => 'Đoạn văn thường',
        ];
    }

    public static function allLevels()
    {
        return [
            self::LEVEL_NB => 'Nhận biết',
            self::LEVEL_TH => 'Thông hiểu',
            self::LEVEL_VD => 'Vận dụng',
            self::LEVEL_VDC => 'Vận dùng cao',
        ];
    }

    /**
     * Set the title and the readable slug.
     *
     * @param string $value
     */
    public function setTitleAttribute($value)
    {
        $slug = str_to_slug($value, 100);

        $this->attributes['title'] = $value;
        $this->attributes['slug'] = $slug;
    }

    public function setContentAttribute($value)
    {
        $value = preg_replace('/[\t\r\n]/', ' ', $value);
        $value = preg_replace('/\s+/', ' ', $value);

        $this->attributes['content'] = $value;
    }

    public function setStaticContentAttribute($value)
    {
        $value = preg_replace('/[\t\r\n]/', ' ', $value);
        $value = preg_replace('/\s+/', ' ', $value);

        $this->attributes['static_content'] = $value;
    }

    public function subject()
    {
        return $this->belongsTo(Subject::class, 'subject_id');
    }

    public function grade()
    {
        return $this->belongsTo(Grade::class, 'grade_id');
    }

    public function tableContent()
    {
        return $this->belongsTo(TableContent::class, 'table_content_id');
    }
}
