<?php

namespace App\Models;

use Illuminate\Support\Str;

class TableContent extends BaseModel
{
    const DISABLE = 0;
    const ACTIVE = 1;

    const TYPE_DEFAULT = 0;
    const TYPE_EXAM_QUIZ = 1;
    const TYPE_THEORY = 2;

    const VIP = 1; // import từ khóa học

    protected $fillable = [
        'title',
        'subtitle',
        'slug',
        'description',
        'banner',
        'course_id',
        'grade_id',
        'subject_id',
        'toc_id',
        'editor_id',
        'index',
        'status',
        'type',
        'vip',
        'time',
        'view',
        'video_url',
        'document_path',
        'document_link',
        'seo_title',
        'seo_keywords',
        'seo_description',
        'parent_id',
        'import_origin',
        'origin_id',
    ];

    // public function resolveRouteBinding($value, $field = null)
    // {
    //     return $this->where('id', $value)->firstOrFail();
    // }

    /**
     * Set the title and the readable slug.
     *
     * @param string $value
     */
    public function setTitleAttribute($value)
    {
        $this->attributes['title'] = $value;

        $this->setUniqueSlug($value);
    }

    /**
     * Get the unique slug.
     *
     * @return param $extra
     */
    public function getUniqueSlug()
    {
        return $this->slug;
    }

    /**
     * Set the unique slug.
     *
     * @param $value
     * @param $extra
     */
    public function setUniqueSlug($value)
    {
        $slug = str_to_slug($value);

        $this->attributes['slug'] = $slug;
    }

    /**
     * Relation to the parent.
     *
     * @return BelongsTo
     */
    public function parent()
    {
        return $this->belongsTo(TableContent::class, 'parent_id');
    }

    /**
     * Relation to children.
     *
     * @return HasMany
     */
    public function children()
    {
        return $this->hasMany(TableContent::class, 'parent_id');
    }

    /**
     * Recursive relations
     */
    public function allChildren()
    {
        return $this->children()->with('allChildren');
    }

    public function course()
    {
        return $this->belongsTo(Course::class);
    }

    public function toc()
    {
        return $this->belongsTo(Toc::class);
    }

    public function grade()
    {
        return $this->belongsTo(Grade::class);
    }

    public function subject()
    {
        return $this->belongsTo(Subject::class);
    }

    public function editor()
    {
        return $this->belongsTo(User::class, 'editor_id');
    }

    public function quiz()
    {
        return $this->hasOne(Quiz::class);
    }

    public function questions()
    {
        return $this->hasMany(Question::class)
            ->orderBy('index');
    }

    public function quizResults()
    {
        return $this->hasMany(QuizResult::class);
    }

    public function classrooms()
    {
        return $this->belongsToMany(Classroom::class);
    }

    public function collections()
    {
        return $this->morphToMany(Collection::class, 'collectionable', 'collectionables');
    }

    public function scopeSelectColumns($query)
    {
        return $query->select([
            'table_contents.id',
            'table_contents.title',
            'table_contents.banner',
            'table_contents.slug',
            'table_contents.grade_id',
            'table_contents.subject_id',
            'table_contents.course_id',
            'table_contents.toc_id',
            'table_contents.editor_id',
            'table_contents.status',
            'table_contents.index',
            'table_contents.type',
            'table_contents.vip',
            'table_contents.view',
            'table_contents.created_at',
        ]);
    }
}
