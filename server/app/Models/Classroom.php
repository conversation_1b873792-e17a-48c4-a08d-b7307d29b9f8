<?php

namespace App\Models;

class Classroom extends BaseModel
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'title',
        'color',
        'code',
        'grade_id',
        'author_id',
    ];

    /**
     * Set the code.
     *
     * @param string $value
     */
    public function setCodeAttribute($value)
    {
        $this->setUniqueCode($value);
    }

    /**
     * Set the unique slug.
     *
     * @param $value
     * @param $extra
     */
    public function setUniqueCode($value)
    {
        if (static::where('code', $value)->where('id', '<>', $this->id)->exists()) {
            $this->setUniqueCode(
                rand(
                    config('web.code.min'),
                    config('web.code.max')
                )
            );

            return;
        }

        $this->attributes['code'] = $value;
    }

    public function users()
    {
        return $this->belongsToMany(
            User::class,
            'classroom_user',
            'classroom_id',
            'user_id'
        )->withTimestamps();
    }

    public function tableContents()
    {
        return $this->belongsToMany(
            TableContent::class,
            'classroom_table_content',
            'classroom_id',
            'table_content_id'
        )
        ->withPivot(['start_time', 'end_time'])
        ->withTimestamps();
    }

    public function author()
    {
        return $this->belongsTo(User::class, 'author_id');
    }

    public function classroomUsers()
    {
        return $this->hasMany(ClassroomUser::class, 'classroom_id');
    }

    public function classroomTableContents()
    {
        return $this->hasMany(ClassroomTableContent::class, 'classroom_id');
    }

    public function grade()
    {
        return $this->belongsTo(Grade::class, 'grade_id');
    }
}
