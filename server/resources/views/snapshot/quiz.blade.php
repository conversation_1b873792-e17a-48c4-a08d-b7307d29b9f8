<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        body {
            font-family: Arial, sans-serif;
            color: #333;
            background-color: #fff;
            margin: 0;
            padding: 20px;
        }
        .print-header {
            border: 1px solid #999;
            padding: 15px;
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }
        .header-left {
            display: flex;
            flex-direction: column;
            max-width: 50%;
        }
        .logo-container {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
        }
        .logo-svg {
            height: 22px;
            width: 82px;
        }
        .logo-title {
            font-size: 16px;
            font-weight: bold;
            margin-top: 5px;
        }
        .logo-subtitle {
            font-size: 14px;
            margin-top: 2px;
        }
        .header-right {
            width: 45%;
        }
        .info-line {
            display: flex;
            margin-bottom: 10px;
        }
        .info-label {
            width: 70px;
            font-weight: bold;
            font-size: 12px;
        }
        .info-value {
            flex: 1;
            border-bottom: 1px solid #ccc;
            font-size: 12px;
        }
        .questions-container {
            padding: 0;
            margin-left: 30px;
        }
        .question-item {
            margin-bottom: 25px;
            position: relative;
            padding-left: 20px;
        }
        .question-number {
            position: absolute;
            left: -20px;
            top: 0;
        }
        .question-content {
            display: inline;
        }
        .options-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-top: 10px;
        }
        @media (max-width: 600px) {
            .options-container {
                grid-template-columns: 1fr;
            }
        }
        .option-item {
            display: flex;
            align-items: center;
            margin: 5px 0;
        }
        .option-box {
            width: 25px;
            height: 25px;
            border: 1px solid #ccc;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 10px;
            color: #999;
            font-weight: normal;
        }
        .box-content {
            flex: 1;
        }
        img {
            max-width: 100%;
            height: auto;
        }
    </style>
</head>
<body>
    <div class="print-header">
        <div class="header-left">
            <div class="logo-container">
                <svg
                  class="logo-svg"
                  width="82"
                  height="22"
                  viewBox="0 0 126 34"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fillRule="evenodd"
                    clipRule="evenodd"
                    d="M27.6508 4.75797C21.3328 -1.56486 11.1017 -1.5853 4.75615 4.69297L4.75454 4.69136C4.7484 4.69751 4.74253 4.7038 4.73667 4.71007C4.7318 4.71528 4.72695 4.72048 4.72195 4.72558C4.71703 4.7304 4.71199 4.73505 4.70694 4.73969L4.70693 4.7397L4.70692 4.73971C4.70043 4.74568 4.69395 4.75164 4.68776 4.75797L4.68936 4.7598C-1.58412 11.1102 -1.5637 21.3493 4.75454 27.6719C9.58881 32.5101 16.7115 33.6582 22.6333 31.1201C23.0017 30.9625 23.318 30.7429 23.5794 30.4815C24.7088 29.3513 24.798 27.43 23.5727 26.2038C22.6949 25.3253 21.3674 25.0361 20.2249 25.5232C16.5265 27.1037 12.0782 26.3841 9.0598 23.3633C5.10798 19.4085 5.09949 13.0044 9.02721 9.03413C12.9944 5.10341 19.3937 5.1119 23.3455 9.06674C26.3639 12.0875 27.083 16.5391 25.5038 20.2404C25.017 21.3835 25.3059 22.7122 26.1838 23.591C27.4091 24.817 29.3289 24.7276 30.4583 23.5974C30.7197 23.3358 30.9386 23.0193 31.0963 22.6506C33.6325 16.7242 32.4853 9.59615 27.6508 4.75797ZM27.6612 32.9161C29.1052 34.3613 31.4469 34.3613 32.8909 32.9161C34.3349 31.4708 34.3349 29.1276 32.8909 27.6825C31.4469 26.2371 29.1052 26.2371 27.6612 27.6825C26.2169 29.1276 26.2169 31.4708 27.6612 32.9161ZM34.5938 8.87168C34.5938 7.22672 35.596 6.02344 37.2395 6.02344C38.8029 6.02344 39.9653 7.26692 39.9653 8.87168V19.1016C39.9653 21.4686 41.4486 22.8327 43.8136 22.8327C45.2969 22.8327 46.6198 22.4714 47.7423 21.7897V8.7913C47.7423 7.22673 48.8646 6.02344 50.4282 6.02344C51.9115 6.02344 53.1138 7.22673 53.1138 8.7913V23.7953C53.1138 24.638 52.7932 25.2795 52.1517 25.681C50.0272 27.2053 47.1809 27.9676 43.6934 27.9676C37.9209 27.9676 34.5938 24.7983 34.5938 19.182V8.87168ZM60.6683 9.12417V24.933C60.6683 26.4515 59.4659 27.6194 57.9826 27.6194C56.5395 27.6194 55.2967 26.3735 55.2967 24.933V9.12417C55.2967 7.60565 56.419 6.4375 57.9826 6.4375C59.4659 6.4375 60.6683 7.60565 60.6683 9.12417ZM77.3718 22.6682H69.5152L78.9356 11.9569C79.9376 10.8338 80.1784 9.67024 79.7373 8.42653C79.2163 7.10266 78.2138 6.46094 76.7306 6.46094H65.8273C64.344 6.46094 63.2213 7.50391 63.2213 8.94836C63.2213 10.4325 64.344 11.3951 65.8273 11.3951H73.243L63.6223 22.3476C63.021 23.0291 62.7403 23.7916 62.7403 24.6343C62.7403 26.3991 64.1035 27.6429 66.0678 27.6429H77.3718C78.8551 27.6429 80.018 26.5999 80.018 25.1557C80.018 23.671 78.8551 22.6682 77.3718 22.6682ZM87.4565 9.12417V24.933C87.4565 26.4515 86.2541 27.6194 84.7708 27.6194C83.3277 27.6194 82.0849 26.3735 82.0849 24.933V9.12417C82.0849 7.60565 83.207 6.4375 84.7708 6.4375C86.2541 6.4375 87.4565 7.60565 87.4565 9.12417ZM104.01 22.6682H96.1531L105.574 11.9569C106.575 10.8338 106.816 9.67024 106.375 8.42653C105.854 7.10266 104.852 6.46094 103.369 6.46094H92.4652C90.9819 6.46094 89.8592 7.50391 89.8592 8.94836C89.8592 10.4325 90.9819 11.3951 92.4652 11.3951H99.881L90.2602 22.3476C89.6589 23.0291 89.3782 23.7916 89.3782 24.6343C89.3782 26.3991 90.7414 27.6429 92.7058 27.6429H104.01C105.493 27.6429 106.656 26.5999 106.656 25.1557C106.656 23.671 105.493 22.6682 104.01 22.6682ZM115.496 22.6682H123.353C124.836 22.6682 125.999 23.671 125.999 25.1557C125.999 26.5999 124.836 27.6429 123.353 27.6429H112.048C110.084 27.6429 108.721 26.3991 108.721 24.6343C108.721 23.7916 109.002 23.0291 109.603 22.3476L119.224 11.3951H111.808C110.325 11.3951 109.202 10.4325 109.202 8.94836C109.202 7.50391 110.325 6.46094 111.808 6.46094H122.711C124.195 6.46094 125.197 7.10266 125.718 8.42653C126.159 9.67024 125.918 10.8338 124.916 11.9569L115.496 22.6682Z"
                    fill="#5D2057"
                  />
                </svg>
            </div>
            <div class="logo-title">{{ $tableContent->title }}</div>
        </div>
        <div class="header-right">
            <div class="info-line">
                <div class="info-label">LỚP :</div>
                <div class="info-value">{{ $tableContent->grade ? $tableContent->grade->title : '' }}</div>
            </div>
            <div class="info-line">
                <div class="info-label">MÔN :</div>
                <div class="info-value">{{ $tableContent->subject ? $tableContent->subject->title : '' }}</div>
            </div>
            <div class="info-line">
                <div class="info-label">SỐ CÂU :</div>
                <div class="info-value">{{ $tableContent->questions_count }}</div>
            </div>
        </div>
    </div>

    <div class="questions-container">
        @foreach($tableContent->questions as $index => $question)
            @if($index >= 10) @break @endif
            <div class="question-item">
                <span class="question-number">{{ $index + 1 }}.</span>
                <div class="question-content">{!! $questionContents[$index] !!}</div>

                @if(!empty($question->content_json) && !empty($question->content_json['options']))
                    <div class="options-container">
                        @foreach($question->content_json['options'] as $optIndex => $option)
                            <div class="option-item">
                                <div class="option-box">{{ chr(65 + $optIndex) }}</div>
                                <div class="box-content">{!! $option['content'] !!}</div>
                            </div>
                        @endforeach
                    </div>
                @endif
            </div>
        @endforeach
    </div>
</body>
</html>
