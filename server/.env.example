APP_NAME=Laravel
APP_ENV=local
APP_KEY=
APP_DEBUG=true
DEBUG_LOG_QUERIES=false
APP_TIMEZONE=UTC

APP_URL=http://localhost:8000
# trong TH dùng docker thì FRONTEND_INTERNAL_URL phải dựa vào service_name của container (ở đây là http://frontend:3000). Còn không dùng docker thì 2 url này là 1
FRONTEND_URL=http://localhost:3000
FRONTEND_INTERNAL_URL=http://localhost:3000

SANCTUM_STATEFUL_DOMAINS=localhost:3000
# COOKIE_NAME bắt buộc không được nằm trong dấu ""
USER_INFO_COOKIE_NAME=_u_info_

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
# APP_MAINTENANCE_STORE=database

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=laravel
DB_USERNAME=root
DB_PASSWORD=

DB_CONNECTION_KHOAHOC=mysql
DB_HOST_KHOAHOC=127.0.0.1
DB_PORT_KHOAHOC=3306
DB_DATABASE_KHOAHOC=laravel
DB_USERNAME_KHOAHOC=root
DB_PASSWORD_KHOAHOC=
DB_PREFIX_KHOAHOC=vjc_

SESSION_DRIVER=cookie
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=.localhost

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database

CACHE_STORE=database
CACHE_PREFIX=

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=log
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

VITE_APP_NAME="${APP_NAME}"
VITE_APP_URL="${APP_URL}"

GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=
GOOGLE_REDIRECT_URL=

CHROMIUM_BINARIES='/usr/bin/chromium' # OR '/usr/bin/chromium-browser'

TOOL_API_DOMAIN=
UPLOAD_API_DOMAIN="${APP_URL}"
