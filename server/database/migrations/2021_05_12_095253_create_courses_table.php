<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCoursesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('courses', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->string('subtitle')->nullable();
            $table->string('slug')->index();
            $table->text('description')->nullable();
            $table->string('banner')->nullable();
            $table->bigInteger('grade_id')->unsigned()->nullable()->index();
            $table->bigInteger('subject_id')->unsigned()->nullable()->index();
            $table->bigInteger('book_id')->unsigned()->nullable()->index();
            $table->bigInteger('editor_id')->unsigned()->nullable();
            $table->integer('index')->default(0);
            $table->tinyInteger('status')->default(0);
            $table->unsignedBigInteger('view')->default(0);
            $table->string('seo_title');
            $table->string('seo_keywords')->nullable();
            $table->string('seo_description', 1000)->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('courses');
    }
}
