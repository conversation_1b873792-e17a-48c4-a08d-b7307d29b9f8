<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddCodeColumnInClassroomTableContentTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('classroom_table_content', function (Blueprint $table) {
            $table->unsignedMediumInteger('code')->unique()->after('table_content_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('classroom_table_content', function (Blueprint $table) {
            $table->dropColumn(['code']);
        });
    }
}
