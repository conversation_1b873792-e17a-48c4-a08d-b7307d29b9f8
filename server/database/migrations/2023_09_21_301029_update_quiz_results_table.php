<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateQuizResultsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('quiz_results', function (Blueprint $table) {
            $table->dropColumn('results');
            $table->integer('total_correct')->unsigned()->default(0)->after('table_content_id');
            $table->integer('total_wrong')->unsigned()->default(0)->after('total_correct');
            $table->integer('not_doing')->unsigned()->default(0)->after('total_wrong');
            $table->integer('total')->unsigned()->default(0)->after('not_doing');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
