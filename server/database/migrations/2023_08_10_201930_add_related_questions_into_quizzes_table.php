<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddRelatedQuestionsIntoQuizzesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('quizzes', function (Blueprint $table) {
            $table->json('related_questions')->nullable()->after('content');
            $table->integer('index')->default(0)->after('type');
            $table->bigInteger('parent_id')->unsigned()->nullable()->after('index');
            $table->foreign('parent_id')->references('id')->on('quizzes')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('quizzes', function (Blueprint $table) {
            $table->dropColumn('related_questions', 'index');
            $table->dropColumn(['parent_id']);
        });
    }
}
