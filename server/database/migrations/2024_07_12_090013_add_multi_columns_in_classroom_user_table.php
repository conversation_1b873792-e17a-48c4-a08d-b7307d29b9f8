<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddMultiColumnsInClassroomUserTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('classroom_user', function (Blueprint $table) {
            $table->string('name')->nullable()->after('user_id');
            $table->string('ident_number')->nullable()->after('role');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('classroom_user', function (Blueprint $table) {
            $table->dropColumn(['name', 'ident_number']);
        });
    }
}
