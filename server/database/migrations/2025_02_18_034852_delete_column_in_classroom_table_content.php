<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('classroom_table_content', function (Blueprint $table) {
            $table->dropColumn(['subtitle', 'description']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('classroom_table_content', function (Blueprint $table) {
            $table->text('description')->nullable()->after('table_content_id');
            $table->string('subtitle')->nullable()->after('table_content_id');
        });
    }
};
