<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddSomeColIntoSomeTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('questions', function (Blueprint $table) {
            $table->string('import_origin')->nullable()->after('seo_description');
            $table->unsignedBigInteger('origin_id')->nullable()->after('import_origin');
        });

        Schema::table('table_contents', function (Blueprint $table) {
            $table->string('import_origin')->nullable()->after('parent_id');
            $table->unsignedBigInteger('origin_id')->nullable()->after('import_origin');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('questions', function (Blueprint $table) {
            $table->dropColumn('import_origin', 'origin_id');
        });

        Schema::table('table_contents', function (Blueprint $table) {
            $table->dropColumn('import_origin', 'origin_id');
        });
    }
}
