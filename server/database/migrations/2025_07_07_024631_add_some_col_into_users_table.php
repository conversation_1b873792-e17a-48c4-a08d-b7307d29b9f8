<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->integer('age')->nullable()->after('avatar');

            // Add new JSON fields for multi-selection
            $table->json('preferred_subjects')->nullable()->after('age');
            $table->json('preferred_grades')->nullable()->after('preferred_subjects');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn(['age', 'preferred_subjects', 'preferred_grades']);
        });
    }
};
