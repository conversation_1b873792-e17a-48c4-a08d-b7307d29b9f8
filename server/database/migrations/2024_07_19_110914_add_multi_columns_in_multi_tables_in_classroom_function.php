<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddMultiColumnsInMultiTablesInClassroomFunction extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('quiz_results', function (Blueprint $table) {
            $table->unsignedBigInteger('classroom_quiz_id')->nullable()->index()->after('course_id');
        });

        Schema::table('classroom_quiz', function (Blueprint $table) {
            $table->tinyInteger('show_answer')->default(0)->after('quiz_id');
            $table->text('description')->nullable()->after('quiz_id');
            $table->string('subtitle')->nullable()->after('quiz_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('quiz_results', function (Blueprint $table) {
            $table->dropColumn('classroom_quiz_id');
        });

        Schema::table('classroom_quiz', function (Blueprint $table) {
            $table->dropColumn(['subtitle', 'description', 'show_answer']);
        });
    }
}
