<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('classroom_table_content', function (Blueprint $table) {
            $table->renameColumn('quiz_id', 'table_content_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('classroom_table_content', function (Blueprint $table) {
            $table->renameColumn('table_content_id', 'quiz_id');
        });
    }
};
