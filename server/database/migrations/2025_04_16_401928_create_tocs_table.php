<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTocsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('tocs', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('title');
            $table->string('subtitle')->nullable();
            $table->string('slug')->index();
            $table->bigInteger('course_id')->unsigned()->nullable()->index();
            $table->bigInteger('grade_id')->unsigned()->nullable()->index();
            $table->bigInteger('subject_id')->unsigned()->nullable()->index();
            $table->bigInteger('editor_id')->unsigned()->nullable()->index();
            $table->integer('index')->default(0);
            $table->unsignedTinyInteger('status')->default(0);
            $table->string('seo_title')->nullable();
            $table->string('seo_description', 500)->nullable();
            $table->bigInteger('parent_id')->unsigned()->nullable();
            $table->foreign('parent_id')->references('id')->on('tocs')->onDelete('set null');
            $table->string('import_origin')->nullable();
            $table->unsignedBigInteger('origin_id')->nullable();
            $table->timestamps();
        });

        Schema::table('table_contents', function (Blueprint $table) {
            $table->bigInteger('toc_id')->unsigned()->nullable()->index()->after('subject_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('tocs');
    }
}
