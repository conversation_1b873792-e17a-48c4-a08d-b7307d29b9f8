<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddTimingFieldsToQuizResultsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('quiz_results', function (Blueprint $table) {
            $table->integer('total_duration')->unsigned()->nullable()->after('timer')->comment('Total quiz duration in seconds');
            $table->timestamp('started_at')->nullable()->after('total_duration')->comment('When quiz actually started');
            $table->timestamp('completed_at')->nullable()->after('started_at')->comment('When quiz was completed');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('quiz_results', function (Blueprint $table) {
            $table->dropColumn(['total_duration', 'started_at', 'completed_at']);
        });
    }
}
