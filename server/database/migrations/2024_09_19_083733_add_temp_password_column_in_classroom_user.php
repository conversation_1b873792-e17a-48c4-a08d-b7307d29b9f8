<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('classroom_user', function (Blueprint $table) {
            $table->string('temp_password')->nullable()->after('ident_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('classroom_user', function (Blueprint $table) {
            $table->dropColumn('temp_password');
        });
    }
};
