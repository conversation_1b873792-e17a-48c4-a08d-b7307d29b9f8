<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddSomeColIntoTableContentsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('table_contents', function (Blueprint $table) {
            $table->string('document_path')->nullable()->after('video_url');
            $table->string('document_link')->nullable()->after('document_path');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('table_contents', function (Blueprint $table) {
            $table->dropColumn('document_path', 'document_link');
        });
    }
}
