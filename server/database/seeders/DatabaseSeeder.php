<?php

namespace Database\Seeders;

use App\Models\Role;
use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // User::factory(10)->create();
        
        $role_admin = Role::firstOrCreate(['name' => Role::ROLE_ADMIN]);
        Role::firstOrCreate(['name' => Role::ROLE_EDITOR]);
        Role::firstOrCreate(['name' => Role::ROLE_TEACHER]);
        Role::firstOrCreate(['name' => Role::ROLE_STUDENT]);

        // Users
        $user = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Admin',
                'password' => Hash::make('admin'),
            ]
        );

        $user->roles()->sync([$role_admin->id]);
    }
}
