<?php

return [
    'image' => [
        'storage_disk' => env('IMAGE_STORAGE_DISK', 'public'),
        'base_folder' => 'uploads/images/',
        'tmp_folder' => 'uploads/tmp/images/',
        'max_size' => 10000, // KB
        'types' => 'jpg,png,jpeg,svg,gif',
        'sizes' => [
            'avatar' => [
                [40, 40], // [width, height]
            ],
            'banner' => [
                [200, 125],
            ],
        ],
    ],

    'file' => [
        'storage_disk' => env('FILE_STORAGE_DISK', 'public'),
        'base_folder' => 'uploads/',
        'max_size' => 1000000, // KB
        'types' => 'pdf,doc,docx,xls,xlsx,ppt,pptx,zip,rar',
        'types_of_input' => '.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.zip,.rar',
        'media_types' => 'pdf,doc,docx,xls,xlsx,ppt,pptx,zip,rar, mp4,mov,webm,ogg,oga,ogx,qt,x-flv,x-mpegURL,MP2T,3gpp,quicktime,x-msvideo,x-ms-wmv,mpga,mp3,wav,bin,application/octet-stream',
    ],

    'file_import' => [
        'types' => 'pdf,doc,docx,xls,xlsx',
        'types_of_input' => '.pdf,.doc,.docx,.xls,.xlsx',
        'max_size' => 10000, // KB
    ]
];
