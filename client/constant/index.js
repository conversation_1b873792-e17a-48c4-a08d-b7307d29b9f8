export const siteMetadata = {
  title: process.env.SITE_TITLE || '<PERSON>hi online cùng VietJack với kho đề thi đầy đủ các môn',
  description: process.env.SITE_DESCRIPTION || '<PERSON><PERSON><PERSON><PERSON> thi trắc nghiệ<PERSON> online, đề thi học k<PERSON>, đề thi thử các m<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> các lớp tiểu học, THCS, THPT',
  siteName: process.env.SITE_NAME || 'vietjack.com',
  siteUrl: process.env.NEXT_PUBLIC_HOST_URL || 'http://localhost:3000',
  author: process.env.SITE_AUTHOR || 'VietJack',
};

export const userCookieName = process.env.USER_INFO_COOKIE_NAME;
export const keyStorageUser = 'vj-user';
export const keyStorageIsLoginGoogle = 'is_login_google';
export const keyCustomization = 'vjtheme-config';

export const defaultCustomization = {
  isOpen: [], // for active default menu
  defaultId: 'default',
  fontFamily: `'Roboto', sans-serif`,
  borderRadius: 8,
  borderCard: '1px solid #09090933',
  opened: true,
  mode: 'light', // dark
  presetColor: 'red',
  outlinedFilled: true,
};

export const gridSpacing = 3;
export const drawerWidth = 260;
export const appDrawerWidth = 320;
export const perPage = 7;
export const mcqPerOption = 5;
export const mcpMinOption = 2;

export const orderOptions = [
  { value: "latest", label: "Mới nhất" },
  { value: "oldest", label: "Cũ nhất" },
  { value: "alphabetical", label: "Bảng chữ cái" },
];

export const statusOptions = [
  { value: 0, label: "Chưa bắt đầu", 'className': 'info' },
  { value: 1, label: "Đang diễn ra", 'className': 'orange' },
  { value: 2, label: "Đã kết thúc", 'className': 'success' },
];

export const colorPickerOptions = [
  { value: "#f44336", label: "#f44336" },
  { value: "#3f51b5", label: "#3f51b5" },
  { value: "#4cae50", label: "#4cae50" },
  { value: "#ffc108", label: "#ffc108" },
  { value: "#9e9e9e", label: "#9e9e9e" },
  { value: "#1e272e", label: "#1e272e" },
];

export const optionBackgrounds = [
  "#2f6dae",
  "#2c9ca6",
  "#eca82c",
  "#d4546a",
  "#9a4292",
];

export const optionBorderColors = [
  "#2d70ae",
  "#2d9da6",
  "#efa929",
  "#d5546d",
  "#9a4292",
];

export const gameNotes = {
  BLANK: "Có thể tùy ý sử dụng các lựa chọn không chính xác hoặc các yếu tố gây nhiễu để tăng độ phức tạp cho câu hỏi.",
};

export const THEMES = [
  { key: 'classic', name: 'Classic', icon: '🏛️' },
  { key: 'synthwave', name: 'Synthwave', icon: '🌊' },
  { key: 'anime', name: 'Anime', icon: '🐾' },
  { key: 'cosmic-picnic', name: 'Cosmic Picnic', icon: '🌌' },
  { key: 'dogsville', name: 'Dogsville', icon: '🐕' },
  { key: 'football', name: 'Football', icon: '⚽' },
  { key: 'testmode', name: 'Test Mode', icon: '📝' },
  { key: 'the8Bit', name: '8-Bit', icon: '🎮' },
];

export const pointsOptions = [
  { value: 1, label: "1 điểm" },
  { value: 2, label: "2 điểm" },
  { value: 3, label: "3 điểm" },
  { value: 4, label: "4 điểm" },
  { value: 5, label: "5 điểm" },
  { value: 6, label: "6 điểm" },
  { value: 7, label: "7 điểm" },
  { value: 8, label: "8 điểm" },
  { value: 9, label: "9 điểm" },
  { value: 10, label: "10 điểm" },
  { value: 15, label: "15 điểm" },
  { value: 20, label: "20 điểm" },
  { value: 25, label: "25 điểm" },
  { value: 30, label: "30 điểm" },
  { value: 40, label: "40 điểm" },
  { value: 50, label: "50 điểm" },
  { value: 100, label: "100 điểm" },
];

export const timeOptions = [
  { value: 5, label: "5s" },
  { value: 10, label: "10s" },
  { value: 15, label: "15s" },
  { value: 20, label: "20s" },
  { value: 30, label: "30s" },
  { value: 45, label: "45s" },
  { value: 60, label: "1 phút" },
  { value: 90, label: "1.5 phút" },
  { value: 120, label: "2 phút" },
  { value: 180, label: "3 phút" },
  { value: 300, label: "5 phút" },
  { value: 600, label: "10 phút" },
  { value: 900, label: "15 phút" },
  { value: 1800, label: "30 phút" },
  { value: 0, label: "Không giới hạn" },
];

export const questionTypes = {
  QUIZX: {
    icon: '<i class="bi bi-check-circle-fill"></i>',
    initQue: {
      content: "",
      options: [
        {
          content: "",
          isCorrect: false,
        },
        {
          content: "",
          isCorrect: false,
        },
        {
          content: "",
          isCorrect: false,
        },
        {
          content: "",
          isCorrect: false,
        },
      ],
      answer: [],
      points: 1,
      timeLimit: 0,
    },
  },
  BLANK: {
    icon: '<i class="bi bi-dash-square-fill"></i>',
    initQue: {
      content: "",
      options: [],
      answer: []
      // content: '<p>1 + <span id="1" class="flag-el">[ __ ]</span> > 2 * <span id="2" class="flag-el">[ __ ]</span></p>',
      // options: [
      //     {
      //         id: 1,
      //         content: '9',
      //     },
      //     {
      //         id: 2,
      //         content: '1',
      //     },
      //     {
      //         id: 3,
      //         content: '2',
      //     },
      // ],
      // answer: [
      //     {
      //         targetId: 1,
      //         optionId: [1]
      //     },
      //     {
      //         targetId: 2,
      //         optionId: [2, 3]
      //     }
      // ]
    },
  },
  DRAG_DROP: {
    icon: '<i class="bi bi-arrows-move"></i>',
    initQue: {
      content: "",
      options: [],
      answer: []
    },
  },
  DROPDOWN: {
    icon: '<i class="bi bi-list-ul"></i>',
    initQue: {
      content: "",
      options: [],
      answer: []
    },
  },
};

export const roleMessages = {
  admin: 'Chào mừng Quản trị viên! Bạn có quyền truy cập đầy đủ vào hệ thống.',
  editor: 'Chào mừng Biên tập viên! Hãy bắt đầu tạo nội dung chất lượng.',
  teacher: 'Chào mừng bạn đến với 2048.vn! Hãy bắt đầu tạo nội dung giáo dục.',
  student: 'Chào mừng bạn đến với 2048.vn! Hãy bắt đầu học tập ngay.',
  trainer: 'Chào mừng Trainer! Hãy bắt đầu tạo các khóa đào tạo chuyên nghiệp.'
};
