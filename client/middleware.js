// import createMiddleware from 'next-intl/middleware';
// import { routing } from './i18n/routing';
import { NextResponse } from "next/server";

import { userCookieName } from '@/constant';

// https://github.com/amannn/next-intl/blob/main/examples/example-app-router-next-auth/src/middleware.ts
// const intlMiddleware = createMiddleware(routing);

const authRoutes = ["/login", "/register"];

// Các route cần xác thực auth
const privateRoutes = [
  "/dashboard",
];

export default async function middleware(request) {
  const { pathname } = request.nextUrl;

  if (
    request.method !== "GET" ||
    /\.(png|jpg|jpeg|gif|svg|css|js)$/.test(pathname)
  ) {
    return NextResponse.next();
  }

  const isAuthenticated = Boolean(request.cookies.get(userCookieName)?.value);
  const isAuthRoute = authRoutes.includes(pathname);
  const isPrivateRoute = privateRoutes.some(
    (route) => pathname === route || pathname.startsWith(`${route}/`)
  );

  if (!isAuthenticated && isPrivateRoute) {
    try {
      const res = await fetch(`${process.env.BACKEND_URL}/api/user`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Cookie': request.cookies.toString(),
          'X-XSRF-TOKEN': request.cookies.get('XSRF-TOKEN')?.value || '',
          'Origin': process.env.NEXT_PUBLIC_HOST_URL || 'http://localhost:3000',
        },
      });

      if (res.ok) {
        const { data } = await res.json();

        if (data) {
          const setCookie = res.headers.get('set-cookie');

          if (setCookie && setCookie.includes(`${userCookieName}=`)) {
            // Set-cookie vào response và thực hiện redirect lại để cookie mới được gửi trong request tiếp theo
            const response = NextResponse.redirect(request.nextUrl);

            response.headers.set("Set-Cookie", setCookie);

            return response;
          }
        }
      }
    } catch (err) {
      console.error('Auth middleware error:', err);
    }

    const loginUrl = new URL("/login", request.url);
    // loginUrl.searchParams.set("action", "0");
    loginUrl.searchParams.set("backUrl", request.nextUrl.pathname + request.nextUrl.search);

    return NextResponse.redirect(loginUrl);
  }

  if (isAuthenticated && isAuthRoute) {
    return NextResponse.redirect(new URL("/dashboard", request.url));
  }

  // return intlMiddleware(request); // khi làm url dạng i18n /vi/...
  return NextResponse.next();
}

export const config = {
  matcher: [
    // Loại trừ các đường dẫn không cần internationalization (API, static, assets, …)
    "/((?!api|_next/static|_next/image|favicon.ico|search.json|assets).*)",
  ],
};
