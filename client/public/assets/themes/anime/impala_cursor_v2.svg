<svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="none" viewBox="0 0 30 30">
  <g clip-path="url(#a)">
    <mask id="b" width="23" height="22" x="4.016" y="4.394" fill="#000" maskUnits="userSpaceOnUse">
      <path fill="#fff" d="M4.016 4.394h23v22h-23z"/>
      <path fill-rule="evenodd" d="m15.4 17.904-7.524-.655-2.51-.557a.286.286 0 0 0-.277.47l2.412 2.712 6.296.37.033.036 4.448 4.448a3.75 3.75 0 0 0 5.303 0L25.1 23.21a3.75 3.75 0 0 0 0-5.303l-4.448-4.448a.574.574 0 0 0-.04-.037L19.5 7.5l-2.628-2.044a.286.286 0 0 0-.448.317l.826 2.477.355 7.45-.076.076-6.293-4.571c-.265-.193-.593.137-.4.4l4.597 6.266-.033.034Z" clip-rule="evenodd"/>
    </mask>
    <path fill="#B0FFE4" d="m15.4 17.904-.046.523.244.02.173-.172-.371-.371Zm-7.524-.655-.114.513.034.008.034.003.046-.524Zm-2.51-.557-.114.512.113-.512Zm-.277.47.393-.35-.393.35Zm2.412 2.712-.393.35.145.162.217.012.03-.524Zm6.296.37.401-.339-.145-.172-.225-.013-.03.524Zm.033.036-.371.37.37-.37Zm4.448 4.448.371-.371-.37.37Zm5.303 0 .372.371-.372-.371ZM25.1 23.21l-.371-.37.371.37Zm0-5.303.371-.371-.371.371Zm-4.448-4.448-.372.371.372-.371Zm-.04-.037-.517.097.035.187.147.12.334-.404ZM19.5 7.5l.516-.096-.037-.196-.157-.122-.322.414Zm-2.628-2.044.322-.414-.322.414Zm-.448.317.498-.166-.498.166Zm.826 2.477.524-.025-.003-.072-.023-.069-.498.166Zm.355 7.45.37.37.165-.163-.011-.232-.524.025Zm-.076.076-.309.425.363.263.317-.317-.371-.371Zm-6.293-4.571-.309.424.309-.424Zm-.4.4-.423.311.423-.31Zm4.597 6.266.372.371.318-.318-.267-.364-.423.311Zm.012-.49-7.524-.654-.09 1.047 7.523.654.091-1.046Zm-7.456-.643-2.51-.558-.227 1.025 2.51.558.227-1.025Zm-2.51-.558c-.768-.17-1.305.743-.782 1.331l.785-.697a.239.239 0 0 1-.23.391l.227-1.025Zm-.782 1.331 2.411 2.713.785-.697-2.411-2.713-.785.697Zm2.773 2.888 6.297.37.061-1.048-6.297-.37-.061 1.048Zm5.927.186c.02.023.04.045.062.067l.742-.743a.083.083 0 0 1-.003-.003l-.801.679Zm.062.067 4.448 4.448.742-.742-4.448-4.449-.742.743Zm4.448 4.448a4.275 4.275 0 0 0 6.046 0l-.743-.742a3.225 3.225 0 0 1-4.56 0l-.743.742Zm6.046 0 1.517-1.518-.742-.742-1.518 1.518.743.742Zm1.517-1.518a4.275 4.275 0 0 0 0-6.045l-.742.742a3.225 3.225 0 0 1 0 4.561l.742.742Zm0-6.045-4.448-4.449-.743.743 4.449 4.448.742-.742Zm-4.448-4.449a1.124 1.124 0 0 0-.077-.07l-.67.81c.002 0 .003.002.004.003l.743-.742Zm.104.238-1.11-5.922-1.032.193 1.11 5.923 1.032-.194Zm-1.304-6.24-2.628-2.044-.645.829 2.629 2.044.644-.829Zm-2.628-2.044c-.634-.493-1.522.135-1.268.897l.996-.332a.239.239 0 0 1-.373.264l.645-.83Zm-1.268.897.826 2.477.996-.332-.826-2.477-.996.332Zm.8 2.336.354 7.45 1.049-.05-.355-7.45-1.049.05Zm.507 7.053-.076.076.743.743.076-.076-.743-.743Zm.604.023-6.293-4.571-.617.85 6.293 4.57.617-.849Zm-6.293-4.571c-.75-.544-1.679.39-1.13 1.136l.846-.62a.247.247 0 0 1-.024.309.247.247 0 0 1-.31.025l.618-.85Zm-1.13 1.136 4.596 6.266.847-.622-4.597-6.265-.847.621Zm4.648 5.584-.034.034.743.742.034-.034-.743-.742Z" mask="url(#b)"/>
    <g filter="url(#c)">
      <path fill="#4DFFBC" d="M5.028 15.28 3.84 5.838a1.567 1.567 0 0 1 1.817-1.74L15.8 5.827c.29.05.52.27.58.557l2.792 13.127L6.24 16.613a1.567 1.567 0 0 1-1.211-1.334Z"/>
    </g>
    <path fill="url(#d)" d="M5.028 15.28 3.84 5.838a1.567 1.567 0 0 1 1.817-1.74L15.8 5.827c.29.05.52.27.58.557l2.792 13.127L6.24 16.613a1.567 1.567 0 0 1-1.211-1.334Z"/>
    <path fill="#00704A" fill-opacity=".5" d="m5.98 4.154 9.82 1.673c.29.05.52.27.58.557l2.792 13.127L5.291 5.477c-.536-.542-.062-1.451.688-1.323Z"/>
    <path fill="url(#e)" fill-opacity=".56" d="m19.174 19.51-12.86-2.847a.716.716 0 0 1-.336-1.22l9.215-8.675a.716.716 0 0 1 1.19.363l2.79 12.38Z"/>
    <path stroke="#00BB7B" stroke-width="1.5" d="m5.772 15.186-1.187-9.44a.817.817 0 0 1 .947-.908l10.12 1.725 2.543 11.96-11.792-2.642a.817.817 0 0 1-.631-.695Z"/>
    <path stroke="#B0FFE4" stroke-width=".525" d="m19.118 19.768.4.09-.086-.4L16.64 6.33a.979.979 0 0 0-.793-.761L5.705 3.84a1.83 1.83 0 0 0-2.122 2.032l1.187 9.44a1.83 1.83 0 0 0 1.415 1.557l12.933 2.898Z"/>
    <path fill="#032B1D" d="m17.726 15.92-2.148 2.148-4.742-6.462c-.193-.264.135-.593.4-.401l6.49 4.715Z"/>
    <path fill="#032B1D" d="M13.875 20.25 16.5 18l-8.625-.75-2.51-.558a.286.286 0 0 0-.276.47L7.5 19.875l6.375.375Zm6.75-6.75-3 2.624-.375-7.875-.826-2.477a.287.287 0 0 1 .448-.317L19.5 7.5l1.125 6Z"/>
    <path fill="url(#f)" stroke="#003221" stroke-width="1.432" d="m18.785 24.222-4.345-4.345 5.808-5.808 4.345 4.345a3.034 3.034 0 0 1 0 4.29l-1.518 1.518a3.034 3.034 0 0 1-4.29 0Zm1.566-10.256Zm-6.015 5.808Zm0 .206Z"/>
    <g filter="url(#g)">
      <ellipse cx="20.61" cy="20.326" fill="url(#h)" rx="2.191" ry="2.633" transform="rotate(-44.678 20.61 20.326)"/>
      <path stroke="url(#i)" stroke-width=".473" d="M22 18.952c.96.97 1.035 2.346.295 3.078-.74.732-2.114.642-3.075-.33-.96-.97-1.034-2.346-.294-3.078.74-.732 2.114-.642 3.075.33Z"/>
    </g>
  </g>
  <defs>
    <linearGradient id="d" x1="18" x2="3.27" y1="18" y2="4.213" gradientUnits="userSpaceOnUse">
      <stop stop-color="#00945D"/>
      <stop offset="1" stop-color="#00FFA4"/>
    </linearGradient>
    <linearGradient id="e" x1="12.083" x2="12.083" y1="5.907" y2="19.51" gradientUnits="userSpaceOnUse">
      <stop stop-color="#CAFFE9"/>
      <stop offset="1" stop-color="#00EEA7"/>
    </linearGradient>
    <linearGradient id="f" x1="16.695" x2="24.534" y1="16.305" y2="24.856" gradientUnits="userSpaceOnUse">
      <stop stop-color="#007F54"/>
      <stop offset="1" stop-color="#002619"/>
    </linearGradient>
    <linearGradient id="h" x1="20.61" x2="20.61" y1="17.933" y2="22.959" gradientUnits="userSpaceOnUse">
      <stop stop-color="#CBFFED"/>
      <stop offset="1" stop-color="#009564"/>
    </linearGradient>
    <linearGradient id="i" x1="18.928" x2="22.462" y1="18.624" y2="22.198" gradientUnits="userSpaceOnUse">
      <stop stop-color="#fff"/>
      <stop offset="1" stop-color="#56FF3B" stop-opacity="0"/>
    </linearGradient>
    <filter id="c" width="26.803" height="26.894" x="-1.901" y="-1.654" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
      <feGaussianBlur result="effect1_foregroundBlur_2053_43232" stdDeviation="2.865"/>
    </filter>
    <filter id="g" width="23.765" height="23.775" x="8.728" y="8.439" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feGaussianBlur in="BackgroundImageFix" stdDeviation="4.731"/>
      <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_2053_43232"/>
      <feBlend in="SourceGraphic" in2="effect1_backgroundBlur_2053_43232" result="shape"/>
      <feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
      <feOffset dy="-1.893"/>
      <feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic"/>
      <feColorMatrix values="0 0 0 0 0.01489 0 0 0 0 0.243001 0 0 0 0 0.453564 0 0 0 0.1 0"/>
      <feBlend in2="shape" result="effect2_innerShadow_2053_43232"/>
    </filter>
    <clipPath id="a">
      <path fill="#fff" d="M0 0h30v30H0z"/>
    </clipPath>
  </defs>
</svg>
