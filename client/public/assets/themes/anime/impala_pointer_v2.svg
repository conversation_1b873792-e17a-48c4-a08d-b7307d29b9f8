<svg xmlns="http://www.w3.org/2000/svg" width="31" height="30" fill="none" viewBox="0 0 31 30">
  <g clip-path="url(#a)">
    <path fill="url(#b)" d="M8.75 21.922c2.218.791 4.766.98 5.99.96 2.815-5.037 6.467-6.5 10.575-6.5-.837-5.767-4.64-8.448-5.477-8.772-.837-.325-4.184-.407-5.706 0-1.902-1.381-4.488-.894-5.249-.407-.608-.39-1.825-2.058-2.358-2.843-.333-.491-.715-1.348-1.75-1.543-.912-.082-2.738 1.056-2.053 4.386 1.369 5.606 5.477 6.337 6.922 7.311 1.445.975.837 1.544.38 1.869-.456.325-3.88-.488-5.02.487-1.948 1.625-1.596 3.408-.913 4.224.173.208.544.377 1.013.427 1.198.128 2.512-.004 3.647.401Z"/>
    <path fill="url(#c)" d="M7.945 21.6c2.371 1.063 5.419 1.305 6.795 1.281 2.815-5.036 6.467-6.498 10.574-6.498-.836-5.768-4.64-8.449-5.477-8.774-.836-.325-4.184-.406-5.705 0-1.902-1.38-4.488-.893-5.249-.406-.307-.334-.755-.703-1.2-1.263C5.01 4.93 3.949 8.15 3.752 9.887c1.793 3.205 4.72 3.836 5.893 4.627 1.445.975.836 1.544.38 1.869-.234.166-1.251.033-2.34-.004-.364.758-.925 2.319-.895 3.648.016.707.511 1.283 1.156 1.573Z"/>
    <path stroke="#2EC692" stroke-linecap="round" stroke-width=".469" d="M8.883 7.203c.76-.487 3.347-.975 5.25.406 1.52-.406 4.868-.325 5.704 0 .837.325 4.64 3.005 5.477 8.774-4.107 0-7.759 1.462-10.574 6.498-1.376.024-4.424-.218-6.795-1.281-.645-.29-1.14-.866-1.156-1.573-.03-1.329.531-2.89.895-3.648 1.089.037 2.106.17 2.34.003.457-.325 1.065-.893-.38-1.868-1.173-.79-4.1-1.422-5.893-4.627C3.948 8.15 5.01 4.93 7.684 5.94c.444.56.892.93 1.2 1.263Zm0 0c.584.634 1.908 1.902 2.537 1.902"/>
    <path fill="url(#d)" d="M4.991 20.501c-.18-.369-.065-1.726 1.66-2.092-.178.63-.43 1.968-.012 2.276-.348.116-.726.28-.964.392-.168-.07-.54-.28-.684-.576Z"/>
    <path fill="url(#e)" d="M16.3 19.02c-.571.322-1.623 2.04-2.078 2.86-2.356.439-5.16-.505-6.439-1.017-.55-.813-.033-2.06.295-2.582.73.06 1.34.28 1.873.529.89.417 4.185.818 6.349.21Z"/>
    <path fill="url(#f)" d="M27.923 19.539c.653-2.45-1.065-3.803-2.13-4.01-2.13-.413-4.245.175-6.314 1.41-4.64 2.68-4.716 5.82-4.716 6.714 0 .731.608 3.684 4.031 3.034 3.424-.65 8.292-4.009 9.129-7.148Z"/>
    <g filter="url(#g)">
      <path fill="#83FFD4" d="m8.75 21.922.08-.22-.08.22Zm5.99.96.005.234.134-.003.066-.117-.205-.114Zm-8.43-1.29-.01.234.01-.235Zm-1.206-.071-.025.233.025-.233Zm-1.013-.427-.18.15.18-.15Zm.912-4.224.15.18.003-.002-.153-.178Zm3.576-.445.014-.234-.014.234Zm1.445-.042.136.19-.136-.19Zm-.38-1.869.131-.194-.131.194Zm-1.113-.582-.1.212.1-.212ZM2.72 7.203l-.23.047.003.009.228-.056Zm2.054-4.386.044-.23-.011-.003h-.012l-.02.233Zm1.6 1.31.2-.124-.2.124Zm.15.233-.194.132.194-.132Zm2.358 2.843-.126.198.126.08.127-.08-.127-.198Zm5.25.407-.138.19.09.065.108-.029-.06-.226Zm5.705 0 .084-.219-.084.219Zm5.307 7.824-.229.049.036.167.17.017.023-.233Zm.648.095.045-.23-.045.23Zm2.13 4.01-.226-.06.226.06Zm-9.128 7.148-.044-.23.044.23Zm-3.968-3.96.232.036-.435-.151.203.116Zm-6.155-.584c2.252.803 4.83.994 6.072.973l-.008-.469c-1.206.021-3.724-.167-5.907-.946l-.157.442ZM6.3 21.826c.843.029 1.649.06 2.37.317l.158-.442c-.802-.286-1.684-.315-2.512-.344l-.016.469Zm-1.222-.072c.403.043.816.057 1.222.072l.016-.469a16.63 16.63 0 0 1-1.188-.07l-.05.467Zm-1.168-.51c.229.274.67.456 1.168.51l.05-.466c-.44-.047-.74-.203-.859-.344l-.36.3Zm.942-4.554c-1.013.846-1.449 1.752-1.526 2.574-.076.816.204 1.527.584 1.98l.36-.3c-.304-.362-.54-.951-.477-1.637.064-.68.426-1.478 1.36-2.257l-.3-.36Zm3.74-.499c-.606-.036-1.335-.08-2.004-.036-.657.044-1.315.176-1.738.537l.305.356c.298-.255.82-.382 1.464-.425.634-.042 1.333 0 1.945.036l.028-.468Zm1.295 0c.005-.003-.015.011-.095.023-.072.01-.168.016-.29.016a15.62 15.62 0 0 1-.91-.039l-.028.468c.352.021.68.041.94.04.131 0 .253-.006.356-.021a.69.69 0 0 0 .3-.104l-.273-.382Zm-.375-1.482c.712.48.816.796.8.961a.483.483 0 0 1-.13.268 1.67 1.67 0 0 1-.295.254l.272.382a2.12 2.12 0 0 0 .378-.33.947.947 0 0 0 .243-.53c.041-.444-.273-.9-1.006-1.394l-.262.389Zm-1.082-.565c.452.213.817.386 1.082.565l.262-.389c-.3-.203-.704-.392-1.144-.6l-.2.424ZM2.494 7.26c.561 2.298 1.578 3.805 2.698 4.847 1.114 1.038 2.322 1.606 3.239 2.038l.2-.424c-.918-.433-2.064-.975-3.12-1.957-1.052-.98-2.022-2.405-2.562-4.615l-.455.11Zm2.302-4.676c-1.127-.1-3.008 1.245-2.304 4.667l.46-.094c-.668-3.24 1.103-4.169 1.802-4.107l.041-.467Zm1.779 1.42c-.305-.488-.76-1.229-1.756-1.417l-.087.46c.774.147 1.134.708 1.445 1.206l.398-.249Zm.144.226a7.458 7.458 0 0 1-.144-.226l-.398.249c.052.082.103.164.154.24l.388-.263Zm2.29 2.777c-.125-.08-.299-.24-.504-.46a12.987 12.987 0 0 1-.646-.762A27.928 27.928 0 0 1 6.72 4.23l-.388.263c.269.396.71 1.014 1.161 1.583.226.285.456.56.67.79.21.225.417.421.595.536l.253-.395Zm5.261.414c-1.003-.728-2.179-.96-3.193-.949-1.005.01-1.89.26-2.32.535l.253.395c.33-.212 1.119-.451 2.072-.46.945-.01 2.014.205 2.912.858l.276-.38Zm5.652-.029c-.24-.094-.634-.162-1.09-.209a17.448 17.448 0 0 0-1.588-.081c-1.137-.01-2.38.07-3.172.282l.12.453c.73-.195 1.921-.276 3.047-.266.56.005 1.097.032 1.544.078.454.047.792.111.97.18l.17-.437Zm5.453 7.994c-.554-2.6-1.703-4.514-2.817-5.819-1.108-1.296-2.193-2.003-2.636-2.175l-.17.437c.348.135 1.373.783 2.45 2.043 1.07 1.253 2.18 3.097 2.714 5.612l.459-.098Zm.463-.086a7.35 7.35 0 0 0-.67-.098l-.046.466c.209.021.417.052.626.092l.09-.46Zm2.311 4.3c.345-1.292.066-2.315-.456-3.043-.515-.718-1.263-1.142-1.855-1.257l-.09.46c.473.092 1.117.448 1.564 1.07.44.613.693 1.491.385 2.65l.453.12Zm-9.31 7.319c1.755-.334 3.86-1.355 5.63-2.684 1.764-1.324 3.242-2.989 3.68-4.635l-.452-.12c-.398 1.493-1.774 3.077-3.509 4.38-1.73 1.298-3.77 2.281-5.437 2.598l.087.46Zm-4.31-3.265c0 .404.162 1.38.78 2.178.314.405.746.765 1.331.98.585.213 1.309.275 2.198.107l-.087-.461c-.822.156-1.458.093-1.95-.087a2.476 2.476 0 0 1-1.12-.826c-.541-.697-.684-1.564-.684-1.891h-.469Zm.066-.96c-.063.42-.067.752-.067.96h.47c0-.198.003-.504.061-.89l-.463-.07Zm.35.303.086-.152-.407-.232-.088.155.409.229Z"/>
    </g>
    <path fill="#83FFD4" d="m8.75 21.922.08-.22-.08.22Zm5.99.96.005.234.134-.003.066-.117-.205-.114Zm-8.43-1.29-.01.234.01-.235Zm-1.206-.071-.025.233.025-.233Zm-1.013-.427-.18.15.18-.15Zm.912-4.224.15.18.003-.002-.153-.178Zm3.576-.445.014-.234-.014.234Zm1.445-.042.136.19-.136-.19Zm-.38-1.869.131-.194-.131.194Zm-1.113-.582-.1.212.1-.212ZM2.72 7.203l-.23.047.003.009.228-.056Zm2.054-4.386.044-.23-.011-.003h-.012l-.02.233Zm1.6 1.31.2-.124-.2.124Zm.15.233-.194.132.194-.132Zm2.358 2.843-.126.198.126.08.127-.08-.127-.198Zm5.25.407-.138.19.09.065.108-.029-.06-.226Zm5.705 0 .084-.219-.084.219Zm5.307 7.824-.229.049.036.167.17.017.023-.233Zm.648.095.045-.23-.045.23Zm2.13 4.01-.226-.06.226.06Zm-9.128 7.148-.044-.23.044.23Zm-3.968-3.96.232.036-.435-.151.203.116Zm-6.155-.584c2.252.803 4.83.994 6.072.973l-.008-.469c-1.206.021-3.724-.167-5.907-.946l-.157.442ZM6.3 21.826c.843.029 1.649.06 2.37.317l.158-.442c-.802-.286-1.684-.315-2.512-.344l-.016.469Zm-1.222-.072c.403.043.816.057 1.222.072l.016-.469a16.63 16.63 0 0 1-1.188-.07l-.05.467Zm-1.168-.51c.229.274.67.456 1.168.51l.05-.466c-.44-.047-.74-.203-.859-.344l-.36.3Zm.942-4.554c-1.013.846-1.449 1.752-1.526 2.574-.076.816.204 1.527.584 1.98l.36-.3c-.304-.362-.54-.951-.477-1.637.064-.68.426-1.478 1.36-2.257l-.3-.36Zm3.74-.499c-.606-.036-1.335-.08-2.004-.036-.657.044-1.315.176-1.738.537l.305.356c.298-.255.82-.382 1.464-.425.634-.042 1.333 0 1.945.036l.028-.468Zm1.295 0c.005-.003-.015.011-.095.023-.072.01-.168.016-.29.016a15.62 15.62 0 0 1-.91-.039l-.028.468c.352.021.68.041.94.04.131 0 .253-.006.356-.021a.69.69 0 0 0 .3-.104l-.273-.382Zm-.375-1.482c.712.48.816.796.8.961a.483.483 0 0 1-.13.268 1.67 1.67 0 0 1-.295.254l.272.382a2.12 2.12 0 0 0 .378-.33.947.947 0 0 0 .243-.53c.041-.444-.273-.9-1.006-1.394l-.262.389Zm-1.082-.565c.452.213.817.386 1.082.565l.262-.389c-.3-.203-.704-.392-1.144-.6l-.2.424ZM2.494 7.26c.561 2.298 1.578 3.805 2.698 4.847 1.114 1.038 2.322 1.606 3.239 2.038l.2-.424c-.918-.433-2.064-.975-3.12-1.957-1.052-.98-2.022-2.405-2.562-4.615l-.455.11Zm2.302-4.676c-1.127-.1-3.008 1.245-2.304 4.667l.46-.094c-.668-3.24 1.103-4.169 1.802-4.107l.041-.467Zm1.779 1.42c-.305-.488-.76-1.229-1.756-1.417l-.087.46c.774.147 1.134.708 1.445 1.206l.398-.249Zm.144.226a7.458 7.458 0 0 1-.144-.226l-.398.249c.052.082.103.164.154.24l.388-.263Zm2.29 2.777c-.125-.08-.299-.24-.504-.46a12.987 12.987 0 0 1-.646-.762A27.928 27.928 0 0 1 6.72 4.23l-.388.263c.269.396.71 1.014 1.161 1.583.226.285.456.56.67.79.21.225.417.421.595.536l.253-.395Zm5.261.414c-1.003-.728-2.179-.96-3.193-.949-1.005.01-1.89.26-2.32.535l.253.395c.33-.212 1.119-.451 2.072-.46.945-.01 2.014.205 2.912.858l.276-.38Zm5.652-.029c-.24-.094-.634-.162-1.09-.209a17.448 17.448 0 0 0-1.588-.081c-1.137-.01-2.38.07-3.172.282l.12.453c.73-.195 1.921-.276 3.047-.266.56.005 1.097.032 1.544.078.454.047.792.111.97.18l.17-.437Zm5.453 7.994c-.554-2.6-1.703-4.514-2.817-5.819-1.108-1.296-2.193-2.003-2.636-2.175l-.17.437c.348.135 1.373.783 2.45 2.043 1.07 1.253 2.18 3.097 2.714 5.612l.459-.098Zm.463-.086a7.35 7.35 0 0 0-.67-.098l-.046.466c.209.021.417.052.626.092l.09-.46Zm2.311 4.3c.345-1.292.066-2.315-.456-3.043-.515-.718-1.263-1.142-1.855-1.257l-.09.46c.473.092 1.117.448 1.564 1.07.44.613.693 1.491.385 2.65l.453.12Zm-9.31 7.319c1.755-.334 3.86-1.355 5.63-2.684 1.764-1.324 3.242-2.989 3.68-4.635l-.452-.12c-.398 1.493-1.774 3.077-3.509 4.38-1.73 1.298-3.77 2.281-5.437 2.598l.087.46Zm-4.31-3.265c0 .404.162 1.38.78 2.178.314.405.746.765 1.331.98.585.213 1.309.275 2.198.107l-.087-.461c-.822.156-1.458.093-1.95-.087a2.476 2.476 0 0 1-1.12-.826c-.541-.697-.684-1.564-.684-1.891h-.469Zm.066-.96c-.063.42-.067.752-.067.96h.47c0-.198.003-.504.061-.89l-.463-.07Zm.35.303.086-.152-.407-.232-.088.155.409.229Z"/>
    <ellipse cx="5.777" cy="2.128" fill="url(#h)" rx="5.777" ry="2.128" transform="matrix(.83146 -.55559 .50555 .8628 16.208 23.573)"/>
    <path fill="url(#i)" d="M19.09 21.4c-1.218.242-2.174-.162-2.95-.89-.369.405-.983 1.375-.983 2.748.111 2.425.922 3.152 2.529 3.152-.776-.08-.843-1.293-.843-1.535 0-.256.07-1.455 2.247-3.475Z"/>
    <path fill="url(#j)" d="M11.359 11.575c.06-.488.28-1.399.73-1.476a1.5 1.5 0 0 1 1.036.22c-.557.161-1.668.633-1.766 1.256Z"/>
    <path fill="url(#k)" d="m14.387 10.05-.013.045a.561.561 0 0 1 .013-.045c.14-.471.504-1.335.961-1.338.472-.003.854.257.986.387-.575.068-1.748.352-1.947.951Z"/>
    <path fill="url(#l)" d="M17.526 9.067c.114-.404.41-1.142.776-1.142.378 0 .682.225.787.337-.46.055-1.401.292-1.563.805Z"/>
  </g>
  <defs>
    <linearGradient id="b" x1="2.571" x2="26.009" y1="6.011" y2="18.367" gradientUnits="userSpaceOnUse">
      <stop stop-color="#E9F3F0"/>
      <stop offset="1" stop-color="#A3EAD2"/>
    </linearGradient>
    <linearGradient id="c" x1="8.777" x2="18.274" y1="8.392" y2="20.782" gradientUnits="userSpaceOnUse">
      <stop stop-color="#204E3F"/>
      <stop offset="1" stop-color="#00130B"/>
    </linearGradient>
    <linearGradient id="d" x1="5.807" x2="4.586" y1="18.441" y2="20.245" gradientUnits="userSpaceOnUse">
      <stop stop-color="#B4D2C6"/>
      <stop offset="1" stop-color="#CCE1D8" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="e" x1="14.41" x2="13.421" y1="18.006" y2="23.383" gradientUnits="userSpaceOnUse">
      <stop stop-color="#186C50"/>
      <stop offset="1" stop-color="#194134" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="f" x1="17.958" x2="24.383" y1="17.154" y2="24.868" gradientUnits="userSpaceOnUse">
      <stop stop-color="#204E3F"/>
      <stop offset="1" stop-color="#00130B"/>
    </linearGradient>
    <linearGradient id="h" x1="-2.92" x2="6.233" y1="5.054" y2="7.334" gradientUnits="userSpaceOnUse">
      <stop stop-color="#158F63"/>
      <stop offset="1" stop-color="#001910"/>
    </linearGradient>
    <linearGradient id="i" x1="18.452" x2="14.253" y1="20.511" y2="24.831" gradientUnits="userSpaceOnUse">
      <stop stop-color="#186C50"/>
      <stop offset="1" stop-color="#194134" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="j" x1="12.648" x2="12.335" y1="9.99" y2="11.829" gradientUnits="userSpaceOnUse">
      <stop stop-color="#186C50"/>
      <stop offset="1" stop-color="#194134" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="k" x1="15.918" x2="15.309" y1="8.696" y2="10.459" gradientUnits="userSpaceOnUse">
      <stop stop-color="#186C50"/>
      <stop offset="1" stop-color="#194134" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="l" x1="18.758" x2="18.197" y1="7.915" y2="9.398" gradientUnits="userSpaceOnUse">
      <stop stop-color="#186C50"/>
      <stop offset="1" stop-color="#194134" stop-opacity="0"/>
    </linearGradient>
    <clipPath id="a">
      <path fill="#fff" d="M.125 0h30v30h-30z"/>
    </clipPath>
    <filter id="g" width="29.713" height="28.186" x=".464" y=".703" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
      <feGaussianBlur result="effect1_foregroundBlur_645_147458" stdDeviation=".938"/>
    </filter>
  </defs>
</svg>
