// Chuyển các biên scss thành các biên trong js vì chế độ build turbopack của next ko tự biên dịch sang js được
// Sử dụng sass để compile SCSS chứa :export { … } và parse khối export thành JS object

const fs   = require('fs');
const path = require('path');
const sass = require('sass');

// 1. Đường dẫn tới file SCSS có khối :export
const scssPath = path.resolve(__dirname, '../styles/_themes-vars.module.scss');

// 2. Compile SCSS ra CSS thuần
const { css } = sass.compile(scssPath, { style: 'expanded' });

// 3. Lấy phần nội dung bên trong :export { ... }
const exportMatch    = css.match(/:export\s*{([\s\S]*?)}/);
const exportContent  = exportMatch ? exportMatch[1] : '';

// 4. Parse từng dòng `key: value;` trong khối export
const vars = {};
exportContent.split(';').forEach(line => {
  const [rawKey, ...rest] = line.split(':');
  if (!rawKey || rest.length === 0) return;
  const rawVal = rest.join(':').trim();
  const key    = rawKey.trim().replace(/-([a-z])/g, (_, c) => c.toUpperCase());
  if (rawVal) {
    vars[key] = rawVal;
  }
});

// 5. Ghi ra file JS
const outPath = path.resolve(__dirname, './theme-vars.js');
const content = `// auto-generated by build-theme.js\nexport default ${JSON.stringify(vars, null, 2)};\n`;
fs.writeFileSync(outPath, content);

console.log(`✅ Generated ${path.relative(process.cwd(), outPath)}`);
