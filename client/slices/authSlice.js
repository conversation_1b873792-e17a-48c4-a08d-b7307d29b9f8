import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { axios, getCsrfToken } from "@/lib/axios";
import { apiUrl } from "@/lib/fetcher";
import localStorageHelper from "@/utils/localStorageHelper";
import { keyStorageUser } from '@/constant';
import {
  AUTH_LOGIN,
  AUTH_REGISTER,
  AUTH_LOGOUT,
  AUTH_FETCH_USER
} from '@/store/actions';

export const login = createAsyncThunk(
  AUTH_LOGIN,
  async (credentials, { rejectWithValue }) => {
    try {
      await getCsrfToken();
      const response = await axios.post(apiUrl("/login"), credentials);
      return response.data;
    } catch (error) {
      // đã làm sạch error bên lib/axios
      return rejectWithValue(error.errors || error);
    }
  }
);

export const register = createAsyncThunk(
  AUTH_REGISTER,
  async (userData, { rejectWithValue }) => {
    try {
      await getCsrfToken();
      const response = await axios.post(apiUrl("/register"), userData);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.errors || error);
    }
  }
);

export const logout = createAsyncThunk(
  AUTH_LOGOUT,
  async (_, { rejectWithValue }) => {
    try {
      await axios.post(apiUrl("/logout"));
      return true;
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

export const fetchUser = createAsyncThunk(
  AUTH_FETCH_USER,
  async ({ refreshToken = false } = {}, { rejectWithValue }) => {
    try {
      await getCsrfToken();
      const response = await axios.get(apiUrl("/api/user"), {
        params: {
          refreshToken: refreshToken ? "true" : undefined
        }
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

const authSlice = createSlice({
  name: "auth",
  initialState: {
    user: null,
    loading: true,
    error: null,
  },
  reducers: {
    clearUser(state) {
      state.user = null;
      state.loading = false;
      localStorageHelper.remove(keyStorageUser);
    },
    setUser(state, action) {
      const { user, persist = false } = action.payload;

      if (persist) {
        const newUser = {
          ...(state.user || {}),
          ...user,
        }
        state.user = newUser;
        localStorageHelper.set(keyStorageUser, newUser);
      } else {
        state.user = user;
      }

      state.loading = false;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(login.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(login.fulfilled, (state, action) => {
        state.loading = false;
        state.user = action.payload;
        localStorageHelper.set(keyStorageUser, action.payload);
      })
      .addCase(login.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });

    builder
      .addCase(register.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(register.fulfilled, (state, action) => {
        state.loading = false;
        state.user = action.payload;
        localStorageHelper.set(keyStorageUser, action.payload);
      })
      .addCase(register.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });

    builder.addCase(logout.fulfilled, (state) => {
      state.user = null;
      state.loading = false;
      localStorageHelper.remove(keyStorageUser);
    });

    builder
      .addCase(fetchUser.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchUser.fulfilled, (state, action) => {
        state.loading = false;
        state.user = action.payload;
        localStorageHelper.set(keyStorageUser, action.payload);
      })
      .addCase(fetchUser.rejected, (state, action) => {
        state.loading = false;
        state.user = null;
      });
  },
});

export const { setUser, clearUser } = authSlice.actions;

export default authSlice.reducer;
