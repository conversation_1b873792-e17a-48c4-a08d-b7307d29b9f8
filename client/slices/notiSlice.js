import { createSlice } from '@reduxjs/toolkit';

const initialState = {
    status: null,
    message: '',
};

const notiSlice = createSlice({
    name: 'noti',
    initialState,
    reducers: {
        setNoti: (state, action) => {
            state.status = action.payload.status;
            state.message = action.payload.message;
        },
        clearNoti: () => initialState,
    },
});

export const { setNoti, clearNoti } = notiSlice.actions;

export default notiSlice.reducer;
