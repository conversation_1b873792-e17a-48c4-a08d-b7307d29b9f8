import { createApi } from "@reduxjs/toolkit/query/react";
import { axiosBaseQuery } from "@/lib/axios";

export const apiSlice = createApi({
    reducerPath: "api",
    baseQuery: axiosBaseQuery(),
    tagTypes: ['Classroom', 'Report', 'User'], // tag sử dụng cho cache
    endpoints: (builder) => ({}),
});

// Nếu ko dùng axiosBaseQuery có thể dùng fetchBaseQuery cuả rtk
// Example:
// import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'

// export const authApi = createApi({
//   reducerPath: 'authApi',
//   baseQuery: fetchBaseQuery({
//     // base url of backend API
//     baseUrl: 'http://127.0.0.1:5000/',
//     // prepareHeaders is used to configure the header of every request and gives access to getState which we use to include the token from the store
//     prepareHeaders: (headers, { getState }) => {
//       const token = getState().auth.token
//       if (token) {
//        // include token in req header
//         headers.set('authorization', `Bearer ${token}`)
//         return headers
//       }
//     },
//   }),
//   endpoints: (builder) => ({
//     getUserDetails: builder.query({
//       query: () => ({
//         url: 'api/user/profile',
//         method: 'GET',
//       }),
//     }),
//   }),
// })

// // export hooks for usage in functional components, which are
// // auto-generated based on the defined endpoints
// export const { useGetUserDetailsQuery } = authApi
