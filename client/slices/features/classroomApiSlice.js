import { apiSlice } from '../apiSlice';
import { apiUrl } from "@/lib/fetcher";

export const classroomApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    fetchClassrooms: builder.query({
      query: (params) => ({
        url: apiUrl('/api/private/classrooms'),
        method: 'GET',
        params,
      }),
      // Cung cấp tag cho cache khi có kết quả trả về
      providesTags: (result, error, arg) => {
        return result
          ? [{ type: 'Classroom', id: 'LIST' }]
          : [];
      },
      keepUnusedDataFor: 60, // Giữ cache trong 60 giây
    }),
    fetchStudents: builder.query({
      query: ({ classroomId, params }) => ({
        url: apiUrl(`api/private/classrooms/${classroomId}/students`),
        method: 'GET',
        params,
      }),
      providesTags: (result, error, { classroomId }) =>
        result
          ? [{ type: 'Classroom', id: `STUDENTS-${classroomId}` }]
          : [],
      keepUnusedDataFor: 60,
    }),
    fetchAssignments: builder.query({
      query: ({ classroomId, params }) => ({
        url: apiUrl(`api/private/classrooms/${classroomId}/assignments`),
        method: 'GET',
        params,
      }),
      providesTags: (result, error, { classroomId }) =>
        result
          ? [{ type: 'Classroom', id: `ASSIGNMENTS-${classroomId}` }]
          : [],
      keepUnusedDataFor: 60,
    }),
    fetchMyClassrooms: builder.query({
      query: (params) => ({
        url: apiUrl('/api/private/my-classrooms'),
        method: 'GET',
        params,
      }),
      providesTags: (result, error, arg) =>
        result
          ? [{ type: 'Classroom', id: 'MYCLASSROOMS' }]
          : [],
      keepUnusedDataFor: 60,
    }),
    fetchMyClassroom: builder.query({
      query: ({ classroomId, params }) => ({
        url: apiUrl(`/api/private/my-classroom/${classroomId}`),
        method: 'GET',
        params,
      }),
      providesTags: (result, error, { classroomId }) =>
        result
          ? [{ type: 'Classroom', id: `MYCLASSROOM-${classroomId}` }]
          : [],
      keepUnusedDataFor: 60,
    }),
  }),
  overrideExisting: false,
});

export const { useFetchClassroomsQuery, useFetchStudentsQuery, useFetchMyClassroomQuery } = classroomApiSlice;
