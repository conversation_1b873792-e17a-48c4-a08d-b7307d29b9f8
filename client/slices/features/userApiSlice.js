import { apiSlice } from '../apiSlice';
import { apiUrl } from "@/lib/fetcher";

export const userApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    fetchMyLibrary: builder.query({
      query: (params) => ({
        url: apiUrl("/api/user/my-library"),
        method: 'GET',
        params,
      }),
      providesTags: (result, error, arg) => {
        return result
          ? [{ type: 'User', id: 'MYLIBRARY' }]
          : [];
      },
      keepUnusedDataFor: 60,
    }),
  }),
  overrideExisting: false,
});

export const { useFetchMyLibraryQuery } = userApiSlice;
