import { apiSlice } from '../apiSlice';
import { apiUrl } from "@/lib/fetcher";

export const reportApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    fetchAssignmentReports: builder.query({
      query: ({ assignmentId, params = {} }) => ({
        url: apiUrl(`api/private/assignments/${assignmentId}/report`),
        method: 'GET',
        params,
      }),
      providesTags: (result, error, { assignmentId }) =>
        result
          ? [{ type: 'Report', id: `ASSIGNMENT-${assignmentId}` }]
          : [],
      keepUnusedDataFor: 60,
    }),
    fetchStudentReports: builder.query({
      query: ({ classroomId, studentId, params = {} }) => ({
        url: apiUrl(`api/private/classrooms/${classroomId}/students/${studentId}/report`),
        method: 'GET',
        params,
      }),
      providesTags: (result, error, { classroomId, studentId }) =>
        result
          ? [{ type: 'Report', id: `CLASSROOM-${classroomId}-STUDENT-${studentId}` }]
          : [],
      keepUnusedDataFor: 60,
    }),
  }),
  overrideExisting: false,
});

export const { useFetchAssignmentReportsQuery, useFetchStudentReportsQuery } = reportApiSlice;
