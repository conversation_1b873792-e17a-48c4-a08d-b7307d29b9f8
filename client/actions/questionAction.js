import { apiUrl } from "@/lib/fetcher";
import { http } from "@/lib/axios";

/*
  - fetcher với option cache: "force-cache" chỉ có tác dụng với server component
  - c<PERSON> thể call fetcher ở client component, lúc đó cache: "force-cache" sẽ không có tác dụng
  - c<PERSON> thể call http ở server component và sẽ ko cache kết quả (muốn cache có thể  dùng 'use cache' hoặc unstable_cache)
*/

export const storeQuestion = async (quizId, data) => {
  return await http({
    url: apiUrl(`/api/private/quiz/${quizId}/question`),
    method: "POST",
    data,
  });
};

export const updateQuestion = async (quizId, data) => {
  return await http({
    url: apiUrl(`/api/private/quiz/${quizId}/question/${data.id}`),
    method: "PUT",
    data,
  });
};

export const removeQuestion = async (quizId, questionId) => {
  return await http({
    url: apiUrl(`/api/private/quiz/${quizId}/question/${questionId}`),
    method: 'DELETE',
  });
};
