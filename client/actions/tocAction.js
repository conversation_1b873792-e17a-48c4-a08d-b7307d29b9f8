import { apiUrl, fetcher } from "@/lib/fetcher";
import { http } from "@/lib/axios";

const url_prefix = "/api/tocs";

/*
  - fetcher với option cache: "force-cache" chỉ có tác dụng với server component
  - <PERSON><PERSON> thể call fetcher ở client component, lúc đó cache: "force-cache" sẽ không có tác dụng
  - c<PERSON> thể call http ở server component và sẽ ko cache kết quả (muốn cache có thể  dùng 'use cache' hoặc unstable_cache)
*/

export const fetchToc = async (tocId) => {
  return await fetcher({
    url: apiUrl(`/${tocId}`, url_prefix),
    method: "GET",
  });
};

/**
 * Cập nhật vị trí của Toc
 */
export const updateTocPosition = async (tocId, data) => {
  return await http({
    url: apiUrl(`/api/private/tocs/${tocId}/update-position`),
    method: "PUT",
    data
  });
};

export const updateTocTitle = async (id, title) => {
  return await http({
    url: apiUrl(`/api/private/tocs/${id}/update-title`),
    method: 'PUT',
    data: { title },
  });
};

export const deleteToc = async (id) => {
  return await http({
    url: apiUrl(`/api/private/tocs/${id}`),
    method: 'DELETE',
  });
};

export const createToc = async (data) => {
  return await http({
    url: apiUrl(`/api/private/tocs`),
    method: 'POST',
    data,
  });
};
