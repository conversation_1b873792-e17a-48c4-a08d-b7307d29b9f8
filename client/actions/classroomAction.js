import { http } from "@/lib/axios";
import { apiUrl } from "@/lib/fetcher";

const url_prefix = "/api/private/classrooms";

/*
  - fetcher với option cache: "force-cache" chỉ có tác dụng với server component
  - <PERSON><PERSON> thể call fetcher ở client component, lúc đó cache: "force-cache" sẽ không có tác dụng
  - c<PERSON> thể call http ở server component và sẽ ko cache kết quả (muốn cache có thể  dùng 'use cache' hoặc unstable_cache)
*/

export const fetchClassrooms = async (queryParams = {}, signal) => {
  return await http({
    url: apiUrl("/", url_prefix),
    method: "GET",
    params: queryParams,
    signal,
  });
};

export const fetchClassroom = async (id, token) => {
  return await http({
    url: apiUrl(`/${id}`, url_prefix),
    method: "GET",
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
};

export const storeClassroom = async (data) => {
  return await http({
    url: apiUrl("/", url_prefix),
    method: "POST",
    data,
  });
};

export const updateClassroom = async (data) => {
  return await http({
    url: apiUrl(`/${data.id}`, url_prefix),
    method: "PUT",
    data,
  });
};

export const removeClassroom = async (id) => {
  return await http({
    url: apiUrl(`/${id}`, url_prefix),
    method: "DELETE",
  });
};

// students tab
export const fetchStudents = async (classroomId, queryParams = {}) => {
  return await http({
    url: apiUrl(`/${classroomId}/students`, url_prefix),
    method: "GET",
    params: queryParams,
  });
};

export const storeStudent = async (classroomId, data) => {
  return await http({
    url: apiUrl(`/${classroomId}/students`, url_prefix),
    method: "POST",
    data,
  });
};

export const updateStudent = async (classroomId, data) => {
  return await http({
    url: apiUrl(`/${classroomId}/students/${data.id}/update`, url_prefix),
    method: "PUT",
    data,
  });
};

export const removeStudent = async (classroomId, studentId) => {
  return await http({
    url: apiUrl(`/${classroomId}/students/${studentId}`, url_prefix),
    method: "DELETE",
  });
};

export const removeStudents = async (classroomId, data) => {
  return await http({
    url: apiUrl(`/${classroomId}/remove-students`, url_prefix),
    method: "POST",
    data,
  });
};

export const importStudents = async (classroomId, formData) => {
  return await http({
    url: apiUrl(`/${classroomId}/students/import`, url_prefix),
    method: "POST",
    data: formData,
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
};

export const exportStudents = async (classroomId) => {
  return await http({
    url: apiUrl(`/${classroomId}/students/export`, url_prefix),
    method: "GET",
    responseType: "blob",
  });
};

// assignments tab
export const fetchAssignments = async (classroomId, queryParams = {}) => {
  return await http({
    url: apiUrl(`/${classroomId}/assignments`, url_prefix),
    method: "GET",
    params: queryParams,
  });
};

export const updateAssignment = async (classroomId, data) => {
  return await http({
    url: apiUrl(`/${classroomId}/assignments/${data.id}/update`, url_prefix),
    method: "PUT",
    data,
  });
};

export const removeAssignment = async (classroomId, assignmentId) => {
  return await http({
    url: apiUrl(`/${classroomId}/assignments/${assignmentId}`, url_prefix),
    method: "DELETE",
  });
};

// reports tab
export const fetchReports = async (classroomId, signal) => {
  return await http({
    url: apiUrl(`/${classroomId}/reports`, url_prefix),
    method: "GET",
    signal,
  });
};

export const joinClassroom = async (data) => {
  return await http({
    url: apiUrl(`/join`, url_prefix),
    method: "POST",
    data,
  });
};

export const leaveClassroom = async (classroomId, data) => {
  return await http({
    url: apiUrl(`/${classroomId}/leave`, url_prefix),
    method: "POST",
    data
  });
};

export const fetchClassRoomReports = async () => {
  return await http({
    url: apiUrl(`/report`, url_prefix),
    method: "GET",
  });
};

export const removeAssignmentsReport = async (ids) => {
  return await http({
    url: apiUrl(`/assignments/delete/report`, url_prefix),
    method: "DELETE",
    data: { ids },
  });
};
