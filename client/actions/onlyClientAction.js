import useSWR from "swr";
import { swrHttpFetcher } from "@/lib/axios";
import { apiUrl } from "@/lib/fetcher";

export const fetchOptions = (option = null, queryParams = {}) => {
  const queryStr = new URLSearchParams(queryParams);

  return useSWR(
    option ? apiUrl(`/api/suggest?option=${option}&${queryStr.toString()}`) : null,
    swrHttpFetcher,
    {
      revalidateIfStale: false,
      revalidateOnFocus: false,
      revalidateOnReconnect: false,
      shouldRetryOnError: false,
    }
  );
};
