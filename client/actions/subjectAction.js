import { apiUrl, fetcher } from "@/lib/fetcher";

const url_prefix = "/api/subjects";

/*
  - fetcher với option cache: "force-cache" chỉ có tác dụng với server component
  - c<PERSON> thể call fetcher ở client component, lúc đó cache: "force-cache" sẽ không có tác dụng
  - c<PERSON> thể call http ở server component và sẽ ko cache kết quả (muốn cache có thể  dùng 'use cache' hoặc unstable_cache)
*/

export const fetchSubjectBySlug = async (subjectSlug) => {
  return await fetcher({
    url: apiUrl(`/${subjectSlug}/show`, url_prefix),
    method: "GET",
  });
};

export const fetchSubjects = async (queryParams = {}) => {
  return await fetcher({
    url: apiUrl("/", url_prefix),
    method: "GET",
    queryParams,
    nextOption: {
      cache: "force-cache",
    },
  });
};

export const fetchQuizsOfSubject = async (subjectId, queryParams = {}) => {
  return await fetcher({
    url: apiUrl(`/${subjectId}/quizzes`, url_prefix),
    method: "GET",
    queryParams,
  });
};
