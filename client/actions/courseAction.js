import { http } from "@/lib/axios";
import { apiUrl, fetcher } from "@/lib/fetcher";

const url_prefix = "/api/courses";

/*
  - fetcher với option cache: "force-cache" chỉ có tác dụng với server component
  - <PERSON><PERSON> thể call fetcher ở client component, lúc đó cache: "force-cache" sẽ không có tác dụng
  - có thể call http ở server component và sẽ ko cache kết quả (muốn cache có thể  dùng 'use cache' hoặc unstable_cache)
*/

export const fetchCourses = async (queryParams = {}) => {
  return await fetcher({
    url: apiUrl(url_prefix),
    method: "GET",
    queryParams,
  });
};

export const fetchCourse = async (courseId) => {
  return await fetcher({
    url: apiUrl(`/${courseId}`, url_prefix),
    method: "GET",
  });
};

export const fetchCourseTocs = async (courseId) => {
  return await http({
    url: apiUrl(`/api/private/courses/${courseId}/tocs`),
    method: "GET",
  });
};

export const updatePositions = async (courseId, positions) => {
  return await http({
    url: apiUrl(`/api/private/courses/${courseId}/update-positions`),
    method: "PUT",
    data: { positions }
  });
};

export const updateCourse = async (courseData) => {
  return await http({
    url: apiUrl(`/api/private/courses/${courseData.id}`),
    method: "PUT",
    data: courseData
  });
};

export const deleteCourse = async (courseId) => {
  return await http({
    url: apiUrl(`/api/private/courses/${courseId}`),
    method: "DELETE"
  });
};
