import { apiUrl, fetcher } from "@/lib/fetcher";
import { http } from "@/lib/axios";

const private_quiz_prefix = "/api/private/quiz";
const public_quiz_prefix = "/api/quiz";

/*
  - fetcher với option cache: "force-cache" chỉ có tác dụng với server component
  - c<PERSON> thể call fetcher ở client component, lúc đó cache: "force-cache" sẽ không có tác dụng
  - có thể call http ở server component và sẽ ko cache kết quả (muốn cache có thể  dùng 'use cache' hoặc unstable_cache)
*/

export const quizInfo = async (id) => {
  return await fetcher({
    url: apiUrl(`${id}`, public_quiz_prefix),
    method: 'GET',
  });
};

export const quizWithQuestions = async (id) => {
  return await fetcher({
    url: apiUrl(`${id}/questions`, public_quiz_prefix),
    method: "GET",
    nextOption: {
      cache: "force-cache",
    },
  });
};

export const fetchQuizByCode = async (code) => {
  return await http({
    url: apiUrl(`search-by-code?code=${code}`, public_quiz_prefix),
    method: 'GET',
  });
};

export const fetchQuizzes = async (params = {}, signal) => {
  return await http({
    url: apiUrl("/", public_quiz_prefix),
    method: "GET",
    params,
    signal,
  });
};

export const fetchQuiz = async (id, token) => {
  return await http({
    url: apiUrl(`/${id}`, private_quiz_prefix),
    method: "GET",
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
};

export const storeQuiz = async (data) => {
  return await http({
    url: apiUrl("/", private_quiz_prefix),
    method: "POST",
    data,
  });
};

export const updateQuiz = async (data) => {
  return await http({
    url: apiUrl(`/${data.id}`, private_quiz_prefix),
    method: 'PUT',
    data: data,
  });
};

export const assignQuiz = async (data) => {
  return await http({
    url: apiUrl(`/${data.id}/assign-quiz`, private_quiz_prefix),
    method: 'POST',
    data: data,
  });
};

export const importQuizByFile = async (formData) => {
  return await http({
    url: apiUrl(`/import-quiz-by-file`, private_quiz_prefix),
    method: 'POST',
    data: formData,
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
}

export const importGoogleForm = async (data) => {
  return await http({
    url: apiUrl(`/import-google-form`, private_quiz_prefix),
    method: 'POST',
    data: data,
  });
}

export const removeQuiz = async (id) => {
  return await http({
    url: apiUrl(`/${id}`, private_quiz_prefix),
    method: "DELETE",
  });
};

export const duplicateQuestions = async (quizId, data) => {
  return await http({
    url: apiUrl(`/${quizId}/duplicate-questions`, private_quiz_prefix),
    method: 'POST',
    data: data,
  });
};

export const fetchClassrooms = async (quizId, queryParams = {}) => {
  return await http({
    url: apiUrl(`/${quizId}/classrooms`, private_quiz_prefix),
    method: "GET",
    params: queryParams,
  });
};

export const assignClassrooms = async (quizId, data) => {
  return await http({
    url: apiUrl(`/${quizId}/assign-classrooms`, private_quiz_prefix),
    method: "POST",
    data,
  });
};

export const copyQuiz = async (quizId) => {
  return await http({
    url: apiUrl(`/${quizId}/copy`, private_quiz_prefix),
    method: 'POST',
  });
};

export const updateQuizTitle = async (quizId, title) => {
  return await http({
    url: apiUrl(`/${quizId}/update-title`, private_quiz_prefix),
    method: 'PUT',
    data: { title },
  });
};

export const removeQuizFromFolder = async (quizId) => {
  return await http({
    url: apiUrl(`/${quizId}/remove-from-folder`, private_quiz_prefix),
    method: 'PATCH',
  });
};

export const fetchAssignment = async (id) => {
  return await http({
    url: `/api/private/assignments/${id}`,
    method: 'get',
  });
};
