import { apiUrl, fetcher } from "@/lib/fetcher";

/*
  - fetcher với option cache: "force-cache" chỉ có tác dụng với server component
  - có thể call fetcher ở client component, lúc đó cache: "force-cache" sẽ không có tác dụng
  - có thể call http ở server component và sẽ ko cache kết quả (muốn cache có thể  dùng 'use cache' hoặc unstable_cache)
*/

export const fetchTreeWorksheets = async (queryParams = {}) => {
  return await fetcher({
    url: apiUrl('/api/worksheets/sidebar'),
    method: "GET",
    queryParams,
  });
};

export const searchWorksheets = async (queryParams = {}) => {
  return await fetcher({
    url: apiUrl('/api/worksheets/search'),
    method: "GET",
    queryParams,
  });
};
