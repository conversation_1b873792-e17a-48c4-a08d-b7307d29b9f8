import { http } from "@/lib/axios";
import { apiUrl } from "@/lib/fetcher";

const url_prefix = "/api/private/collections";

/*
  - fetcher với option cache: "force-cache" chỉ có tác dụng với server component
  - c<PERSON> thể call fetcher ở client component, lúc đó cache: "force-cache" sẽ không có tác dụng
  - c<PERSON> thể call http ở server component và sẽ ko cache kết quả (muốn cache có thể  dùng 'use cache' hoặc unstable_cache)
*/

export const fetchCollections = async (params, signal) => {
  return await http({
    url: apiUrl("/", url_prefix),
    method: "GET",
    params,
    signal,
  });
};

export const fetchCollection = async (id, params, signal) => {
  return await http({
    url: apiUrl(`/${id}`, url_prefix),
    method: "GET",
    params,
    signal,
  });
};

export const storeCollection = async (data) => {
  return await http({
    url: apiUrl("/", url_prefix),
    method: "POST",
    data,
  });
};

export const addQuizToCollection = async (collectionId, quizId) => {
  return await http({
    url: apiUrl(`/${collectionId}/add-quiz/${quizId}`, url_prefix),
    method: "POST",
  });
};

export const removeQuizFromCollection = async (collectionId, quizId) => {
  return await http({
    url: apiUrl(`/${collectionId}/remove-quiz/${quizId}`, url_prefix),
    method: "DELETE",
  });
};

export const updateCollection = async (collectionId, data) => {
  return await http({
    url: apiUrl(`/${collectionId}`, url_prefix),
    method: "PUT",
    data,
  });
};

export const deleteCollection = async (collectionId) => {
  return await http({
    url: apiUrl(`/${collectionId}`, url_prefix),
    method: "DELETE",
  });
};
