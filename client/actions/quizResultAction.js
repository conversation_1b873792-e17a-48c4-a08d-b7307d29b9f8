import { apiUrl, fetcher } from "@/lib/fetcher";
import { http } from "@/lib/axios";

const url_prefix = "/api/play-quiz";

/*
  - fetcher với option cache: "force-cache" chỉ có tác dụng với server component
  - <PERSON><PERSON> thể call fetcher ở client component, lúc đó cache: "force-cache" sẽ không có tác dụng
  - có thể call http ở server component và sẽ ko cache kết quả (muốn cache có thể  dùng 'use cache' hoặc unstable_cache)
*/

export const gameInfo = async (quizSessionId, token) => {
  return await fetcher({
    url: apiUrl(`/${quizSessionId}/info`, url_prefix),
    method: 'GET',
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
};

export const soloJoin = async (data) => {
  return await http({
    url: apiUrl('/soloJoin', url_prefix),
    method: 'POST',
    data,
    headers: {
      refreshCsrfcookie: true,
    },
  });
};

export const soloProceed = async (quizSessionId, data) => {
  return await http({
    url: apiUrl(`/${quizSessionId}/soloProceed`, url_prefix),
    method: 'POST',
    data,
  });
};

export const soloEnd = async (quizSessionId, data) => {
  return await http({
    url: apiUrl(`/${quizSessionId}/soloEnd`, url_prefix),
    method: 'POST',
    data: data,
  });
};

export const fetchQuizResult = async (quizResultId) => {
  return await http({
    url: apiUrl(`/${quizResultId}`, url_prefix),
    method: 'GET',
  });
};
