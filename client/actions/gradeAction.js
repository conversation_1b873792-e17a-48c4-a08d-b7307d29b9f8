import { unstable_cache } from 'next/cache';
import { apiUrl, fetcher } from "@/lib/fetcher";

const url_prefix = "/api/grades";

/*
  - fetcher với option cache: "force-cache" chỉ có tác dụng với server component
  - <PERSON><PERSON> thể call fetcher ở client component, lúc đó cache: "force-cache" sẽ không có tác dụng
  - c<PERSON> thể call http ở server component và sẽ ko cache kết quả (muốn cache có thể  dùng 'use cache' hoặc unstable_cache)
*/

export const fetchGrades = unstable_cache(
  async () => {
    return await fetcher({
      url: apiUrl("/", url_prefix),
      method: "GET",
      nextOption: {
        cache: "force-cache",
        next: { revalidate: 3600 }
      },
    });
  },
  [],
  { revalidate: 3600 }
);

export const fetchGradeBySlug = async (gradeSlug) => {
  return await fetcher({
    url: apiUrl(`/${gradeSlug}/show`, url_prefix),
    method: "GET",
  });
};
