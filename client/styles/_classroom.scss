.ass-status-label {
  padding: 4px 8px;
  border-radius: 4px;
  white-space: nowrap;

  &.success {
    color: #02754e;
    background-color: #00c98533;
    border-left: 3px solid #00c985;
  }

  &.danger {
    color: #e01212;
    background-color: #ffe1e1;
    border-left: 3px solid #e01212;
  }

  &.info {
    color: #1e88e5;
    background-color: #0dcaf042;
    border-left: 3px solid #1e88e5;
  }

  &.warning {
    color: #ff8707;
    background-color: #ffde0752;
    border-left: 3px solid #ffc107;
  }

  &.orange {
    color: #ff0000;
    background-color: #ffeba8;
    border-left: 3px solid #ff8307;
  }
}

.classroom-label {
  padding: 4px 8px;
  border-radius: 4px;
  width: 80px;
  font-weight: 500;
  color: rgb(111, 17, 211);
  background-color: rgb(237, 230, 246);
  border-left-color: rgb(111, 17, 211);
  border-left: 3px solid #9d69d4;
}

.q-ass-results {
  .q-ass-title {
    font-weight: 500;
    font-size: 15px;
    line-height: 1.2;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: 1;
  }

  .stats-top {
    background: rgb(249, 249, 249);
    border-bottom: 1px solid rgb(229, 229, 229);

    .q-ass-label {
      padding: 6px 8px;
      border-radius: 4px;
      font-weight: 500;
      max-width: 83px;
      width: 100%;
      color: #02754e;
      background-color: #00c98533;
      border-left-color: #00c985;
      border-left: 3px solid #00c985;
    }
  }

  .stats-bottom {
    .stats-box-items {
      .stat-box-item {
        padding: 0.5rem;
        border-radius: 0.5rem;
        border: 1px solid rgb(229, 229, 229);
        width: 23.6%;
        display: flex;
        gap: 8px;

        .icon {
          width: 44px;
          height: 44px;
          border-radius: 4px;
          background: rgba(9, 9, 9, 0.05);
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .data {
          flex: 1;

          .label {
            color: rgb(109, 109, 109);
            margin-bottom: 4px;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
            -webkit-line-clamp: 1;
          }

          .value {
            font-weight: 500;
            font-size: 16px;
          }
        }
      }
    }

    .class-insight {
      gap: 16px;
      flex-wrap: wrap;

      .class-insight-label {
        font-weight: 700;
      }

      .insight-stats {
        gap: 12px;
        flex-wrap: wrap;

        .completed {
          svg {
            font-size: 1rem;
            margin-top: 1px;
            color: rgb(0, 201, 133);
          }
        }

        .incomplete {
          svg {
            font-size: 1rem;
            margin-top: 1px;
            color: rgb(239, 60, 105);
          }
        }

        .unattempted {
          svg {
            font-size: 1rem;
            margin-top: 1px;
            color: rgb(109, 109, 109);
          }
        }
      }
    }
  }
}

@media (max-width: 1400px) {
  .q-ass-results {
    .stats-bottom {
      .stats-box-items {
        flex-wrap: wrap;
        gap: 16px 0;

        .stat-box-item {
          width: 48.6%;
        }
      }
    }

    .q-ass-results-skeleton {
      height: 309px;
    }
  }
}

@media (max-width: 496px) {
  .q-ass-results {
    .q-ass-results-skeleton {
      height: 380px;
    }
  }
}

@media (max-width: 400px) {
  .q-ass-results {
    .stats-bottom {
      .stats-box-items {
        .stat-box-item {
          width: 100%;
        }
      }
    }
  }
}

.error-message{
  font-size: 16px;
  margin-top: -18px;
  margin-bottom: 18px;
  color: #f7e0e5;
  background-color: #d5546d;
  padding: 0.25rem 0.5rem;
  border-radius: 0.5rem;
  width: 50%;
  max-width: 700px;
  text-align: center;
}
