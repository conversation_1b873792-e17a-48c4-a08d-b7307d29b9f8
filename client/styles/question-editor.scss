.distractor-option {
  transition: all 0.2s ease;
  position: relative;

  &:hover {
    background-color: #f8f9fa;
    border-radius: 4px;

    .btn-delete-distractor {
      opacity: 1;
      visibility: visible;
    }
  }
}

.distractor-item-wrapper {
  width: 100%;
  padding: 4px 8px;
  border-radius: 3px;
  transition: all 0.2s ease;
}

.distractor-content {
  cursor: pointer;
}

.btn-delete-distractor {
  top: -6px;
  right: -28px;
  transform: translateY(-50%);
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s ease;
  color: #fff;
  background-color: #333;
  border: 1px solid gray;
  font-size: 14px;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  z-index: 10;

  &:hover {
    background-color: #dc3545;
    color: white;
  }
}

.distractor-input-container {
  min-height: 38px;

  input {
    border: 2px solid #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);

    &:focus {
      border-color: #0056b3;
      box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.5);
    }
  }
}

.q-option-blank {
  min-height: 40px;
}

#btn-add-distractor {
  transition: all 0.2s ease;
  min-height: 40px;

  &.cursor-not-allowed {
    background-color: #e5e5e5;
    opacity: 0.6;
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

.cursor-not-allowed {
  cursor: not-allowed;
}
