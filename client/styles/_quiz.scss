.quiz-name {
  font-weight: 400;
  font-size: 18px;
  line-height: 1.2;
  margin: 0 0 .5rem;
}

.text-limit {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: var(--lines, 2);
}

.card-quiz-banner {
  display: block;
  -webkit-background-size: cover;
  background-size: cover;
  background-repeat: no-repeat;
  -webkit-background-position: center;
  background-position: center;
  height: 140px;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=)
    #eee no-repeat 50% / cover;
}

.quiz-banner {
  position: relative;
  border: 2px solid #fff;
  width: 150px;
  height: 150px;
  overflow: hidden;
  padding: 5px;

  button {
    background-color: #8f5aed;
    position: absolute;
    top: 5px;
    right: 5px;
    // display: none;
    // left: 50%;
    // top: 50%;
    // transform: translate(-50%, -50%);
  }

  &:hover,
  &.show {
    border: 2px solid #8854c09c;

    button {
      display: inline-flex;
    }
  }

  img {
    object-fit: contain;
    width: 100%;
    height: 100%;
  }
}

.quiz-related {
  .quiz-questions-count {
    background-color: #09090963;
    color: white;
    border-radius: 4px;
    padding: 0 4px;
    position: absolute;
    bottom: 5px;
    right: 5px;
    font-size: 10px;
  }
}

.question-editor-wrapper {
  margin-top: 70px;
  height: 100%;
  width: 100%;
  padding: 1.5rem;
  background-color: #f8f9fa;
  display: flex;
  align-items: center;

  .question-editor-main {
    max-width: 1280px;
    width: 100%;
    height: 100%;
    margin: auto;

    .question-editor-body {
      background-color: #461a42;
      border-radius: 1rem;
      width: 100%;
      padding: 1rem;

      .question-editor {
        aspect-ratio: 16 / 9;
        display: grid;
        grid-template-rows: repeat(2, minmax(0, 1fr));
        height: 100%;

        .q-content {
          background-color: rgba(9, 9, 9, 0.5);
          color: #fff;
          border: 1px solid #8854c0;
          border-radius: 0.5rem;
          display: grid;
          align-items: center;
          cursor: text;
          padding: 10px;
          font-size: 24px;
          line-height: 1.5;
          overflow: auto;
          text-align: center;
          min-height: 200px;

          img {
            border-radius: 4px;
            background: #fff;
            object-fit: contain;
            height: 100%;
            padding: 4px;
            margin: 0 8px;
            max-width: 100%;
          }
        }

        .q-option-content {
          background-color: rgba(9, 9, 9, 0.5);
          width: 100%;
          height: 100%;
          display: grid;
          align-items: center;
          font-size: 24px;
          line-height: 1.5;
          overflow-y: auto;
          cursor: text;
          padding: 5px;
          text-align: center;

          img {
            border-radius: 4px;
            background: #fff;
            object-fit: contain;
            height: 100%;
            padding: 4px;
            margin: 0 8px;
            max-width: 100%;
          }
        }

        .q-option-container {
          display: grid;
          gap: 0.5rem;
          grid-auto-flow: column;
          grid-auto-columns: minmax(0, 1fr);
          width: 100%;
          height: 100%;
        }

        .q-option-item {
          overflow-y: auto;
          height: 100%;
          position: relative;
          display: flex;
          flex-direction: column;
          padding: 0.25rem;
          gap: 0.5rem;
          background-color: #09090980;
          color: #fff;
          border-radius: 0.5rem;
          min-height: 100px;

          .q-option-action {
            width: 100%;
            font-size: 18px;
          }
        }

        .q-answer-quizs {
          display: flex;
          gap: 7px;
          align-items: center;
          padding: 1rem 0;

          .btn-add-option span {
            display: none;
          }
        }

        .q-answer-blank {
          background-color: #ffffff0d;
          color: #fff;
          margin-top: 20px;
          padding: 10px;
          border: 1px solid #fff3;
          border-radius: .5rem;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          overflow-y: auto;
          font-size: 24px;
          line-height: 1.5;
        }

        .q-options-blank {
          display: flex;
          align-items: center;
          gap: 1rem;
        }

        .q-option-blank {
          border-radius: 8px;
          border: 3px solid #2d9da6;
          font-weight: 700;
          padding: 5px 1.25rem;
          background-color: rgba(255, 255, 255, 1);
          color: rgba(9, 9, 9, 1);
          display: flex;
          justify-content: center;
          align-items: center;
          letter-spacing: -.01em;
          font-size: .875rem;
          line-height: 20px;
        }
      }

      .q-placeholder {
        color: rgba(255, 255, 255, 0.45);
        margin: 0;
      }
    }
  }
}

.question-wrapper {
  position: relative;

  .q-content {
    color: #fff;
    display: grid;
    align-items: center;
    font-size: 24px;
    line-height: 1.5;
    overflow: auto;
    height: 100%;
    text-align: center;
    padding: 20px 0;

    img {
      border-radius: 4px;
      background: #fff;
      object-fit: contain;
      height: 100%;
      padding: 4px;
      margin: 0 8px;
      max-width: 100%;
    }
  }

  .q-option-content {
    display: grid;
    align-items: center;
    font-size: 28px;
    line-height: 1.5;
    overflow-y: auto;
    padding: 5px;
    text-align: center;
    width: 100%;
    height: 100%;

    img {
      border-radius: 4px;
      background: #fff;
      object-fit: contain;
      height: 100%;
      padding: 4px;
      margin: 0 8px;
      max-width: 100%;
    }
  }

  &:not(:has(.flashcard-container)) {
    overflow-y: auto;
  }

  .question-box {
    height: 100%;

    .q-option-container {
      display: grid;
      gap: .5rem;
      grid-auto-flow: column;
      grid-auto-columns: minmax(0, 1fr);
      width: 100%;
      height: 100%;
      padding: 10px;
      padding-bottom: 20px;
    }

    .q-option-item {
      overflow-y: auto;
      height: 100%;
      position: relative;
      display: flex;
      flex-direction: column;
      padding: .25rem;
      gap: .5rem;
      background-color: #09090980;
      color: #fff;
      border-radius: .5rem;
      transition: all 0.35s;
      cursor: pointer;

      .icon-check {
        background-color: #8854c0;
        color: #fff;
        font-size: 22px;
        opacity: 0;
      }

      &:hover {
        // transform: translateY(-0.25em);
        box-shadow: 0 0 0.5em 0em #fff;
      }

      &.active {
        transform: translateY(7px);
        box-shadow: none;

        .icon-check {
          opacity: 1;
        }
      }

      &.disable {
        opacity: 0;
        pointer-events: none;
        cursor: not-allowed;
      }

      &.isCorrect {
        background-color: #62c370 !important;

        &:not(.active) {
          .icon-check {
            background-color: #e63946;
            color: #fff;
            opacity: 1;

            .fa-check:before {
              content: "\f068";
            }
          }
        }
      }

      &.isWrong {
        background-color: #e63946 !important;
      }
    }
  }

  .flashcard-container {
    position: relative;
    padding: 15px;
    height: 100%;
    margin: auto;
    display: flex;
    flex-wrap: nowrap;
    place-content: center;
    align-items: center;
    flex-direction: column;

    .flashcard-box {
      margin: 1rem 0;
      perspective: 600px;
      flex: 1 0;
      height: 100%;
      width: 100%;
      max-height: 382px;
      max-width: 680px;

      .flashcard {
        position: relative;
        width: 100%;
        height: 100%;
        text-align: center;
        transition: transform 1s;
        transform-style: preserve-3d;

        .flashcard-content {
          border: 1px solid #ffffff0d;
          background-color: #181818;
          color: #fff;
          position: absolute;
          width: 100%;
          height: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
          border-radius: 12px;
          overflow-wrap: anywhere;
          padding: 1.5rem;

          .q-answer {
            height: 100%;
            width: 100%;
            padding: 10px;

            .q-option-container {
              height: 100%;
              overflow-y: auto;
            }

            .q-option-content {
              border: 1px solid #555;
              border-radius: 5px;
              padding-top: 1.25rem;
            }
          }
        }

        .flashcard-back {
          transform: rotateY(180deg);
        }

        .answer-container {
          height: 100%;
          width: 100%;
          background-color: #efa92933;
          border: 1px solid #efa92933;
        }
      }

      &.is-flipped .flashcard {
        transform: rotateY(180deg);
      }
    }
  }

  .btn-rt {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 10;
    color: #dcd9d9;
    font-size: 16px;
    padding: 4px 10px;
  }
}

.screen-container {
  max-width: 550px;
  margin: 0 auto;
  padding: 20px 0;
  display: flex;
  flex-direction: column;
  height: 100%;

  .screen-summary {
    padding: 24px 8px 0;
    position: relative;
    background-color: #00000080;
    display: flex;
    flex-direction: column;

    .main-section {
      padding: 16px;

      .top-section-wrapper {
        max-width: 480px;
        margin: 0 auto;
        position: relative;
      }

      .middle-section-wrapper {
        margin-top: 40px;
        animation-delay: .5s;

        .middle-section {
          color: #fff;
          text-align: center;
          max-width: 480px;
          margin: 0 auto;
        }

        .bg-image {
          background-image: url(https://cf.quizizz.com/image/student_summary_sprite_full.png);
          background-size: 328px;
          width: 68px;
          height: 72px;
          position: absolute;
          bottom: 0;
        }
      }
    }

    .review-section-wrapper {
      box-sizing: border-box;
      z-index: 1;
      max-width: 512px;
      transition: all .3s;
      position: relative;

      .review-section {
        box-sizing: border-box;
        border-radius: 8px;
        margin: 0;
        background: #000;
      }
    }

    .stat-box {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 5px;
      padding: 15px 10px;
      margin-bottom: 15px;
      background-color: #3a3a3a;
      border: 1px solid #3a3a3a;
      border-radius: 8px;

      .value {
        font-size: 32px;
        font-weight: 600;
      }

      .label {
        color: #ffffffc4;
        font-size: 15px;
      }
    }

    .correct-box {
      background-color: #258c2a;
    }

    .notdo-box {
      background-color: #7a7a7a;
    }

    .incorrect-box {
      background-color: #e75100;
    }
  }
}

@media (max-width: 1200px) {
  .question-editor-wrapper {
    height: auto;
    padding: 0.5rem;

    .question-editor-main {
      .question-editor-body {
        .question-editor {
          grid-template-rows: auto;
          aspect-ratio: auto;

          .q-option-container {
            grid-auto-flow: row;
          }

          .q-answer-quizs {
            display: block;
            padding-bottom: 2rem;

            .btn-add-option {
              margin-top: 10px;
              width: 100%;

              span {
                display: inline;
              }
            }
          }
        }
      }
    }
  }

  .question-wrapper {
    .question-box {
      height: auto;
      .h-50 {
        height: auto !important;
      }
      .q-option-container {
        grid-auto-flow: row;
        overflow-y: auto;

        .q-option-item {
          overflow: initial;

          &.active {
            transform: none;
          }
        }
      }
    }
  }
}

.slogan-noti {
  position: fixed;
  z-index: 1200;
  left: auto;
  right: 0;
  top: auto;
  bottom: -70px;
  min-height: 70px;
  width: 100%;
  background-color: transparent;
  color: #fff;
  padding: .5rem 0;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-sizing: border-box;
  justify-content: center;
  transition: all .5s;
  font-size: 20px;
  font-weight: 600;
  line-height: 1.35;

  &.slogan-success {
    background-color: rgb(0, 201, 133);
  }

  &.slogan-danger {
    background-color: rgb(236, 11, 67);
  }

  &.show {
    bottom: 0;
    transition: all .5s;
  }

  .close-item-icon {
    position: absolute;
    cursor: pointer;
    top: 10px;
    right: 0;
    margin-right: 20px;
  }
}

.submit-button {
  color: #fff;
  background: #8854c0;
  padding: 15px;
  transition: all 0.3s ease;
  box-shadow: inset 2px 2px 2px 0px rgba(255, 255, 255, 0.5),
    7px 7px 20px 0px rgba(0, 0, 0, 0.1), 4px 4px 5px 0px rgba(0, 0, 0, 0.1);
  outline: none;

  &:hover {
    background-color: #9567c7;
  }

  &:active {
    box-shadow: 4px 4px 6px 0 rgba(255, 255, 255, 0.5),
      -4px -4px 6px 0 rgba(116, 125, 136, 0.5),
      inset -4px -4px 6px 0 rgba(255, 255, 255, 0.2),
      inset 4px 4px 6px 0 rgba(0, 0, 0, 0.4);
  }

  &:disabled {
    color: #ffffff26;
    background: #474747;
    transition: all 0.3s;
    box-shadow: inset 2px 2px 2px 0px rgba(255, 255, 255, 0.5),
      7px 7px 20px 0px rgba(0, 0, 0, 0.1), 4px 4px 5px 0px rgba(0, 0, 0, 0.1);
  }
}

.box-content {
  counter-reset: blanks;

  p:last-child {
    margin-bottom: 0;
  }

  span.flag-el {
    counter-increment: blanks;
    display: inline-block;
    line-height: 1;

    &::before {
      content: "(" counter(blanks, lower-alpha) ") ";
      // margin-right: 0.25em;
      color: #666;
      font-weight: 400;
    }
  }
}

// Edit Quiz
.import-actions-container {
  justify-content: center;

  .section-title {
    font-weight: 600;
    padding: 1rem 0.75rem 0.5rem;
  }

  .import-quiz-btn {
    padding: .75rem;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    display: flex;

    &:hover {
      background-color: #f1f1f1;
    }

    span {
      color: #090909;
      font-size: 14px;
      font-weight: 500;
      line-height: 1.5rem;
    }

    .icon {
      margin-right: .75rem;
      font-size: 18px;
    }

    i {
      font-size: 16px;
    }
  }
}

.file-icon {
  font-size: 48px;
  margin-bottom: 0.75rem;
}

.file-icon-bg {
  font-size: 40px;
  color: #8854c0;
  background: rgba(246, 240, 255, 1);
  border-radius: 50%;
  padding: 0.5rem;
}

.file-excel {
  color: #00a06a !important;
}

.file-word {
  color: #1976d2 !important;
}

// Print settings header styles
.print-settings-header {
  border-radius: 0 !important;

  .MuiFormControlLabel-root {
    margin-right: 0;

    .MuiFormControlLabel-label {
      font-size: 14px;
      color: #495057;
    }
  }

  .MuiRadio-root {
    padding: 4px 8px;
  }

  .MuiFormLabel-root {
    color: #495057;
    font-size: 14px;
  }
}

.quiz-preview-container {
  overflow-x: hidden;
  color: #333;
  background-color: #fff;
  margin: 0 auto;
  max-width: 800px;
  border: 1px solid #09090933;
  border-radius: 8px;
  margin: 10px;

  // Font size classes
  &.font-size-small {
    font-size: 12px;

    .question-content, .box-content {
      font-size: 12px;
    }

    .question-number {
      font-size: 12px;
    }
  }

  &.font-size-medium {
    font-size: 14px;

    .question-content, .box-content {
      font-size: 14px;
    }

    .question-number {
      font-size: 14px;
    }
  }

  &.font-size-large {
    font-size: 16px;

    .question-content, .box-content {
      font-size: 16px;
    }

    .question-number {
      font-size: 16px;
    }
  }

  &.font-size-extra-large {
    font-size: 18px;

    .question-content, .box-content {
      font-size: 18px;
    }

    .question-number {
      font-size: 18px;
    }
  }

  .print-header {
    display: flex;
    justify-content: space-between;
    padding: 24px 32px;
    gap: 40px;
    background-color: #f9f9f9;
  }

  .header-left {
    display: flex;
    flex-direction: column;
  }

  .header-right {
    min-width: 250px;
  }

  .logo-container {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
  }

  .logo-title {
    font-size: 16px;
    font-weight: bold;
    margin-top: 7px;
    margin-bottom: 0;
  }

  .info-line {
    display: flex;
    margin-bottom: 10px;
  }

  .info-label {
    width: 60px;
    font-weight: bold;
    font-size: 12px;
  }

  .info-value {
    flex: 1;
    border-bottom: 1px solid #ccc;
    font-size: 12px;
  }

  .questions-container {
    padding: 24px 32px;
    margin-left: 20px;
  }

  .question-item {
    margin-bottom: 25px;
    position: relative;
    padding-left: 15px;
  }

  .question-number {
    position: absolute;
    left: -20px;
    top: 0;
  }

  .question-content {
    display: inline;
  }

  .options-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    margin-top: 10px;
  }

  .option-item {
    display: flex;
    align-items: center;
    margin: 5px 0;
  }

  .option-box {
    width: 25px;
    height: 25px;
    border: 1px solid #ccc;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 10px;
    color: #999;
    font-weight: normal;
  }

  .box-content {
    flex: 1;
  }

  img {
    max-width: 100%;
    height: auto;
  }

  // Answer key section styles
  .answer-key-section {
    margin-top: 40px;
    padding: 20px;
    page-break-before: always;

    .answer-key-title {
      margin-top: 32px;
      margin-bottom: 16px;
      font-weight: bold;
      text-align: center;
      font-size: 1.25rem;
      line-height: 1.6;
      letter-spacing: 0.0075em;
    }

    .answer-key-grid {
      display: grid;
      grid-template-columns: repeat(10, 1fr);
      gap: 10px;
      margin-top: 20px;

      .answer-key-item {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
        background-color: #f8f9fa;

        .question-num {
          font-weight: bold;
          margin-right: 5px;
          min-width: 20px;
        }

        .answer {
          font-weight: bold;
          color: #0d6efd;
        }
      }
    }
  }

  @media (max-width: 600px) {
    .print-header {
      flex-direction: column;
      padding: 16px;
    }

    .questions-container {
      padding: 16px;
    }

    .options-container {
      grid-template-columns: 1fr;
    }

    .answer-key-grid {
      grid-template-columns: repeat(5, 1fr) !important;
    }
  }
}

@media print {
  .print-settings-header {
    display: none !important;
  }

  .print-header {
    margin: 5px 5px 30px !important;
  }

  .option-box {
    border: none !important;
    color: #000 !important;

    .print-dot {
      display: inline !important;
    }
  }

  .answer-key-section {
    page-break-before: always;

    .answer-key-grid {
      grid-template-columns: repeat(5, 1fr);
    }
  }
}

.question-explain:hover {
  text-decoration: underline;
  color: #8854c0 !important;
  cursor: pointer;
}

.q-separato {
  color: #666f74;
  font-size: 16px;
  margin-bottom: 20px;
  margin-top: 20px;
  position: relative;
  text-align: center;
}

.q-separato>:first-child {
  background-color: #fff;
  display: inline-block;
  padding: 0 15px;
  position: relative;
}

.q-separato::before {
  border-top: 1px solid #ebebeb;
  bottom: 0;
  content: "";
  left: 0;
  margin: 0 auto;
  position: absolute;
  right: 0;
  top: 50%;
  width: 95%;
  z-index: 0;
}

.btn-download-quiz {
  text-decoration: none;
  padding: 1px 10px;

  &:hover {
    text-decoration: underline;
  }
}

.custom-style-shareq {
  font-weight: 600 !important;
}

// QuizResultsPage
.game-type-title {
  color: #fff;
  background-color: #ffffff1a;
  font-size: 12px;
  border-radius: 0.25rem;
  padding: 0.25rem 0.75rem;
  margin-top: 0.5rem;
}

.flashcards-cta {
  align-items: center;
  display: flex;
  align-self: center;
  margin-right: 0;

  .review-flashcard-btn {
    background-color: #ffffff54;
    padding: 8px 12px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 700;

    &:hover {
      background-color: #ffffff80;
    }
  }
}

.correct-selected:before, .wrong-selected:before, .user-selected:before {
  content: "Bạn chọn";
  color: #4a4a4a;
  background-color: rgba(165,205,237,0.2);
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
  height: 18px;
  padding: 1px 8px 0;
  font-size: 13px;
  position: absolute;
  top: -18px;
  left: 8px;
}

.rounded-xl {
  border-radius: 0.75rem !important;
}

.anim-400-duration {
  animation-duration: .4s;
}

.fadeInUp {
  animation-name: fadeInUp;
}

.animated {
  animation-duration: 1s;
  animation-fill-mode: both;
}

.determinate-progress {
  .MuiLinearProgress-bar {
    background-image: repeating-linear-gradient(135deg, transparent, transparent 6px, #A076CC 6px, #A076CC 15px);
    animation: slide-animation 50s linear infinite;
    transition: transform .2s;
    transform: translate(-100%);
  }
}

@keyframes slide-animation {
  0% {
    background-position-x: 0;
  }

  100% {
    background-position-x: 2000px;
  }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(100%);
  }

  100% {
    opacity: 1;
    transform: none;
  }
}

@media screen and (max-width: 992px) {
  .question-editor-wrapper {
    .question-editor-main {
      .question-editor-body {
        .question-editor {
          .q-answer-blank {
            font-size: 18px;
          }
        }
      }
    }
  }
}

@media screen and (max-width: 768px) {
  .question-wrapper {
    .q-content {
      font-size: 22px;
    }

    .q-option-content {
      font-size: 22px;
    }

    .flashcard-container {
      .flashcard-box {
        max-height: 420px;
      }
    }
  }
}
