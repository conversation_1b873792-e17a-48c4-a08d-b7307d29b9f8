// theme classic
.theme-classic {
  background-image: url(/assets/themes/classic/joinClassicWBg.jpg);
  background-size: cover;
  background-position: center;
}

// theme synthwave
.theme-synthwave {
  background-image: url(/assets/themes/synthwave/bg_image.jpg);
  --theme-bg-image: url(/assets/themes/synthwave/bg_image.jpg);

  .cursor-pointer {
    cursor: url(/assets/themes/synthwave/synth_pointer_v2.svg) 1 2, pointer !important;
  }
}

.theme-synthwave, .theme-synthwave .cursor-auto {
  cursor: url(/assets/themes/synthwave/synth_cursor_v2.svg) 2 5, auto;
}

// theme anime
.theme-anime {
  background-image: url(/assets/themes/anime/bg_image.jpg);
  --theme-bg-image: url(/assets/themes/anime/bg_image.jpg);

  .cursor-pointer {
    cursor: url(/assets/themes/anime/impala_pointer_v2.svg) 1 2, pointer !important;
  }
}

.theme-anime, .theme-anime .cursor-auto {
  cursor: url(/assets/themes/anime/impala_cursor_v2.svg) 2 5, auto;
}

// theme cosmic-picnic
.theme-cosmic-picnic {
  background-image: url(/assets/themes/cosmic-picnic/bg_image.jpg);
  --theme-bg-image: url(/assets/themes/cosmic-picnic/bg_image.jpg);

  .cursor-pointer {
    cursor: url(/assets/themes/cosmic-picnic/cosmic_pointer_v2.svg) 1 2, pointer !important;
  }
}

.theme-cosmic-picnic, .theme-cosmic-picnic .cursor-auto {
  cursor: url(/assets/themes/cosmic-picnic/cosmic_cursor_v2.svg) 2 5, auto;
}

// theme dogsville
.theme-dogsville {
  background-image: url(/assets/themes/dogsville/bg_image.jpg);
  --theme-bg-image: url(/assets/themes/dogsville/bg_image.jpg);

  .cursor-pointer {
    cursor: url(/assets/themes/dogsville/dog_pointer_v2.svg) 1 2, pointer !important;
  }
}

.theme-dogsville, .theme-dogsville .cursor-auto {
  cursor: url(/assets/themes/dogsville/dog_cursor_v2.svg) 2 5, auto;
}

// theme football
.theme-football {
  background-image: url(/assets/themes/football/bg_image.jpg);

  .cursor-pointer {
    cursor: url(/assets/themes/football/football_pointer_v2.svg) 1 2, pointer !important;
  }
}

.theme-football, .theme-football .cursor-auto {
  cursor: url(/assets/themes/football/football_cursor_v2.svg) 2 5, auto;
}

// theme the8Bit
.theme-the8Bit {
  background-image: url(/assets/themes/the8Bit/bg_image.jpg);

  .cursor-pointer {
    cursor: url(/assets/themes/the8Bit/8bit_pointer_v2.svg) 1 2, pointer !important;
  }
}

.theme-the8Bit, .theme-the8Bit .cursor-auto {
  cursor: url(/assets/themes/the8Bit/8bit_cursor_v2.svg) 2 5, auto;
}

// theme testmode
.theme-testmode {
  background-image: url(/assets/themes/testmode/bg_image.jpg);
  --theme-bg-image: url(/assets/themes/testmode/bg_image.jpg);
}
