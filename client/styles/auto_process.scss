// Auto Progress Bar styles
.auto-progress-container {
  margin: 16px 0;
  padding: 16px;
  border-radius: 8px;
  backdrop-filter: blur(10px);

  @media (max-width: 768px) {
    margin: 12px 0;
    padding: 12px;
    font-size: 14px;
  }

  .progress-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 8px;
      align-items: stretch;
    }

    .progress-text {
      color: #fff;
      font-size: 14px;
      font-weight: 500;

      @media (max-width: 768px) {
        text-align: center;
        font-size: 13px;
      }
    }

    .skip-button {
      min-width: auto;
      padding: 4px 12px;
      font-size: 12px;
      border-radius: 16px;
      text-transform: none;

      @media (max-width: 768px) {
        padding: 8px 16px;
        font-size: 13px;
      }
    }
  }

  .progress-bar {
    height: 6px;
    border-radius: 3px;
    overflow: hidden;

    @media (max-width: 768px) {
      height: 8px;
      border-radius: 4px;
    }

    .MuiLinearProgress-bar {
      transition: transform 0.1s linear;
    }
  }
}

// Correct answer progress (green)
.auto-progress-correct {
  background-color: rgba(46, 204, 113, 0.1);
  border: 1px solid rgba(46, 204, 113, 0.2);

  .skip-button {
    color: #2ecc71;
    border-color: #2ecc71;

    &:hover {
      background-color: rgba(46, 204, 113, 0.2);
      border-color: #2ecc71;
    }
  }
}

// Wrong answer progress (red)
.auto-progress-wrong {
  background-color: rgba(231, 76, 60, 0.1);
  border: 1px solid rgba(231, 76, 60, 0.2);

  .skip-button {
    color: #e74c3c;
    border-color: #e74c3c;

    &:hover {
      background-color: rgba(231, 76, 60, 0.2);
      border-color: #e74c3c;
    }
  }
}
