.my-collection {
  height: 100%;
  position: -webkit-sticky;
  position: sticky;
  top: 80px;

  .collection-group {
    max-height: calc(100vh - 200px);

    .collection-item {
      background-color: #fff;
      border: 1px solid #eee;
      border-radius: 5px;
      padding: 1rem;
      cursor: pointer;
      margin-bottom: 10px;

      &:hover {
        background-color: #7b1fa21a;
      }

      &.active {
        color: #5e35b1;
        background-color: #ede7f6;
      }
    }
  }
}

@media only screen and (max-width: 768px) {
  .my-collection {
    position: static;

    .collection-group {
      display: flex;
      flex-direction: row;
      flex-wrap: nowrap;
      align-items: center;

      .collection-item {
        flex-grow: 0;
        flex-shrink: 0;
        margin: 10px;

        &:first-child {
          margin-left: 0;
        }
      }
    }
  }
}
