/*
==============================================
slideDown
==============================================
*/
.slideDown {
  animation-name: slideDown;
  -webkit-animation-name: slideDown;
  animation-duration: 1s;
  -webkit-animation-duration: 1s;
  animation-timing-function: ease;
  -webkit-animation-timing-function: ease;
  visibility: visible !important;
  animation-count: 1;
}

@keyframes slideDown {
  0% {
    transform: translateY(-100%);
  }
  // 50% {
  //   transform: translateY(8%);
  // }
  // 65% {
  //   transform: translateY(-4%);
  // }
  // 80% {
  //   transform: translateY(4%);
  // }
  // 95% {
  //   transform: translateY(-2%);
  // }
  100% {
    transform: translateY(0%);
  }
}

@-webkit-keyframes slideDown {
  0% {
    -webkit-transform: translateY(-100%);
  }
  // 50% {
  //   transform: translateY(8%);
  // }
  // 65% {
  //   transform: translateY(-4%);
  // }
  // 80% {
  //   transform: translateY(4%);
  // }
  // 95% {
  //   transform: translateY(-2%);
  // }
  100% {
    -webkit-transform: translateY(0%);
  }
}

/*
==============================================
fadeIn
==============================================
*/

.fadeIn {
  animation-name: fadeIn;
  -webkit-animation-name: fadeIn;
  animation-duration: 1s;
  -webkit-animation-duration: 1s;
  animation-timing-function: ease-in-out;
  -webkit-animation-timing-function: ease-in-out;
  visibility: visible !important;
  animation-count: 1;
  // animation-delay: 1.5s;
}

@keyframes fadeIn {
  0% {
    transform: scale(0);
    opacity: 0.0;
  }
  60% {
    transform: scale(1.1);
  }
  80% {
    transform: scale(0.9);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@-webkit-keyframes fadeIn {
  0% {
    -webkit-transform: scale(0);
    opacity: 0.0;
  }
  60% {
    -webkit-transform: scale(1.1);
  }
  80% {
    -webkit-transform: scale(0.9);
    opacity: 1;
  }
  100% {
    -webkit-transform: scale(1);
    opacity: 1;
  }
}

/*
==============================================
fadeInRight
==============================================
*/

.fadeInRight {
  animation-name: fadeInRight;
  -webkit-animation-name: fadeInRight;
  animation-duration: .5s;
  -webkit-animation-duration: .5s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
  animation-timing-function: ease-in-out;
  -webkit-animation-timing-function: ease-in-out;
  visibility: visible !important;
  animation-count: 1;
  // animation-delay: 1.5s;
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
  }

  to {
    opacity: 1;
    -webkit-transform: none;
    transform: none;
  }
}

@-webkit-keyframes fadeInRight {
  from {
    opacity: 0;
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
  }

  to {
    opacity: 1;
    -webkit-transform: none;
    transform: none;
  }
}

/*
==============================================
fadeInLeft
==============================================
*/

.fadeInLeft {
  animation-name: fadeInLeft;
  -webkit-animation-name: fadeInLeft;
  animation-duration: .5s;
  -webkit-animation-duration: .5s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
  animation-timing-function: ease-in-out;
  -webkit-animation-timing-function: ease-in-out;
  visibility: visible !important;
  animation-count: 1;
  // animation-delay: 1.5s;
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
  }

  to {
    opacity: 1;
    -webkit-transform: none;
    transform: none;
  }
}

@-webkit-keyframes fadeInLeft {
  from {
    opacity: 0;
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
  }

  to {
    opacity: 1;
    -webkit-transform: none;
    transform: none;
  }
}

/*
==============================================
fadeOutLeft
==============================================
*/

.fadeOutLeft {
  animation-name: fadeOutLeft;
  -webkit-animation-name: fadeOutLeft;
  animation-duration: .5s;
  -webkit-animation-duration: .5s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
  animation-timing-function: ease-in-out;
  -webkit-animation-timing-function: ease-in-out;
  visibility: visible !important;
  animation-count: 1;
  /* animation-delay: 1.5s; */
}

@keyframes fadeOutLeft {
  from {
    opacity: 1;
    -webkit-transform: none;
    transform: none;
  }

  to {
    opacity: 0;
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
  }
}

@-webkit-keyframes fadeOutLeft {
  from {
    opacity: 1;
    -webkit-transform: none;
    transform: none;
  }

  to {
    opacity: 0;
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
  }
}
