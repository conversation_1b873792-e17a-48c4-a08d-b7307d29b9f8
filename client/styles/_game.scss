:root {
  --fb-default-bg: #8854C0;
  --fb-hover-bg: #AE7DD9;
  --fb-active-bg: #6A3DA5;
}

// MCQ Submitting styles
.question-box.submitting {
  .q-option-item {
    transition: opacity 0.3s ease, transform 0.3s ease;

    &.disabled {
      cursor: not-allowed !important;
      transform: scale(0.98);
    }
  }
}

.mcq-submitting-text {
  animation: pulse 1.5s ease-in-out infinite;

  i {
    animation: spin 1s linear infinite;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.7;
  }
  50% {
    opacity: 1;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.joinGame-input {
  input {
    font-size: 1.25rem;

    &::placeholder {
      font-size: 1.25rem !important;
    }
  }
}

.joinGame-button {
  font-size: 1.25rem;
  line-height: 1.75rem;
  color: #fff;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  background-color: var(--fb-default-bg);

  &:hover {
    background-color: var(--fb-hover-bg);
  }

  &:active {
    background-color: var(--fb-active-bg);
  }
}

.floatingButton {
  -webkit-box-shadow: 0 4px 0 var(--fb-default-bg);
  box-shadow: 0 4px 0 var(--fb-default-bg);
  transition: transform .2s, box-shadow .2s;
}

.featured-section-title {
  color: #292a3a;
  align-items: center;
  font-size: 22px;
  font-weight: 400;
  display: flex;
}

.see-all-link, .see-more-btn {
  color: var(--fb-default-bg);
  background-color: rgba(136, 84, 192, 0.1);
  font-size: 16px;
  font-weight: 600;
  border-radius: 4px;
  padding: 5px 12px;
  transition: 0.2s;

  &:hover {
    color: var(--fb-default-bg);
    background-color: rgba(136, 84, 192, 0.2);
  }
}

.see-all-link {
  display: inline-block;
  text-decoration: none;
}

.see-more-btn {
  white-space: nowrap;
  border: none;
  padding: 6px 12px !important;
}

.swiper-pagination-bullet-active {
  background-color: var(--fb-default-bg) !important;
}

.swiper-button-prev, .swiper-button-next {
  color: var(--fb-default-bg) !important;
  border: none !important;
}

.bg-ds-dark {
  background-color: #09090933;
}

.bg-ds-light {
  background-color: #fff3;

  &:hover {
    background-color: #ffffff4d;
  }

  &:active {
    background-color: #ffffff4d !important;
  }
}

.btn-play-game {
  color: #8854c0;
  background-color: #ede6f6;
  border-color: #8854c033;

  &:hover, &:active {
    color: #a076cc !important;
    background-color: #ede6f6 !important;
    border-color: #8854c033;
  }

  &:disabled {
    background-color: #8854c0 !important;
    color: #fff !important;
    border-color: #8854c0 !important;
    cursor: not-allowed;
  }
}

.btn-share-game {
  color: #222222;
  background-color: #0909090d;
  border-color: #09090933;

  &:hover, &:active {
    color: #424242 !important;
    background-color: #0909090d !important;
    border-color: #09090933;
  }

  span {
    font-family: Roboto, sans-serif;
  }
}

.quiz-and-host-info-card {
  background-image: linear-gradient(#ffffff14, #ffffff0a);
  border-radius: 16px;
}

.share-button {
  border: none;
  border-radius: 0.5rem;
  color: #fff !important;
  padding: 0.5rem 1rem;
  transition: 0.2s;
}

.primary-button, .mystic-peak-start-game-button {
  -webkit-box-shadow: 0 4px 0 0 #00a06a;
  box-shadow: 0 4px 0 0 #00a06a;
  background-color: #00c985;
  border: none;
  border-radius: 8px;
  justify-content: center;
  align-items: center;
  padding: 10px 30px;
  font-size: 18px;
  font-weight: 700;
  transition: all .2s;
  display: flex;
  position: relative;
  color: #fff !important;

  &:hover {
    background-color: #34d49d;
  }
}

.secondary-button {
  background-color: var(--fb-default-bg);
  -webkit-box-shadow: 0 4px 0 #6c4298;
  box-shadow: 0 4px 0 #6c4298;
  box-sizing: border-box;
  border: none;
  border-radius: 8px;
  justify-content: center;
  align-items: center;
  width: 100%;
  margin-top: 12px;
  padding: 10px 30px;
  font-size: 18px;
  font-weight: 700;
  transition: all .2s;
  display: flex;
  position: relative;
  color: #fff !important;

  &:hover {
    background-color: #a076cc;
  }
}

/* Blank Drag & Drop Mode */
.blank-question {
  height: 100% !important;

  .drop-zone {
    display: inline-flex;
    border-radius: 0.5rem;
    border: 2px dashed #fff;
    background-color: #ffffff1a;
    transition: all 0.2s;
    min-width: 10rem;
    width: fit-content;
    height: 2.5rem;
    margin-left: 0.5rem;
    margin-right: 0.5rem;
    margin-top: 1rem;

    &:hover {
      background-color: #fff3 !important;
      border-color: #fff !important;
    }

    &:focus {
      outline: 3px solid #a076cc;
      border: none;
    }
  }

  .blank-filled {
    background-color: #f8f9fa;
    color: #000;
    font-weight: 500;
    width: -webkit-fill-available;

    &:hover {
      border-radius: 0.5rem;
      outline: 3px solid #a076cc;
    }
  }

  .blank-empty {
    color: #6c757d;
    width: -webkit-fill-available;
  }

  .options-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
    margin-top: 10px;
  }

  .blank-option {
    border: 1px solid #ddd;
    border-radius: 6px;
    text-align: center;
    transition: all 0.2s;
    user-select: none;
    font-size: 24px;

    &:hover {
      border-color: #007bff;
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    &:active {
      transform: translateY(0);
    }
  }
}

/* Blank Click Mode */
.blank-question {
  .drop-zone {
    cursor: pointer;

    &:hover {
      border-color: #007bff;
      background-color: #e3f2fd;
    }
  }

  .blank-option.selected {
    background-color: #007bff !important;
    color: white !important;
    border-color: #0056b3;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,123,255,0.3);
  }
}

/* Blank Input Styles */
.blank-question {
  .blank-input {
    border: 2px solid #007bff;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 14px;
    min-width: 80px;
    background-color: #fff;
    outline: none;
  }

  &:focus {
    border-color: #0056b3;
    box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
  }

  .blank-filled-input {
    background-color: #17a2b8;
    color: white;
    border-radius: 4px;
    font-weight: 500;
    width: -webkit-fill-available;
  }

  .blank-input-mode {
    background-color: #f8f9fa;
    border-radius: 6px;
    padding: 15px;
    border-left: 4px solid #007bff;
  }

  .input-helper {
    margin-top: 10px;
    padding: 8px;
    background-color: #e3f2fd;
    border-radius: 4px;
    border-left: 3px solid #2196f3;
  }

  .btn {
    transition: all 0.2s;

    &:hover {
      transform: translateY(-1px);
    }
  }
}

/*v4*/
.blank-question {
  .blank-input-overlay {
    border: none;
    border-bottom: 2px solid #ffffff54;
    border-radius: 6px;
    padding: 12px 14px;
    font-size: 20px;
    width: 100%;
    max-width: 600px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s;
    background-color: #fff3;
    color: #fff;
    outline: none;
    box-shadow: none;

    &::placeholder {
      color: rgba(255, 255, 255, 0.7);
      font-size: 20px;
    }

    &:focus {
      border-bottom-color: #fff;
      box-shadow: 0 2px 0 0 rgba(255, 255, 255, 0.5);
      background-color: rgba(255, 255, 255, 0.05);
    }
  }

  .blank-input-overlay1 {
    border: 2px solid #007bff;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 14px;
    background-color: #fff;
    outline: none;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    width: -webkit-fill-available;
    top: 30px !important;

    &:focus {
      border-color: #0056b3;
      box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
    }
  }

  .blank-input-placeholder {
    color: rgba(255, 255, 255, 0.7);
    background-color: #09090980;
    border-color: #a076cc !important;
    border-radius: 0.5rem;
    outline: 3px solid #9d69d4;
    width: -webkit-fill-available;
  }
}

/* Result colors for blank input */
.blank-correct {
  background-color: #08ba63 !important;
  color: white !important;
}

.blank-incorrect {
  background-color: #f44336 !important;
  color: white !important;
}

.correct-answers-display {
  min-width: 500px;
  border-left: 4px solid #08ba63;
}

/* Result colors for drag and drop */
.drag-drop-question {
  .correct-answer {
    background-color: #08ba63 !important;
    color: white !important;
    border-color: #08ba63 !important;
  }

  .wrong-answer {
    background-color: #f44336 !important;
    color: white !important;
    border-color: #f44336 !important;
  }

  .drop-zone-correct {
    border-color: #08ba63 !important;
    background-color: rgba(8, 186, 99, 0.1) !important;
  }

  .drop-zone-wrong {
    border-color: #f44336 !important;
    background-color: rgba(244, 67, 54, 0.1) !important;
  }

  .option-correct {
    outline: 3px solid #08ba63 !important;
    background-color: rgba(8, 186, 99, 0.1) !important;
  }

  .option-wrong {
    outline: 3px solid #f44336 !important;
    background-color: rgba(244, 67, 54, 0.1) !important;
  }
}

/* Dropdown Question Styles */
.dropdown-question {
  height: 100%;

  .dropdown-container {
    display: inline-block;
    vertical-align: baseline;
    position: relative;
  }

  .dropdown-answer {
    padding: 4px 8px;
    transition: all 0.2s ease;
    background-color: #ffffff33;
    font-size: 1.5rem;
    color: #ffffff80;
    border-color: #fff6;
    border-width: 2px;
    border-radius: 0.5rem;
    cursor: pointer;
    line-height: 2rem;
    box-shadow: inset 0 2px 4px #0909090f;
    margin-top: 1rem !important;

    &:focus {
      outline: 3px solid #a076cc;
      box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    &:disabled {
      opacity: 1;
      cursor: not-allowed;
    }

    &.is-valid {
      border-color: #01ad71;
      background-color: #01ad71 !important;
      color: white !important;

      option {
        background-color: white;
        color: #333;
      }
    }

    &.is-invalid {
      border-color: #ed3550;
      background-color: #ed3550 !important;
      color: white !important;

      option {
        background-color: white;
        color: #333;
      }
    }

    option {
      background-color: white;
      color: #333;
      padding: 4px 8px;
    }
  }

  .option-filled {
    color: #222;
    background: #fff;
    border: 2px solid #fff;

    &:hover {
      outline: 3px solid #a076cc;
    }
  }
}

.q-mode-v1 {
  .q-label {
    position: absolute;
    top: -16px;
    left: 46%;
    color: #fff;
    font-size: 0.875rem;
    background-color: #090909cc;
    border-radius: 9999px;
    border-color: #fff3 !important;
  }

  .q-content-text {
    position: relative;
    background: #09090980;
    border-radius: 0.5rem;
    border: 1px solid #ffffff1a;
    padding: 2rem 1rem;
    align-items: center;
    align-self: center;
    min-height: 3.75rem;
    min-width: 60%;
    margin: auto;
    overflow: visible; // Quan trong
  }

  .q-answer2 {
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    background-color: #09090933;
    border-radius: 0.5rem;
  }
}

.msq-text {
  align-items: center;
  align-self: center;
  width: max-content;
  border: 1px solid #fff3;
  background-color: #ffffff80;
}

.rounded-full {
  border-radius: 9999px;
}

.text-sm {
  letter-spacing: -.01em;
  font-size: .875rem;
  line-height: 1.5rem;
}

.transition-all {
  transition-property: all;
  transition-duration: .15s;
  transition-timing-function: cubic-bezier(.4,0,.2,1);
}

.drag-drop-player-education {
  z-index: 999;
  position: absolute;
  left: 50%;

  img {
    border-radius: 8px;
    width: 64px;
    height: 64px;
    animation: 1.5s ease-in-out infinite drop-drag-education-vertical;
  }
}

.option-highlight {
  animation: 1s ease-in-out infinite glowingOption;
  border: none!important
}

.draggable-clone {
  pointer-events: none;
  position: fixed;
  top: -30px;
  left: 0
}

.option-selected {
  background: #a076cc !important;
  cursor: grab;
  cursor: -webkit-grab;
  outline: none
}

.option-selected {
  .dnd-option-text {
    color: #fff !important;
  }

  .icon-fas-grip-dots-vertical {
    color: #fff !important;
  }
}

.option-faded {
  opacity: .2;

  .dnd-option-text {
    color: #222
  }
}

.dragdrop-options-grid {
  width: calc(100% + 4px);
  height: calc(100% + 4px);

  padding: 1.5rem !important;
  background-color: #09090933;
}

.text-ds-light-500 {
  --tw-text-opacity: 1;
  color: rgba(255, 255, 255, var(--tw-text-opacity));
}

.text-ds-dark-200 {
  --tw-text-opacity: 1;
  color: rgba(86, 86, 86, var(--tw-text-opacity));
}

.game-note {
  top: .5rem;
  color: #FFFFFFA8;
  flex-shrink: 0;
}

.game-note-2 {
  color: #FFFFFF80;
  letter-spacing: -.01em;
  font-size: .875rem;
  line-height: 1.5rem;
  bottom: .25rem;
}

.option-placeholder {
  background-color: #fff3;
  display: flex;

  &.selected {
    opacity: 0.8;
    outline: 3px solid #a076cc;
  }
}

.drag-option {
  background: #fff;
  outline: 2px solid #fff3;
  flex-grow: 0;
  flex-shrink: 1;
  padding: 8px 16px;
  z-index: 1;

  &:hover {
    outline: 3px solid #a076cc;
  }

  .keyboard-interaction-shortcuts {
    color: #fff;
    background: #b6b6b6;
    border-radius: 4px;
    flex-grow: 1;
    flex-shrink: 0;
    width: 100%;
    height: 100%;
    margin-right: 4px;
    font-size: 12px;
    font-weight: 900
  }
}

.dnd-option-icon {
  flex-grow: 1;
  flex-shrink: 0;
  justify-content: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin-right: 8px;
  display: flex;

  .icon-fas-grip-dots-vertical {
    color: #6d6d6d;
    font-size: 20px;
  }
}

.resizeable-text {
  text-align: center;
  flex: 1;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  font-size: 24px;
  font-weight: 500;
  display: flex;

  .resizeable {
    word-wrap: break-word;
    overflow-wrap: break-word;
    word-break: break-word;
    width: 100%;
    max-height: 100%;
    font-weight: 500;
  }
}

.dnd-option-text {
  line-height: 32px;
  color: #222 !important;
  font-weight: 700 !important;
}

.grabbable {
  cursor: grab;
  cursor: -webkit-grab;

  &:active {
    cursor: grabbing;
    cursor: -webkit-grabbing;
  }
}
.font-bold {
  font-weight: 700;
}
.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}
.rounded-lg {
  border-radius: .5rem;
}
.blur-20 {
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
}
.gap-x-2 {
  -webkit-column-gap: .5rem;
  -moz-column-gap: .5rem;
  column-gap: .5rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .dropdown-question {
    .dropdown-answer {
      min-width: 100px !important;
      font-size: 1.25rem;
      padding: 3px 8px;
    }
  }

  .options-grid {
    display: grid;
  }
}

@media screen and (max-width: 768px) {
  .blank-question {
    .drop-zone {
      min-width: 6rem;
    }
  }

  .correct-answers-display {
    min-width: 300px;
  }

  .icon-full-screen {
    display: none !important;
  }
}

@media screen and (max-width: 576px) {
  .drag-drop-player-education {
    left:50%
  }

  .drag-drop-player-education img {
    animation-name: drop-drag-education-vertical
  }

  .drag-drop-image-player-education img {
    width:48px;
    height: 48px
  }

  .dnd-option-text {
    line-height:24px;
    font-size: 18px!important
  }
}

@media screen and (min-width: 576px) {
  .game-type-info {
    padding: 4px 8px;
    left: 12px;
  }

  .primary-button, .mystic-peak-start-game-button {
    font-size: 20px;
  }
}

@media screen and (min-width: 1025px) {
  .secondary-button {
    font-size: 20px;
  }
}

.game-type-info {
  color: #fff;
  background-color: #292a3ae6;
  border-radius: 4px;
  align-items: center;
  padding: 4px;
  font-size: 12px;
  display: inline-flex;
  position: absolute;
  top: 8px;
  left: 8px;
  box-shadow: inset 0 0 0 1px #0000001a;
}

.game-type-icon {
  font-size: 12px;
}

@keyframes drop-drag-education-vertical {
  0% {
    content: url(https://cf.quizizz.com/join/img/education/open_hand.png);
    transform: translateY(250%)
  }

  30% {
    content: url(https://cf.quizizz.com/join/img/education/close_hand.png)
  }

  60% {
    opacity: 1
  }

  80% {
    opacity: 0
  }

  to {
    opacity: 0;
    transform: translateY(-400%)
  }
}

@keyframes glowingOption {
  0% {
    opacity: 1
  }

  50% {
    opacity: .5
  }

  to {
    opacity: 1
  }
}
