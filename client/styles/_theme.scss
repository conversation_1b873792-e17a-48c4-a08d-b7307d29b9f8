// ==============================|| LIGHT BOX ||============================== //
.fullscreen .react-images__blanket {
  z-index: 1200;
}

// ==============================|| APEXCHART ||============================== //

.apexcharts-legend-series .apexcharts-legend-marker {
  margin-right: 8px;
}

// ==============================|| PERFECT SCROLLBAR ||============================== //

.scrollbar-container {
  .ps__rail-y {
    &:hover > .ps__thumb-y,
    &:focus > .ps__thumb-y,
    &.ps--clicking .ps__thumb-y {
      background-color: #697586;
      width: 5px;
    }
  }
  .ps__thumb-y {
    background-color: #697586;
    border-radius: 6px;
    width: 5px;
    right: 0;
  }
}

.scrollbar-container.ps,
.scrollbar-container > .ps {
  &.ps--active-y > .ps__rail-y {
    width: 5px;
    background-color: transparent !important;
    z-index: 999;
    &:hover,
    &.ps--clicking {
      width: 5px;
      background-color: transparent;
    }
  }
  &.ps--scrolling-y > .ps__rail-y,
  &.ps--scrolling-x > .ps__rail-x {
    opacity: 0.4;
    background-color: transparent;
  }
}

// ==============================|| ANIMATION KEYFRAMES ||============================== //

@keyframes wings {
  50% {
    transform: translateY(-40px);
  }
  100% {
    transform: translateY(0px);
  }
}

@keyframes blink {
  50% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes bounce {
  0%,
  20%,
  53%,
  to {
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    transform: translateZ(0);
  }
  40%,
  43% {
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    transform: translate3d(0, -5px, 0);
  }
  70% {
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    transform: translate3d(0, -7px, 0);
  }
  80% {
    transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    transform: translateZ(0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

@keyframes slideY {
  0%,
  50%,
  100% {
    transform: translateY(0px);
  }
  25% {
    transform: translateY(-10px);
  }
  75% {
    transform: translateY(10px);
  }
}

@keyframes slideX {
  0%,
  50%,
  100% {
    transform: translateX(0px);
  }
  25% {
    transform: translateX(-10px);
  }
  75% {
    transform: translateX(10px);
  }
}

@for $i from 10 through 26 {
  .fs-#{$i} {
    font-size: #{$i}px !important;
    line-height: 1.4em;
  }
}

/* Small devices (landscape phones, 576px and up)*/
@media (min-width: 576px) {
  .w-sm-auto {
    width: auto !important;
  }

  .w-sm-100 {
    width: 100% !important;
  }

  .w-sm-75 {
    width: 75% !important;
  }

  .w-sm-50 {
    width: 50% !important;
  }

  .w-sm-25 {
    width: 25% !important;
  }

  .h-sm-auto {
    height: auto !important;
  }

  .h-sm-100 {
    height: 100% !important;
  }

  .h-sm-75 {
    height: 75% !important;
  }

  .h-sm-50 {
    height: 50% !important;
  }

  .h-sm-25 {
    height: 25% !important;
  }
}

/* Medium devices (tablets, 768px and up)*/
@media (min-width: 768px) {
  .w-md-auto {
    width: auto !important;
  }

  .w-md-100 {
    width: 100% !important;
  }

  .w-md-75 {
    width: 75% !important;
  }

  .w-md-50 {
    width: 50% !important;
  }

  .w-md-25 {
    width: 25% !important;
  }

  .h-md-auto {
    height: auto !important;
  }

  .h-md-100 {
    height: 100% !important;
  }

  .h-md-75 {
    height: 75% !important;
  }

  .h-md-50 {
    height: 50% !important;
  }

  .h-md-25 {
    height: 25% !important;
  }
}

/* Large devices (desktops, 992px and up)*/
@media (min-width: 992px) {
  .w-lg-auto {
    width: auto !important;
  }

  .w-lg-100 {
    width: 100% !important;
  }

  .w-lg-75 {
    width: 75% !important;
  }

  .w-lg-50 {
    width: 50% !important;
  }

  .w-lg-25 {
    width: 25% !important;
  }

  .h-lg-auto {
    height: auto !important;
  }

  .h-lg-100 {
    height: 100% !important;
  }

  .h-lg-75 {
    height: 75% !important;
  }

  .h-lg-50 {
    height: 50% !important;
  }

  .h-lg-25 {
    height: 25% !important;
  }
}

/* Extra large devices (large desktops, 1200px and up)*/
@media (min-width: 1200px) {
  .w-xl-auto {
    width: auto !important;
  }

  .w-xl-100 {
    width: 100% !important;
  }

  .w-xl-75 {
    width: 75% !important;
  }

  .w-xl-50 {
    width: 50% !important;
  }

  .w-xl-25 {
    width: 25% !important;
  }

  .h-xl-auto {
    height: auto !important;
  }

  .h-xl-100 {
    height: 100% !important;
  }

  .h-xl-75 {
    height: 75% !important;
  }

  .h-xl-50 {
    height: 50% !important;
  }

  .h-xl-25 {
    height: 25% !important;
  }
}

.py-0-5 {
  padding-top: .125rem;
  padding-bottom: .125rem
}

.py-1-5 {
  padding-top: .375rem;
  padding-bottom: .375rem
}

.px-0-5 {
  padding-left: .125rem;
  padding-right: .125rem
}

.px-1-5 {
  padding-left: .375rem;
  padding-right: .375rem
}

.btn-green {
  background: #44a500;
  border: 1px solid transparent;
  color: #fff;
  padding: 7px 20px;

  &:hover {
    background: #ff5722;
    color: #fff;
  }
}

.btn-primary2 {
  color: #fff;
  background-color: #8854c0;
  border-color: #8854c0;

  &:hover, &:active {
    background-color: #7a38c0 !important;
    color: #fff !important;
    border-color: #7a38c0 !important;
  }

  &:disabled {
    background-color: #8854c0 !important;
    color: #fff !important;
    border-color: #8854c0 !important;
    cursor: not-allowed;
  }
}

.btn-outline-primary2 {
  color: #8854c0;
  background-color: transparent;
  background-image: none;
  border-color: #8854c0;

  &:hover, &:active {
    color: #fff !important;
    background-color: #8854c0 !important;
    border-color: #8854c0 !important;
  }
}

.btn-primary3 {
  background-color: #469704;
  border-color: #469704;
  color: #fff;

  &:hover, &:active {
    color: #fff !important;
    background-color: #52b700 !important;
    border-color: #52b700 !important;
  }
}

.btn-primary4 {
  background-color: #f6f0ff;
  border-color: #8854c066;
  color: #6A3DA5;

  &:hover, &:active {
    color: #6A3DA5 !important;
    background-color: #efe5ff !important;
    border-color: #8854c066 !important;
  }
}

.btn-default {
  background-color: #fff;
  border-color: #09090933;
  color: #222222;

  &:hover, &:active {
    background-color: #eaedf0 !important;
    color: #222222 !important;
    border-color: #09090933 !important;
  }

  &.disabled {
    background-color: #bdbdbd;
    color: #666666;
    cursor: not-allowed;
    opacity: 0.5;
    border-color: #09090933;
  }
}

.btn-warning2 {
  background-color: #d69724;
  border-color: #d69724;
  color: #fff;

  &:hover, &:active {
    background-color: #ff9800 !important;
    border-color: #ff9800 !important;
    color: #fff !important;
  }
}

.btn-success2 {
  color: #fff;
  background-color: #38a03c;
  border-color: #38a03c;

  &:hover, &:active {
    color: #fff !important;
    background-color: #2e8531 !important;
    border-color: #2e8531 !important;
  }

  &:disabled {
    color: #fff !important;
    background-color: #38a03c !important;
    border-color: #38a03c !important;
    cursor: not-allowed;
  }
}

.btn-small {
  font-size: 16px !important;
  padding: 5px !important;
  min-width: auto !important;

  svg {
    font-size: 16px !important;
  }
}

.btn-group {
  display: grid;
  gap: 0.5rem;
  grid-template-columns: repeat(2, minmax(0, 1fr));
  width: 100%;
  border: 1px solid #e5e5e5;
  border-radius: 0.5rem;
  padding: 1rem;
  background: #fff;
}

@media (max-width: 500px) {
  .btn-group {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
}

.btn-icon-link {
  color: #424242;
  padding: 0.25rem;
  border-radius: 0.5rem !important;
  align-items: flex-start;
  display: flex;
  font-size: 18px;
  text-align: left;

  &:hover {
    background-color: #0909090d;
    color: #9c27b0;
  }
}

.btn-icon {
  width: 1.5rem;
  height: 1.5rem;
  background-color: #fff3;
  border: 1px solid rgb(229, 229, 229);
  color: #fff;
  border-radius: 0.25rem;
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  font-size: 14px;

  &:hover {
    background-color: rgba(9, 9, 9, 0.05);
    border-color: rgb(229, 229, 229);
  }
}

.btn-icon-md {
  color: #fff;
  background-color: #fff3;
  border: 0;
  border-radius: 8px;
  flex-shrink: 0;
  transition: background-color .2s ease-out;
  background-image: none;
  justify-content: center;
  align-items: center;
  padding: 10px;
  font-size: 16px;
  display: inline-flex;
  position: relative;
  cursor: pointer;

  &:hover {
    background-color: #ffffff54;
  }
}

.btn-icon-primary2 {
  color: #8854c0;
  background-color: #f6f0ff;
  border-color: #8854c066;

  &:hover {
    background-color: #efe5ff;
    border-color: #8854c066;
    color: #6a3da5;
  }
}

.btn-icon-danger {
  &:hover,
  &.active {
    color: #fff;
    background-color: #dc3545;
    border-color: #dc3545;
  }
}

.btn-icon-success {
  background-color: #0909093d;
  color: #ffffff80;
  border-color: rgba(255, 255, 255, 0.5);

  &:hover {
    color: #fff;
    background-color: #28a745;
    border-color: #28a745;
  }

  &.active {
    color: #fff;
    background-color: #28a745;
    border-color: #fff;
  }
}

.icon-link {
  background-color: #8854c0;
  // width: 1.5rem;
  // height: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  border-radius: 0.25rem;
  color: #fff;
  line-height: 18px;
  font-size: 16px;
  padding: 5px;
}

.cursor-pointer {
  cursor: pointer;
}

.dropdown-menu {
  a {
    cursor: pointer;
  }

  .dropdown-item {
    &:hover, &:focus {
      background-color: #eaedf0;
    }

    &:active, &.active {
      background-color: #dfe2e7;
      color: inherit;
    }
  }
}

.dropdown-menu-2 {
  border-radius: 0.75rem;

  .dropdown-item {
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
  }
}

#share-menu {
  top: 6px;

  .MuiMenu-paper {
    border-radius: 0.75rem;
    border: 1px solid #0000002d;
    box-shadow: none;
  }

  .MuiMenu-list {
    padding: 4px;
  }

  .share-item {
    border-radius: 0.5rem;
    padding: 8px 12px;

    &:hover {
      background-color: #eaedf0;
    }

    &:active, &.active {
      background-color: #dfe2e7;
      color: inherit;
    }
  }
}

.quiz-action {
  display: flex;
  gap: 30px;
  align-items: center;
  padding: 10px 10px 0 10px;
  background: #eef2f6;
  transition: all 0.15s ease;

  &.sticky {
    position: sticky;
    top: 68px;
    z-index: 100;
    background: #fff;
    border: 1px solid #09090933;
    box-shadow: rgba(99, 99, 99, 0.2) 0 2px 8px 0;
  }

  .btn-default {
    border-radius: .75rem;
    padding: .5rem 1rem;
  }
}

.badge-default {
  padding: 2px 8px;
  border-radius: 9999px;
  font-size: 12px;
  font-weight: 500;
  border: 1px solid #dfe2e7;
  background-color: #fff3;
  color: #121517;
}

.draft-badge {
  background-color: #fff3;
  border-color: #ff319f66;
  color: #e0067b;
}

.premium-badge {
  background-color: #fff5fa;
  border-color: #ff319f66;
  color: #e0067b;
}

.btn-dropdown-small.dropdown-toggle {
  padding: 2px;
  border-radius: 50%;
  font-size: 0.875rem;
  line-height: 1.5;

  &:hover {
    background-color: rgba(0, 0, 0, 0.04);
  }
}

.btn-dropdown-small.dropdown-toggle::after {
  content: none;
}

.overflow-x-el {
  position: relative;
  overflow-x: auto;
  padding-bottom: 20px;

  &::-webkit-scrollbar {
    height: 4px;
    width: 4px;
    background: #dedede;
  }

  &::-webkit-scrollbar-thumb {
    background: #ff940e;
    border-radius: 10px;
  }
}

iframe[src*="www.youtube.com"] {
  aspect-ratio: 16 / 9;
  max-width: 100%;
}

@media only screen and (max-width: 500px) {
  iframe[src*="www.youtube.com"] {
    height: 100% !important;
    width: 100% !important;
  }
}

.q-progress {
  width: 100%;
  margin: 10px auto;

  .q-progress-main {
    position: relative;
    overflow: hidden;
    width: 100%;
    background-color: #7b4cad4d;
    border-radius: 12px;
    /* box-shadow: inset 0px 1px 3px rgba(0, 0, 0, 0.2); */
  }

  .q-progress-bar {
    display: block;
    float: left;
    background-color: #7b4cad;
    box-shadow: inset 0px -1px 2px rgba(0, 0, 0, 0.1);
    transition: width 0.8s ease-in-out;
    width: 0%;
    height: 18px;
    border-radius: 12px;
  }

  .q-progress-label {
    position: absolute;
    overflow: hidden;
    left: 0px;
    right: 0px;
    color: #fff;
    text-shadow: 0 2px 7px #607D8B;
    font-size: 12px;
    // font-weight: 600;
    margin: 0 10px;
  }

  .q-progress-label2 {
    display: block;
    margin: 2px 0;
    padding: 0 8px;
    font-size: 0.8em;
  }

  .q-progress-label3 {
    position: absolute;
    overflow: hidden;
  }

  &.q-progress-danger {
    .q-progress-main {
      background-color: #bb0a473b;
    }

    .q-progress-bar {
      background-color: #E91E63;
    }
  }

  &.q-progress-warning {
    .q-progress-main {
      background-color: #ffe5002e;
    }

    .q-progress-bar {
      background-color: #ffe60e;
    }

    .q-progress-label {
      color: #6a6a6a;
    }
  }

  &.q-progress-success {
    .q-progress-main {
      background-color: #00790540;
    }

    .q-progress-bar {
      background-color: #00a107;
    }
  }

  &.q-progress-default {
    .q-progress-main {
      background-color: #9592974d;
    }

    .q-progress-bar {
      background-color: #949dab;
    }
  }

  .q-progress-active {
    // background-image: linear-gradient(
    //   -45deg,
    //   rgba(255, 255, 255, 0.125) 25%,
    //   transparent 25%,
    //   transparent 50%,
    //   rgba(255, 255, 255, 0.125) 50%,
    //   rgba(255, 255, 255, 0.125) 75%,
    //   transparent 75%,
    //   transparent
    // );
    background-size: 35px 35px;
    animation: ProgressActive 2s linear infinite;
  }
}

@-webkit-keyframes ProgressActive {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 35px 35px;
  }
}

@keyframes ProgressActive {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 35px 35px;
  }
}

.mySwiper {
  position: relative;
  min-height: 100px;
  padding: 10px 0 !important;

  .swiper-wrapper {
    opacity: 0;
    visibility: hidden;
    transition: all 1.2s ease;
  }

  &::before,
  &::after {
    content: "";
    position: absolute;
    background-color: rgba(54, 65, 82, 0.11);
    border-radius: 4px / 6.7px;
    background-image: linear-gradient(
      90deg,
      rgba(255, 255, 255, 0),
      rgba(255, 255, 255, 0.5),
      rgba(255, 255, 255, 0)
    );
    background-repeat: no-repeat;
    background-size: 100px 200px, 50px 200px, 150px 200px, 350px 200px,
      300px 200px, 250px 200px;
    background-position: 0 0, 0 0, 120px 0, 120px 40px, 120px 80px, 120px 120px;
  }

  &::before {
    top: 0;
    height: 17%;
    width: 100%;
    animation: shine 1.5s ease infinite;
  }

  &::after {
    bottom: 0;
    height: 80%;
    width: 100%;
    animation: shine 1.5s ease infinite;
  }

  &.mySwiper-sm {
    &::before {
      height: 20%;
    }
  }

  &.swiper-initialized {
    .swiper-wrapper {
      opacity: 1;
      visibility: visible;
    }

    &::before,
    &::after {
      content: none;
    }
  }
}

.mySwiper-base {
  .swiper-wrapper {
    opacity: 0;
    visibility: hidden;
    transition: all 1.2s ease;
  }

  &.swiper-initialized {
    .swiper-wrapper {
      opacity: 1;
      visibility: visible;
    }
  }
  .swiper-slide {
    text-align: center;
  }
}

@keyframes shine {
  to {
    // Move shine from left to right, with offset on the right based on the width of the shine - see background-size
    background-position: right -40px top 0;
  }
}

.swiper-button-prev:after,
.swiper-button-next:after {
  font-size: 20px !important;
  font-weight: 600;
}

.swiper-button-next,
.swiper-rtl .swiper-button-prev,
.swiper-button-prev,
.swiper-rtl .swiper-button-next {
  background: white;
  border-radius: 4px;
  border: 1px solid #7bc1e0;
  box-shadow: 0 2px 4px 2px #0000001a;
}

.swiper-button-prev.swiper-button-disabled,
.swiper-button-next.swiper-button-disabled {
  pointer-events: inherit !important;
}

.swiper-pagination {
  bottom: 0 !important;
}

.drag-drop-crop {
  position: relative;
  height: 350px;
  background-color: #ececec;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 5px;
  border-radius: 4px;
  overflow: hidden;

  img {
    height: auto;
    width: 100% !important;
  }

  label {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }

  .btn-float-left {
    position: absolute;
    left: 0;
    top: 0;
    margin: 10px;
    z-index: 2;
  }

  .btn-float-right {
    position: absolute;
    right: 0;
    top: 0;
    margin: 10px;
    z-index: 2;
  }

  .ReactCrop {
    margin-top: 5px;
  }
}

.input-large input {
  font-size: 24px;
  padding: 15px 10px;

  &::placeholder {
    font-size: 24px;
  }
}

.height-cutom1 {
  height: calc(100% - 100px);
}

.bg-hl {
  background-color: #fff;
}

.media-group {
  .card {
    background-color: #fff;
    cursor: pointer;
    margin-bottom: 10px;

    &:hover {
      background-color: #f9f9f9;
    }

    &.active {
      background-color: #f6f0ff;
      border: 1px solid #8854c0 !important;
    }

    img {
      border: 1px solid #fff;
    }
  }
}

@media only screen and (max-width: 768px) {
  .input-large input {
    font-size: 18px;

    &::placeholder {
      font-size: 18px;
    }
  }

  .media-md-group {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    align-items: center;

    .card {
      flex-grow: 0;
      flex-shrink: 0;
      margin: 10px;

      &:first-child {
        margin-left: 0;
      }

      .col-3 {
        width: 16.66666667%;
      }

      .col-9 {
        width: 83.33333333%;
      }
    }
  }

  .height-cutom1 {
    height: calc(100% - 330px);
  }

  .bg-hl {
    background-color: #fffaf2;
  }
}

@media only screen and (max-width: 992px) {
  .media-lg-group {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    align-items: center;

    .card {
      flex-grow: 0;
      flex-shrink: 0;
      margin: 10px;

      &:first-child {
        margin-left: 0;
      }

      .col-3 {
        width: 16.66666667%;
      }

      .col-9 {
        width: 83.33333333%;
      }
    }
  }
}

/* fly to box */
.fly-item {
  position: absolute;
  z-index: 9999;
  display: flex;
  height: 24px;
  width: 24px;
  background: #ff5722;
  color: #fff;
  font-size: 14px;
  font-weight: 600;
  border-radius: 20px;
  align-items: center;
  justify-content: center;
}

@keyframes flyToBox {
  0% {
    transform: translate(0, 0);
    opacity: 1;
  }
  50% {
    transform: translate(calc(var(--fly-x) / 2), calc(var(--fly-y) / 2));
    opacity: 0.5;
  }
  100% {
    transform: translate(var(--fly-x), var(--fly-y));
    opacity: 0;
    visibility: hidden;
  }
}

.send-to-box {
  animation: flyToBox 2s ease-in-out forwards;
}

.shake {
  animation: shake 0.5s;
}

@keyframes shake {
  0%,
  100% {
    transform: translateX(0);
  }
  25%,
  75% {
    transform: translateX(-10px);
  }
  50% {
    transform: translateX(10px);
  }
}

.shutter-wrapper {
  width: 100%;
  position: fixed;
  right: 0;
  bottom: 0;
  top: 0;

  .stripe {
    position: absolute;
    width: 100%;
    height: 50%;
    opacity: 0.43;
    background-color: #000;
    z-index: 1;
    transition: height 0.7s ease-in-out;
  }

  .stripe:first-of-type {
    top: 0;
  }

  .stripe + .stripe {
    bottom: 0;
  }

  &.hover-state .stripe,
  input:focus ~ .stripe {
    height: 0;
  }
}

.countdown {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  color: white;
  font-size: 10rem;
  text-align: center;
  font-weight: bold;
  margin: auto;
  width: 100%;
  opacity: 1;
  transition: opacity 0.5s ease;
  /* solve for flicker problem */
  backface-visibility: hidden;
}

.countdown.puffer {
  opacity: 0;
  transform: scale(0.5);
  transition: all 0.5s ease-out;
}

// .animate {
//   animation: zoom-in-zoom-out 1s ease infinite;
// }

// @keyframes zoom-in-zoom-out {
//   0% {
//     transform: scale(1);
//   }
//   50% {
//     transform: scale(1);
//   }
//   100% {
//     transform: scale(0.5);
//   }
// }

@media (max-width: 768px) {
  .countdown {
    .countdown-text {
      font-size: 4rem;
    }
  }
}

.btn-filter {
  background-color: #fff;
  border-color: #09090933;

  &:hover {
    background-color: #f6f0ff;
  }

  &.active {
    background-color: #f6f0ff;
    border-bottom-color: #8854c0;
    border-bottom-width: 2px;
  }
}

.invite-link {
  flex: 1;
  border: none;
  background: initial;
  outline: none;
}

.sticky-top {
  position: sticky;
  top: 70px;
  z-index: 99;
}

.search-box {
  max-height: calc(100vh - 62px);

  @media (max-width: 992px) {
    max-height: unset;
  }
}

.quiz-grid {
  display: grid;
  gap: 1rem;
  grid-template-columns: repeat(auto-fit, minmax(12.5rem, max-content));

  &:not(:has(> :nth-child(6))) {
    > * {
      width: 16em;

      @media screen and (max-width: 350px) {
        width: 100%;
      }
    }
  }
}

.quiz-grid-column {
  display: grid;
  gap: 1rem;
  grid-auto-flow: column;
  grid-template-columns: repeat(6, 12.5rem);
  overflow-x: auto;
}

@media screen and (min-width: 1024px) {
  .quiz-grid {
    grid-template-columns: repeat(auto-fit, minmax(14em, max-content));
  }

  .quiz-grid-column {
    grid-template-columns: repeat(6, 14em);
  }
}

.worksheets {
  a {
    color: #222;
    text-decoration: none!important;
  }

  .active1 {
    color: #8854c0;
    border-color: #8854c0;
    background-color: #ede6f6;
  }

  .active2, .active3 {
    color: #8854c0;
    background-color: #ede6f6!important;
    font-weight: 500!important;

    .bi-caret-right-fill::before {
      content: "\f229";
    }
  }

  .active3 {
    background-color: transparent!important;
  }

  .active4 {
    color: #0e477b;
    background-color: #2d70ae1a;
    border-color: #2d70ae1a;
  }

  .active5 {
    color: #6A3DA5;
    background-color: #efe5ff;
    border-color: rgba(136, 84, 192, 0.4);
  }

  .small {
    font-size: .75em;
  }

  .topics {
    display: flex;
    align-items: center;
    gap: 10px;
    overflow-x: auto;
    overflow-y: hidden;

    .topic {
      white-space: nowrap;
    }
  }

  .bg-white:has(+ .quiz-grid) {
    background-color: transparent!important;
  }
}

.list-tag li a {
  padding: 0.175rem 0.75rem;
  border-radius: .5rem;
  &:hover{
    background-color: rgba(242, 242, 242, 1) !important;
  }

  @media (max-width: 899px) {
    margin-left: 8px;
    margin-top: 8px;
  }
}

.banner-wrapper {
  position: relative;
  height: 10rem;
  width: 100%;
  margin: auto;
  overflow: hidden;
  background: #eee;
}

.bg-linear-copy {
  background: linear-gradient(90.57deg, #ff5722, #9c27b0);
}

@media (max-width: 1099px) {
  .bgroup-action {
    flex-direction: column !important;
  }
}
