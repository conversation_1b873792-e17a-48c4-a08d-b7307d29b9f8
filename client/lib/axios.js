// nên sử dụng cho client action
import Axios from "axios";
import { isClient } from "@/utils/helpers";
import { apiUrl } from "@/lib/fetcher";

const axios = Axios.create({
  withCredentials: true,
  withXSRFToken: true,
});

const http = async ({ url, method, data, headers, params, signal }) => {
  return await axios({
    url,
    method,
    data,
    headers: {
      "Access-Control-Allow-Credentials": true,
      Accept: "application/json",
      "Content-Type": "application/json;charset=UTF-8",
      "X-Requested-With": "XMLHttpRequest",
      ...headers
    },
    params,
    signal, // Thêm signal từ AbortController
  });
};

axios.interceptors.request.use(async (config) => {
  if (isClient() && config?.headers?.refreshCsrfcookie) {
    await getCsrfToken();
  }

  return config;
}, (error) => {
  return Promise.reject(error);
});

axios.interceptors.response.use(
  function (response) {
    return response.data;
  },
  function (error) {
    let err;

    if (error.response) {
      const { response } = error;

      if (typeof response.data === "object") {
        err = response.data;
      } else {
        try {
          err = JSON.parse(response.data);
        } catch (e) {
          console.log("Error unparsed:", response.data, e);
          err = {
            message: "An unexpected error occurred",
          };
        }
      }

      err = {
        status: response.status,
        ...err,
      };
      // } else if (error.request) {
      //   err = {
      //     message: error.request,
      //     status: 500,
      //   };
    } else {
      err = {
        message: error.message,
        status: 500,
      };
    }

    console.log("Fetch Error: ", err);

    if (isClient()) {
      throw err;
    } else {
      return err;
    }
  }
);

// Set the Bearer auth token.
const setBearerToken = (token) => {
  axios.defaults.headers.common["Authorization"] = `Bearer ${token}`;
};

const swrHttpFetcher = (url) => axios.get(url).then((res) => res.data);

const getCsrfToken = () => {
  return axios.get(apiUrl("/sanctum/csrf-cookie"));
};

const axiosBaseQuery = () => async ({
  url,
  method,
  data,
  headers,
  params
}) => {
  try {
    console.log('axiosBaseQuery call');
    const res = await http({ url, method, data, headers, params });

    return { data: res.data };
  } catch (error) {
    return { error };
  }
};

export { axios, getCsrfToken, setBearerToken, http, swrHttpFetcher, axiosBaseQuery };
