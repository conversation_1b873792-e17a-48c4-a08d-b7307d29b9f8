// nên sử dụng cho server action
import { isClient } from "@/utils/helpers";

export const apiUrl = (url = "", prefix = "") => {
  let endPoint = isClient()
    ? process.env.NEXT_PUBLIC_BACKEND_URL
    : process.env.BACKEND_URL;

  // xóa ký tự / ở đầu và cuối
  url = url.replace(/^\/|\/$/g, "");
  prefix = prefix.replace(/^\/|\/$/g, "");

  if (prefix) {
    endPoint += `/${prefix}`;
  }

  if (url) {
    endPoint += `/${url}`;
  }

  console.log("Api call:", endPoint, "Client:", isClient());

  return endPoint;
};

// doc: https://developer.mozilla.org/en-US/docs/Web/API/Fetch_API/Using_Fetch
export async function fetcher(props) {
  let {
    url,
    method = "GET",
    useCredentials = false,
    body,
    headers = {},
    queryParams = {},
    nextOption = {},
    signal,
  } = props;

  const options = {
    method: method,
    // Mặc định thiết lập content-type là kiểu json
    headers: new Headers({
      "Access-Control-Allow-Credentials": true,
      // 'X-XSRF-TOKEN': mytoken,
      Accept: "application/json",
      "Content-Type": "application/json;charset=UTF-8",
      "X-Requested-With": "XMLHttpRequest",
      ...headers,
    }),
    body: body ? body : null,
    ...nextOption,
    signal,
  };
  // Note that if a cookie's SameSite attribute is set to Strict or Lax, then the cookie will not be sent cross-site, even if credentials is set to include.
  if (useCredentials) options.credentials = "include"; // Include cookies in the request and default is: same-origin
  if (Object.keys(queryParams).length) {
    const queryString = new URLSearchParams(queryParams).toString();
    url = `${url}?${queryString}`;
  }

  const res = await fetch(url, options);

  console.log("Fetch Done.");

  if (res.ok) {
    return res.json();
  } else {
    let err = await res.text();

    try {
      err = JSON.parse(err);
    } catch (e) {
      console.log("Error unparsed:", err, e);
      err = {
        message: "An unexpected error occurred",
      };
    }

    err = {
      status: res.status,
      ...err,
    };

    console.log("Fetch Error: ", url, err);

    if (isClient()) {
      throw err;
    } else {
      return err;
    }
  }
}

export async function swrFetcher(...args) {
  const res = await fetch(...args);

  return res.json();
}
