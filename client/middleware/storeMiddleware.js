import { setNoti } from "@/slices/notiSlice";

const storeMiddleware = (store) => (next) => async (action) => {
  console.log("Middleware", action.type);

  if (action.type.endsWith("rejected")) {
    console.error("Action rejected:", action);

    if (!action.type.startsWith("@auth") && action?.payload?.message) {
      store.dispatch(setNoti(action.payload));
    }
  } else if (action.type.endsWith("fulfilled")) {
    console.log("Action fulfilled:", action);
  }

  return next(action);
};

export default storeMiddleware;
