const withNextIntl = require("next-intl/plugin")();
const path = require('path');

const IGNORE = ['api','_next','static','fonts','dashboard','join','print','quiz'];
const REGEX_SOURCE = `((?!${IGNORE.join("|")})[^/]+)`;

/** @type {import('next').NextConfig} */
const config = {
  // cacheHandler: require.resolve('./cache-handler.js'),
  // cacheMaxMemorySize: 0, // disable default in-memory caching
  output: "standalone",
  reactStrictMode: true,
  poweredByHeader: false, // Tắt header "X-Powered-By" tiết lộ Next.js
  compress: true, // bật GZIP/Brotli
  compiler: {
    styledComponents: true,
    // removeConsole: {          // loại bỏ console.log trong production
    //   exclude: ['error','warn']
    // }
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  httpAgentOptions: {
    keepAlive: true
  },
  logging: {
    fetches: {
      fullUrl: true,
      hmrRefreshes: true,
    },
  },
  experimental: {
    // inlineCss: true,
    useCache: true,
    staticGenerationRetryCount: 3,
    staticGenerationMaxConcurrency: 6,
    staticGenerationMinPagesPerWorker: 200,
    staleTimes: {
      dynamic: 30, // dynamic luôn fresh nếu set là 0
      static: 300, // cache các segment static 5 phút
    },
  },
  images: {
    // unoptimized: true,
    qualities: [25, 50, 75],
    remotePatterns: [
      {
        protocol: "http",
        hostname: "localhost",
        // port: "",
      },
      {
        protocol: "http",
        hostname: "backend_nginx",
      },
      {
        protocol: 'https',
        hostname: 'lh3.googleusercontent.com',
      },
      {
        protocol: 'https',
        hostname: '*.vietjack.com',
      },
      {
        protocol: 'https',
        hostname: 'vietjack.com',
      },
    ],
  },
  async headers() {
    return [
      {
        source: '/_next/static/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable'
          }
        ]
      },
      {
        source: '/(fonts|static)/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable'
          }
        ]
      }
    ];
  },
  async redirects() {
    return [
      {
        // /slug/subslug/2 → /slug/subslug?page=2
        source: `/:slug${REGEX_SOURCE}/:subslug${REGEX_SOURCE}/:page(\\d+)`,
        destination: "/:slug/:subslug?page=:page",
        permanent: true,
      },
      {
        // /slug/2 → /slug?page=2
        source: `/:slug${REGEX_SOURCE}/:page(\\d+)`,
        destination: "/:slug?page=:page",
        permanent: true,
      },
    ];
  },
  async rewrites() {
    return [
      {
        source: `/:slug${REGEX_SOURCE}/:subslug${REGEX_SOURCE}`,
        has: [{ type: "query", key: "page" }],
        destination: "/:slug/:subslug/:page",
      },
      {
        source: `/:slug${REGEX_SOURCE}`,
        has: [{ type: "query", key: "page" }],
        destination: "/:slug/:page",
      },
    ];
  },
  // turbopack: {
  //   root: path.join(__dirname, '..'),
  // },
};

module.exports = withNextIntl(config);
