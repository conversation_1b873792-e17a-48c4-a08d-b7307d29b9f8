import * as actionTypes from './actions';
import { defaultCustomization } from '@/constant';

const customizationReducer = (state = defaultCustomization, action) => {
  switch (action.type) {
    case actionTypes.MENU_OPEN:
      return {
        ...state,
        isOpen: [action.id]
      };
    case actionTypes.MENU_TOGGLE:
      return {
        ...state,
        opened: action.opened
      };
    case actionTypes.SET_FONT_FAMILY:
      return {
        ...state,
        fontFamily: action.fontFamily
      };
    case actionTypes.SET_BORDER_RADIUS:
      return {
        ...state,
        borderRadius: action.borderRadius
      };
    case actionTypes.SET_BORDER_CARD:
      return {
        ...state,
        borderCard: action.borderCard
      };
    case actionTypes.SET_CUSTOMIZATION:
      return {
        ...state,
        ...action.payload
      };
    default:
      return state;
  }
};

export default customizationReducer;
