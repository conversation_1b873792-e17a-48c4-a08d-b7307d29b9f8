import Link from 'next/link';
import { useTheme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';

// project imports
import Logo, { LogoMb } from '@/components/Logo';

// ==============================|| MAIN LOGO ||============================== //

export default function LogoSection() {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  return (
    <Link href="/">
      {isMobile ? (
        <LogoMb />
      ) : (
        <Logo />
      )}
    </Link>
  );
}
