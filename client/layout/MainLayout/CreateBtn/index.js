"use client";

import React, { useState, forwardRef, useCallback, memo, useMemo } from "react";
import { useRouter } from "next/navigation";
import { useSelector } from "react-redux";

// material-ui
import { styled } from '@mui/material/styles';
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import Slide from "@mui/material/Slide";
import Card from "@mui/material/Card";
import CardHeader from "@mui/material/CardHeader";
import Avatar from "@mui/material/Avatar";
import CardContent from "@mui/material/CardContent";
import { purple, red, blue } from "@mui/material/colors";

// assets
import AddIcon from "@mui/icons-material/Add";

import DialogQuizForm from "@/components/quizzes/DialogQuizForm";

// Styled Button
const ColorButton = styled(Button)(({ theme }) => ({
  width: "100%",
  color: theme.palette.getContrastText(purple[500]),
  backgroundColor: purple[700],
  "&:hover": {
    backgroundColor: purple[500],
  },
}));

// Transition Component
const Transition = forwardRef(function Transition(props, ref) {
  return <Slide direction="up" ref={ref} {...props} />;
});

const DialogCreate = memo(({ open, onClose, onOpenDialogQuizFrom }) => {
  const router = useRouter();

  const createQuiz = () => {
    // router.push("/dashboard/quiz/create");
    onClose();
    onOpenDialogQuizFrom();
  };

  const createLesson = () => {
    alert("Đang cập nhật!");
    onClose();
  };

  return (
    <>
      <Dialog
        open={open}
        TransitionComponent={Transition}
        keepMounted
        onClose={onClose}
        sx={{
          '& .MuiDialog-paper': {
            backgroundColor: '#E0E0E0',
          },
        }}
      >
        <DialogTitle className="h4" sx={{ textAlign: "center" }}>
          Bạn muốn tạo gì?
        </DialogTitle>
        <DialogContent>
          <Card
            onClick={createQuiz}
            sx={{
              mb: 3,
              cursor: "pointer",
              border: "3px solid #90A4AE",
              "&:hover": { borderColor: "#a076cc" },
            }}
          >
            <CardHeader
              avatar={
                <Avatar
                  sx={{ bgcolor: red[500], color: "#fff" }}
                  aria-label="recipe"
                >
                  Q
                </Avatar>
              }
              title="Quiz"
            />
            <CardContent sx={{ paddingTop: 0 }}>
              <p className="fs-18">
                <i className="bi bi-patch-check text-success"></i> Đưa ra đánh giá và thực hành tạo động lực với các câu hỏi tương
                tác
              </p>
            </CardContent>
          </Card>
          <Card
            onClick={createLesson}
            sx={{
              mb: 3,
              cursor: "pointer",
              border: "3px solid #90A4AE",
              "&:hover": { borderColor: "#a076cc" },
            }}
          >
            <CardHeader
              avatar={
                <Avatar
                  sx={{ bgcolor: blue[500], color: "#fff" }}
                  aria-label="recipe"
                >
                  B
                </Avatar>
              }
              title="Bài học"
            />
            <CardContent sx={{ paddingTop: 0 }}>
              <p className="fs-18">
                <i className="bi bi-patch-check text-success"></i> Thêm các slide vui nhộn và tương tác vào các bài đánh giá mà học
                sinh đã yêu thích
              </p>
            </CardContent>
          </Card>
        </DialogContent>
      </Dialog>
    </>
  );
});

DialogCreate.displayName = "DialogCreate";

const CreateBtn = () => {
  const { opened: drawerOpen } = useSelector((state) => state.customization);

  const [openDialog, setOpenDialog] = useState(false);
  const [openDialogQuizFrom, setOpenDialogQuizFrom] = useState(false);

  const handleOpenDialog = useCallback(() => {
    setOpenDialog(true);
  }, []);

  const handleCloseDialog = useCallback(() => {
    setOpenDialog(false);
  }, []);

  const handleOpenDialogQuizFrom = useCallback(() => {
    setOpenDialogQuizFrom(true);
  }, []);

  const handleCloseDialogQuizFrom = useCallback(() => {
    setOpenDialogQuizFrom(false);
  }, []);

  return (
    <Box sx={{ textAlign: "center", margin: '0 16px 10px 10px;' }}>
      <ColorButton
        variant="contained"
        onClick={handleOpenDialog}
        sx={{ minWidth: '100%', borderRadius: '0.75rem', padding: '10px 16px' }}
      >
        <AddIcon />
        { drawerOpen && (<span className="ms-2">Tạo mới</span>) }
      </ColorButton>
      {openDialog && (
        <DialogCreate
          open={openDialog}
          onClose={handleCloseDialog}
          onOpenDialogQuizFrom={handleOpenDialogQuizFrom}
        />
      )}
      {openDialogQuizFrom && (
        <DialogQuizForm
          open={openDialogQuizFrom}
          onClose={handleCloseDialogQuizFrom}
        />
      )}
    </Box>
  );
};

export default CreateBtn;
