"use client";

import { memo, useState, useEffect, useMemo } from 'react';
import { useSelector } from "react-redux";

import { useTheme } from "@mui/material/styles";
import useMediaQuery from '@mui/material/useMediaQuery';
import Chip from '@mui/material/Chip';
import Drawer from '@mui/material/Drawer';
import Stack from '@mui/material/Stack';
import Box from '@mui/material/Box';

// third party
import PerfectScrollbar from 'react-perfect-scrollbar';

// project imports
import MenuCard from './MenuCard';
import MenuList from '../MenuList';
import LogoSection from '../LogoSection';
import CreateBtn from "../CreateBtn";
import MiniDrawerStyled from './MiniDrawerStyled';

import { drawerWidth } from '@/constant';

// ==============================|| SIDEBAR DRAWER ||============================== //

function Sidebar({ drawerToggle }) {
  const theme = useTheme();
  const [mounted, setMounted] = useState(false);

  useEffect(() => setMounted(true), []);

  const downMD = useMediaQuery((theme) => theme.breakpoints.down('md'));
  const { opened } = useSelector((state) => state.customization);

  const logo = useMemo(
    () => (
      <Box sx={{ display: 'flex', p: 2 }}>
        <LogoSection />
      </Box>
    ),
    []
  );

  const drawer = useMemo(() => {
    const drawerContent = <MenuCard />;

    let drawerSX = { paddingLeft: '0px', paddingRight: '0px', marginTop: '20px' };
    if (opened) drawerSX = { paddingLeft: '16px', paddingRight: '16px', marginTop: '5px' };

    return (
      <>
        {downMD ? (
          <Box sx={drawerSX}>
            {opened && drawerContent}
            <CreateBtn />
            <MenuList />
          </Box>
        ) : (
          <PerfectScrollbar style={{ height: 'calc(100vh - 68px)', ...drawerSX }}>
            {opened && drawerContent}
            <CreateBtn />
            <MenuList />
          </PerfectScrollbar>
        )}
      </>
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [downMD, opened]);

   if (!mounted) return null;

  return (
    <Box component="nav" sx={{ flexShrink: { md: 0 }, width: { xs: 'auto', md: drawerWidth }, backgroundColor: theme.palette.grey[100] }} aria-label="mailbox folders">
      {downMD || opened ? (
        <Drawer
          variant={downMD ? 'temporary' : 'persistent'}
          anchor="left"
          open={opened}
          onClose={drawerToggle}
          sx={{
            '& .MuiDrawer-paper': {
              mt: downMD ? 0 : 8.4,
              zIndex: 1101,
              width: drawerWidth,
              bgcolor: 'background.default',
              color: 'text.primary',
              borderColor: '#09090933',
            }
          }}
          ModalProps={{
            keepMounted: true,
            disableEnforceFocus: true,
            disableAutoFocus: true,
          }}
          color="inherit"
        >
          {downMD && logo}
          {drawer}
        </Drawer>
      ) : (
        <MiniDrawerStyled variant="permanent" open={opened}>
          {logo}
          {drawer}
        </MiniDrawerStyled>
      )}
    </Box>
  );
}

export default memo(Sidebar);
