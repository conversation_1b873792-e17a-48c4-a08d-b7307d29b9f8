import { useSelector } from "react-redux";
import Link from "next/link";
import { useRouter } from "next/navigation";
// material-ui
import { useTheme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import Avatar from '@mui/material/Avatar';
import Box from '@mui/material/Box';
import Button from "@mui/material/Button";
import CircularProgress from "@mui/material/CircularProgress";
import HouseIcon from "@mui/icons-material/House";
// project imports
import LogoSection from '../LogoSection';
import SearchSection from './SearchSection';
import ProfileSection from './ProfileSection';
import NotificationSection from './NotificationSection';

// assets
import MenuIcon from "@mui/icons-material/Menu";

// ==============================|| MAIN NAVBAR / HEADER ||============================== //

export default function Header({ sidebar, drawerToggle }) {
  const theme = useTheme();
  const router = useRouter();
  const downMD = useMediaQuery(theme.breakpoints.down('md'));

  const { user, loading } = useSelector((state) => state.auth);

  const handleLogin = () => {
    router.push(`/login?backUrl=${encodeURIComponent(window?.location.href)}`);
  }

  return (
    <>
      {/* logo & toggler button */}
      <Box sx={{ width: downMD ? 'auto' : 228, display: 'flex', alignItems: 'center' }}>
        <Box component="span" sx={{ flexGrow: 1 }}>
          <LogoSection />
        </Box>
        { sidebar == 1 && (
          <Avatar
            variant="rounded"
            sx={{
              ...theme.typography.commonAvatar,
              ...theme.typography.mediumAvatar,
              overflow: 'hidden',
              transition: 'all .2s ease-in-out',
              bgcolor: 'secondary.light',
              color: 'secondary.dark',
              '&:hover': {
                bgcolor: 'secondary.dark',
                color: 'secondary.light'
              }
            }}
            onClick={drawerToggle}
            color="inherit"
          >
            <MenuIcon />
          </Avatar>
        ) }
      </Box>

      {/* header search */}
      <SearchSection />
      <Box sx={{ flexGrow: 1 }} />
      <Box sx={{ flexGrow: 1 }} />

      {loading ? (
        <CircularProgress size={20} sx={{ mx: 2 }} />
      ) : user ? (
        <>
          {/* notification */}
          <NotificationSection />

          { sidebar == 0 && (
            <Button
              className="px-2 ms-3"
              sx={{
                minWidth: 30,
                borderRadius: '0.5rem',
                "&:hover": { color: "#fff" },
                "& .MuiButton-startIcon": {
                  margin: 0,
                },
              }}
              variant="contained"
              color="secondary"
              startIcon={<HouseIcon />}
              onClick={() => router.push("/dashboard")}
            >
              <span className="d-none d-md-block ms-2">Dashboard</span>
            </Button>
          )}

          {/* profile */}
          <ProfileSection user={user} />
        </>
      ) : (
        <Button onClick={handleLogin} className="btn btn-warning">
          <i className="bi bi-box-arrow-right"></i>
          <span className="d-none d-md-inline ms-2">Đăng nhập</span>
        </Button>
      )}
    </>
  );
}
