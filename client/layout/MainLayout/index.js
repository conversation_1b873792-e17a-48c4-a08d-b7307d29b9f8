"use client";

import { useState, useEffect, useCallback, useMemo } from 'react';
import { useDispatch, useSelector } from "react-redux";

// material-ui
import { useTheme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import AppBar from '@mui/material/AppBar';
import Toolbar from '@mui/material/Toolbar';
import Box from '@mui/material/Box';

// project imports
import Footer from './Footer';
import Header from './Header';
import Sidebar from './Sidebar';
import MainContentStyled from './MainContentStyled';
import Customization from '../Customization';
import Breadcrumbs from '@/components/extended/Breadcrumbs';

import { MENU_TOGGLE, SET_CUSTOMIZATION } from "@/store/actions";
import { keyCustomization } from '@/constant';
import localStorageHelper from "@/utils/localStorageHelper";

// ==============================|| MAIN LAYOUT ||============================== //

export default function MainLayout({ sidebar = 1, children }) {
  const theme = useTheme();
  const dispatch = useDispatch();
  const downMD = useMediaQuery(theme.breakpoints.down('md'));
  const { borderRadius, opened } = useSelector((state) => state.customization);
  const { user } = useSelector((state) => state.auth);

  const [sidebarValue, setSidebarValue] = useState(sidebar);

  useEffect(() => {
    if (localStorageHelper.isSupported()) {
      const stored = localStorageHelper.get(keyCustomization);

      if (stored) {
        dispatch({
          type: SET_CUSTOMIZATION,
          payload: stored,
        });
      }
    }
  }, []);

  useEffect(() => {
    downMD && dispatch({ type: MENU_TOGGLE, opened: false });
  }, [downMD]);

  useEffect(() => {
    if (user && sidebar > 1) {
      if (!downMD) dispatch({ type: MENU_TOGGLE, opened: !opened });
      setSidebarValue(1);
    }
  }, [user]);

  const handleDrawerToggle = useCallback(() => {
    dispatch({ type: MENU_TOGGLE, opened: !opened });
  }, [dispatch, opened]);

  // horizontal menu-list bar : drawer

  return (
    <Box sx={{ display: 'flex' }}>
      <Customization />
      {/* header */}
      <AppBar enableColorOnDark position="fixed" color="inherit" elevation={0} sx={{ bgcolor: 'background.default', borderBottom: '1px solid #09090933', minHeight: '58px' }}>
        <Toolbar sx={{ p: 2 }}>
          <Header sidebar={sidebarValue} drawerToggle={handleDrawerToggle} />
        </Toolbar>
      </AppBar>

      {/* menu / drawer */}
      { sidebarValue == 1 && <Sidebar drawerToggle={handleDrawerToggle} /> }

      {/* main content */}
      <MainContentStyled {...{ borderRadius, open: sidebarValue == 1 ? opened : true }}>
        <Box sx={{ ...{ px: { xs: 0 } }, minHeight: 'calc(100vh - 108px)', display: 'flex', flexDirection: 'column' }}>
          {/* breadcrumb */}
          <Breadcrumbs />
          { children }
          <Footer />
        </Box>
      </MainContentStyled>
    </Box>
  );
}
