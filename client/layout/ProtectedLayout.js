"use client";

import { useRouter } from "next/navigation";
import React, { useEffect, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import Loader from '@/components/Loader';
import { fetchUser, clearUser } from "@/slices/authSlice";

const ProtectedLayout = React.memo(({ initialUser, children }) => {
  console.log("ProtectedLayout");
  const dispatch = useDispatch();
  const router = useRouter();
  const { user, loading } = useSelector(state => state.auth);
  const didRun = useRef(false); // chống double-run trong StrictMode

  useEffect(() => {
    if (didRun.current) return;
    didRun.current = true;

    if (!user) {
      (async () => {
        try {
          const fetched = await dispatch(fetchUser()).unwrap();
          if (!fetched?.id) throw new Error('no user');
        } catch {
          dispatch(clearUser());
          router.replace(`/api/auth/logout?backUrl=${encodeURIComponent(window?.location.href || '')}`);
        }
      })();
    }
  }, [user, dispatch, router]);

  if (loading || !user) return <Loader />;

  return <>{children}</>;
});

ProtectedLayout.displayName = "ProtectedLayout";

export default ProtectedLayout;
