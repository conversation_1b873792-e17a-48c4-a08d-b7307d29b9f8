"use client";

import React, { useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";

import {keyStorageIsLoginGoogle, keyStorageUser} from '@/constant';
import localStorageHelper from "@/utils/localStorageHelper";
import { fetchUser, setUser } from "@/slices/authSlice";

import CircularProgress from "@mui/material/CircularProgress";

const PublicLayout = React.memo(({ children }) => {
  const dispatch = useDispatch();
  const { user } = useSelector(state => state.auth);

  useEffect(() => {
    if (!user) {
      const isLoginGoogle = localStorageHelper.get(keyStorageIsLoginGoogle) || null;
      const storedUser = localStorageHelper.get(keyStorageUser) || null;
      dispatch(setUser({user: storedUser}));

      if (isLoginGoogle) {
        dispatch(fetchUser()).finally(() => {
          localStorageHelper.remove(keyStorageIsLoginGoogle);
        });
      }
    }
  }, [user, dispatch]);

  return <>{children}</>;
});

PublicLayout.displayName = "PublicLayout";

export default PublicLayout;
