import { useState } from 'react';
import { useSelector } from "react-redux";

import FormControlLabel from '@mui/material/FormControlLabel';
import Radio from '@mui/material/Radio';
import RadioGroup from '@mui/material/RadioGroup';
import Typography from '@mui/material/Typography';

// ==============================|| CUSTOMIZATION - BORDER OPTIONS ||============================== //

export default function BorderCard({ onChangeCustomization }) {
  const customization = useSelector((state) => state.customization);
  const [borderCard, setBorderCard] = useState(customization.borderCard);

  const handleBorderCardChange = (event) => {
    setBorderCard(event.target.value);
    onChangeCustomization({ borderCard: event.target.value });
  };

  const options = [
    {
      id: 'none',
      label: 'No border',
      value: 'none',
    },
    {
      id: '1_solid',
      label: 'Solid 1px',
      value: '1px solid #09090933',
    },
  ];

  return (
    <div className="p-2 w-100">
      <Typography variant="h5" className="mb-3">BORDER CARD</Typography>
      <RadioGroup
        aria-label="border-card"
        name="border-card"
        value={borderCard}
        onChange={handleBorderCardChange}
        className="w-100"
      >
        {options.map((item, index) => (
          <FormControlLabel
            key={index}
            value={item.value}
            control={<Radio />}
            label={
              <div
                className="p-2 w-100 rounded d-block mt-1 mb-1"
                style={{
                  border: item.value,
                  minHeight: '36px'
                }}
              >
                <span className="d-block">{item.label}</span>
              </div>
            }
            className="w-100 m-0 d-flex"
            sx={{
              '& .MuiFormControlLabel-label': {
                width: '100%'
              }
            }}
          />
        ))}
      </RadioGroup>
    </div>
  );
}
