import { useState, useCallback } from 'react';
import { useDispatch, useSelector } from "react-redux";

// material-ui
import { useTheme } from '@mui/material/styles';
import Divider from '@mui/material/Divider';
import Drawer from '@mui/material/Drawer';
import Fab from '@mui/material/Fab';
import Grid from '@mui/material/Grid';
import IconButton from '@mui/material/IconButton';
import Tooltip from '@mui/material/Tooltip';

// third party
import PerfectScrollbar from 'react-perfect-scrollbar';

// project imports
import FontFamily from './FontFamily';
import BorderRadius from './BorderRadius';
import BorderCard from './BorderCard';

import { SET_CUSTOMIZATION } from '@/store/actions';
import { keyCustomization } from '@/constant';
import localStorageHelper from "@/utils/localStorageHelper";

import AnimateButton from '@/components/extended/AnimateButton';

// assets
import SettingsIcon from '@mui/icons-material/Settings';
// ==============================|| LIVE CUSTOMIZATION ||============================== //

export default function Customization() {
  const theme = useTheme();
  const dispatch = useDispatch();
  const customization = useSelector((state) => state.customization);

  const onChangeCustomization = useCallback((value) => {
    const newCustomization = {
      ...customization,
      ...value,
    };

    if (localStorageHelper.isSupported()) {
      localStorageHelper.set(keyCustomization, newCustomization);
    }

    dispatch({ type: SET_CUSTOMIZATION, payload: newCustomization });
  }, [customization, dispatch]);

  // drawer on/off
  const [open, setOpen] = useState(false);
  const handleToggle = () => {
    setOpen(!open);
  };

  return (
    <>
      {/* toggle button */}
      <Tooltip title="Live Customize">
        <Fab
          component="div"
          onClick={handleToggle}
          size="medium"
          variant="circular"
          color="secondary"
          sx={{
            borderRadius: 0,
            borderTopLeftRadius: '50%',
            borderBottomLeftRadius: '50%',
            borderTopRightRadius: '50%',
            borderBottomRightRadius: '4px',
            top: '25%',
            position: 'fixed',
            right: 10,
            zIndex: 1200,
            boxShadow: theme.customShadows.secondary
          }}
        >
          <AnimateButton type="rotate">
            <IconButton color="inherit" size="large" disableRipple aria-label="live customize">
              <SettingsIcon />
            </IconButton>
          </AnimateButton>
        </Fab>
      </Tooltip>
      <Drawer anchor="right" onClose={handleToggle} open={open} PaperProps={{ sx: { width: 280 } }}>
        <PerfectScrollbar>
          <Grid container spacing={2}>
            <Grid size={12}>
              {/* font family */}
              <FontFamily onChangeCustomization={onChangeCustomization} />
              <Divider />
            </Grid>
            <Grid size={12}>
              {/* border radius */}
              <BorderRadius onChangeCustomization={onChangeCustomization} />
              <Divider />
            </Grid>
            <Grid size={12}>
              {/* border style */}
              <BorderCard onChangeCustomization={onChangeCustomization} />
              <Divider />
            </Grid>
          </Grid>
        </PerfectScrollbar>
      </Drawer>
    </>
  );
}
