{"name": "client", "version": "0.1.0", "private": true, "scripts": {"re_build": "node scripts/build-theme.js", "dev": "next dev", "dev2": "next dev --turbopack", "build": "next build", "build2": "next build --turbopack", "start": "next start", "lint": "next lint"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/cache": "^11.14.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@mui/icons-material": "^7.3.2", "@mui/material": "^7.3.2", "@mui/material-nextjs": "^7.3.2", "@mui/x-charts": "^8.11.1", "@mui/x-date-pickers": "^8.11.1", "@reduxjs/toolkit": "^2.9.0", "axios": "^1.11.0", "bootstrap-icons": "^1.13.1", "classnames": "^2.5.1", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.18", "formik": "^2.4.6", "framer-motion": "^12.23.12", "howler": "^2.2.4", "html-react-parser": "^5.2.6", "laravel-echo": "^1.16.1", "lodash": "^4.17.21", "material-ui-popup-state": "^5.3.6", "next": "^15.5.2", "next-intl": "^4.3.6", "prop-types": "^15.8.1", "pusher-js": "^8.4.0", "qrcode.react": "^4.2.0", "react": "^19.1.1", "react-dom": "^19.1.1", "react-hot-toast": "^2.6.0", "react-image-crop": "^11.0.10", "react-perfect-scrollbar": "^1.5.8", "react-redux": "^9.2.0", "swiper": "^11.2.10", "swr": "^2.3.6", "uuid": "^12.0.0", "yup": "^1.7.0"}, "devDependencies": {"@types/node": "^24.3.1", "@types/react": "^19.1.12", "@types/react-dom": "^19.1.9", "autoprefixer": "^10.4.20", "eslint": "^9.35.0", "eslint-config-next": "^15.5.2", "postcss": "^8.5.6", "sass": "^1.92.1", "typescript": "^5.9.2"}}