import React, { createContext, useContext, useState, useCallback } from 'react';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';

const ConfirmContext = createContext(null);

export function ConfirmProvider({ children }) {
  const [dialogState, setDialogState] = useState({
    open: false,
    title: 'Confirm',
    message: 'Are you sure?',
    hasClose: true,
    resolvePromise: null,
  });

  const confirm = useCallback((title, message, hasClose = true) => {
    return new Promise((resolve) => {
      setDialogState({
        open: true,
        title,
        message,
        hasClose,
        resolvePromise: resolve,
      });
    });
  }, []);

  const handleConfirm = useCallback(() => {
    if (dialogState.resolvePromise) {
      dialogState.resolvePromise(true);
    }
    setDialogState((prev) => ({ ...prev, open: false, resolvePromise: null }));
  }, [dialogState]);

  const handleCancel = useCallback(() => {
    if (dialogState.resolvePromise) {
      dialogState.resolvePromise(false);
    }
    setDialogState((prev) => ({ ...prev, open: false, resolvePromise: null }));
  }, [dialogState]);

  return (
    <ConfirmContext.Provider value={confirm}>
      {children}
      <Dialog open={dialogState.open} onClose={handleCancel} fullWidth>
        <DialogTitle className="h5 fw-normal py-1">
          <i className="bi bi-exclamation-octagon-fill text-warning me-1"></i>
          {dialogState.title}
        </DialogTitle>
        <DialogContent className="pb-2">
          <DialogContentText
            variant="subtitle"
            className="fw-light fs-14"
          >
            {dialogState.message}
          </DialogContentText>
        </DialogContent>
        <DialogActions className="justify-content-end">
          {dialogState.hasClose && (
            <Button onClick={handleCancel} color="inherit" variant="outlined">
              Hủy
            </Button>
          )}
          <Button onClick={handleConfirm} color="error" variant="contained">
            OK
          </Button>
        </DialogActions>
      </Dialog>
    </ConfirmContext.Provider>
  );
}

export function useConfirm() {
  const confirm = useContext(ConfirmContext);
  if (!confirm) {
    throw new Error("useConfirm must be used within ConfirmProvider");
  }
  return confirm;
}
