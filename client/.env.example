# -----------------------------------------------------------------------------
# Cá<PERSON> biến NEXT_PUBLIC_* cần thêm tương ứng vào .docker/.env
# -----------------------------------------------------------------------------

NEXT_PRIVATE_DEBUG_CACHE=1
# same in APP_NAME at backend
APP_NAME=Laravel
NEXT_PUBLIC_HOST_URL=http://localhost:3000
NEXT_PUBLIC_EDITOR_DOMAIN=http://localhost:800/
# trong TH dùng docker thì BACKEND_URL phải dựa vào service_name của container (ở đây là http://backend_nginx). Còn không dùng docker thì 2 url này là 1
# for client call
NEXT_PUBLIC_BACKEND_URL=http://localhost:8000
# for server call nội bộ
BACKEND_URL=http://localhost:8000
# COOKIE_NAME bắt buộc không được nằm trong dấu ""
USER_INFO_COOKIE_NAME=_u_info_
# SESSION_DOMAIN bắt buộc giống SESSION_DOMAIN ở .env server
SESSION_DOMAIN=.localhost

NEXT_PUBLIC_GOOGLE_CLIENT_ID=
NEXT_PUBLIC_GOOGLE_API_KEY=
