import { useEffect, useState, useRef, useCallback } from "react";

export function useFakeProgress({
  max = 90,
  interval = 200,
  step = 15,
  autoStart = false
} = {}) {
  const [progress, setProgress] = useState(0);
  const intervalRef = useRef(null);

  const clear = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  }, []);

  const start = useCallback(() => {
    setProgress(0);
    clear();

    intervalRef.current = setInterval(() => {
      setProgress(prev => {
        if (prev >= max) {
          clear();
          return prev;
        }
        const next = prev + Math.random() * step;
        return Math.round(next);
      });
    }, interval);
  }, [max, interval, step, clear]);

  const reset = useCallback(() => {
    clear();
    setProgress(0);
  }, [clear]);

  useEffect(() => {
    if (autoStart) start();
    return () => clear();
  }, [autoStart, start, clear]);

  return { progress, setProgress, start, reset };
}
