"use client";

import useSWRImmutable from "swr/immutable";
import { axios, setBearerToken } from "@/lib/axios";
import { apiUrl } from "@/lib/fetcher";
import { useEffect } from "react";
import { useParams, useRouter } from "next/navigation";

export const useAuth = ({ middleware, redirectIfAuthenticated } = {}) => {
  const router = useRouter();
  const params = useParams();

  const {
    data: user,
    error,
    isLoading: userLoading,
    mutate,
  } = useSWRImmutable(
    apiUrl("/api/user"),
    () =>
      axios
        .get(apiUrl("/api/user"))
        .then((res) => {
          return res.data || res;
        })
        .catch((error) => {
          const status = error.status;

          if (status !== 409) throw error;

          router.push("/verify-email");
        }),
    {
      errorRetryCount: 0,
      errorRetryInterval: 1000,
    }
  );

  const csrf = () => axios.get(apiUrl("/sanctum/csrf-cookie"));

  const register = async ({ setErrors, ...props }) => {
    await csrf();

    setErrors([]);

    axios
      .post(apiUrl("/register"), props)
      .then((res) => {
        mutate();
        if (res.token) setBearerToken(res.token);
      })
      .catch((error) => {
        const status = error.status;

        if (status !== 422) throw error;

        setErrors(error.errors);
      });
  };

  const login = async ({ setErrors, setStatus, ...props }) => {
    await csrf();

    setErrors([]);
    setStatus(null);

    axios
      .post(apiUrl("/login"), props)
      .then((res) => {
        mutate();
        if (res.token) setBearerToken(res.token);
      })
      .catch((error) => {
        const status = error.status;

        if (status !== 422) throw error;

        setErrors(error.errors);
      });
  };

  const forgotPassword = async ({ setErrors, setStatus, email }) => {
    await csrf();

    setErrors([]);
    setStatus(null);

    axios
      .post(apiUrl("/forgot-password"), { email })
      .then((response) => setStatus(response.data.status))
      .catch((error) => {
        if (error.response.status !== 422) throw error;

        setErrors(error.response.data.errors);
      });
  };

  const resetPassword = async ({ setErrors, setStatus, ...props }) => {
    await csrf();

    setErrors([]);
    setStatus(null);

    axios
      .post(apiUrl("/reset-password"), { token: params.token, ...props })
      .then((response) =>
        router.push("/login?reset=" + btoa(response.data.status))
      )
      .catch((error) => {
        if (error.response.status !== 422) throw error;

        setErrors(error.response.data.errors);
      });
  };

  const resendEmailVerification = ({ setStatus }) => {
    axios
      .post(apiUrl("/email/verification-notification"))
      .then((response) => setStatus(response.data.status));
  };

  const logout = async () => {
    if (!error) {
      await axios.post(apiUrl("/logout")).then(() => mutate());
    }

    window.location.pathname = "/";
  };

  useEffect(() => {
    if (middleware === "guest" && redirectIfAuthenticated && user)
      router.push(redirectIfAuthenticated);
    if (window.location.pathname === "/verify-email" && user?.email_verified_at)
      router.push(redirectIfAuthenticated);
    if (middleware === "auth" && error) logout();
  }, [user, error]);

  return {
    user,
    userLoading,
    register,
    login,
    forgotPassword,
    resetPassword,
    resendEmailVerification,
    logout,
  };
};
