"use client";

import { useState, useRef, useCallback } from "react";
import toast from "react-hot-toast";
import { validateFileType, validateFileSize, getFileTypeConfig } from "@/utils/importHelper";

/**
 * Custom hook for handling file import functionality
 * @param {Object} options - Configuration options
 * @param {string} options.fileType - Type of files to accept ('excel', 'word', 'image')
 * @param {number} options.maxSizeBytes - Maximum file size in bytes (default: 10MB)
 * @param {string[]} options.allowedTypes - Custom allowed MIME types (overrides fileType)
 * @param {Function} options.onFileSelect - Callback when file is selected
 * @param {Function} options.onFileRemove - Callback when file is removed
 * @param {Object} options.customMessages - Custom validation messages
 * @returns {Object} Hook state and handlers
 */
export const useFileImport = ({
  fileType = 'excel',
  maxSizeBytes = 10 * 1024 * 1024, // 10MB default
  allowedTypes = null,
  onFileSelect = null,
  onFileRemove = null,
  customMessages = {}
} = {}) => {
  const [selectedFile, setSelectedFile] = useState(null);
  const [isDragOver, setIsDragOver] = useState(false);
  const [validationErrors, setValidationErrors] = useState([]);
  const fileInputRef = useRef(null);

  // Get file type configuration
  const fileConfig = getFileTypeConfig(fileType);
  const finalAllowedTypes = allowedTypes || fileConfig.allowedTypes;
  const messages = { ...fileConfig, ...customMessages };

  /**
   * Validate and select a file
   */
  const handleFileSelect = useCallback((file) => {
    if (!file) return;

    // Validate file type
    if (!validateFileType(file, finalAllowedTypes)) {
      toast.error(messages.errorMessage);
      return;
    }

    // Validate file size
    if (!validateFileSize(file, maxSizeBytes)) {
      const maxSizeMB = Math.round(maxSizeBytes / (1024 * 1024));
      toast.error(`File không được vượt quá ${maxSizeMB}MB`);
      return;
    }

    setSelectedFile(file);
    setValidationErrors([]);

    // Call custom callback if provided
    if (onFileSelect) {
      onFileSelect(file);
    }
  }, [finalAllowedTypes, maxSizeBytes, messages.errorMessage, onFileSelect]);

  /**
   * Handle file input change
   */
  const handleFileInputChange = useCallback((event) => {
    const file = event.target.files[0];
    if (file) {
      handleFileSelect(file);
    }
  }, [handleFileSelect]);

  /**
   * Handle drag and drop
   */
  const handleDrop = useCallback((event) => {
    event.preventDefault();
    setIsDragOver(false);

    const file = event.dataTransfer.files[0];
    if (file) {
      handleFileSelect(file);
    }
  }, [handleFileSelect]);

  const handleDragOver = useCallback((event) => {
    event.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((event) => {
    event.preventDefault();
    setIsDragOver(false);
  }, []);

  /**
   * Remove selected file
   */
  const removeSelectedFile = useCallback(() => {
    setSelectedFile(null);
    setValidationErrors([]);

    // Clear file input
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }

    // Call custom callback if provided
    if (onFileRemove) {
      onFileRemove();
    }
  }, [onFileRemove]);

  /**
   * Trigger file input click
   */
  const triggerFileInput = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  /**
   * Set validation errors from external source
   */
  const setErrors = useCallback((errors) => {
    setValidationErrors(Array.isArray(errors) ? errors : [errors]);
  }, []);

  /**
   * Clear validation errors
   */
  const clearErrors = useCallback(() => {
    setValidationErrors([]);
  }, []);

  /**
   * Reset all state (file and errors)
   */
  const resetState = useCallback(() => {
    setSelectedFile(null);
    setValidationErrors([]);

    // Clear file input
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  }, []);

  return {
    // State
    selectedFile,
    isDragOver,
    validationErrors,
    fileInputRef,

    // Configuration
    fileConfig: {
      ...fileConfig,
      allowedTypes: finalAllowedTypes,
      maxSizeBytes
    },

    // Handlers
    handleFileInputChange,
    handleDrop,
    handleDragOver,
    handleDragLeave,
    removeSelectedFile,
    triggerFileInput,

    // Additional state management methods
    setErrors,
    clearErrors,
    resetState,

    // Computed properties
    hasFile: !!selectedFile,
    hasErrors: validationErrors.length > 0
  };
};

export default useFileImport;
