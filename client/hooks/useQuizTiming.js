import { useState, useEffect, useRef, useCallback } from 'react';

/**
 * Custom hook for managing quiz timing
 * Tracks total quiz duration and individual question durations
 */
const useQuizTiming = () => {
  const [quizStartTime, setQuizStartTime] = useState(null);
  const [currentQuestionStartTime, setCurrentQuestionStartTime] = useState(null);
  const [questionTimes, setQuestionTimes] = useState({});
  const [totalDuration, setTotalDuration] = useState(0);

  const intervalRef = useRef(null);
  const isActiveRef = useRef(false);

  // Start the overall quiz timer
  const startQuiz = useCallback(() => {
    const now = Date.now();
    setQuizStartTime(now);
    isActiveRef.current = true;

    // Start interval to update total duration
    intervalRef.current = setInterval(() => {
      if (isActiveRef.current && quizStartTime) {
        setTotalDuration(Math.floor((Date.now() - now) / 1000));
      }
    }, 1000);
  }, []);

  // Start timing for a specific question
  const startQuestion = useCallback((questionId) => {
    const now = Date.now();
    setCurrentQuestionStartTime(now);

    // Store the start time for this question
    setQuestionTimes(prev => ({
      ...prev,
      [questionId]: {
        ...prev[questionId],
        startTime: now,
        duration: 0
      }
    }));
  }, []);

  // End timing for a specific question
  const endQuestion = useCallback((questionId) => {
    if (!currentQuestionStartTime) {
      console.warn("No active question to end.");
      return 0;
    }

    const now = Date.now();
    const duration = Math.floor((now - currentQuestionStartTime) / 1000);

    setQuestionTimes(prev => ({
      ...prev,
      [questionId]: {
        ...prev[questionId],
        endTime: now,
        duration: duration
      }
    }));

    setCurrentQuestionStartTime(null);
    return duration;
  }, [currentQuestionStartTime]);

  // End the overall quiz timer
  const endQuiz = useCallback(() => {
    isActiveRef.current = false;
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }

    if (quizStartTime) {
      const finalDuration = Math.floor((Date.now() - quizStartTime) / 1000);
      setTotalDuration(finalDuration);
      return finalDuration;
    }
    return totalDuration;
  }, [quizStartTime, totalDuration]);

  // Get duration for a specific question
  const getQuestionDuration = useCallback((questionId) => {
    return questionTimes[questionId]?.duration || 0;
  }, [questionTimes]);

  // Get current question duration (while in progress)
  const getCurrentQuestionDuration = useCallback(() => {
    if (!currentQuestionStartTime) return 0;
    return Math.floor((Date.now() - currentQuestionStartTime) / 1000);
  }, [currentQuestionStartTime]);

  // Cleanup on unmounting
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  return {
    // State
    quizStartTime,
    currentQuestionStartTime,
    questionTimes,
    totalDuration,

    // Actions
    startQuiz,
    startQuestion,
    endQuestion,
    endQuiz,

    // Getters
    getQuestionDuration,
    getCurrentQuestionDuration,

    // Computed
    isQuizActive: isActiveRef.current,
    isQuestionActive: currentQuestionStartTime !== null,
  };
};

export default useQuizTiming;
