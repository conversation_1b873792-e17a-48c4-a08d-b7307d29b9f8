import React, { useState, useRef, useEffect, useCallback } from 'react';

export default function useSound(
  src,
  {
    volume = 1,
    playbackRate = 1,
    soundEnabled = true,
    interrupt = false,
    onload,
    ...delegated
  } = {}
) {
  const HowlConstructor = useRef(null);
  const isMounted = useRef(false);
  const [duration, setDuration] = useState(null);
  const [sound, setSound] = useState(null);
  const isPlayingRef = useRef(false);

  const handleLoad = function () {
    onload?.call(this);
    if (isMounted.current) setDuration(this.duration() * 1000);
    setSound(this);
  };

  useEffect(() => {
    import('howler').then(mod => {
      if (!isMounted.current) {
        // Depending on the module system used, `mod` might hold
        // the export directly, or it might be under `default`.
        HowlConstructor.current = mod.Howl ?? mod.default.Howl;

        isMounted.current = true;

        new HowlConstructor.current({
          src: Array.isArray(src) ? src : [src],
          volume,
          rate: playbackRate,
          onload: handleLoad,
          ...delegated,
        });
      }
    });

    return () => {
      isMounted.current = false;
    };
  }, []);

  useEffect(() => {
    if (HowlConstructor.current && sound) {
      setSound(
        new HowlConstructor.current({
          src: Array.isArray(src) ? src : [src],
          volume,
          onload: handleLoad,
          ...delegated,
        })
      );
    }
  }, [JSON.stringify(src)]);

  useEffect(() => {
    if (sound) {
      sound.volume(volume);
      sound.rate(playbackRate);
    }
  }, [volume, playbackRate]);

  useEffect(() => {
    if (sound) {
      const handleVisibilityChange = () => {
        if (document.hidden) {
          sound?.pause();
        } else {
          if (isPlayingRef.current) sound?.play();
        }
      };

      document.addEventListener('visibilitychange', handleVisibilityChange);

      return () => {
        document.removeEventListener('visibilitychange', handleVisibilityChange);
      };
    }
  }, [sound]);

  const play = useCallback(
    (options = {}) => {
      if (!sound || (!soundEnabled && !options.forceSoundEnabled)) return;

      if (interrupt) sound.stop();

      if (options.playbackRate) sound.rate(options.playbackRate);

      sound.play(options.id);
      isPlayingRef.current = true;
    },
    [sound, soundEnabled, interrupt]
  );

  const stop = useCallback(id => {
    sound?.stop(id);
    isPlayingRef.current = false;
  }, [sound]);

  const pause = useCallback(id => {
    sound?.pause(id);
    isPlayingRef.current = false;
  }, [sound]);

  return [play, { sound, stop, pause, duration }];
}
