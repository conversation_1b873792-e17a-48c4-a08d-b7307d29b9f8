/**
 * Helper functions for traditional browser downloads
 */

/**
 * Download a blob as a file using traditional browser download
 * @param {Blob} blob - The blob to download
 * @param {string} filename - The filename for the download
 */
export const downloadBlob = (blob, filename) => {
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement("a");
  a.href = url;
  a.download = filename;
  a.style.display = 'none';
  document.body.appendChild(a);
  a.click();

  // Cleanup
  setTimeout(() => {
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
  }, 100);
};

/**
 * Download a file from a URL using traditional browser download
 * @param {string} url - The URL to download from
 * @param {string} filename - The filename for the download
 * @param {object} options - Fetch options
 */
export const downloadFromUrl = async (url, filename, options = {}) => {
  try {
    const response = await fetch(url, {
      credentials: "include",
      headers: {
        "X-Requested-With": "XMLHttpRequest",
      },
      ...options,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const blob = await response.blob();
    downloadBlob(blob, filename);
    
    return { success: true, blob };
  } catch (error) {
    console.error("Download error:", error);
    return { success: false, error };
  }
};

/**
 * Generate a filename with current date
 * @param {string} prefix - Filename prefix
 * @param {string} suffix - Filename suffix (e.g., classroom code)
 * @param {string} extension - File extension (default: .xlsx)
 */
export const generateFilename = (prefix, suffix = '', extension = '.xlsx') => {
  const currentDate = new Date().toLocaleDateString('vi-VN').replace(/\//g, '_');
  const parts = [prefix, suffix, currentDate].filter(Boolean);
  return parts.join('_') + extension;
};

/**
 * Check if the browser supports downloads
 */
export const supportsDownload = () => {
  return typeof window !== 'undefined' && 'URL' in window && 'createObjectURL' in window.URL;
};

/**
 * Get user's default download folder info (for display purposes)
 */
export const getDownloadInfo = () => {
  const userAgent = navigator.userAgent;
  let browserName = 'trình duyệt';
  
  if (userAgent.includes('Chrome')) {
    browserName = 'Chrome';
  } else if (userAgent.includes('Firefox')) {
    browserName = 'Firefox';
  } else if (userAgent.includes('Safari')) {
    browserName = 'Safari';
  } else if (userAgent.includes('Edge')) {
    browserName = 'Edge';
  }

  return {
    browserName,
    defaultFolder: 'Downloads',
    message: `File sẽ được tải xuống vào thư mục Downloads của ${browserName}`
  };
};
