export function downloadCSVStream(stream, fileName) {
  const url = window.URL.createObjectURL(new Blob([stream]));
  const link = document.createElement("a");
  link.href = url;
  link.setAttribute("download", fileName);
  document.body.appendChild(link);
  link.click();
  link.remove();
}

export const createFormdata = (data = {}, method = null) => {
  const formData = new FormData();
  // if need to put _method attribute
  method && formData.append("_method", method);

  // append each element
  for (const key in data) {
    // when input value is an array
    if (Array.isArray(data[key])) {
      for (const i in data[key]) {
        formData.append(`${key}[${i}]`, data[key][i]);
      }
    } else if (data[key]) {
      // if element is not null and not an array
      formData.append(key, data[key]);
    }
  }

  return formData;
};

export const imageFormats = (formats = []) => {
  const defaultFormats = ["image/jpg", "image/jpeg", "image/png"];
  return formats.length > 0 ? [...defaultFormats, ...formats] : defaultFormats;
};

export const timeElapsedString = (time) => {
  let timeElapsed = Math.floor((Date.now() - new Date(time).getTime()) / 1000);
  timeElapsed = timeElapsed < 1 ? 1 : timeElapsed;

  const datas = [
    { unit: 31536000, text: "năm" },
    { unit: 2592000, text: "tháng" },
    { unit: 604800, text: "tuần" },
    { unit: 86400, text: "ngày" },
    { unit: 3600, text: "giờ" },
    { unit: 60, text: "phút" },
    { unit: 1, text: "giây" },
  ];

  for (const { unit, text } of datas) {
    if (timeElapsed < unit) continue;
    const numberOfUnits = Math.floor(timeElapsed / unit);
    return `${numberOfUnits} ${text} trước`;
  }
};

export function getRandomBrightColor(opacity = 1) {
  const red = Math.floor(Math.random() * 156 + 100);
  const green = Math.floor(Math.random() * 156 + 100);
  const blue = Math.floor(Math.random() * 156 + 100);

  // Tạo chuỗi hex từ ba giá trị RGB
  const color = `rgba(${red}, ${green}, ${blue}, ${opacity})`;

  return color;
}

export function getRandomDarkColor(opacity = 1) {
  const red = Math.floor(Math.random() * 100);
  const green = Math.floor(Math.random() * 100);
  const blue = Math.floor(Math.random() * 100);

  // Tạo chuỗi hex từ ba giá trị RGB
  const color = `rgba(${red}, ${green}, ${blue}, ${opacity})`;

  return color;
}

export function findElementByKeyValue(array, key, value) {
  return array.find((element) => element[key] === value);
}

export function mapDataQuestionJson(qOrigin, qOther) {
  return {
    ...qOrigin,
    content_json: {
      ...qOrigin.content_json,
      ...Object.keys(qOrigin.content_json).reduce((item, key) => {
        if (qOther.content_json.hasOwnProperty(key)) {
          item[key] = qOther.content_json[key];
        }
        return item;
      }, {}),
    },
  };
}

export const checkOverflow = (selector) => {
  if (selector) {
    const elements = document.querySelectorAll(selector);

    elements.forEach((element) => {
      const hasOverflowY = element.scrollHeight > element.clientHeight;
      const hasOverflowX = element.scrollWidth > element.clientWidth;

      element.classList.toggle("has-overflow-y", hasOverflowY);
      element.classList.toggle("has-overflow-x", hasOverflowX);
    });
  }
};

export const buildQueryString = ({ keyword, filter }) => {
  const params = new URLSearchParams();

  const trimmedKeyword = keyword.trim();

  if (trimmedKeyword !== "") {
    params.set("q", trimmedKeyword);
  }

  Object.entries(filter).forEach(([key, value]) => {
    if (Array.isArray(value)) {
      if (value.length > 0) {
        const joined = value.join(",");
        if (joined.trim() !== "") {
          params.set(key, joined);
        }
      }
    } else if (value !== null && value !== undefined) {
      const strValue = String(value).trim();

      if (strValue !== "") {
        params.set(key, strValue);
      }
    }
  });

  return params.toString();
}

const handleFormatDateOfWeek = (date) => {
  const daysOfWeek = [
    "Chủ Nhật",
    "Thứ 2",
    "Thứ 3",
    "Thứ 4",
    "Thứ 5",
    "Thứ 6",
    "Thứ 7",
  ];

  const dayOfWeek = daysOfWeek[date.getDay()];
  const day = String(date.getDate()).padStart(2, "0");
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const year = date.getFullYear();

  return `${dayOfWeek}, Ngày ${day}/${month}/${year}`;
};

export const formatDateOfWeek = (dateString) => {
  const date = new Date(dateString);
  return handleFormatDateOfWeek(date);
};

export const formatDateOfWeekFromTs = (timestamp) => {
  const date = new Date(timestamp * 1000);
  return handleFormatDateOfWeek(date);
};

export const formatDateTime = (dateTimeString, showTime = true) => {
  const date = new Date(dateTimeString);

  const padTo2Digits = (num) => {
    return num.toString().padStart(2, "0");
  };

  const formattedDate = [
    date.getFullYear(),
    padTo2Digits(date.getMonth() + 1),
    padTo2Digits(date.getDate()),
  ].join("-");

  const formattedTime = [
    padTo2Digits(date.getHours()),
    padTo2Digits(date.getMinutes()),
    padTo2Digits(date.getSeconds()),
  ].join(":");

  if (!showTime) return formattedDate;

  return `${formattedDate} ${formattedTime}`;
};

export const splitPositionStr = (pathname, position) => {
  const parts = pathname.split("/");
  return parts.length > position ? parts[position] : null;
};

export const isClient = () => typeof window !== "undefined";

export const cleanString = (str) => {
  return str.trim().replace(/\s+/g, "").toLowerCase();
};

export const decodeSlug = (fatSlug) => {
  const [fat = null, slug = null, id = null] =
    fatSlug.match(/^(.+)-([0-9]+)$/) || [];

  return { slug, id };
};

export const isBase64 = (str) => {
  if (typeof str !== "string") {
    return false;
  }

  // Loại bỏ padding '='
  str = str.trim().replace(/^data:image\/[a-z]+;base64,/, "");

  // Kiểm tra định dạng Base64
  const regex =
    /^(?:[A-Za-z0-9+\/]{4})*(?:[A-Za-z0-9+\/]{2}==|[A-Za-z0-9+\/]{3}=)?$/;

  return regex.test(str);
};

export const getImageByProxy = (url) => {
  if (!url) return "";

  // Kiểm tra xem đang chạy trên client hay server
  if (!isClient) {
    // Trên server, trả về URL gốc
    return url;
  }

  try {
    const imageUrl = new URL(url, window.location.href);
    const currentOrigin = window.location.origin;

    if (imageUrl.origin !== currentOrigin) {
      // Ảnh từ domain khác, sử dụng proxy
      return `/api/proxy/image?url=${encodeURIComponent(url)}`;
    } else {
      // Ảnh cùng domain, sử dụng URL trực tiếp
      return url;
    }
  } catch (error) {
    console.error("Invalid image URL:", url);
    return url;
  }
};

export const getColorByPercentage = (percentage) => {
  if (percentage >= 70) return "#4caf50";  // green
  if (percentage >= 40) return "#ff9800";  // orange
  return "#f44336";  // red
};

export function buildTree(items, {
  idKey             = 'id',
  parentKey         = 'parent_id',
  rootId            = null,
  transform         = item => item,
  isActive          = node => false,
  sortFn            = null,
} = {}) {
  const map = new Map();

  for (const raw of items) {
    const node = transform(raw);
    node.children = [];
    const active = Boolean(isActive(node));
    node.expanded = active;
    map.set(node[idKey], node);
  }

  let roots = [];

  for (const node of map.values()) {
    const pid = node[parentKey];

    if (pid === rootId || !map.has(pid)) {
      roots.push(node);
    } else {
      const parent = map.get(pid);
      parent.children.push(node);

      // Nếu node này active/expanded, bubble flag lên tổ tiên
      if (node.expanded) {
        let anc = parent;

        while (anc) {
          if (anc.expanded) break;
          anc.expanded = true;
          anc = map.get(anc[parentKey]);
        }
      }
    }
  }

  if (typeof sortFn === 'function') {
    const sortRecursively = nodes => {
      nodes.sort(sortFn);
      for (const n of nodes) {
        if (n.children.length) {
          sortRecursively(n.children);
        }
      }
    };
    sortRecursively(roots);
  }

  return roots;
}

export function decodeBase64Cookies(base64) {
  if (!base64) return null;

  try {
    const json = atob(decodeURIComponent(base64));

    return JSON.parse(json);
  } catch (error) {
    console.error("Failed to decode base64 cookie:", error);
    return null;
  }
}

export const convertToSlug = (title) => {
  return title
    .toLowerCase() // Chuyển thành chữ thường
    .trim() // Loại bỏ khoảng trắng đầu và cuối
    .normalize('NFD') // Chuẩn hóa để tách dấu
    .replace(/[\u0300-\u036f]/g, '') // Loại bỏ dấu tiếng Việt
    .replace(/đ/g, 'd') // Thay thế 'đ' bằng 'd'
    .replace(/[^a-z0-9\s-]/g, '') // Loại bỏ ký tự đặc biệt
    .replace(/\s+/g, '-') // Thay thế khoảng trắng bằng dấu gạch ngang
    .replace(/-+/g, '-') // Loại bỏ các dấu gạch ngang thừa
    .replace(/^-+|-+$/g, ''); // Loại bỏ dấu gạch ngang ở đầu và cuối
}

export const getDayCurrent = (separator = '_') => {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    return [year, month, day].join(separator);
};

// Format duration to human-readable format
export const formatDuration = (seconds) => {
  if (!seconds || seconds === 0) return '0s';

  if (seconds < 60) {
    return `${seconds}s`;
  } else if (seconds < 3600) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return remainingSeconds > 0 ? `${minutes}m ${remainingSeconds}s` : `${minutes}m`;
  } else {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;
    return remainingSeconds > 0 ? `${hours}h ${minutes}m ${remainingSeconds}s` : `${hours}h ${minutes}m`;
  }
};

export const delay = (ms) => new Promise((resolve) => setTimeout(resolve, ms));
