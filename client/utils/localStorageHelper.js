const localStorageHelper = {
  /**
   * Determine if browser supports local storage.
   *
   * @return {boolean}
   */
  isSupported() {
    return typeof window !== "undefined" && !!window.localStorage;
  },

  /**
   * Check if key exists in local storage.
   *
   * @param {string} key
   * @return {boolean}
   */
  has(key) {
    return this.isSupported() ? localStorage.getItem(key) !== null : false;
  },

  /**
   * Retrieve an object from local storage.
   *
   * @param {string} key
   * @return {any}
   */
  get(key) {
    if (!this.isSupported()) return null;

    let item = localStorage.getItem(key);

    if (item === null) return null;
    if (item === "undefined") return undefined;
    if (item === "null") return null;

    try {
      return JSON.parse(item);
    } catch (error) {
      return item;
    }
  },

  /**
   * Save some value to local storage.
   *
   * @param {string} key
   * @param {any} value
   * @return {void}
   */
  set(key, value) {
    if (!this.isSupported()) return;

    if (typeof key !== "string") {
      throw new TypeError(`localStorage: Key must be a string. (reading '${key}')`);
    }

    const storeValue = typeof value === "string" ? value : JSON.stringify(value);

    localStorage.setItem(key, storeValue);
  },

  /**
   * Remove element from local storage.
   *
   * @param {string} key
   * @return {void}
   */
  remove(key) {
    if (this.isSupported()) {
      localStorage.removeItem(key);
    }
  },
};

export default localStorageHelper;
