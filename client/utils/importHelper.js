/**
 * Format file size in bytes to human-readable format
 * @param {number} bytes - File size in bytes
 * @returns {string} Formatted file size
 */
export const formatFileSize = (bytes) => {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

/**
 * Validate file type against allowed types
 * @param {File} file - File to validate
 * @param {string[]} allowedTypes - Array of allowed MIME types
 * @returns {boolean} True if file type is allowed
 */
export const validateFileType = (file, allowedTypes) => {
  return allowedTypes.includes(file.type);
};

/**
 * Validate file size against maximum size
 * @param {File} file - File to validate
 * @param {number} maxSizeBytes - Maximum file size in bytes
 * @returns {boolean} True if file size is within limit
 */
export const validateFileSize = (file, maxSizeBytes) => {
  return file.size <= maxSizeBytes;
};

/**
 * Get file type configuration for different import types
 * @param {string} type - Import type ('excel', 'word', 'image', etc.)
 * @returns {object} Configuration object with allowed types, extensions, and messages
 */
export const getFileTypeConfig = (type) => {
  const configs = {
    excel: {
      allowedTypes: [
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        "application/vnd.ms-excel"
      ],
      extensions: ".xlsx,.xls",
      acceptMessage: "Chỉ chấp nhận file Excel (.xlsx, .xls)",
      errorMessage: "Chỉ chấp nhận file Excel (.xlsx, .xls)",
      chipLabel: "Excel",
      chipColor: "success",
      icon: "bi-file-earmark-excel"
    },
    word: {
      allowedTypes: [
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document", // .docx
        "application/msword", // .doc
        "application/vnd.ms-word", // Một số trường hợp khác
        "application/octet-stream" // Đôi khi file Word có type này
      ],
      extensions: ".docx,.doc",
      acceptMessage: "Chỉ chấp nhận file .doc, .docx",
      errorMessage: "Chỉ chấp nhận file Word (.docx, .doc)",
      chipLabel: "Word",
      chipColor: "primary",
      icon: "bi-file-earmark-word"
    },
    image: {
      allowedTypes: [
        "image/jpeg",
        "image/jpg",
        "image/png",
        "image/gif",
        "image/svg+xml"
      ],
      extensions: ".jpg,.jpeg,.png,.gif,.svg",
      acceptMessage: "Chỉ chấp nhận file ảnh (.jpg, .png, .gif, .svg)",
      errorMessage: "Chỉ chấp nhận file ảnh (.jpg, .png, .gif, .svg)",
      chipLabel: "Image File",
      chipColor: "info",
      icon: "bi-file-earmark-image"
    }
  };

  return configs[type] || configs.excel;
};
