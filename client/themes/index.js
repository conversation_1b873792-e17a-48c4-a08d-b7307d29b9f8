"use client";

import PropTypes from 'prop-types';
import { useMemo } from 'react';
import { useSelector } from "react-redux";

// material-ui
import { useTheme, ThemeProvider, createTheme, StyledEngineProvider } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';

// project imports
import Palette from './palette';
import Typography from './typography';

import componentStyleOverrides from './compStyleOverride';
import customShadows from './shadows';

import Notification from '@/components/Notification';

export default function ThemeCustomization({ children }) {
  const parentTheme = useTheme();

  const {
    borderRadius,
    fontFamily,
    mode,
    outlinedFilled,
    presetColor,
    borderCard,
  } = useSelector((state) => state.customization);

  const themePalette = useMemo(() => Palette(mode, presetColor), [
    mode,
    presetColor,
  ]);
  const themeTypography = useMemo(
    () => Typography(themePalette, borderRadius, fontFamily),
    [themePalette, borderRadius, fontFamily]
  );
  const themeCustomShadows =  useMemo(
    () => customShadows(mode, themePalette),
    [mode, themePalette]
  );

  const themeOptions = useMemo(
    () => ({
      direction: 'ltr',
      palette: {
        ...parentTheme.palette,
        ...themePalette.palette,
      },
      typography: themeTypography,
      customShadows: themeCustomShadows,
      mixins: {
        toolbar: {
          minHeight: '48px',
          padding: '8px 16px!important',
          alignItems: 'center',
          '@media (min-width: 600px)': {
            minHeight: '48px'
          }
        }
      },
    }),
    [parentTheme, themePalette, themeCustomShadows, themeTypography]
  );

  const dynamicTheme = useMemo(() => createTheme(themeOptions), [themeOptions]);

  dynamicTheme.components = useMemo(
    () => componentStyleOverrides(dynamicTheme, borderRadius, outlinedFilled, borderCard),
    [dynamicTheme, borderRadius, outlinedFilled, borderCard]
  );

  return (
    // <StyledEngineProvider injectFirst>
      <ThemeProvider theme={dynamicTheme}>
        <CssBaseline enableColorScheme />
        {children}
        <Notification />
      </ThemeProvider>
    // </StyledEngineProvider>
  );
}

ThemeCustomization.propTypes = { children: PropTypes.node };
