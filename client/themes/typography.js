export default function Typography(theme, borderRadius, fontFamily) {
  return {
    fontFamily,
    subtitle1: {
      fontSize: '0.9rem',
      fontWeight: 500,
      color: theme.palette.text.dark
    },
    subtitle2: {
      fontSize: '0.8rem',
      fontWeight: 400,
      color: theme.palette.text.secondary
    },
    caption: {
      fontSize: '0.8rem',
      color: theme.palette.text.secondary,
      fontWeight: 400
    },
    body0: {
      fontSize: '0.9rem',
      fontWeight: 600,
      lineHeight: '1.334em'
    },
    body1: {
      fontSize: '0.9rem',
      fontWeight: 400,
      lineHeight: '1.334em'
    },
    body2: {
      letterSpacing: '0em',
      fontWeight: 400,
      lineHeight: '1.5em',
      color: theme.palette.text.primary
    },
    button: {
      textTransform: 'inherit'
    },
    customInput: {
      marginTop: 1,
      marginBottom: 1,
      '& > label': {
        top: 23,
        left: 0,
        color: theme.palette.grey[500],
        '&[data-shrink="false"]': {
          top: 5
        }
      },
      '& > div > input': {
        padding: '30.5px 14px 11.5px !important'
      },
      '& legend': {
        display: 'none'
      },
      '& fieldset': {
        top: 0
      }
    },
    mainContent: {
      backgroundColor: theme.palette.grey[100],
      width: '100%',
      minHeight: 'calc(100vh - 68px)',
      flexGrow: 1,
      padding: '20px',
      marginTop: '68px',
      marginRight: '20px',
      borderRadius: `${borderRadius}px`
    },
    menuCaption: {
      fontSize: '0.9rem',
      fontWeight: 500,
      color: theme.palette.grey[900],
      padding: '6px',
      textTransform: 'capitalize',
      marginTop: '10px'
    },
    subMenuCaption: {
      fontSize: '0.7rem',
      fontWeight: 500,
      color: theme.palette.text.secondary,
      textTransform: 'capitalize'
    },
    commonAvatar: {
      cursor: 'pointer',
      borderRadius: '8px'
    },
    smallAvatar: {
      width: '22px',
      height: '22px',
      fontSize: '1rem'
    },
    mediumAvatar: {
      width: '34px',
      height: '34px',
      fontSize: '1.2rem'
    },
    largeAvatar: {
      width: '44px',
      height: '44px',
      fontSize: '1.5rem'
    }
  };
}
