import React, { useState, useRef } from "react";
import PropTypes from "prop-types";
import OutlinedInput from "@mui/material/OutlinedInput";
import InputAdornment from "@mui/material/InputAdornment";
import IconButton from "@mui/material/IconButton";
import SaveIcon from "@mui/icons-material/Save";

const ENTER_KEY_CODE = 13;
const DEFAULT_PLACEHOLDER = "Click To Edit";

const EditableText = ({
  textVariant="h4",
  className = "",
  value = "",
  emptyInit = false,
  onFocus = () => {},
  onBlur = () => {},
  ...props
}) => {
  const Tag = textVariant;
  const [isEditing, setEditing] = useState(false);
  const [textValue, setTextValue] = useState(value);
  const inputRef = useRef(null);

  const isTextValueValid = () =>
    typeof textValue !== "undefined" && textValue.trim().length > 0;

  const handleFocus = () => {
    const fn = isEditing ? onBlur : onFocus;

    if (isEditing) {
      textValue != value && onBlur(textValue);
    } else {
      onFocus(textValue);
    }

    handleEditState();
  };

  const handleChange = (e) => setTextValue(inputRef.current.value);

  const handleKeyDown = (e) => {
    if (e.keyCode === ENTER_KEY_CODE) {
      handleEnterKey();
    }
  };

  const handleEditState = () => {
    if (!isTextValueValid()) return;
    setEditing((prev) => !prev);
  };

  const handleEnterKey = () => {
    handleFocus();
  };

  if (isEditing) {
    return (
      <OutlinedInput
        className="w-100"
        color="secondary"
        inputRef={inputRef}
        value={textValue}
        size="small"
        sx={{
          borderRadius: "5px",
          "& ~ fieldset": {
            borderRadius: "5px",
          },
          paddingRight: "5px",
          "& .MuiInputBase-input": {
            paddingRight: "0!important",
            fontSize: "16px",
          },
        }}
        endAdornment={
          <InputAdornment position="end" size="small">
            <IconButton
              size="small"
              color="secondary"
              onClick={handleFocus}
              onMouseDown={handleFocus}
              edge="end"
            >
              <SaveIcon fontSize="small" />
            </IconButton>
          </InputAdornment>
        }
        onChange={handleChange}
        onBlur={handleFocus}
        onKeyDown={handleKeyDown}
        autoFocus
      />
    );
  }

  const text = isTextValueValid()
    ? textValue
    : emptyInit
    ? ""
    : props.textPlaceHolder || DEFAULT_PLACEHOLDER;

  return (
    <div className="d-block">
      <Tag
        onClick={handleFocus}
        className={`d-inline ${className || "h5 fw-bold"}`}
      >
        {text}
      </Tag>
      <button
        className="btn btn-icon btn-icon-primary2 d-inline-flex ms-3 fs-13"
        onClick={handleFocus}
      >
        <i className="bi bi-pen" />
      </button>
    </div>
  );
};

EditableText.propTypes = {
  value: PropTypes.string.isRequired,
  emptyInit: PropTypes.bool,
  onFocus: PropTypes.func,
  onBlur: PropTypes.func,
};

export default EditableText;
