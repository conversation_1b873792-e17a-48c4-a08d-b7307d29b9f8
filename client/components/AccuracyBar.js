import React from 'react';

import Box from '@mui/material/Box';
import Typography from '@mui/material/Box';
import LinearProgress from '@mui/material/LinearProgress';
import Tooltip from '@mui/material/Tooltip';
import IconButton from '@mui/material/IconButton';
import Paper from '@mui/material/Paper';

import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';

export default function AccuracyBar({ title = "Độ chính xác", accuracy = 13, current = 2, total = 15 }) {
  const tooltipText = `<PERSON><PERSON> chính xác = Tổng số điểm đạt được cho câu trả lời đúng và đúng một phần / Tổng số điểm cho bài kiểm tra\n\nBạn đã đạt được ${current} / ${total} điểm`;

  return (
    <Box mb={2} sx={{ backgroundColor: '#000', padding: 2, borderRadius: 2 }}>
      {/* Tiêu đề + icon info */}
      <Box display="flex" alignItems="center" gap={1} mb={1}>
        <Typography variant="body2" fontSize="14px" color="white">{title}</Typography>
        <Tooltip
          title={
            <Paper
              sx={{
                backgroundColor: '#fff',
                color: '#3e173b',
                padding: 1,
                fontSize: '0.75rem',
                whiteSpace: 'pre-line',
                boxShadow: 3,
              }}
              elevation={3}
            >
              {tooltipText}
            </Paper>
          }
          arrow
          placement="right"
        >
          <IconButton size="small" sx={{ p: 0, color: 'white' }}>
            <InfoOutlinedIcon fontSize="small" />
          </IconButton>
        </Tooltip>
      </Box>

      {/* Tooltip label đè giữa thanh */}
      <Box position="relative" mt={0.5} mb={0.5}>
        <Tooltip
          title={
            <Paper
              sx={{
                backgroundColor: '#fff',
                color: '#3e173b',
                padding: 1,
                fontSize: '0.75rem',
                whiteSpace: 'pre-line',
                boxShadow: 3,
              }}
              elevation={3}
            >
              {current}/{total} pts
            </Paper>
          }
          arrow
          placement="top"
        >
          <Box
            sx={{
              position: 'absolute',
              top: '50%',
              left: `${accuracy}%`,
              transform: 'translate(-50%, -50%)',
              backgroundColor: 'white',
              color: 'black',
              px: 1.5,
              py: 0.5,
              border: '1px solid #e5e7eb',
              borderRadius: '8px',
              fontSize: '0.75rem',
              fontWeight: 600,
              zIndex: 1,
              whiteSpace: 'nowrap',
            }}
          >
            {accuracy}%
          </Box>
        </Tooltip>

        {/* Progress bar */}
        <LinearProgress
          variant="determinate"
          value={accuracy}
          sx={{
            height: 14,
            borderRadius: 7,
            backgroundColor: '#ef3c69',
            '& .MuiLinearProgress-bar': {
              backgroundColor: '#00c985',
            },
          }}
        />
      </Box>
    </Box>
  );
}
