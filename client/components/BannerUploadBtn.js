import React, { useEffect, useState, forwardRef } from "react";
// material-ui
import Dialog from "@mui/material/Dialog";
import DialogContent from "@mui/material/DialogContent";
import DialogActions from "@mui/material/DialogActions";
import DialogTitle from "@mui/material/DialogTitle";
import Slide from "@mui/material/Slide";
import Button from "@mui/material/Button";

import EditIcon from "@mui/icons-material/Edit";

import DragDropCropImage from "./DragDropCropImage";

import { uploadImageBase64 } from "../actions/commonService";

const Transition = forwardRef(function Transition(props, ref) {
  return <Slide direction="up" ref={ref} {...props} />;
});

const BannerUploadBtn = ({ imgSrc = null, setImgSrc = () => {} }) => {
  const [open, setOpen] = useState(false);
  const [img, setImg] = useState(imgSrc);

  useEffect(() => {
    setImg(imgSrc);
  }, [imgSrc]);

  const handleOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  const handleSave = async () => {
    if (img && img.startsWith("data:image/")) {
      // ckeck is base64
      try {
        const response = await uploadImageBase64({ image: img });

        if (response.url) {
          setImgSrc(response.url);
          handleClose();
        }
      } catch (error) {
        console.log(error);
      }
    } else {
      setImgSrc(img);
      handleClose();
    }
  };

  return (
    <>
      <Button
        className="btn-small"
        variant="contained"
        color="secondary"
        aria-label="edit"
        onClick={handleOpen}
        size="small"
      >
        <EditIcon size="small" />
      </Button>
      <Dialog
        fullWidth={true}
        maxWidth={"sm"}
        open={open}
        TransitionComponent={Transition}
        keepMounted
        onClose={handleClose}
      >
        <DialogTitle className="h5" sx={{ textAlign: "center" }}>
          Tải lên một hình ảnh
        </DialogTitle>
        <DialogContent>
          <DragDropCropImage imgSrc={img} setImgSrc={setImg} />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose} color="error" size="small">
            Hủy
          </Button>
          <Button variant="contained" onClick={handleSave} size="small">
            Lưu
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default BannerUploadBtn;
