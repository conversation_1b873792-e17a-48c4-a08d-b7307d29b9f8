import React, {
  useRef,
  useLayoutEffect,
  useCallback,
  useState,
  useMemo
} from 'react';
import PropTypes from 'prop-types';

const AutoResizeInput = React.memo(({
  value,
  defaultValue = '',
  onChange = () => {},
  placeholder = '',
  style: styleProp = {},
  minWidth = 60,
  transitionDuration = '0.15s',
  transitionTimingFunction = 'ease',
  ...props
}) => {
  const inputRef = useRef(null);

  // Nội bộ value nếu uncontrolled
  const [internalValue, setInternalValue] = useState(defaultValue);
  const isControlled  = value != null;
  const displayValue  = isControlled ? value : internalValue;

  // Tạo 1 canvas/ctx tái sử dụng
  const { canvas, ctx } = useMemo(() => {
    const c = document.createElement('canvas');
    return { canvas: c, ctx: c.getContext('2d') };
  }, []);

  const combinedStyle = useMemo(() => ({
    boxSizing: 'border-box',
    transition: `width ${transitionDuration} ${transitionTimingFunction}`,
    margin: '0 5px',
    padding: '7px 10px',
    backgroundColor: '#fff7da',
    color: '#00f',
    fontSize: '18px',
    minWidth: `${minWidth}px`,
    ...styleProp
  }), [
    transitionDuration,
    transitionTimingFunction,
    minWidth,
    styleProp
  ]);

  const resizeInput = useCallback((text) => {
    const el = inputRef.current;
    if (!el) return;

    const cs = window.getComputedStyle(el);
    ctx.font = `${cs.fontStyle} ${cs.fontWeight} ${cs.fontSize} ${cs.fontFamily}`;

    const content = text || placeholder;
    const textWidth = ctx.measureText(content).width;

    const paddingLR = parseFloat(cs.paddingLeft) + parseFloat(cs.paddingRight);
    const borderLR  = parseFloat(cs.borderLeftWidth) + parseFloat(cs.borderRightWidth);

    const newWidth = Math.max(
      minWidth,
      textWidth + paddingLR + borderLR + 2
    );
    el.style.width = `${newWidth}px`;
  }, [ctx, placeholder, minWidth]);

  // Resize on mount và khi displayValue thay đổi
  useLayoutEffect(() => {
    resizeInput(displayValue);
  }, [resizeInput, displayValue]);

  // Stable handler
  const handleChange = useCallback((e) => {
    const txt = e.target.value;
    if (!isControlled) setInternalValue(txt);
    onChange(e);
    resizeInput(txt);
  }, [isControlled, onChange, resizeInput]);

  return (
    <input
      {...props}
      ref={inputRef}
      value={displayValue}
      onChange={handleChange}
      placeholder={placeholder}
      style={combinedStyle}
    />
  );
});

AutoResizeInput.propTypes = {
  value:    PropTypes.string,
  defaultValue: PropTypes.string,
  onChange: PropTypes.func,
  placeholder: PropTypes.string,
  style:    PropTypes.object,
  minWidth: PropTypes.number,
  transitionDuration:       PropTypes.string,
  transitionTimingFunction: PropTypes.string,
};

AutoResizeInput.displayName = 'AutoResizeInput';

export default AutoResizeInput;
