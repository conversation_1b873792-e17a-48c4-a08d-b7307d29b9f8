"use client";

import { useState, memo, forwardRef } from "react";
import { useDispatch } from "react-redux";

import { useTheme } from "@mui/material/styles";
import Tabs from "@mui/material/Tabs";
import Tab from "@mui/material/Tab";
import OutlinedInput from "@mui/material/OutlinedInput";
import Dialog from "@mui/material/Dialog";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import FormControl from "@mui/material/FormControl";
import InputLabel from "@mui/material/InputLabel";
import Slide from "@mui/material/Slide";
import FormHelperText from "@mui/material/FormHelperText";
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";

import ContentCopyIcon from "@mui/icons-material/ContentCopy";

import * as Yup from "yup";
import toast from "react-hot-toast";
import { Formik } from "formik";

import { setNoti } from "@/slices/notiSlice";
import useCopyToClipboard from "@/hooks/useCopyToClipboard";
import StudentImportComponent from "./StudentImportComponent";

import { storeStudent, updateStudent } from "@/actions/classroomAction";

const Transition = forwardRef(function Transition(props, ref) {
  return <Slide direction="up" ref={ref} {...props} />;
});

function CustomTabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 1 }}>{children}</Box>}
    </div>
  );
}

const DialogStudentForm = ({
  classroom,
  currentStudent,
  submitStudent,
  open,
  onClose,
}) => {
  console.log("DialogStudentForm");
  const theme = useTheme();
  const dispatch = useDispatch();
  const [tabValue, setTabValue] = useState(currentStudent.id ? 1 : 0);
  const { textAreaRef, copyToClipboard } = useCopyToClipboard();
  const [showPasswordInput, setShowPasswordInput] = useState(false);

  const handleChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const copyJoinLink = (e) => {
    if (navigator?.clipboard) {
      copyToClipboard(e);
      toast.success('Copy link thành công!');
    } else {
      toast.error('Trình duyệt của bạn không hỗ trợ copy.');
    }
  };

  const handleImportSuccess = () => {
    submitStudent(false);
  };

  return (
    <Dialog
      open={open}
      TransitionComponent={Transition}
      keepMounted
      fullWidth={true}
      maxWidth="sm"
      onClose={onClose}
    >
      <DialogTitle className="h5 mx-2">
        {currentStudent.id ? "Cập nhật học sinh" : "Thêm học sinh"}
      </DialogTitle>
      <DialogContent>
        {!currentStudent.id && (
          <Tabs
            value={tabValue}
            onChange={handleChange}
            aria-label="Quản lý học sinh"
          >
            <Tab label="Chia sẻ Liên kết này đến học sinh" />
            <Tab label={currentStudent.id ? "Thông tin" : "Thêm thủ công"} />
            <Tab label="Thêm nhanh bằng file Excel" />
          </Tabs>
        )}

        <CustomTabPanel value={tabValue} index={0}>
          <div className="py-3">
            <p className="mb-2 fs-16">
              Khi học sinh truy cập vào liên kết sẽ có hướng dẫn trên màn hình
              để tạo tài khoản và tham gia lớp học của bạn.
            </p>
            <div
              className="d-flex align-items-center justify-content-between p-1 mt-3 mb-2"
              style={{ background: "rgb(237, 230, 246)", borderRadius: "4px" }}
            >
              <input
                className="invite-link px-2"
                ref={textAreaRef}
                defaultValue={`${window.location.origin}/dashboard/my-classroom?code=${classroom.code}`}
                readOnly
              />
              <Button
                variant="contained"
                color="secondary"
                size="small"
                onClick={copyJoinLink}
              >
                <ContentCopyIcon
                  sx={{ fontSize: "1rem", marginRight: "4px" }}
                />
                Copy
              </Button>
            </div>
            <p className="my-2 text-center">— hoặc sử dụng mã code —</p>
            <div
              className="d-flex align-items-center justify-content-center p-2 mt-3 mb-2"
              style={{ background: "rgb(237, 230, 246)", borderRadius: "4px" }}
            >
              {classroom.code}
            </div>
            <p>và yêu cầu học sinh của bạn làm theo hướng dẫn dưới đây:</p>
            <div
              className="guild mt-2 p-3"
              style={{ background: "rgb(242, 242, 242)", borderRadius: "4px" }}
            >
              <p className="mb-2">
                1. Truy cập <b>{`${window.location.origin}/dashboard/my-classroom`}</b>
              </p>
              <p className="mb-2">
                2. Làm theo hướng dẫn trên màn hình để đăng nhập hoặc tạo tài khoản
              </p>
              <p className="mb-0">3. Nhập mã lớp học bên trên</p>
            </div>
          </div>
        </CustomTabPanel>
        <CustomTabPanel value={tabValue} index={1}>
          <Formik
            initialValues={{
              name: currentStudent.name || "",
              email: currentStudent.email || "",
              ident_number: currentStudent.ident_number || "",
              password: "",
            }}
            validationSchema={Yup.object().shape({
              name: Yup.string()
                .min(3)
                .max(500)
                .required("Họ tên không được để trống"),
              email: Yup.string()
                .min(3)
                .max(500)
                .required("Email không được để trống"),
              password: showPasswordInput
                ? Yup.string().max(255).required("Password is required")
                : Yup.string().notRequired(),
              password_confirmation: showPasswordInput
                ? Yup.string()
                .required("Confirm password is required")
                .oneOf([Yup.ref("password"), null], "Passwords must match")
                : Yup.string().notRequired(),
            })}
            onSubmit={async (values, { setErrors, setSubmitting }) => {
              setTimeout(async () => {
                try {
                  const {data: newStudent } = currentStudent?.id
                    ? await updateStudent(classroom.id, {
                        ...currentStudent,
                        ...values,
                      })
                    : await storeStudent(classroom.id, values);

                  if (newStudent) {
                    submitStudent(currentStudent?.id ? true : false);
                  } else {
                    throw new Error("Đã xảy ra lỗi.");
                  }
                } catch (err) {
                  if (err?.status == 422) {
                    setErrors(err.errors);
                  } else if (err?.status == 404) {
                    setShowPasswordInput(true);
                  } else {
                    dispatch(setNoti(err));
                  }
                } finally {
                  setSubmitting(false);
                }
              }, 400);
            }}
          >
            {({
              errors,
              handleBlur,
              handleChange,
              handleSubmit,
              isSubmitting,
              touched,
              values,
            }) => (
              <form noValidate onSubmit={handleSubmit}>
                <Box className="d-flex flex-column gap-4 py-3">
                  <FormControl
                    fullWidth
                    error={Boolean(touched.name && errors.name)}
                  >
                    <InputLabel htmlFor="outlined-adornment-name">
                      Họ Tên
                    </InputLabel>
                    <OutlinedInput
                      id="outlined-adornment-name"
                      type="text"
                      label="Họ Tên"
                      value={values.name}
                      name="name"
                      onBlur={handleBlur}
                      onChange={handleChange}
                      inputProps={{}}
                    />
                    {touched.name && errors.name && (
                      <FormHelperText
                        error
                        id="standard-weight-helper-text-name"
                      >
                        {errors.name}
                      </FormHelperText>
                    )}
                  </FormControl>

                  <FormControl
                    fullWidth
                  >
                    <InputLabel htmlFor="outlined-adornment-ident_number">
                      Số báo danh
                    </InputLabel>
                    <OutlinedInput
                      id="outlined-adornment-ident_number"
                      type="text"
                      label="Số báo danh"
                      value={values.ident_number}
                      name="ident_number"
                      onBlur={handleBlur}
                      onChange={handleChange}
                      inputProps={{}}
                    />
                    {touched.ident_number && errors.ident_number && (
                      <FormHelperText
                        error
                        id="standard-weight-helper-text-ident_number"
                      >
                        {errors.ident_number}
                      </FormHelperText>
                    )}
                  </FormControl>

                  <FormControl
                    fullWidth
                    error={Boolean(touched.email && errors.email)}
                  >
                    <InputLabel htmlFor="outlined-adornment-email">
                      Email
                    </InputLabel>
                    <OutlinedInput
                      id="outlined-adornment-email"
                      type="text"
                      label="Email"
                      value={values.email}
                      name="email"
                      onBlur={handleBlur}
                      onChange={handleChange}
                      inputProps={{}}
                      readOnly={currentStudent.id ? true : false}
                      sx={
                        currentStudent.id
                          ? {
                              "&.MuiOutlinedInput-root .MuiOutlinedInput-input":
                                { background: "#e9ecef" },
                            }
                          : {}
                      }
                    />
                    {touched.email && errors.email && (
                      <FormHelperText
                        error
                        id="standard-weight-helper-text-email"
                      >
                        {errors.email}
                      </FormHelperText>
                    )}
                  </FormControl>

                  {showPasswordInput && (
                    <>
                      <p className="my-2 text-primary">Học sinh chưa có tài khoản. Giáo viên cần tạo tài khoản cho HS để đăng nhập</p>
                      <FormControl
                        fullWidth
                        error={Boolean(touched.password && errors.password)}
                      >
                        <InputLabel htmlFor="outlined-adornment-password">
                          Mật khẩu tạm thời
                        </InputLabel>
                        <OutlinedInput
                          id="outlined-adornment-password"
                          type="password"
                          label="Mật khẩu tạm thời"
                          value={values.password}
                          name="password"
                          onBlur={handleBlur}
                          onChange={handleChange}
                          inputProps={{}}
                        />
                        {touched.password && errors.password && (
                          <FormHelperText error id="standard-weight-helper-text-password">
                            {errors.password}
                          </FormHelperText>
                        )}
                      </FormControl>
                      <FormControl
                        fullWidth
                        error={Boolean(
                          touched.password_confirmation && errors.password_confirmation
                        )}
                      >
                        <InputLabel htmlFor="outlined-adornment-password_confirmation">
                          Nhập lại mật khẩu tạm thời
                        </InputLabel>
                        <OutlinedInput
                          id="outlined-adornment-password_confirmation"
                          type="password"
                          label="Nhập lại mật khẩu tạm thời"
                          value={values.password_confirmation}
                          name="password_confirmation"
                          onBlur={handleBlur}
                          onChange={handleChange}
                          inputProps={{}}
                        />
                        {touched.password_confirmation &&
                          errors.password_confirmation && (
                            <FormHelperText
                              error
                              id="standard-weight-helper-text-password_confirmation-login"
                            >
                              {errors.password_confirmation}
                            </FormHelperText>
                          )}
                      </FormControl>
                    </>
                  )}
                </Box>
                <Box sx={{ mt: 2 }}>
                  <Button
                    variant="outlined"
                    type="button"
                    onClick={onClose}
                    color="inherit"
                    size="small"
                    sx={{ mr: 2 }}
                  >
                    Hủy
                  </Button>
                  <Button
                    disableElevation
                    disabled={isSubmitting}
                    size="small"
                    type="submit"
                    variant="contained"
                    color="secondary"
                  >
                    Lưu
                  </Button>
                </Box>
              </form>
            )}
          </Formik>
        </CustomTabPanel>
        <CustomTabPanel value={tabValue} index={2}>
          <div className="py-3">
            <StudentImportComponent
              classroom={classroom}
              onImportSuccess={handleImportSuccess}
              title="Import học sinh từ file Excel"
            />
          </div>
        </CustomTabPanel>
      </DialogContent>
    </Dialog>
  );
};

export default memo(DialogStudentForm);
