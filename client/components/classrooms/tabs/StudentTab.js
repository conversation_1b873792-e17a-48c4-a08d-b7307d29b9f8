"use client";

import { memo, useState, useCallback } from "react";
import { useDispatch } from "react-redux";
import { useRouter } from "next/navigation";

import { styled } from "@mui/material/styles";
import IconButton from "@mui/material/IconButton";
import Paper from "@mui/material/Paper";
import InputBase from "@mui/material/InputBase";
import Skeleton from "@mui/material/Skeleton";
import Checkbox from "@mui/material/Checkbox";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogTitle from "@mui/material/DialogTitle";
import DialogContent from "@mui/material/DialogContent";
import Typography from "@mui/material/Typography";
import Box from "@mui/material/Box";
import Stack from "@mui/material/Stack";
import CircularProgress from "@mui/material/CircularProgress";

import DeleteOutlineIcon from "@mui/icons-material/DeleteOutline";
import SearchIcon from "@mui/icons-material/Search";
import EditIcon from "@mui/icons-material/Edit";
import FileDownloadIcon from "@mui/icons-material/FileDownload";
import CloseIcon from "@mui/icons-material/Close";
import DescriptionIcon from "@mui/icons-material/Description";
import UploadFileIcon from "@mui/icons-material/UploadFile";
import PersonAddIcon from "@mui/icons-material/PersonAdd";
import DownloadIcon from "@mui/icons-material/Download";

import NoDataOverlay from "@/components/NoDataOverlay";
import DialogStudentForm from "../DialogStudentForm";
import StudentExportComponent from "../StudentExportComponent";
import StudentImportComponent from "../StudentImportComponent";

import toast from "react-hot-toast";
import debounce from "lodash/debounce";

import { useConfirm } from "@/contexts/ConfirmContext";

import { classroomApiSlice } from "@/slices/features/classroomApiSlice";
import {
  removeStudent,
  removeStudents,
} from "@/actions/classroomAction";
import {formatDateTime} from "@/utils/helpers";

const GroupInputSearch = styled(Paper)(() => {
  return {
    padding: "2px 4px",
    display: "flex",
    alignItems: "center",
    border: "1px solid #e5e5e5",
    width: "100%",
    height: "39px",
    maxWidth: "250px",
    borderRadius: "8px",
  };
});

const StudentTab = ({ classroom }) => {
  console.log("StudentTab");
  const dispatch = useDispatch();
  const confirmDelete = useConfirm();
  const router = useRouter();

  const [keyword, setKeyword] = useState("");

  const { data: students, isLoading } = classroomApiSlice.useFetchStudentsQuery({
    classroomId: classroom.id,
    params: { keyword },
  });

  const [currentStudent, setCurrentStudent] = useState({});
  const [selectedIds, setSelectedIds] = useState([]);
  const [checkAll, setCheckAll] = useState(false);
  const [openDialogStudentForm, setOpenDialogStudentForm] = useState(false);
  const [showImportDialog, setShowImportDialog] = useState(false);
  const [isExporting, setIsExporting] = useState(false);

  const debouncedSearch = useCallback(
    debounce((value) => {
      setKeyword(value);
    }, 700),
    []
  );

  const handleSearch = useCallback((event) => {
    debouncedSearch(event.target.value);
  }, []);

  const addStudent = useCallback(() => {
    setCurrentStudent({});
    setOpenDialogStudentForm(true);
  }, []);

  const editStudent = useCallback((student) => {
    setCurrentStudent(student);
    setOpenDialogStudentForm(true);
  }, []);

  const submitStudent = useCallback((isUpdate = false) => {
    dispatch(
      classroomApiSlice.util.invalidateTags([{ type: "Classroom", id: `STUDENTS-${classroom.id}` }])
    );

    toast.success(`${ isUpdate ? 'Chỉnh sửa' : 'Thêm mới' } học sinh thành công`, {
      position: "top-right",
    });

    setOpenDialogStudentForm(false);
  }, [dispatch]);

  const handleImportSuccess = useCallback(() => {
    dispatch(
      classroomApiSlice.util.invalidateTags([{ type: "Classroom", id: `STUDENTS-${classroom.id}` }])
    );
    setShowImportDialog(false);
  }, [dispatch, classroom.id]);

  const handleQuickExport = useCallback(async () => {
    // This will trigger the export from StudentExportComponent
    // We'll use a ref to call the export function directly
    if (window.exportStudentsRef) {
      setIsExporting(true);
      try {
        await window.exportStudentsRef();
      } catch (error) {
      } finally {
        setIsExporting(false);
      }
    } else {
      console.error("handleQuickExport - window.exportStudentsRef is not available");
    }
  }, []);

  const deleteStudent = useCallback(
    async (student) => {
      const confirm = await confirmDelete(
        `Xóa: ${student.name}?`,
        "Sau khi xóa học sinh sẽ không còn xuất hiện!"
      );

      if (confirm) {
        try {
          await removeStudent(classroom.id, student.id);

          dispatch(
            classroomApiSlice.util.invalidateTags([{ type: "Classroom", id: `STUDENTS-${classroom.id}` }])
          );

          toast.success("Xóa học sinh thành công", { position: "top-right" });
        } catch (error) {
          console.error(error);
          toast.error("Đã xảy ra lỗi! Vui lòng thử lại");
        }
      }
    },
    [classroom.id, confirmDelete]
  );

  const deleteStudents = useCallback(async () => {
    const confirm = await confirmDelete(
      `Xóa: ${selectedIds.length} học sinh?`,
      "Sau khi xóa học sinh sẽ không còn xuất hiện!"
    );

    if (confirm) {
      try {
        await removeStudents(classroom.id, {
          studentIds: selectedIds,
        });

        dispatch(
          classroomApiSlice.util.invalidateTags([{ type: "Classroom", id: `STUDENTS-${classroom.id}` }])
        );

        setSelectedIds([]);
        setCheckAll(false);

        toast.success("Xóa học sinh thành công!");
      } catch (error) {
        console.error(error);
        toast.error("Đã xảy ra lỗi! Vui lòng thử lại");
      }
    }
  }, [classroom.id, selectedIds, confirmDelete]);

  const handleCheckAll = useCallback(
    (event) => {
      const isChecked = event.target.checked;
      setCheckAll(isChecked);

      setSelectedIds(isChecked ? students.map((student) => student.id) : []);
    },
    [students]
  );

  const handleCheck = useCallback(
    (event, id) => {
      const isChecked = event.target.checked;

      setSelectedIds((prevIds) => {
        const updatedIds = isChecked
          ? [...prevIds, id]
          : prevIds.filter((selectedId) => selectedId !== id);

        setCheckAll(updatedIds.length === students.length);

        return updatedIds;
      });
    },
    [students]
  );

  const viewStudentDetails = useCallback((student) => {
    router.push(`/dashboard/classroom/${classroom.id}/student/${student.id}`);
  }, [classroom.id, router]);

  return (
    <>
      <div className="d-flex flex-wrap align-items-center justify-content-between my-3 gap-2">
        <div className="d-flex align-items-center gap-2 mb-1">
          <GroupInputSearch>
            <IconButton
              size="small"
              color="inherit"
              aria-label="search"
              sx={{ minWidth: "20px" }}
              disabled
            >
              <SearchIcon />
            </IconButton>
            <InputBase
              placeholder="Tìm học sinh"
              inputProps={{ "aria-label": "Tìm học sinh" }}
              onChange={handleSearch}
            />
          </GroupInputSearch>
          {selectedIds.length > 0 && (
            <Button
              variant="contained"
              color="error"
              size="small"
              onClick={deleteStudents}
            >
              <i className="bi bi-trash3-fill mx-1" />
              Xóa <strong className="mx-1">({selectedIds.length})</strong>
            </Button>
          )}
        </div>
        <Stack direction="row" spacing={2}>
          {classroom?.classroom_user_count > 0 && (
            <Button
              variant="contained"
              color="success"
              size="small"
              onClick={handleQuickExport}
              disabled={isExporting}
              startIcon={
                isExporting ? (
                  <CircularProgress size={16} sx={{ color: 'white' }} />
                ) : (
                  <FileDownloadIcon />
                )
              }
            >
              {isExporting ? 'Đang tải xuống...' : 'Xuất danh sách HS'}
            </Button>
          )}
          <Button
            variant="contained"
            color="primary"
            size="small"
            onClick={() => setShowImportDialog(!showImportDialog)}
            startIcon={<UploadFileIcon />}
          >
            Import HS từ file Excel
          </Button>
          <Button
            variant="contained"
            color="warning"
            size="small"
            onClick={addStudent}
            startIcon={<PersonAddIcon />}
          >
            Thêm học sinh
          </Button>
        </Stack>
      </div>

      {/* Hidden StudentExportComponent for quick export functionality */}
      <div style={{ display: 'none' }}>
        <StudentExportComponent
          classroom={classroom}
          onExport={() => {
            // Optional: Close dialog after export or keep it open
            // setShowImportDialog(false);
          }}
          enableQuickExport={true}
        />
      </div>

      {/* Import MUI Dialog */}
      <Dialog
        open={showImportDialog}
        onClose={() => setShowImportDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center" }}>
            <DescriptionIcon sx={{ mr: 1, mb: 1 }} />
            <Typography component="h5" variant="h6">
              Import từ file Excel
            </Typography>
          </Box>
          <IconButton
            aria-label="close"
            onClick={() => setShowImportDialog(false)}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent>
          <StudentImportComponent
            classroom={classroom}
            onImportSuccess={handleImportSuccess}
          />
        </DialogContent>
      </Dialog>

      <div className="student-container">
        {isLoading ? (
          Array.from({ length: 3 }, (_, index) => (
            <Skeleton
              variant="rounded"
              height={60}
              key={index}
              sx={{ my: "10px", borderRadius: "4px" }}
            />
          ))
        ) : (
          <>
            <div className="d-none d-md-block px-3">
              <div className="row align-items-center my-3 py-2 bg-white">
                <div className="col-md-1">
                  <Checkbox
                    size="small"
                    checked={checkAll}
                    onChange={handleCheckAll}
                  />
                </div>
                <div className="col-md-1">
                  <div className="fw-bold p-2 text-center">STT</div>
                </div>
                <div className="col-md-4">
                  <div className="fw-bold p-2">Họ và tên</div>
                </div>
                <div className="col-md-2">
                  <div className="fw-bold p-2">Số báo danh</div>
                </div>
                <div className="col-md-2">
                  <div className="fw-bold p-2">Bài tập đã làm</div>
                </div>
                <div className="col-md-2 text-md-end">
                  <div className="fw-bold p-2">Hành động</div>
                </div>
              </div>
            </div>
            <div className="px-3">
              {students?.length ? (
                students.map((item) => (
                  <div
                    key={item.id}
                    className="row align-items-center my-3 py-3 bg-white"
                  >
                    <div className="col-md-1">
                      <Checkbox
                        size="small"
                        checked={selectedIds.includes(item.id)}
                        onChange={(event) => handleCheck(event, item.id)}
                      />
                    </div>
                    <div className="col-md-1">
                      <div className="p-2 text-break">
                        <p className="mb-0 fw-bold text-center">
                          {students.indexOf(item) + 1}
                        </p>
                      </div>
                    </div>
                    <div className="col-md-4">
                      <div className="p-2 text-break">
                        <p
                          className="mb-0 fw-bold"
                          style={{ cursor: 'pointer'}}
                          onClick={() => viewStudentDetails(item)}
                        >
                          {item.name}
                        </p>
                        <p className="mb-0 text-success fs-12">
                          <i className="bi bi-person-check-fill me-1"></i> Tham gia lúc: { item.joined_at ? formatDateTime(item?.joined_at) : '__' }
                        </p>
                      </div>
                      <div className="p-2 text-break">
                        <p className="mb-0 text-muted fs-12">ID: {item.id}</p>
                        {item.phone && ( <p className="mb-0 text-muted fs-12">
                          <i className="bi bi-telephone me-1"></i> {item.phone}
                        </p>
                        )}
                        <p className="mb-0 text-muted fs-12">
                          <i className="bi bi-person-plus me-1"></i> Ngày tạo: {formatDateTime(item.created_at, false)}
                        </p>
                        <p className="mb-0 text-muted fs-12 mt-3">
                          <i className="bi bi-envelope me-1"></i> {item.email}
                        </p>
                        {item.temp_password && (
                          <p className="mb-0 text-muted fs-12">
                            <i className="bi bi-key me-1"></i> Mật khẩu tạm thời:{" "}
                            <span className="text-info">{item.temp_password}</span>
                          </p>
                        )}
                      </div>
                    </div>
                    <div className="col-md-2">
                      <div className="p-2 text-break">
                        <p className="mb-0">
                          <span className="ms-2 fs-14 fw-bold">
                            {item.ident_number || ""}
                          </span>
                        </p>
                      </div>
                    </div>
                    <div className="col-md-2">
                      <div className="p-2">
                        <p className="mb-0">
                          <span className={`ms-2 fs-14 fw-bold ${item.quiz_results_count ? "text-primary" : "text-danger"}`}>
                            {item.quiz_results_count || 0}
                          </span>
                          <span className="ms-2 fs-14 fw-bold">/ {classroom?.classroom_quiz_count || 0}</span>
                          <span className="ms-2 fs-14">bài tập</span>
                        </p>
                      </div>
                    </div>
                    <div className="col-md-2 text-md-end">
                      <Button
                        className="m-1"
                        variant="outlined"
                        size="small"
                        onClick={() => viewStudentDetails(item)}
                        color="secondary"
                        sx={{
                          minWidth: "2rem",
                          padding: "3px",
                          border: "none",
                          background: "#9c27b026",
                          "&:hover": { border: "none" },
                        }}
                        title="Xem báo cáo chi tiết"
                      >
                        <i className="bi bi-graph-up-arrow"></i>
                      </Button>
                      <Button
                        className="m-1"
                        variant="outlined"
                        size="small"
                        onClick={() => editStudent(item)}
                        sx={{
                          minWidth: "2rem",
                          padding: "3px",
                          border: "none",
                          background: "#cde8ff",
                          "&:hover": { border: "none" },
                        }}
                      >
                        <i className="bi bi-pencil-square"></i>
                      </Button>
                      <Button
                        className="m-1"
                        variant="outlined"
                        color="error"
                        size="small"
                        onClick={() => deleteStudent(item)}
                        sx={{
                          minWidth: "2rem",
                          padding: "3px",
                          border: "none",
                          background: "#ffd4d1",
                          "&:hover": { border: "none" },
                        }}
                      >
                        <i className="bi bi-trash3-fill" />
                      </Button>
                    </div>
                  </div>
                ))
              ) : (
                <NoDataOverlay message="Chưa có dữ liệu học sinh" />
              )}
            </div>
          </>
        )}
      </div>
      {openDialogStudentForm && (
        <DialogStudentForm
          classroom={classroom}
          currentStudent={currentStudent}
          submitStudent={submitStudent}
          open={openDialogStudentForm}
          onClose={() => setOpenDialogStudentForm(false)}
        />
      )}
    </>
  );
};

export default memo(StudentTab);
