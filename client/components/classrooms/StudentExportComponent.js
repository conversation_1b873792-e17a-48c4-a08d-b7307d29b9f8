"use client";

import { useState, useEffect } from "react";

import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import CircularProgress from "@mui/material/CircularProgress";

import FileDownloadIcon from "@mui/icons-material/FileDownload";
import toast from "react-hot-toast";
import { downloadBlob } from "@/utils/downloadHelper";

const StudentExportComponent = ({
  classroom,
  onExport,
  showExportButton = false,
  enableQuickExport = false,
  buttonText = "Xuất danh sách HS",
  loadingText = "Đang tải xuống...",
  variant = "outlined",
  color = "info",
  size = "small",
  fullWidth = false,
  startIcon = null,
}) => {
  const [isExporting, setIsExporting] = useState(false);

  // Expose export function for quick export
  useEffect(() => {
    if (enableQuickExport) {
      window.exportStudentsRef = async () => {
        return await handleExport();
      };
      return () => {
        delete window.exportStudentsRef;
      };
    }
  }, [enableQuickExport]);

  const handleExport = async () => {
    setIsExporting(true);

    try {
      // Generate filename with current date
      const currentDate = new Date().toLocaleDateString('vi-VN').replace(/\//g, '_');
      const slug = classroom.title?.normalize('NFD').replace(/[\u0300-\u036f]/g, '').toLowerCase().replace(/\s+/g, '_').replace(/[^a-z0-9_]/g, '') || 'unknown';
      const filename = `danh_sach_hoc_sinh_${slug}_${currentDate}.xlsx`;

      // Fetch the Excel file from server
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_BACKEND_URL}/api/private/classrooms/${classroom.id}/students/export`,
        {
          method: "GET",
          credentials: "include",
          headers: {
            "X-Requested-With": "XMLHttpRequest",
            "Accept": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
          },
        }
      );

      if (response.ok) {
        // Check if response is actually an Excel file
        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')) {
          const blob = await response.blob();

          // Traditional browser download using helper
          downloadBlob(blob, filename);

          toast.success("File đã được tải xuống thành công!");

          // Call onExport callback if provided
          if (onExport) {
            onExport();
          }
        } else {
          // Handle JSON error response
          const result = await response.json();
          toast.error(result.message || "Có lỗi xảy ra khi xuất file");
        }
      } else {
        // Handle different error types
        if (response.status === 419) {
          toast.error("Phiên làm việc đã hết hạn. Vui lòng tải lại trang và thử lại.");
        } else {
          // Try to parse JSON error response
          try {
            const result = await response.json();
            toast.error(result.message || "Có lỗi xảy ra khi xuất file");
          } catch {
            toast.error(`Lỗi server: ${response.status} ${response.statusText}`);
          }
        }
      }
    } catch (error) {
      // Handle specific error types
      if (error.name === 'TypeError' && error.message.includes('Failed to fetch')) {
        toast.error("Không thể kết nối đến server. Vui lòng kiểm tra kết nối mạng.");
      } else if (error.message && error.message.includes('CSRF')) {
        toast.error("Lỗi xác thực. Vui lòng tải lại trang và thử lại.");
      } else {
        toast.error("Có lỗi xảy ra khi xuất file");
      }
    } finally {
      setIsExporting(false);
    }
  };

  if (!showExportButton) {
    return null;
  }

  return (
    <Box>
      <Button
        variant={variant}
        color={color}
        startIcon={
          startIcon || (isExporting ? <CircularProgress size={16} /> : <FileDownloadIcon />)
        }
        onClick={handleExport}
        disabled={isExporting}
        size={size}
        fullWidth={fullWidth}
      >
        {isExporting ? loadingText : (classroom.classroom_user_count ? buttonText : "Tải file biểu mẫu") }
      </Button>
    </Box>
  );
};

export default StudentExportComponent;
