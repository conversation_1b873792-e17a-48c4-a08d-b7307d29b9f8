"use client";

import { useState, useCallback } from "react";
import { useRouter, useSearchParams } from "next/navigation";

import { styled } from "@mui/material/styles";
import Box from "@mui/material/Box";
import IconButton from '@mui/material/IconButton';
import List from "@mui/material/List";
import ListItem from "@mui/material/ListItem";
import ListItemButton from "@mui/material/ListItemButton";
import ListItemIcon from "@mui/material/ListItemIcon";
import ListItemText from "@mui/material/ListItemText";
import Typography from "@mui/material/Typography";
import Container from '@mui/material/Container';

import PersonIcon from "@mui/icons-material/Person";
import EventAvailableIcon from "@mui/icons-material/EventAvailable";
import WestIcon from '@mui/icons-material/West';

import StudentTab from "@/components/classrooms/tabs/StudentTab";
import AssignmentTab from "@/components/classrooms/tabs/AssignmentTab";

export default function ClassroomShow({ initClassroom }) {
  const router = useRouter();
  const searchParams = useSearchParams();

  const tab = searchParams.get("tab") || 1;

  const [classroom, setClassroom] = useState(initClassroom);
  const [tabbarIndex, setTabbarIndex] = useState(Number(tab));

  const handleTabbarClick = useCallback((event, index) => {
    setTabbarIndex(index);
  }, []);

  return classroom && (
    <Container maxWidth="xl">
      <div className="d-flex gap-3 align-items-center mb-3">
        <IconButton onClick={() => router.push('/dashboard/classroom')}>
          <WestIcon />
        </IconButton>
        <h4 className="fw-bold h4 m-0">{classroom.title}</h4>
      </div>
      <div className="row">
        <div className="col-12 col-lg-3">
          <List
            sx={{ width: "100%", bgcolor: "transparent" }}
            component="nav"
            aria-labelledby="nested-list-subheader"
          >
            <ListItem
              secondaryAction={<Typography>{classroom?.classroom_user_count || 0}</Typography>}
              disablePadding
              className="my-1"
            >
              <ListItemButton
                selected={tabbarIndex === 1}
                onClick={(event) => handleTabbarClick(event, 1)}
              >
                <ListItemIcon>
                  <PersonIcon fontSize="small" />
                </ListItemIcon>
                <ListItemText primary="DS học sinh" />
              </ListItemButton>
            </ListItem>
            <ListItem
              secondaryAction={<Typography>{classroom?.classroom_quiz_count || 0}</Typography>}
              disablePadding
              className="my-1"
            >
              <ListItemButton
                selected={tabbarIndex === 2}
                onClick={(event) => handleTabbarClick(event, 2)}
              >
                <ListItemIcon>
                  <EventAvailableIcon fontSize="small" />
                </ListItemIcon>
                <ListItemText primary="Bài tập" />
              </ListItemButton>
            </ListItem>
          </List>
        </div>
        <div className="col-12 col-lg-9">
          <Box>
            { tabbarIndex === 1 && <StudentTab classroom={classroom} /> }
            { tabbarIndex === 2 && <AssignmentTab classroom={classroom} /> }
          </Box>
        </div>
      </div>
    </Container>
  );
}
