import { memo, useState, useEffect, useRef, useMemo, useCallback } from 'react';
import { useDispatch } from "react-redux";

import { styled } from '@mui/material/styles';
import Box from '@mui/material/Box';
import Paper from '@mui/material/Paper';
import InputBase from '@mui/material/InputBase';
import Drawer from '@mui/material/Drawer';
import IconButton from '@mui/material/IconButton';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemText from '@mui/material/ListItemText';
import Toolbar from '@mui/material/Toolbar';
import Checkbox from '@mui/material/Checkbox';
import FormControlLabel from '@mui/material/FormControlLabel';
import Button from '@mui/material/Button';
import Skeleton from "@mui/material/Skeleton";

import ChevronLeftIcon from '@mui/icons-material/ChevronLeft';
import MenuIcon from '@mui/icons-material/Menu';
import SearchIcon from '@mui/icons-material/Search';

import DialogClassroomForm from "@/components/classrooms/DialogClassroomForm";
import NoDataOverlay from "../NoDataOverlay";

import { classroomApiSlice } from "@/slices/features/classroomApiSlice";
import debounce from "lodash/debounce";
import toast from "react-hot-toast";
import groupBy from "lodash/groupBy";

const drawerWidth = 240;

const GroupInputSearch = styled(Paper)(() => ({
  padding: '2px 4px',
  display: 'flex',
  alignItems: 'center',
  border: '1px solid #e5e5e5',
  width: '100%',
  height: '39px',
  maxWidth: 200,
  borderRadius: '8px',
}));

const Main = styled('div', { shouldForwardProp: (prop) => prop !== 'open' })(
  ({ theme, open }) => ({
    position: 'relative',
    flexGrow: 1,
    transition: theme.transitions.create('margin', {
      easing: open ? theme.transitions.easing.easeOut : theme.transitions.easing.sharp,
      duration: open ? theme.transitions.duration.enteringScreen : theme.transitions.duration.leavingScreen,
    }),
    marginLeft: open ? 0 : `-${drawerWidth}px`,
    [theme.breakpoints.down('md')]: {
      width: '100%',
      marginLeft: 0,
    },
    '& .MuiDrawer-root': {
      position: 'absolute',
    },
    '& .MuiBackdrop-root': {
      position: 'absolute',
    },
    '& .MuiDrawer-paper': {
      boxSizing: 'border-box',
      width: drawerWidth,
      position: 'absolute',
    },
  })
);

function ClassroomPicker({ defaultSelected = [], fieldName, setFieldValue }) {
  console.log('ClassroomPicker');
  const dispatch = useDispatch();
  const [open, setOpen] = useState(true);
  const [mobileOpen, setMobileOpen] = useState(false);
  const [isClosing, setIsClosing] = useState(false);
  const [checkedIds, setCheckedIds] = useState(() => new Set(defaultSelected));
  const [selectedGrade, setSelectedGrade] = useState('all');
  const [searchGroup, setSearchGroup] = useState('');
  const [searchClassRoom, setSearchClassRoom] = useState('');
  const [openDialogClassRoomForm, setOpenDialogClassRoomForm] = useState(false);

  const containerRef = useRef(null);

  const { data: classrooms, isLoading } = classroomApiSlice.useFetchClassroomsQuery({ keyword: "" });

  const handleClassroomChange = useCallback(
    (selectedIds) => {
      setFieldValue(fieldName, selectedIds);
    },
    [setFieldValue]
  );

  const classroomsMemo = useMemo(() => {
    if (!classrooms) return {};

    let filteredItems = classrooms;

    if (searchGroup) {
      filteredItems = filteredItems.filter(item =>
        item.grade && item.grade.title.toLowerCase().includes(searchGroup.toLowerCase())
      );
    }

    return filteredItems;
  }, [classrooms, searchGroup]);

  const groupedMemo = useMemo(() => {
    return groupBy(classroomsMemo, "grade_id");
  }, [classroomsMemo]);

  const groupedClassroomsMemo = useMemo(() => {
    let filteredItems = classroomsMemo;

    if (searchClassRoom) {
      filteredItems = filteredItems.filter(item =>
        item.title.toLowerCase().includes(searchClassRoom.toLowerCase())
      );
    }

    return groupBy(filteredItems, "grade_id");
  }, [classroomsMemo, searchClassRoom]);

  useEffect(() => {
    handleClassroomChange(Array.from(checkedIds));
  }, [checkedIds]);

  const handleGroupSelect = (id) => {
    setSelectedGrade(id);
  };

  const handleCheckAll = useCallback((event) => {
    const newSet = new Set();

    if (event.target.checked && classrooms) {
      classrooms.forEach(item => newSet.add(item.id));
    }
    setCheckedIds(newSet);
  }, [classrooms]);

  const handleCheckSubAll = useCallback((event, gradeId) => {
    const groupItems = groupedClassroomsMemo[gradeId] || [];

    setCheckedIds(prevSet => {
      const newSet = new Set(prevSet);

      if (event.target.checked) {
        groupItems.forEach(item => newSet.add(item.id));
      } else {
        groupItems.forEach(item => newSet.delete(item.id));
      }

      return newSet;
    });
  }, [groupedClassroomsMemo]);

  const handleCheck = useCallback((event, id) => {
    setCheckedIds(prevSet => {
      const newSet = new Set(prevSet);

      if (event.target.checked) {
        newSet.add(id);
      } else {
        newSet.delete(id);
      }

      return newSet;
    });
  }, []);

  const handleDrawerOpen = () => {
    setOpen(prev => !prev);
  };

  const handleDrawerMobileClose = () => {
    setIsClosing(true);
    setMobileOpen(false);
  };

  const handleDrawerTransitionEnd = () => {
    setIsClosing(false);
  };

  const handleDrawerMobileToggle = () => {
    if (!isClosing) {
      setMobileOpen(prev => !prev);
    }
  };

  const debouncedSearchGroup = useCallback(
    debounce((value) => {
      setSearchGroup(value);
    }, 700),
    []
  );

  const debouncedSearchClassRoom = useCallback(
    debounce((value) => {
      setSearchClassRoom(value);
    }, 700),
    []
  );

  const handleSearchGroup = (event) => {
    debouncedSearchGroup(event.target.value);
  };

  const handleSearchClassRoom = (event) => {
    debouncedSearchClassRoom(event.target.value);
  };

  const handleOpenDialogClassRoomForm = useCallback(() => {
    setOpenDialogClassRoomForm(true);
  }, []);

  const handleCloseDialogClassRoomForm = useCallback(() => {
    setOpenDialogClassRoomForm(false);
  }, []);

  const submitClassroom = useCallback(() => {
    dispatch(
      classroomApiSlice.util.invalidateTags([{ type: "Classroom", id: "LIST" }])
    );

    toast.success('Thêm mới lớp học thành công', {
      position: "top-right",
    });

    handleCloseDialogClassRoomForm();
  }, [dispatch, handleCloseDialogClassRoomForm]);

  const allClassroomsChecked = classrooms ? classrooms.every(item => checkedIds.has(item.id)) : false;
  const someClassroomsChecked = classrooms && classrooms.some(item => checkedIds.has(item.id));

  const drawer = (
    <Box className="h-100">
      <Toolbar className="bg-light">
        <GroupInputSearch>
          <IconButton
            size="small"
            color="inherit"
            aria-label="search"
            sx={{ minWidth: '20px' }}
            disabled
          >
            <SearchIcon />
          </IconButton>
          <InputBase
            placeholder="Tìm kiếm khối"
            inputProps={{ 'aria-label': 'Tìm kiếm khối' }}
            onChange={handleSearchGroup}
          />
        </GroupInputSearch>
      </Toolbar>
      <Box sx={{ height: 'calc(100% - 55px)', overflowY: 'auto' }}>
        <List>
          <ListItem disablePadding>
            <ListItemButton
              selected={selectedGrade === 'all'}
              onClick={() => handleGroupSelect('all')}
            >
              <ListItemText primary="Tất cả" />
            </ListItemButton>
          </ListItem>
          {Object.keys(groupedMemo).map((groupedKey, index) => (
            <ListItem className="mt-1" key={`group-${index}`} disablePadding>
              <ListItemButton
                selected={selectedGrade === groupedKey}
                onClick={() => handleGroupSelect(groupedKey)}
              >
                <ListItemText primary={groupedMemo[groupedKey][0]?.grade?.title || 'Danh sách'} />
              </ListItemButton>
            </ListItem>
          ))}
        </List>
      </Box>
    </Box>
  );

  return (
    <div className="mt-4">
      <p className="m-1">Giao cho lớp</p>
      <Box sx={{ display: 'flex', position: 'relative', border: '1px solid #eee' }}>
        {/* Drawer phiên bản Desktop */}
        <Drawer
          sx={{
            width: drawerWidth,
            flexShrink: 0,
            display: { xs: 'none', md: 'block' },
            '& .MuiDrawer-paper': {
              boxSizing: 'border-box',
              width: drawerWidth,
              position: 'absolute',
              overflowY: 'hidden',
            },
          }}
          variant="persistent"
          anchor="left"
          open={open}
        >
          {drawer}
        </Drawer>
        <Main open={open} ref={containerRef}>
          {/* Drawer phiên bản Mobile */}
          <Drawer
            container={containerRef.current}
            variant="temporary"
            open={mobileOpen}
            onTransitionEnd={handleDrawerTransitionEnd}
            onClose={handleDrawerMobileClose}
            ModalProps={{
              container: containerRef.current,
              disablePortal: true,
            }}
            sx={{
              display: { xs: 'block', md: 'none' },
            }}
            slotProps={{
              root: {
                keepMounted: true,
              },
            }}
          >
            {drawer}
          </Drawer>
          <Toolbar className="d-flex flex-wrap align-items-center justify-content-between bg-light mb-3">
            <Box className="d-flex align-items-center gap-1">
              <IconButton
                color="inherit"
                aria-label="open drawer"
                edge="start"
                onClick={handleDrawerOpen}
                sx={{
                  display: { xs: 'none', md: 'block' },
                }}
              >
                {open ? <ChevronLeftIcon /> : <MenuIcon />}
              </IconButton>
              <IconButton
                color="inherit"
                aria-label="open drawer"
                edge="start"
                onClick={handleDrawerMobileToggle}
                sx={{
                  display: { xs: 'block', md: 'none' },
                }}
              >
                <MenuIcon />
              </IconButton>
              <Button variant="outlined" size="small" color="secondary" onClick={handleOpenDialogClassRoomForm}>
                Thêm lớp mới
              </Button>
              <FormControlLabel
                sx={{
                  display: { xs: 'none', md: 'block' },
                }}
                control={
                  <Checkbox
                    size="small"
                    onChange={handleCheckAll}
                    checked={allClassroomsChecked}
                    indeterminate={!allClassroomsChecked && someClassroomsChecked}
                  />
                }
                label="Chọn tất cả lớp"
                labelPlacement="start"
              />
            </Box>
            <GroupInputSearch>
              <IconButton
                size="small"
                color="inherit"
                aria-label="search"
                sx={{ minWidth: '20px' }}
                disabled
              >
                <SearchIcon />
              </IconButton>
              <InputBase
                placeholder="Tìm kiếm lớp"
                inputProps={{ 'aria-label': 'Tìm kiếm lớp' }}
                onChange={handleSearchClassRoom}
              />
            </GroupInputSearch>
          </Toolbar>
          <Box sx={{
            height: '300px',
            overflowY: 'auto',
            overflowX: 'hidden',
            position: 'relative'
          }}>
            {isLoading ? (
              Array.from({ length: 3 }, (_, index) => (
                <Skeleton
                  key={index}
                  variant="rounded"
                  height={40}
                  sx={{ m: "15px", borderRadius: "4px" }}
                />
              ))
            ) : (
              classrooms && classrooms.length ? (
                (selectedGrade == 'all' ? Object.keys(groupedClassroomsMemo) : [selectedGrade]).map((groupedKey, index) => {
                  const groupItems = groupedClassroomsMemo[groupedKey] || [];
                  const groupAllChecked = groupItems.every(item => checkedIds.has(item.id));
                  const groupSomeChecked = groupItems.some(item => checkedIds.has(item.id));

                  if (groupItems.length == 0) return null;

                  return (
                    <Box key={`item-${index}`} className="mb-3">
                      <FormControlLabel
                        label={
                          <strong className="fs-16">
                            {groupItems[0]?.grade?.title || 'Danh sách'}
                          </strong>
                        }
                        labelPlacement="start"
                        control={
                          <Checkbox
                            sx={{ '&:hover': { bgcolor: 'transparent' } }}
                            size="small"
                            onChange={(e) => handleCheckSubAll(e, groupedKey)}
                            checked={groupAllChecked}
                            indeterminate={!groupAllChecked && groupSomeChecked}
                            icon={<span className="mt-1 text-primary">Chọn tất cả lớp</span>}
                            indeterminateIcon={<span className="mt-1">Chọn tất cả lớp</span>}
                            checkedIcon={<span className="mt-1">Bỏ chọn tất cả lớp</span>}
                          />
                        }
                      />
                      <Box className="row ps-4">
                        {groupItems.map((item, idx) => (
                          <Box className="col-12 col-lg-6 col-xl-4" key={`child-${idx}`}>
                            <FormControlLabel
                              label={item.title}
                              control={
                                <Checkbox
                                  size="small"
                                  checked={checkedIds.has(item.id)}
                                  onChange={(e) => handleCheck(e, item.id)}
                                />
                              }
                            />
                          </Box>
                        ))}
                      </Box>
                    </Box>
                  );
                })
              ) : (
                <NoDataOverlay message="Không có lớp học nào!" />
              )
            )}
          </Box>
        </Main>
      </Box>
      {openDialogClassRoomForm && (
        <DialogClassroomForm
          classroom={{}}
          open={openDialogClassRoomForm}
          onClose={handleCloseDialogClassRoomForm}
          onSubmitClassroom={submitClassroom}
        />
      )}
    </div>
  );
}

export default memo(ClassroomPicker);
