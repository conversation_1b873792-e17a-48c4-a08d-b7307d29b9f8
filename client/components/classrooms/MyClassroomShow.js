"use client";

import { useState, useEffect, useCallback, useMemo, memo } from "react";
import Link from "next/link";

import { styled } from "@mui/material/styles";
import Paper from "@mui/material/Paper";
import InputBase from "@mui/material/InputBase";
import Skeleton from "@mui/material/Skeleton";
import IconButton from "@mui/material/IconButton";
import Card from "@mui/material/Card";
import CardContent from "@mui/material/CardContent";
import Accordion from "@mui/material/Accordion";
import AccordionDetails from "@mui/material/AccordionDetails";
import AccordionSummary from "@mui/material/AccordionSummary";
import CardActionArea from "@mui/material/CardActionArea";
import Tabs from "@mui/material/Tabs";
import Tab from "@mui/material/Tab";
import Box from "@mui/material/Box";
import Stack from "@mui/material/Stack";
import Container from '@mui/material/Container';

import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import SearchIcon from "@mui/icons-material/Search";

import NoDataOverlay from "@/components/NoDataOverlay";

import debounce from "lodash/debounce";
import groupBy from "lodash/groupBy";
import dayjs from "dayjs";
import "dayjs/locale/vi";

import { useFetchMyClassroomQuery } from '@/slices/features/classroomApiSlice';
import { formatDateOfWeekFromTs } from "@/utils/helpers";

const GroupInputSearch = styled(Paper)(() => ({
  padding: "2px 4px",
  display: "flex",
  alignItems: "center",
  border: "1px solid #e5e5e5",
  width: "100%",
  height: "39px",
  maxWidth: "256px",
  borderRadius: "8px",
}));

const CustomAccordion = styled(Accordion)(() => ({
  background: "transparent",
  ".MuiAccordionSummary-root": {
    justifyContent: "start",
    minHeight: "30px !important",
    gap: "8px",
  },
  ".MuiAccordionSummary-content": {
    flexGrow: "inherit",
    margin: "8px 0 !important",
  },
}));

const MyClassroomShow = ({ classroomId }) => {
  console.log('MyClassroomShow');
  const [keyword, setKeyword] = useState("");
  const [tabValue, setTabValue] = useState("all");
  const { data: assignments, isLoading } = useFetchMyClassroomQuery({ classroomId });

  const handleChangeTab = useCallback((event, newValue) => {
    setTabValue(newValue);
  }, []);

  const debouncedSearch = useCallback(
    debounce((value) => {
      setKeyword(value);
    }, 700),
    []
  );

  const handleSearch = useCallback(
    (event) => {
      debouncedSearch(event.target.value);
    },
    [debouncedSearch]
  );

  const filterAssignments = useMemo(() => {
    if (!assignments) return [];
    const searchText = keyword ? keyword.toLowerCase() : '';

    return assignments.filter((assignment) => {
        const isDone = assignment.quiz_results?.length > 0;
        if (tabValue === "done" && !isDone) return false;
        if (tabValue === "notDone" && isDone) return false;

        if (searchText && !assignment.title.toLowerCase().includes(searchText)) {
            return false;
        }

        return true;
    });

}, [assignments, tabValue, keyword]);

  const groupedAssignmentsMemo = useMemo(() => {
    return filterAssignments ? groupBy(filterAssignments, "start_day") : {};
  }, [filterAssignments]);

  const GroupedAssignments = memo(
    ({ groupedAssignments }) => {
      console.log("GroupedAssignments");

      return Object.keys(groupedAssignments).map((start_day, index) => (
        <div className="mb-4" key={index}>
          <CustomAccordion className="border-0" defaultExpanded>
            <AccordionSummary
              expandIcon={<ExpandMoreIcon />}
              aria-controls={`panel${index}-content`}
              id={`panel${index}-header`}
              className="px-0 fs-16"
            >
              <span className="fw-medium">
                {start_day > 0
                  ? formatDateOfWeekFromTs(start_day)
                  : "Bài tập không có thời hạn"}
              </span>
              :
              <span className="ms-2">
                ({groupedAssignments[start_day].length} bài tập)
              </span>
            </AccordionSummary>
            <AccordionDetails className="p-0">
              <div className="row">
                {groupedAssignments[start_day].map((item) => {
                  let status = 0;

                  if (item?.quiz_results.length > 0) {
                    status = 1;
                  } else if (item?.end_time && dayjs(item.end_time).isBefore(dayjs())) {
                    status = 2;
                  }

                  return (
                    <div className="col-12" key={item.id}>
                      <Card className="p-1 mb-3">
                        <CardContent className="p-0">
                          <Stack
                            spacing={1}
                            direction={{ xs: "column", sm: "row" }}
                            sx={{
                              justifyContent: "space-between",
                              alignItems: "stretch",
                            }}
                          >
                            {status == 0 && (
                              <div className="m-0 bg-dark bg-opacity-10 fw-semibold text-muted">
                                <Box className="d-flex align-items-center justify-content-center text-center h-100 p-1" sx={{ width: 80 }}>Chưa làm</Box>
                              </div>
                            )}
                            {status == 1 && (
                             <div className="m-0 bg-success bg-opacity-10 fw-semibold text-success">
                                <Box className="d-flex align-items-center justify-content-center text-center h-100 p-1" sx={{ width: 80 }}>Đã làm</Box>
                              </div>
                            )}
                            {status == 2 && (
                              <div className="m-0 bg-danger text-white fw-semibold rounded-1">
                                <Box className="d-flex align-items-center justify-content-center text-center h-100 p-1" sx={{ width: 80 }}>Hết hạn</Box>
                              </div>
                            )}
                            <CardActionArea className="p-3 m-0">
                              <h4 className="fs-18 fw-normal">
                                <i className="bi bi-calendar-check me-1"></i> {item.subtitle || item.title}
                              </h4>
                              <div className="mt-3 d-flex flex-wrap justify-content-between align-items-center">
                                <span className="d-flex align-items-center flex-wrap gap-4">
                                  <span className="text-black-50 fs-14">
                                    <i className="bi bi-person-lines-fill me-1 text-primary"></i>
                                    Đã làm:
                                    <strong className="ms-1">{ item?.quiz_results.length || 0 }</strong> lần
                                  </span>
                                  { item.end_time && (
                                    <span className="text-black-50 fs-14">
                                      <i className="bi bi-gear me-1"></i>
                                      Hạn nộp: <span className="text-danger">{dayjs(item.end_time).format('DD/MM/YYYY HH:mm')}</span>
                                    </span>
                                  ) }
                                </span>
                                { status < 2 && (
                                  <Link href={`/join/quiz/${item.quiz_id}/start/?assignment=${item.id}`} className="btn btn-outline-primary2 btn-sm fs-14">
                                    Làm bài <i className="bi bi-arrow-right-short ms-1"></i>
                                  </Link>
                                ) }
                              </div>
                            </CardActionArea>
                          </Stack>
                        </CardContent>
                      </Card>
                    </div>
                  );
                })}
              </div>
            </AccordionDetails>
          </CustomAccordion>
        </div>
      ));
    }
  );

  GroupedAssignments.displayName = "GroupedAssignments";

  return (
    <Container>
      <div className="row">
        <div className="col-md-10 offset-md-1">
          { isLoading ? (
            <Box>
              <Box className="d-flex align-items-center gap-3">
                <Skeleton sx={{ width: { xs: '30%', sm: '10%' }, height: 40 }} />
                <Skeleton sx={{ width: { xs: '30%', sm: '10%' }, height: 40 }} />
              </Box>
              { Array.from({ length: 3 }, (_, index) => (
                <Skeleton
                  variant="rounded"
                  height={70}
                  key={index}
                  sx={{ my: "30px", borderRadius: "4px" }}
                />
              ))}
            </Box>
          ) : (
            assignments?.length > 0 ? (
              <>
                <p className="fs-18 text-center">Danh sách các bài tập đã giao</p>
                <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
                  <Tabs
                    value={tabValue}
                    onChange={handleChangeTab}
                    aria-label="basic tabs example"
                  >
                    <Tab value="all" label="Tất cả" />
                    <Tab value="done" label="Đã làm" />
                    <Tab value="notDone" label="Chưa làm" />
                  </Tabs>
                </Box>
                <GroupInputSearch className="my-4">
                  <IconButton
                    size="small"
                    color="inherit"
                    aria-label="search"
                    sx={{ minWidth: "20px" }}
                    disabled
                  >
                    <SearchIcon />
                  </IconButton>
                  <InputBase
                    placeholder="Tìm bài tập"
                    inputProps={{ "aria-label": "Tìm bài tập" }}
                    onChange={handleSearch}
                  />
                </GroupInputSearch>
                <GroupedAssignments
                  groupedAssignments={groupedAssignmentsMemo}
                />
              </>
            ) : <NoDataOverlay message="Không có dữ liệu!" />
          ) }
        </div>
      </div>
    </Container>
  );
};

export default MyClassroomShow;
