"use client";

import { useEffect, useState, useMemo, useCallback, memo, forwardRef } from "react";
import { useDispatch } from "react-redux";

import { useTheme } from "@mui/material/styles";
import Tabs from "@mui/material/Tabs";
import Tab from "@mui/material/Tab";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import Slide from "@mui/material/Slide";
import Box from "@mui/material/Box";
import FormControl from "@mui/material/FormControl";
import FormHelperText from "@mui/material/FormHelperText";
import InputLabel from "@mui/material/InputLabel";
import OutlinedInput from "@mui/material/OutlinedInput";
import Radio from '@mui/material/Radio';
import RadioGroup from '@mui/material/RadioGroup';
import FormControlLabel from '@mui/material/FormControlLabel';
import CircularProgress from "@mui/material/CircularProgress";

import { DemoContainer } from '@mui/x-date-pickers/internals/demo';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { renderTimeViewClock } from '@mui/x-date-pickers/timeViewRenderers';

import ClassroomPicker from "../classrooms/ClassroomPicker";
import DateTimePickerField from "../DateTimePickerField";

import * as Yup from "yup";
import toast from "react-hot-toast";
import { Formik } from "formik";
import dayjs from "dayjs";
import "dayjs/locale/vi";

import { setNoti } from "@/slices/notiSlice";

import { updateAssignment } from "@/actions/classroomAction";
import { fetchClassrooms, assignClassrooms } from "@/actions/quizAction";
import { delay } from "@/utils/helpers";

const Transition = forwardRef(function Transition(props, ref) {
  return <Slide direction="up" ref={ref} {...props} />;
})

function CustomTabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 1 }}>{children}</Box>}
    </div>
  );
}

const DialogAssignmentForm = ({
  classroom,
  currentAssignment,
  submitAssignment,
  open,
  onClose,
  defaultTabValue = 0,
}) => {
  console.log("DialogAssignmentForm", currentAssignment);
  const theme = useTheme();
  const dispatch = useDispatch();

  const [tabValue, setTabValue] = useState(defaultTabValue);
  const [classroomIds, setClassroomIds] = useState(null);

  const handleChange = (event, newValue) => {
    setTabValue(newValue);
  };

  useEffect(() => {
    if (open && tabValue == 1 && classroomIds === null) {
      fetchClassrooms(currentAssignment.quiz_id)
        .then((res) => {
          setClassroomIds(res?.data || [ư]);
        })
        .catch((error) => {
          setClassroomIds([]);
        });
    }
  }, [open, tabValue, classroomIds, currentAssignment]);

  return (
    <Dialog
      open={open}
      TransitionComponent={Transition}
      keepMounted
      fullWidth={true}
      maxWidth="md"
      onClose={onClose}
    >
      <DialogTitle className="h5 mx-2">
        Cập nhật
      </DialogTitle>
      <DialogContent>
        <Tabs
          value={tabValue}
          onChange={handleChange}
          aria-label="Cài đặt bài tập"
        >
          <Tab label="Cài đặt" />
          <Tab label="Giao thêm cho lớp" />
        </Tabs>
        <CustomTabPanel value={tabValue} index={0}>
          <Formik
            initialValues={{
              title: currentAssignment.title || "Bài Quiz không có tiêu đề",
              start_time: currentAssignment.start_time
                ? dayjs(currentAssignment.start_time)
                : null,
              end_time: currentAssignment.end_time
                ? dayjs(currentAssignment.end_time)
                : null,
              show_answer: !currentAssignment.show_answer ? 0 : 1,
            }}
            validationSchema={Yup.object().shape({
              title: Yup.string()
                .min(3)
                .max(500)
                .required("Tiêu đề không được để trống"),
              start_time: Yup.date()
                .nullable(),
              end_time: Yup.date()
                .nullable()
                .min(Yup.ref("start_time"), "Thời gian kết thúc phải sau thời gian bắt đầu"),
            })}
            onSubmit={async (values, { setErrors, setSubmitting }) => {
              try {
                await delay(400);

                await updateAssignment(classroom.id, {
                  ...currentAssignment,
                  ...values,
                  start_time: values.start_time
                    ? dayjs(values.start_time).format("YYYY-MM-DD HH:mm:ss")
                    : null,
                  end_time: values.end_time
                    ? dayjs(values.end_time).format("YYYY-MM-DD HH:mm:ss")
                    : null,
                });

                submitAssignment([classroom.id]);
              } catch (err) {
                if (err?.status == 422) {
                  setErrors(err.errors);
                } else {
                  dispatch(setNoti(err));
                }
              } finally {
                setSubmitting(false);
              }
            }}
          >
            {({
                errors,
                handleBlur,
                handleChange,
                handleSubmit,
                isSubmitting,
                touched,
                values,
                setFieldValue,
              }) => (
                <form noValidate onSubmit={handleSubmit}>
                  <Box className="d-flex flex-column gap-4 py-3">
                    <div className="px-3">
                      <div className="row">
                        <div className="col-sm-2">
                          <p>Tên:</p>
                        </div>
                        <div className="col-sm-10">
                          <FormControl
                            fullWidth
                            error={Boolean(touched.title && errors.title)}
                          >
                            <OutlinedInput
                              id="outlined-adornment-title"
                              type="text"
                              value={values.title}
                              name="title"
                              onBlur={handleBlur}
                              onChange={handleChange}
                              inputProps={{}}
                            />
                            {touched.title && errors.title && (
                              <FormHelperText
                                error
                                id="standard-weight-helper-text-title"
                              >
                                {errors.title}
                              </FormHelperText>
                            )}
                          </FormControl>
                        </div>
                      </div>
                    </div>
                    <div className="row mt-4">
                      <div className="col-sm-2">
                        <p>Thời gian nộp bài:</p>
                      </div>
                      <div className="col-sm-5 mb-3">
                        <DateTimePickerField
                          label="Từ"
                          value={values.start_time}
                          fieldName="start_time"
                          setFieldValue={setFieldValue}
                        />
                        {touched.start_time && errors.start_time && (
                          <FormHelperText error>{errors.start_time}</FormHelperText>
                        )}
                      </div>
                      <div className="col-sm-5 mb-3">
                        <DateTimePickerField
                          label="Đến"
                          value={values.end_time}
                          fieldName="end_time"
                          setFieldValue={setFieldValue}
                        />
                        {touched.end_time && errors.end_time && (
                          <FormHelperText error>{errors.end_time}</FormHelperText>
                        )}
                      </div>
                    </div>
                    <div className="row align-items-center">
                      <div className="col-sm-2">
                        <p className="mb-0">Cho xem kết quả:</p>
                      </div>
                      <div className="col-sm-10">
                        <FormControl>
                          <RadioGroup
                            row
                            aria-labelledby="show_answer-group"
                            name="show_answer-group"
                            value={values.show_answer}
                            onChange={(event) => setFieldValue("show_answer", event.target.value)}
                          >
                            <FormControlLabel value="0" control={<Radio />} label="Không" />
                            <FormControlLabel value="1" control={<Radio />} label="Có" />
                          </RadioGroup>
                        </FormControl>
                      </div>
                    </div>
                  </Box>
                  <Box className="text-end mt-4">
                    <Button
                      variant="outlined"
                      type="button"
                      onClick={onClose}
                      color="inherit"
                      sx={{ mr: 2 }}
                    >
                      Hủy
                    </Button>
                    <Button
                      disableElevation
                      disabled={isSubmitting}
                      type="submit"
                      variant="contained"
                      color="secondary"
                      endIcon={isSubmitting ? <CircularProgress size={18} color="inherit" /> : null}
                    >
                      Lưu
                    </Button>
                  </Box>
                </form>
              )}
          </Formik>
        </CustomTabPanel>
        <CustomTabPanel value={tabValue} index={1}>
          { classroomIds!== null && (
            <Formik
              initialValues={{
                classrooms: classroomIds,
              }}
              validationSchema={Yup.object().shape({
                classrooms: Yup.array().min(1, "Bạn chưa chọn lớp"),
              })}
              onSubmit={async (values, { setErrors, setSubmitting }) => {
                try {
                  await delay(400);

                  const res = await assignClassrooms(currentAssignment.quiz_id, { ...values, assignmentId: currentAssignment.id });

                  submitAssignment(res?.data || [classroom.id]);
                } catch (err) {
                  if (err?.status == 422) {
                    setErrors(err.errors);
                  } else {
                    dispatch(setNoti(err));
                  }
                } finally {
                  setSubmitting(false);
                }
              }}
            >
              {({
                errors,
                handleBlur,
                handleChange,
                handleSubmit,
                isSubmitting,
                touched,
                values,
                setFieldValue,
              }) => {
                return (
                  <form noValidate onSubmit={handleSubmit}>
                    <ClassroomPicker
                      defaultSelected={values.classrooms}
                      fieldName="classrooms"
                      setFieldValue={setFieldValue}
                    />
                    {touched.classrooms && errors.classrooms && (
                      <FormHelperText error>
                        {errors.classrooms}
                      </FormHelperText>
                    )}
                    <Box className="text-end mt-4">
                      <Button
                        variant="outlined"
                        type="button"
                        onClick={onClose}
                        color="inherit"
                        sx={{ mr: 2 }}
                      >
                        Hủy
                      </Button>
                      <Button
                        disableElevation
                        disabled={isSubmitting}
                        type="submit"
                        variant="contained"
                        color="secondary"
                        endIcon={isSubmitting ? <CircularProgress size={18} color="inherit" /> : null}
                      >
                        Giao bài
                      </Button>
                    </Box>
                  </form>
                );
              }}
            </Formik>
          ) }
        </CustomTabPanel>
      </DialogContent>
    </Dialog>
  );
};

export default memo(DialogAssignmentForm);
