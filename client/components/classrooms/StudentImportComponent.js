"use client";

import { useState } from "react";

import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import Typography from "@mui/material/Typography";
import CircularProgress from "@mui/material/CircularProgress";

import toast from "react-hot-toast";
import { getDownloadInfo } from "@/utils/downloadHelper";
import StudentExportComponent from "./StudentExportComponent";
import { importStudents } from "@/actions/classroomAction";
import {
  useFileImport,
  FileUploadArea,
  SelectedFileDisplay,
  ValidationErrorsDisplay
} from "@/components/shared/FileImport";

const StudentImportComponent = ({
  classroom,
  onImportSuccess,
  showImportButton = true,
  showTemplateButton = true,
  title = "",
}) => {
  const [isUploading, setIsUploading] = useState(false);

  // Use the centralized file import hook
  const {
    selectedFile,
    isDragOver,
    validationErrors,
    fileInputRef,
    fileConfig,
    handleFileInputChange,
    handleDrop,
    handleDragOver,
    handleDragLeave,
    removeSelectedFile,
    triggerFileInput,
    hasFile,
    hasErrors,
    setErrors,
    resetState
  } = useFileImport({
    fileType: 'excel',
    onFileSelect: (file) => {
      console.log('📁 StudentImportComponent: File selected:', file);
    },
    onFileRemove: () => {
      console.log('🗑️ StudentImportComponent: File removed');
    }
  });
  const handleImport = async () => {
    if (!selectedFile) {
      toast.error("Vui lòng chọn file Excel");
      return;
    }

    setIsUploading(true);
    setErrors([]);

    try {
      const formData = new FormData();
      formData.append("file", selectedFile);

      const response = await importStudents(classroom.id, formData);

      if (response.status === 200) {
        toast.success(`Import thành công ${response?.imported_count || response.imported_count} học sinh`);
        // Reset the file selection and errors
        resetState();
        if (onImportSuccess) {
          onImportSuccess();
        }
      } else {
        // Handle different error types
        if (response.status === 419) {
          toast.error("Phiên làm việc đã hết hạn. Vui lòng tải lại trang và thử lại.");
        } else if (response?.errors || response.errors) {
          toast.error(response.message);
        } else {
          toast.error(response.message || "Có lỗi xảy ra khi import");
        }
      }
    } catch (error) {
      console.error("Import error:", error);
      if (error.name === 'TypeError' && error.message.includes('Failed to fetch')) {
        toast.error("Không thể kết nối đến server. Vui lòng kiểm tra kết nối mạng.");
      } else if (error?.validation_errors || error.validation_errors) {
        const validationErrors = error?.validation_errors || error.validation_errors;
        setErrors(validationErrors);
      } else {
        toast.error(error.message || "Có lỗi xảy ra khi import!");
      }
    } finally {
      setIsUploading(false);
    }
  };

  if (!showImportButton) {
    return null;
  }

  return (
    <Box>
      {title && (
        <Typography variant="h6" gutterBottom>
          {title}
        </Typography>
      )}

      {/* Validation Errors Display */}
      {hasErrors && (
        <ValidationErrorsDisplay errors={validationErrors} />
      )}

      {/* Selected File Display - Show when file is selected */}
      {hasFile && (
        <SelectedFileDisplay
          file={selectedFile}
          onRemove={removeSelectedFile}
          fileConfig={fileConfig}
          fileType = "excel"
        />
      )}

      {/* File Upload Area - Hidden when file is selected */}
      {!hasFile && (
        <FileUploadArea
          isDragOver={isDragOver}
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onClick={triggerFileInput}
          onFileInputChange={handleFileInputChange}
          fileInputRef={fileInputRef}
          fileConfig={fileConfig}
          title="Kéo thả file Excel (.xlsx, .xls) hoặc click để chọn file"
        />
      )}

      {/* Template Download Button - Always visible */}
      {showTemplateButton && (
        <Box mt={2} mb={2}>
          <StudentExportComponent
            classroom={classroom}
            showExportButton={true}
          />
          <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
            {getDownloadInfo().message}
          </Typography>
        </Box>
      )}

      {/* Import Button */}
      <Box mt={2}>
        <Button
          variant="contained"
          color="primary"
          onClick={handleImport}
          disabled={!selectedFile || isUploading}
          startIcon={isUploading ? <CircularProgress size={20} /> : null}
          fullWidth
        >
          {isUploading ? "Đang import..." : "Xác nhận import"}
        </Button>
      </Box>
    </Box>
  );
};

export default StudentImportComponent;
