"use client";

import { useState, useEffect, useCallback, useMemo, memo } from "react";
import { useDispatch } from "react-redux";
import Link from "next/link";

import { styled } from "@mui/material/styles";
import { purple } from "@mui/material/colors";
import Card from "@mui/material/Card";
import Stack from "@mui/material/Stack";
import Button from "@mui/material/Button";
import Skeleton from "@mui/material/Skeleton";
import CardActionArea from "@mui/material/CardActionArea";
import CardContent from "@mui/material/CardContent";
import Accordion from "@mui/material/Accordion";
import AccordionSummary from "@mui/material/AccordionSummary";
import AccordionDetails from "@mui/material/AccordionDetails";
import Paper from "@mui/material/Paper";
import InputBase from "@mui/material/InputBase";
import IconButton from "@mui/material/IconButton";
import Container from '@mui/material/Container';

import SearchIcon from "@mui/icons-material/Search";

import MoreVertIcon from "@mui/icons-material/MoreVert";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";

import Dropdown from "@/components/Dropdown";
import NoDataOverlay from "../NoDataOverlay";
import PieChartProgress from "../PieChartProgress";
import DialogClassroomForm from "./DialogClassroomForm";

import toast from "react-hot-toast";
import debounce from "lodash/debounce";
import groupBy from "lodash/groupBy";

import { useConfirm } from "@/contexts/ConfirmContext";

import { removeClassroom } from "@/actions/classroomAction";
import { classroomApiSlice } from "@/slices/features/classroomApiSlice";
import { getColorByPercentage } from "@/utils/helpers";

const CustomAccordion = styled(Accordion)(() => {
  return {
    background: "transparent",
    ".MuiAccordionSummary-root": {
      justifyContent: "start",
      minHeight: "30px !important",
      gap: "8px",
    },
    ".MuiAccordionSummary-content": {
      flexGrow: "inherit",
      margin: "8px 0 !important",
    },
  };
});

const ColorButton = styled(Button)(({ theme }) => ({
  color: theme.palette.getContrastText(purple[500]),
  backgroundColor: purple[700],
  "&:hover": {
    backgroundColor: purple[500],
  },
}));

const GroupInputSearch = styled(Paper)(() => {
  return {
    padding: "2px 4px",
    display: "flex",
    alignItems: "center",
    border: "1px solid #e5e5e5",
    width: "100%",
    height: "39px",
    maxWidth: "300px",
    borderRadius: "8px",
  };
});

const ClassroomsIndex = () => {
  console.log("ClassroomsIndex");
  const dispatch = useDispatch();
  const confirmDelete = useConfirm();

  const [currentClassroom, setCurrentClassroom] = useState({});
  const [openDialogClassRoomForm, setOpenDialogClassRoomForm] = useState(false);
  const [keyword, setKeyword] = useState("");

  const { data: classrooms, isLoading } = classroomApiSlice.useFetchClassroomsQuery({keyword});

  const handleOpenDialogClassRoomForm = useCallback(() => {
    setOpenDialogClassRoomForm(true);
  }, []);

  const handleCloseDialogClassRoomForm = useCallback(() => {
    setOpenDialogClassRoomForm(false);
  }, []);

  const debouncedSearch = useCallback(
    debounce((value) => {
      setKeyword(value);
    }, 700),
    []
  );

  const handleSearch = useCallback((event) => {
    debouncedSearch(event.target.value);
  }, []);

  const addClassroom = () => {
    setCurrentClassroom({});
    handleOpenDialogClassRoomForm();
  };

  const editClassroom = useCallback((classroom) => {
    setCurrentClassroom(classroom);
    handleOpenDialogClassRoomForm();
  }, []);

  const submitClassroom = useCallback((isUpdate = false) => {
    dispatch(
      classroomApiSlice.util.invalidateTags([{ type: "Classroom", id: "LIST" }])
    );

    toast.success(`${ isUpdate ? 'Chỉnh sửa' : 'Thêm mới' } lớp học thành công`, {
      position: "top-right",
    });

    handleCloseDialogClassRoomForm();
  }, [dispatch, handleCloseDialogClassRoomForm]);

  const deleteClassroom = useCallback(async (classroom) => {
    const confirm = await confirmDelete(
      `Xóa: ${classroom.title}?`,
      "Sau khi xóa, nội dung đã xóa sẽ không còn xuất hiện!"
    );

    if (confirm) {
      try {
        await removeClassroom(classroom.id);

        dispatch(
          classroomApiSlice.util.invalidateTags([{ type: "Classroom", id: "LIST" }])
        );

        toast.success("Xóa lớp học thành công");
      } catch (error) {
        toast.error("Xóa lớp học thất bại");
      }
    }
  }, [confirmDelete]);

  const groupedClassroomsMemo = useMemo(() => {
    return classrooms ? groupBy(classrooms, "grade_id") : {};
  }, [classrooms]);

  const GroupedClassrooms = memo(
    ({ groupedClassrooms, onUpdate, onDelete }) => {
      console.log("GroupedClassrooms");

      return Object.keys(groupedClassrooms).map((groupedKey, index) => (
        <div className="mb-4" key={index}>
          <CustomAccordion className="border-0" defaultExpanded>
            <AccordionSummary
              expandIcon={<ExpandMoreIcon />}
              aria-controls={`panel${index}-content`}
              id={`panel${index}-header`}
              className="px-0 fs-16"
            >
              <strong>
                {groupedClassrooms[groupedKey][0]?.grade?.title || 'Danh sách'}
              </strong>
              :
              <span className="ms-2">
                ({groupedClassrooms[groupedKey].length} lớp)
              </span>
            </AccordionSummary>
            <AccordionDetails className="p-0">
              <div className="row">
                {groupedClassrooms[groupedKey].map((item) => (
                  <div
                    className="col-sm-12 col-md-6 col-lg-4 mb-4"
                    key={item.id}
                  >
                    <Card
                      className="h-100"
                      sx={{
                        borderLeft: `5px solid ${item?.color || "#eee"}`,
                      }}
                    >
                      <CardContent className="p-2">
                        <Stack
                          spacing={1}
                          direction="row"
                          sx={{
                            justifyContent: "space-between",
                            alignItems: "center",
                          }}
                        >
                          <CardActionArea
                            className="d-flex align-items-center justify-content-between gap-2 p-3"
                            sx={{ padding: "5px" }}
                            component={Link}
                            href={`/dashboard/classroom/${item.id}`}
                          >
                            <div style={{ minWidth: '100px' }}>
                              <h4 className="h5 fw-bold">{item.title}</h4>
                              <div className="mt-2 d-flex flex-wrap gap-2">
                                <span className="text-black-50 fs-14">
                                  <i className="bi bi-people me-1"></i>
                                  {item.classroom_user_count || 0} học sinh
                                </span>
                                <span className="text-black-50 fs-14">
                                  <i className="bi bi-book me-1"></i>
                                  {item.classroom_quiz_count || 0} bài tập
                                </span>
                              </div>
                            </div>
                            <PieChartProgress
                              value={item.completion_rate || 0}
                              color={getColorByPercentage(item.completion_rate || 0)}
                            />
                          </CardActionArea>
                          <Dropdown
                            renderToggle={({ onClick }) => (
                              <button
                                type="button"
                                onClick={onClick}
                                className="btn btn-link btn-dropdown-small dropdown-toggle"
                              >
                                <MoreVertIcon />
                              </button>
                            )}
                            placement="bottom-start"
                            renderMenu={() => (
                              <div className="dropdown-menu dropdown-menu-2 p-1">
                                <a
                                  onClick={() => onUpdate(item)}
                                  className="dropdown-item fs-14"
                                >
                                  <i className="bi bi-pencil-square me-2"></i>
                                  Chỉnh sửa chi tiết lớp
                                </a>
                                <a
                                  onClick={() => onDelete(item)}
                                  className="dropdown-item text-danger fs-14"
                                >
                                  <i className="bi bi-trash me-2"></i> Xóa lớp
                                </a>
                              </div>
                            )}
                          />
                        </Stack>
                      </CardContent>
                    </Card>
                  </div>
                ))}
              </div>
            </AccordionDetails>
          </CustomAccordion>
        </div>
      ));
    }
  );

  GroupedClassrooms.displayName = "GroupedClassrooms";

  return (
    <Container maxWidth="cxl">
      <div className="d-flex flex-wrap align-items-center justify-content-between gap-2">
        <div className="d-flex align-items-center gap-3">
          <h4 className="fs-16 fw-light flex-shrink-0 mb-0">
            <i className="bi bi-calendar-check"></i> Danh sách lớp học:
          </h4>
          <GroupInputSearch>
            <IconButton
              size="small"
              color="inherit"
              aria-label="search"
              sx={{ minWidth: "20px" }}
              disabled
            >
              <SearchIcon />
            </IconButton>
            <InputBase
              placeholder="Tìm kiếm lớp học"
              inputProps={{ "aria-label": "Tìm kiếm lớp học" }}
              onChange={handleSearch}
            />
          </GroupInputSearch>
        </div>
        <ColorButton onClick={addClassroom}>
          <i className="bi bi-plus-circle me-2"></i>
          Tạo một lớp học
        </ColorButton>
      </div>
      <div className="mt-4">
        {isLoading ? (
          <>
            <Skeleton
              variant="rounded"
              width={110}
              height={34}
              sx={{ my: "10px", borderRadius: "4px" }}
            />
            <div className="row">
              {Array.from({ length: 6 }, (_, index) => (
                <div className="col-sm-12 col-md-6 col-lg-4" key={index}>
                  <Skeleton
                    variant="rounded"
                    height={97}
                    sx={{ my: "10px", borderRadius: "4px" }}
                  />
                </div>
              ))}
            </div>
          </>
        ) : (
          classrooms?.length ? (
            <GroupedClassrooms
              groupedClassrooms={groupedClassroomsMemo}
              onUpdate={editClassroom}
              onDelete={deleteClassroom}
            />
          ) : (
            <NoDataOverlay message="Không có lớp học nào!" />
          )
        )}
      </div>
      {openDialogClassRoomForm && (
        <DialogClassroomForm
          classroom={currentClassroom}
          open={openDialogClassRoomForm}
          onClose={handleCloseDialogClassRoomForm}
          onSubmitClassroom={submitClassroom}
        />
      )}
    </Container>
  );
};

export default ClassroomsIndex;
