'use client'

import { useEffect, useState } from 'react'
import { useSelector } from "react-redux";
import Dialog from '@mui/material/Dialog'
import DialogTitle from '@mui/material/DialogTitle'
import DialogContent from '@mui/material/DialogContent'
import Box from '@mui/material/Box'
import TextField from '@mui/material/TextField'
import Button from '@mui/material/Button'
import { joinClassroom } from "@/actions/classroomAction";

export default function JoinClassPopup({ classCode = '', onSubmit, onClose }) {
  const [code, setCode] = useState(classCode)
  const [error, setError] = useState('')
  const [loading, setLoading] = useState(false)
  const user = useSelector((state) => state.auth.user)

  useEffect(() => {
    if (classCode) setCode(classCode)
  }, [classCode])

  const handleSubmit = async () => {
    if (!code.trim()) return setError('Vui lòng nhập mã lớp.')

    if (code.length < 6) {
      return setError('Mã lớp phải có ít nhất 6 ký tự.')
    }

    if (code.length > 8) {
      return setError('Mã lớp không được vượt quá 8 ký tự.')
    }

    setError('')
    setLoading(true)

    try {
      const res = await joinClassroom({ class_code: code, user_id: user?.id, user_name: user?.name });

      if (!res.status) throw new Error('Không thể tham gia lớp học.');
      onSubmit()
    } catch (err) {
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog
      open
      keepMounted
      onClose={onClose}
      fullWidth={true}
      maxWidth="sm"
    >
      <DialogTitle className="h5">Tham gia lớp học</DialogTitle>
      <DialogContent>
        <Box className="d-flex flex-column gap-2 py-2">
          <p className="mb-0 text-secondary">Nhập mã code của lớp mà giáo viên đã chia sẻ với bạn.</p>
          <TextField
            autoFocus
            fullWidth
            margin="dense"
            label="Mã lớp"
            value={code}
            onChange={(e) => setCode(e.target.value)}
            error={!!error}
            helperText={error}
          />
        </Box>
        <Box className="text-end" sx={{ mt: 2 }}>
          <Button
            variant="outlined"
            type="button"
            onClick={onClose}
            color="inherit"
            size="small"
            sx={{ mr: 2 }}
          >
            Hủy
          </Button>
          <Button
            disableElevation
            onClick={handleSubmit}
            size="small"
            type="submit"
            variant="contained"
            color="secondary"
          >
            {loading ? 'Đang tham gia...' : 'Tham gia'}
          </Button>
        </Box>
      </DialogContent>
    </Dialog>
  )
}
