"use client";

import { forwardRef } from "react";
import { useDispatch } from "react-redux";

import { useTheme } from "@mui/material/styles";
import Box from "@mui/material/Box";
import Dialog from "@mui/material/Dialog";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import Button from "@mui/material/Button";
import FormControl from "@mui/material/FormControl";
import InputLabel from "@mui/material/InputLabel";
import OutlinedInput from "@mui/material/OutlinedInput";
import Select from "@mui/material/Select";
import MenuItem from "@mui/material/MenuItem";
import FormHelperText from "@mui/material/FormHelperText";
import Slide from "@mui/material/Slide";
import CircularProgress from "@mui/material/CircularProgress";

import TagItemPicker from "@/components/TagItemPicker";

import { colorPickerOptions } from "@/constant";
import { storeClassroom, updateClassroom } from "@/actions/classroomAction";
import { fetchOptions } from "@/actions/onlyClientAction";
import { setNoti } from "@/slices/notiSlice";

// third party
import * as Yup from "yup";
import { Formik } from "formik";
import { useTranslations } from "next-intl";
import { delay } from "@/utils/helpers";

const Transition = forwardRef(function Transition(props, ref) {
  return <Slide direction="up" ref={ref} {...props} />;
});

const DialogClassroomForm = ({
  classroom,
  open,
  onClose,
  onSubmitClassroom,
}) => {
  console.log("DialogClassroomForm");
  const theme = useTheme();
  const dispatch = useDispatch();
  const t = useTranslations("Common");

  const { data: dataOptions } = fetchOptions(open ? "grades" : null);

  return (
    <Dialog
      open={open}
      TransitionComponent={Transition}
      keepMounted
      fullWidth={true}
      maxWidth="sm"
    >
      <DialogTitle className="h5">
        {classroom.id ? "Chỉnh sửa lớp học" : "Tạo lớp học mới"}
      </DialogTitle>
      <DialogContent>
        <Formik
          initialValues={{
            title: classroom.title || "",
            grade_id: classroom.grade_id || "",
            color: classroom.color || "#e91d63",
          }}
          validationSchema={Yup.object().shape({
            title: Yup.string()
              .min(3)
              .max(500)
              .required("Tên không được để trống"),
            grade_id: Yup.number()
              .required("Lớp không được để trống")
              .positive()
              .integer(),
          })}
          onSubmit={async (values, { setErrors, setSubmitting }) => {
            try {
              await delay(400);

              const {data: newClasssRoom } = classroom?.id
                ? await updateClassroom({ ...classroom, ...values })
                : await storeClassroom(values);

              if (newClasssRoom) {
                onSubmitClassroom(classroom?.id ? true : false);
              } else {
                throw new Error("Đã xảy ra lỗi.");
              }
            } catch (err) {
              if (err?.status == 422) {
                setErrors(err.errors);
              } else {
                dispatch(setNoti(err));
              }
            } finally {
              setSubmitting(false);
            }
          }}
        >
          {({
            errors,
            handleBlur,
            handleChange,
            handleSubmit,
            setFieldValue,
            isSubmitting,
            touched,
            values,
          }) => (
            <form noValidate onSubmit={handleSubmit}>
              <Box className="d-flex flex-column gap-4 py-3">
                <FormControl
                  fullWidth
                  error={Boolean(touched.title && errors.title)}
                >
                  <InputLabel htmlFor="outlined-adornment-title">
                    Tên
                  </InputLabel>
                  <OutlinedInput
                    id="outlined-adornment-title"
                    type="text"
                    label="Tên"
                    value={values.title}
                    name="title"
                    onBlur={handleBlur}
                    onChange={handleChange}
                  />
                  {touched.title && errors.title && (
                    <FormHelperText
                      error
                      id="standard-weight-helper-text-title"
                    >
                      {errors.title}
                    </FormHelperText>
                  )}
                </FormControl>
                {dataOptions &&
                  Object.keys(dataOptions).map((key, index) => {
                    let key_name = "";

                    switch (key) {
                      case "subjects":
                        key_name = "subject_id";
                        break;
                      case "grades":
                        key_name = "grade_id";
                        break;
                      default:
                        key_name = key;
                    }

                    return (
                      <FormControl
                        key={key}
                        fullWidth
                        error={Boolean(touched[key_name] && errors[key_name])}
                      >
                        <InputLabel htmlFor={`select-${key_name}`}>
                          {t(key)}
                        </InputLabel>
                        <Select
                          id={`select-${key_name}`}
                          value={values[key_name]}
                          name={key_name}
                          onChange={handleChange}
                          label={t(key)}
                        >
                          {dataOptions[key].map((option) => (
                            <MenuItem
                              key={`${key}-${option.value}`}
                              value={option.value}
                            >
                              {option.label}
                            </MenuItem>
                          ))}
                        </Select>
                        {touched[key_name] && errors[key_name] && (
                          <FormHelperText error>
                            {errors[key_name]}
                          </FormHelperText>
                        )}
                      </FormControl>
                    );
                  })
                }
                <TagItemPicker
                  options={colorPickerOptions}
                  defaultOption={
                    {
                      value: values?.color || '#e91d63',
                      label: values?.color || '#e91d63',
                    }
                  }
                  renderDefaultItem={(option) => (
                    <>
                      <Box component="span" sx={{ bgcolor: option.value, width: 38, height: 38, borderRadius: '50%' }} />
                      <span style={{ color: option.value }} className="ms-2 fs-12">Mã màu lớp</span>
                    </>
                  )}
                  renderItem={(option) => <Box component="span" sx={{ bgcolor: option.value, width: 20, height: 20, borderRadius: '50%' }} />}
                  onChange={(value) => setFieldValue("color", value)}
                />
              </Box>
              <Box className="text-end" sx={{ mt: 2 }}>
                <Button
                  variant="outlined"
                  type="button"
                  onClick={onClose}
                  color="inherit"
                  size="small"
                  sx={{ mr: 2 }}
                >
                  Hủy
                </Button>
                <Button
                  disableElevation
                  disabled={isSubmitting}
                  size="small"
                  type="submit"
                  variant="contained"
                  color="secondary"
                  startIcon={isSubmitting ? <CircularProgress size={18} color="inherit" /> : null}
                >
                  Lưu
                </Button>
              </Box>
            </form>
          )}
        </Formik>
      </DialogContent>
    </Dialog>
  );
};

export default DialogClassroomForm;
