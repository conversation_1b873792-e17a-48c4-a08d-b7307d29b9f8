'use client';

import { useState, useEffect, useCallback, useMemo, memo } from "react";
import Link from "next/link";
import toast from "react-hot-toast";

import { styled } from "@mui/material/styles";
import Paper from "@mui/material/Paper";
import Card from '@mui/material/Card';
import CardHeader from '@mui/material/CardHeader';
import CardActionArea from "@mui/material/CardActionArea";
import CardActions from "@mui/material/CardActions";
import CardContent from '@mui/material/CardContent';
import Typography from '@mui/material/Typography';
import InputBase from "@mui/material/InputBase";
import IconButton from '@mui/material/IconButton';
import Avatar from '@mui/material/Avatar';
import Box from '@mui/material/Box';
import Skeleton from "@mui/material/Skeleton";
import Accordion from '@mui/material/Accordion';
import AccordionSummary from '@mui/material/AccordionSummary';
import AccordionDetails from '@mui/material/AccordionDetails';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import ListItemText from '@mui/material/ListItemText';
import ListItemAvatar from '@mui/material/ListItemAvatar';
import Chip from '@mui/material/Chip';
import Container from '@mui/material/Container';
import Button from "@mui/material/Button";

import { pink, purple } from '@mui/material/colors';
import SearchIcon from "@mui/icons-material/Search";
import MoreVertIcon from '@mui/icons-material/MoreVert';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import AssignmentIcon from '@mui/icons-material/Assignment';

import Dropdown from "@/components/Dropdown";
import JoinClassPopup from '@/components/classrooms/join/JoinClassPopup'
import DoneSvgIcon from "../DoneSvgIcon";
import NoDataOverlay from "../NoDataOverlay";

import debounce from "lodash/debounce";
import dayjs from "dayjs";
import "dayjs/locale/vi";

import { classroomApiSlice } from "@/slices/features/classroomApiSlice";
import { leaveClassroom } from "@/actions/classroomAction";
import { useSelector } from "react-redux";
import { useConfirm } from "@/contexts/ConfirmContext";

const ColorButton = styled(Button)(({ theme }) => ({
  color: theme.palette.getContrastText(purple[500]),
  backgroundColor: purple[700],
  borderRadius: '8px',
  padding: '6px 14px',
  "&:hover": {
    backgroundColor: purple[500],
  },
}));

const GroupInputSearch = styled(Paper)(() => {
  return {
    padding: "2px 4px",
    display: "flex",
    alignItems: "center",
    border: "1px solid #e5e5e5",
    width: "100%",
    height: "39px",
    maxWidth: "300px",
    borderRadius: "8px",
  };
});

const MyClassroomIndex = () => {
  console.log("MyClassroomIndex");
  const [keyword, setKeyword] = useState("");
  const [showPopup, setShowPopup] = useState(false)
  const user = useSelector((state) => state.auth.user)
  const confirmLeave = useConfirm();

  const url = new URL(window.location.href);
  const classCode = url.searchParams.get("code") || '';

  const { data: myClassrooms, isLoading } = classroomApiSlice.useFetchMyClassroomsQuery({keyword});

  useEffect(() => {
    if (classCode) setShowPopup(true)
  }, [classCode]);

  const handleClassCodeSubmit = () => {
    setShowPopup(false)

    toast.success("Tham gia lớp học thành công");

    setTimeout(() => {
      window.location.href = '/dashboard/my-classroom'
    }, 500)
  }

  const debouncedSearch = useCallback(
    debounce((value) => {
      setKeyword(value);
    }, 700),
    []
  );

  const handleSearch = useCallback((event) => {
    debouncedSearch(event.target.value);
  }, []);

  const handleLeaveClassroom = async (classroomId) => {
    const confirmed = await confirmLeave(
      "Bạn có chắc muốn rời lớp học này?",
      "Sau khi rời, bạn sẽ không thể xem được thông tin và bài tập của lớp!"
    );

    if (!confirmed) return

    try {
      const res = await leaveClassroom(classroomId, {user_id: user?.id});

      if (!res.status) throw new Error('Không thể rời lớp học')

      toast.success('Đã rời lớp học')
      setTimeout(() => window.location.reload(), 500)
    } catch (err) {
      toast.error(err.message || 'Đã có lỗi xảy ra')
    }
  }

  return (
    <Container maxWidth="cxl">
      <div className="d-flex flex-wrap align-items-center justify-content-between gap-2">
        <div className="d-flex align-items-center gap-3">
          <h4 className="fs-16 fw-light flex-shrink-0 mb-0">
            <i className="bi bi-calendar-check"></i> Lớp học của tôi:
          </h4>
          <GroupInputSearch>
            <IconButton
              size="small"
              color="inherit"
              aria-label="search"
              sx={{ minWidth: "20px" }}
              disabled
            >
              <SearchIcon />
            </IconButton>
            <InputBase
              placeholder="Tìm kiếm lớp học"
              inputProps={{ "aria-label": "Tìm kiếm lớp học" }}
              onChange={handleSearch}
            />
          </GroupInputSearch>
        </div>
        <ColorButton onClick={() => setShowPopup(true)}>
          <i className="bi bi-plus-circle me-2"></i>
          Tham gia lớp học mới
        </ColorButton>
      </div>
      {showPopup && (
        <JoinClassPopup
          classCode={classCode || ''}
          onSubmit={handleClassCodeSubmit}
          onClose={() => setShowPopup(false)}
        />
      )}
      <div className="mt-5">
        {isLoading ? (
          <div className="row">
            {Array.from({ length: 3 }, (_, index) => (
              <div className="col-12 col-sm-6 col-md-4 my-3" key={index}>
                <Skeleton
                  variant="rounded"
                  height={260}
                  sx={{ borderRadius: "4px" }}
                />
              </div>
            ))}
          </div>
        ) : (
          myClassrooms?.length ? (
            <div className="row">
              {myClassrooms.map((classroom) => {
                // 0: chưa có quiz nào được giao, 1: có quiz chưa làm, 2: Đã hoàn thành hết
                const type = classroom.classroom_quiz_count ? (classroom.classroom_quiz.length ? 1 : 2) : 0;

                return (
                  <div className="col-12 col-lg-6 col-xl-4 mb-3" key={classroom.id}>
                    <Card sx={{ height: '100%', position: 'relative' }}>
                      <CardActionArea
                        component={Link}
                        href={`/dashboard/my-classroom/${classroom.id}/details`}
                      >
                        <CardHeader
                          sx={{ borderTop: `5px solid ${classroom?.color || "#eee"}` }}
                          className="py-2 ps-3 pe-5 bg-light"
                          avatar={
                            <Avatar>
                              {classroom?.author?.name?.charAt(0)}
                            </Avatar>
                          }
                          title={classroom.title}
                          subheader={classroom?.author?.name || ''}
                        />
                      </CardActionArea>
                      <CardActions sx={{ position: 'absolute', top: 0, right: 0, padding: '10px 3px' }}>
                        <Dropdown
                          renderToggle={({ onClick }) => (
                            <IconButton onClick={onClick}>
                              <MoreVertIcon />
                            </IconButton>
                          )}
                          placement="bottom-end"
                          renderMenu={() => (
                            <div className="dropdown-menu">
                              <button
                                className="dropdown-item fs-14 text-danger"
                                onClick={() => handleLeaveClassroom(classroom.id)}
                              >
                                Rời lớp
                              </button>
                            </div>
                          )}
                        />
                      </CardActions>
                      <CardContent className="p-0">
                        {type == 1 ? (
                          <Accordion defaultExpanded>
                            <AccordionSummary
                              expandIcon={<ExpandMoreIcon />}
                              aria-controls={`panel${classroom.id}-content`}
                              id={`panel${classroom.id}-header`}
                              className="fs-15"
                            >
                              <span className="fw-bold">
                                <i className="bi bi-calendar-check me-2"></i>
                                Bài tập, Đề thi chưa làm ({classroom.classroom_quiz?.length || 0} đề thi)
                              </span>
                            </AccordionSummary>
                            <AccordionDetails>
                              { classroom.classroom_quiz.length > 0 && (
                                <Box sx={{ width: '100%' }} className="py-0">
                                  {classroom.classroom_quiz.slice(0, 3).map((item, index) => {
                                    const quiz = item.quiz;

                                    return (
                                      <Link key={index} href={`/join/quiz/${quiz.id}/start?assignment=${item.id}`}>
                                        <Box className="mb-3 px-3 py-3 rounded-1" sx={{ bgcolor: '#ff980005', border: '1px solid #cfcfcf' }}>
                                          <h5 className="fs-16 mb-1 text-body fw-normal">
                                            <i className="bi bi-calendar2-check text-primary me-1"></i> {quiz.subtitle || quiz.title}
                                          </h5>
                                          <ul className="ps-3 mb-0 fs-13 text-muted mt-2">
                                            {item.start_time && <li className="mb-1">Bắt đầu: <span>{dayjs(item.start_time).format('DD/MM/YYYY HH:mm')}</span></li>}
                                            {item.end_time && <li className="mb-1">Hết hạn: <span className="text-danger">{dayjs(item.end_time).format('DD/MM/YYYY HH:mm')}</span></li>}
                                            <li>
                                              <strong className="text-danger">Chưa làm</strong>
                                            </li>
                                          </ul>
                                        </Box>
                                      </Link>
                                    )
                                  })}
                                </Box>
                              ) }
                              <div className="text-center">
                                <Link href={`/dashboard/my-classroom/${classroom.id}/details`} className="btn btn-sm btn-link">
                                  <i className="bi bi-plus me-1"></i>
                                  Xem thêm
                                </Link>
                              </div>
                            </AccordionDetails>
                          </Accordion>
                        ) : (type == 2 ? (
                          <Box className="text-center mb-3">
                            <DoneSvgIcon />
                            <p className="mb-1">
                              Bạn đã hoàn thành tất cả các bài tâp, đề thi.
                            </p>
                            <a className="d-block">Xem chi tiết <i className="bi bi-arrow-right-short ms-1"></i></a>
                          </Box>
                        ) : (
                          <Box className="text-center mb-3">
                            <NoDataOverlay message="Chưa có bài tập, đề thi nào!" />
                          </Box>
                        ))}
                      </CardContent>
                    </Card>
                  </div>
                )
              })}
            </div>
          ) : (
            <NoDataOverlay message="Bạn chưa tham gia lớp học nào!" />
          )
        )}
      </div>
    </Container>
  );
};

export default MyClassroomIndex;
