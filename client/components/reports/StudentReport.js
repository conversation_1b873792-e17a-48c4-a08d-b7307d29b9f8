"use client";

import React, { useState, useMemo, useEffect, memo, forwardRef, useCallback } from 'react';
import { useRouter } from 'next/navigation';

import { styled, useTheme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import IconButton from '@mui/material/IconButton';
import Divider from '@mui/material/Divider';
import Avatar from '@mui/material/Avatar';
import CircularProgress from '@mui/material/CircularProgress';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell, { tableCellClasses } from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import TableSortLabel from '@mui/material/TableSortLabel';
import Paper from '@mui/material/Paper';
import Chip from '@mui/material/Chip';
import FormControl from '@mui/material/FormControl';
import Select from '@mui/material/Select';
import MenuItem from '@mui/material/MenuItem';
import InputBase from '@mui/material/InputBase';
import Button from '@mui/material/Button';
import Container from '@mui/material/Container';
import { visuallyHidden } from '@mui/utils';

import WestIcon from '@mui/icons-material/West';
import PersonIcon from '@mui/icons-material/Person';
import TimelineIcon from '@mui/icons-material/Timeline';
import AssignmentIcon from '@mui/icons-material/Assignment';
import ReportProblemIcon from '@mui/icons-material/ReportProblem';
import SearchIcon from '@mui/icons-material/Search';

import { LineChart } from '@mui/x-charts/LineChart';
import debounce from 'lodash/debounce';

import NoDataOverlay from '@/components/NoDataOverlay';
import PieChartProgress from '@/components/PieChartProgress';
import { getColorByPercentage } from "@/utils/helpers";

import { useFetchStudentReportsQuery } from '@/slices/features/reportApiSlice';

const StyledTableCell = styled(TableCell)(({ theme }) => ({
  [`&.${tableCellClasses.head}`]: {
    backgroundColor: theme.palette.common.black,
    borderColor: theme.palette.common.black,
    borderBottom: 'none',
    color: theme.palette.grey[400],
    minWidth: '130px',
    padding: '12px 16px',
    '.Mui-active': {
      color: theme.palette.common.white,
    },
  },
  [`&.${tableCellClasses.body}`]: {
    fontSize: 14,
    padding: '12px 16px',
  },
}));

const GroupInputSearch = styled(Paper)(() => ({
  padding: "2px 4px",
  display: "flex",
  alignItems: "center",
  border: "1px solid #e5e5e5",
  width: "100%",
  height: "39px",
  maxWidth: "300px",
  borderRadius: "8px",
}));

const headCells = [
  {
    id: 'name',
    align: 'left',
    disablePadding: false,
    label: 'Tên bài tập',
  },
  {
    id: 'accuracy',
    align: 'center',
    disablePadding: false,
    label: 'Tỉ lệ đúng',
  },
  {
    id: 'attempts',
    align: 'center',
    disablePadding: false,
    label: 'Số lần làm',
  },
  {
    id: 'actions',
    align: 'center',
    disablePadding: false,
    label: '',
    disableSorting: true,
  },
];

const StatCard = ({ icon, value, label, color, isPieChart = false }) => (
  <Card variant="outlined" className="h-100">
    <CardContent className="p-2 text-center d-flex flex-column justify-content-center gap-2 h-100 bg-light">
      <Box className="d-flex align-items-center gap-2 justify-content-center">
        {icon && icon}
        <p className="mb-0">
          {label}
        </p>
      </Box>
      <div>
        {isPieChart ? (
          <PieChartProgress value={value} color={color} size={35} />
        ) : (
          <Typography variant="h3" color={color} className="mb-1">
            {value}
          </Typography>
        )}
      </div>
    </CardContent>
  </Card>
);

const AccuracyLineChart = ({ datas = [], height = 300, limit = 15, onLimitChange }) => {
  const limitedDatas = useMemo(() => {
    return [...datas]
      .sort((a, b) => new Date(b.start_time || 0) - new Date(a.start_time || 0))
      .slice(0, limit);
  }, [datas, limit]);
  const [dataType, setDataType] = useState('last');

  const { seriesData, xAxisLabels } = useMemo(() => {
    const accuracy = [];
    const labels = [];

    limitedDatas.forEach((data, index) => {
      const percentageField = dataType === 'first' ? 'first_correct_percentage' : 'last_correct_percentage';
      const roundedValue = Math.round(parseFloat(data[percentageField] ?? 0));

      accuracy.push(roundedValue);
      labels.push(`${data.title} (#${data.id})`);
    });

    return {
      seriesData: accuracy,
      xAxisLabels: labels,
    };
  }, [limitedDatas, dataType]);

  const handleDataTypeChange = (event) => {
    setDataType(event.target.value);
  };

  return (
    <div className="bg-light">
      <div className="d-flex gap-2 mb-3 p-2 align-items-center flex-wrap">
        <FormControl size="small" sx={{ minWidth: 150 }}>
          <Select
            value={limit}
            onChange={(e) => onLimitChange(e.target.value)}
            displayEmpty
            variant="outlined"
          >
            <MenuItem value={15}>
              <div className="d-flex align-items-center justify-content-between w-100">
                <span>15 bài gần nhất</span>
              </div>
            </MenuItem>
            <MenuItem value={30}>
              <div className="d-flex align-items-center justify-content-between w-100">
                <span>30 bài gần nhất</span>
              </div>
            </MenuItem>
          </Select>
        </FormControl>
        <FormControl size="small" sx={{ minWidth: 180 }}>
          <Select
            value={dataType}
            onChange={handleDataTypeChange}
            displayEmpty
            variant="outlined"
          >
            <MenuItem value="last">
              <div className="d-flex align-items-center justify-content-between w-100">
                <span>Lần làm bài cuối cùng</span>
              </div>
            </MenuItem>
            <MenuItem value="first">
              <div className="d-flex align-items-center justify-content-between w-100">
                <span>Lần làm bài đầu tiên</span>
              </div>
            </MenuItem>
          </Select>
        </FormControl>
      </div>
      <LineChart
        series={[{
          data: seriesData,
          color: '#4caf50',
          label: 'Tỷ lệ đúng',
          valueFormatter: (value) => `${value}%`
        }]}
        xAxis={[{
          data: xAxisLabels,
          scaleType: 'band',
          tickLabelStyle: {
            angle: 45,
            textAnchor: 'start',
            fontSize: 12
          }
        }]}
        yAxis={[{
          min: -5,
          max: 100,
          tickValues: [0, 25, 50, 75, 100],
          valueFormatter: v => `${v}%`,
        }]}
        height={height}
        margin={{ top: 10, bottom: 70, left: 40, right: 10 }}
        slotProps={{
          legend: { hidden: true },
        }}
      />
    </div>
  );
};

const StudentReport = ({ studentId, classroomId }) => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const [sortBy, setSortBy] = useState('starttime');
  const [sortDirection, setSortDirection] = useState('asc');
  const [searchQuery, setSearchQuery] = useState('');
  const [chartLimit, setChartLimit] = useState(15);

  const { data, isLoading } = useFetchStudentReportsQuery({ classroomId, studentId });

  const stats = useMemo(() => {
    const assignments = data?.assignments || [];
    if (assignments.length === 0) return null;

    let completed = 0;
    let total = 0;
    let correctSum = 0;

    for (const a of assignments) {
      if (a.quiz_results_count > 0) {
        completed += 1;
        total += a.quiz_results_count;
      }
      correctSum += parseFloat(a.correct_percentage ?? 0);
    }

    return {
      totalAssignments: assignments.length,
      totalCount: total,
      completedCount: completed,
      correctPercentage: (correctSum / assignments.length).toFixed(),
    };
  }, [data]);

  const debouncedSearch = useCallback(
    debounce((value) => {
      setSearchQuery(value);
    }, 700),
    []
  );

  const handleSearch = (event) => {
    debouncedSearch(event.target.value);
  };

  const handleChartLimitChange = useCallback((newLimit) => {
    setChartLimit(newLimit);
  }, []);

  const filteredAssignments = useMemo(() => {
    const assignments = data?.assignments || [];

    const lowerQuery = searchQuery.toLowerCase();
    const filtered = searchQuery
      ? assignments.filter(assignment =>
          assignment.title.toLowerCase().includes(searchQuery.toLowerCase()))
      : [...assignments];

    return filtered.sort((a, b) => {
      let comparison = 0;

      switch (sortBy) {
        case 'name':
          comparison = a.title.localeCompare(b.title);
          break;
        case 'accuracy':
          comparison = a.last_correct_percentage - b.last_correct_percentage;
          break;
        case 'attempts':
          comparison = a.quiz_results_count - b.quiz_results_count;
          break;
        case 'starttime':
        default: {
          const dateA = new Date(a.start_time || 0);
          const dateB = new Date(b.start_time || 0);
          comparison = dateA - dateB;
          break;
        }
      }

      return sortDirection === 'asc' ? comparison : -comparison;
    });
  }, [data, searchQuery, sortBy, sortDirection]);

  const handleSortChange = useCallback((property) => {
    if (sortBy === property) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(property);
      setSortDirection('desc');
    }
  }, [sortBy, sortDirection]);

  const handleViewDetails = useCallback((assignment) => {
    const url = `/dashboard/reports/${assignment.id}?student=${studentId}`;
    window.open(url, '_blank');
  }, [studentId]);

  const AssignmentRow = memo(function AssignmentRow({ assignment }) {
    return (
      <TableRow
        hover
        sx={{
          cursor: 'pointer',
          '&:last-child td, &:last-child th': { border: 0 },
          '&:hover': { backgroundColor: 'rgba(0, 0, 0, 0.04)' },
          height: '64px',
        }}
      >
        <TableCell onClick={() => handleViewDetails(assignment)}>
          <div className="text-truncate" style={{ maxWidth: '300px' }}>
            <h6 className="fs-15 mb-0">
              {assignment.title}
            </h6>
          </div>
        </TableCell>
        <TableCell align="center" onClick={() => handleViewDetails(assignment)}>
          {assignment.last_correct_percentage > 0 ? (
            <PieChartProgress
              value={assignment.last_correct_percentage}
              color={getColorByPercentage(assignment.last_correct_percentage)}
              size={40}
              showLabel={true}
            />
          ) : (
            <Chip
              label="Chưa làm"
              icon={<ReportProblemIcon fontSize="small" />}
              color="secondary"
              size="small"
              title="Chưa làm"
            />
          )}
        </TableCell>
        <TableCell align="center" onClick={() => handleViewDetails(assignment)}>
          <Typography variant="body2">
            {assignment.quiz_results_count || 0}
          </Typography>
        </TableCell>
        <TableCell align="center">
          <Button
            size="small"
            variant="outlined"
            onClick={(e) => {
              e.stopPropagation();
              handleViewDetails(assignment);
            }}
          >Xem chi tiết</Button>
        </TableCell>
      </TableRow>
    );
  });

  if (isLoading) {
    return (
      <div className="text-center">
        <CircularProgress />
      </div>
    );
  }

  return (
    <Container maxWidth="xl">
      <div className="d-flex flex-wrap gap-2 align-items-start">
        <IconButton className="me-2" onClick={() => router.back()}>
          <WestIcon />
        </IconButton>
        <div>
          <div className="d-flex align-items-center gap-2">
            <span className="fs-16 fw-normal">Chi tiết học sinh:</span>
            <Avatar
              src={data?.student?.avatar}
              alt={data?.student?.name}
              sx={{
                width: 30,
                height: 30
              }}
            >
              <PersonIcon fontSize="small" />
            </Avatar>
            <h1 className="h5 mb-0">{ data?.student?.name || '__' }</h1>
          </div>
          {data?.classroom && (
            <Typography className="mt-2" variant="body2" color="text.secondary">
              { data.classroom.title }
            </Typography>
          )}
        </div>
      </div>
      <Card variant="outlined" className="mt-4">
        <CardContent>
          <p className="text-danger">
            <i className="bi bi-graph-up-arrow me-2"></i>
            Hiệu suất làm bài
          </p>
          <Divider />
          {stats ? (
            <>
              <div className="row">
                <div className="col-md-6 col-lg-3">
                  <StatCard
                    value={`${stats.completedCount || 0}/${stats.totalAssignments || 0}`}
                    color="primary.main"
                    label="Hoàn thành"
                  />
                </div>
                <div className="col-md-6 col-lg-3">
                  <StatCard
                    isPieChart={true}
                    value={stats.correctPercentage || 0}
                    color={getColorByPercentage(stats.correctPercentage || 0)}
                    label="Tỉ lệ đúng trung bình"
                  />
                </div>
                <div className="col-md-6 col-lg-3">
                  <StatCard
                    value={stats.totalCount || 0}
                    color="error.main"
                    label="Tổng số lần làm"
                  />
                </div>
              </div>
              <Divider className="my-3" />
              <AccuracyLineChart
                datas={data?.assignments || []}
                height={300}
                limit={chartLimit}
                onLimitChange={handleChartLimitChange}
              />
            </>
          ) : (
            <NoDataOverlay message="Học sinh chưa có kết quả làm bài" />
          )}
        </CardContent>
      </Card>
      <Card variant="outlined" className="mt-4">
        <CardContent>
          <p className="text-primary">
            <i className="bi bi-calendar-check me-2"></i>
            Danh sách bài tập
          </p>
          <div className="bg-light p-3 mb-3">
            <div className="row align-items-center">
              <div className="col-md-6">
                <GroupInputSearch className="mb-3 mb-md-0">
                  <IconButton
                    size="small"
                    color="inherit"
                    aria-label="search"
                    sx={{ minWidth: "20px" }}
                    disabled
                  >
                    <SearchIcon />
                  </IconButton>
                  <InputBase
                    placeholder="Tìm kiếm bài tập"
                    inputProps={{ "aria-label": "Tìm kiếm bài tập" }}
                    onChange={handleSearch}
                  />
                </GroupInputSearch>
              </div>
            </div>
          </div>
          <TableContainer
            component={Paper}
            variant="outlined"
            elevation={0}
            sx={{
              border: 'none',
              borderRadius: 0,
              overflowX: 'auto',
              maxWidth: '100%',
              '& .MuiTable-root': {
                borderCollapse: 'collapse',
                tableLayout: isMobile ? 'auto' : 'fixed'
              },
              '& .MuiTableCell-root': {
                borderBottom: '1px solid rgba(224, 224, 224, 1)',
                whiteSpace: isMobile ? 'nowrap' : 'normal'
              },
            }}
          >
            <Table
              stickyHeader
              aria-label="assignment table"
              size={isMobile ? "small" : "medium"}
            >
              <TableHead>
                <TableRow>
                  {headCells.map((headCell) => (
                    <StyledTableCell
                      key={headCell.id}
                      align={headCell.align}
                      padding={headCell.disablePadding ? 'none' : 'normal'}
                      sortDirection={sortBy === headCell.id ? sortDirection : false}
                    >
                      <TableSortLabel
                        active={sortBy === headCell.id}
                        direction={sortBy === headCell.id ? sortDirection : 'asc'}
                        onClick={() => handleSortChange(headCell.id)}
                      >
                        {headCell.label}
                        {sortBy === headCell.id ? (
                          <Box component="span" sx={visuallyHidden}>
                            {sortDirection === 'desc' ? 'sorted descending' : 'sorted ascending'}
                          </Box>
                        ) : null}
                      </TableSortLabel>
                    </StyledTableCell>
                  ))}
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredAssignments.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={4} align="center" className="py-3">
                      <Typography variant="body1" color="text.secondary">
                        Không có bài tập nào
                      </Typography>
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredAssignments.map((assignment) => (
                    <AssignmentRow key={assignment.id} assignment={assignment} />
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>
    </Container>
  );
};

export default StudentReport;
