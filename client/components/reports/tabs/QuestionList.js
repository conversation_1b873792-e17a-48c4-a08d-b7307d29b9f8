import React, { useMemo, useState } from 'react';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import FormControl from '@mui/material/FormControl';
import Select from '@mui/material/Select';
import MenuItem from '@mui/material/MenuItem';
import InputLabel from '@mui/material/InputLabel';
import IconButton from '@mui/material/IconButton';

import MoreVertIcon from '@mui/icons-material/MoreVert';

import { useTranslations } from "next-intl";

import PieChartProgress from '@/components/PieChartProgress';
import NoDataOverlay from '@/components/NoDataOverlay';
import QuestionCard from "../../questions/QuestionCard";
import QuestionDetailsModal from '../modals/QuestionDetailsModal';
import { getColorByPercentage } from '@/utils/helpers';

const QuestionList = ({ questions = [], groupResults = {}, totalStudents = 0, quizResults = [] }) => {
  const t = useTranslations("Common");

  const [display, setDisplay] = useState('first');
  const [selectedQuestion, setSelectedQuestion] = useState(null);
  const [modalOpen, setModalOpen] = useState(false);

  const questionsDataLog = useMemo(() => {
    const result = {};

    Object.values(groupResults).forEach(studentResults => {
      if (studentResults.length === 0) return;

      studentResults.sort((a, b) => a.id - b.id);

      let studentResult =
        display === 'first' ? studentResults[studentResults.length - 1] : studentResults[0];

      if (!studentResult.data_log) return;

      let dataLog = studentResult.data_log;

      if (typeof dataLog === 'string') {
        try {
          dataLog = JSON.parse(studentResult.data_log);
        } catch (e) {
          console.warn("Lỗi JSON parse:", e);
        }
      }

      for (const [questionId, userSaveData] of Object.entries(dataLog)) {
        if (userSaveData?.qtype === 'QUIZX') {
          const { answer: answers, isCorrect } = userSaveData;

          if (!Array.isArray(answers)) continue;

          if (!result[questionId]) {
            result[questionId] = {
              answerTotal: 0,
              correctCount: 0,
              wrongCount: 0,
              optionCount: {}
            };
          }

          const questionResult = result[questionId];

          questionResult.answerTotal++;

          isCorrect ? questionResult.correctCount++ : questionResult.wrongCount++;

          answers.forEach(ans => {
            questionResult.optionCount[ans] = (questionResult.optionCount[ans] || 0) + 1;
          });
        }
      }
    });

    return result;
  }, [questions, groupResults, display]);


  const handleOpenModal = (question) => {
    setSelectedQuestion(question);
    setModalOpen(true);
  };

  const handleCloseModal = () => {
    setModalOpen(false);
  };

  if (!questions.length) {
    return <NoDataOverlay message="Không có câu hỏi nào trong bài kiểm tra này." />;
  }

  return (
    <>
      <Box className="d-flex flex-wrap align-items-center justify-content-between mb-3">
        <h5 className="fs-16 mb-3"><i className="bi bi-patch-question"></i> Danh sách câu hỏi:</h5>
        <FormControl size="small">
          <InputLabel id="attempt-filter-label">Hiển thị</InputLabel>
          <Select
            labelId="attempt-filter-label"
            value={display}
            label="Hiển thị"
            onChange={(e) => setDisplay(e.target.value)}
            sx={{
              '.MuiInputBase-input': { fontWeight: 400, color: '#444' }
            }}
          >
            <MenuItem value="first">Lần làm đầu tiên</MenuItem>
            <MenuItem value="last">Lần làm gần nhất</MenuItem>
          </Select>
        </FormControl>
      </Box>

      <div className="bg-light p-lg-3">
        { questions.map((question, index) => {
          const qDataLog = questionsDataLog[question.id] || {answerTotal: 0, correctCount: 0, wrongCount: 0};
          const rate = qDataLog.answerTotal > 0 ? Math.round((qDataLog.correctCount / qDataLog.answerTotal) * 100) : 0;

          let notDoingCount = 0;
          let correctRate = 0;
          let wrongRate = 0;
          let notDoingRate = 0;

          if (totalStudents > 0) {
            notDoingCount = totalStudents - qDataLog.answerTotal;
            correctRate = Math.round((qDataLog.correctCount / totalStudents) * 100);
            wrongRate = Math.round((qDataLog.wrongCount / totalStudents) * 100);
            notDoingRate = Math.round((notDoingCount / totalStudents) * 100);
          }

          return (
            <Card
              variant="outlined"
              key={question.id}
              className="p-3 pb-0 mb-4"
              sx={{
                cursor: 'pointer',
                transition: 'all 0.2s ease',
                '&:hover': { boxShadow: '0 4px 8px rgba(0,0,0,0.1)' }
              }}
              onClick={() => handleOpenModal(question, )}
            >
              <div className="d-flex flex-wrap align-items-center justify-content-between mb-2">
                <span className="fw-normal my-1">
                  <i className="bi bi-check2 me-2"></i>
                  {t(question.type)}
                </span>
                <div className="d-flex align-items-center gap-2">
                  <p className="mb-0">Tỉ lệ đúng:</p>
                  <PieChartProgress
                    value={rate}
                    color={getColorByPercentage(rate)}
                    size={45}
                  />
                </div>
                <IconButton
                  size="small"
                  onClick={(e) => {
                    e.stopPropagation(); // Ngăn không cho sự kiện click lan đến Card
                  }}
                >
                  <MoreVertIcon fontSize="small" />
                </IconButton>
              </div>
              <CardContent className="p-0 pt-3 border-top">
                <div className="row align-items-center">
                  <div className="col-md-8 col-lg-9">
                    <QuestionCard question={question} questionLogs={qDataLog?.optionCount ? qDataLog : {}} />
                  </div>
                  <div className="col-md-4 col-lg-3">
                    <div>
                      <div className="q-progress q-progress-success mb-3">
                        <div className="q-progress-label2 d-flex justify-content-between align-items-center fs-12 gap-1">
                          <span>Làm đúng:</span>
                          <span><strong>{qDataLog.correctCount}</strong> học sinh</span>
                        </div>
                        <div className="q-progress-main">
                          <div
                            className="q-progress-bar q-progress-active"
                            data-percent={correctRate}
                            style={{
                              width: `${correctRate}%`,
                              transition: "none 0s ease 0s",
                            }}
                          >
                            <span className="q-progress-label">
                              {correctRate}%
                            </span>
                          </div>
                        </div>
                      </div>
                      <div className="q-progress q-progress-danger mb-3">
                        <div className="q-progress-label2 d-flex justify-content-between align-items-center fs-12 gap-1">
                          <span>Làm sai:</span>
                          <span><strong>{qDataLog.wrongCount}</strong> học sinh</span>
                        </div>
                        <div className="q-progress-main">
                          <div
                            className="q-progress-bar q-progress-active"
                            data-percent={wrongRate}
                            style={{
                              width: `${wrongRate}%`,
                              transition: "none 0s ease 0s",
                            }}
                          >
                            <span className="q-progress-label">
                              {wrongRate}%
                            </span>
                          </div>
                        </div>
                      </div>
                      <div className="q-progress q-progress-default">
                        <div className="q-progress-label2 d-flex justify-content-between align-items-center fs-12 gap-1">
                          <span>Chưa làm:</span>
                          <span><strong>{ notDoingCount }</strong> học sinh</span>
                        </div>
                        <div className="q-progress-main">
                          <div
                            className="q-progress-bar q-progress-active"
                            data-percent={notDoingRate}
                            style={{
                              width: `${notDoingRate}%`,
                              transition: "none 0s ease 0s",
                            }}
                          >
                            <span className="q-progress-label">
                              {notDoingRate}%
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      { selectedQuestion && (
        <QuestionDetailsModal
          open={modalOpen}
          onClose={handleCloseModal}
          question={selectedQuestion}
          quizResults={quizResults}
        />
      ) }
    </>
  );
};

export default QuestionList;
