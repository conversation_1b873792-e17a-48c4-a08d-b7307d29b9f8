import React, { useState, useEffect, useRef, useCallback, useMemo, memo, forwardRef } from 'react';
import { useRouter, useSearchParams, usePathname } from "next/navigation";

import { useTheme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import Box from '@mui/material/Box';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell, { tableCellClasses } from '@mui/material/TableCell';
import TableSortLabel from '@mui/material/TableSortLabel';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Paper from '@mui/material/Paper';
import Avatar from '@mui/material/Avatar';
import Typography from '@mui/material/Typography';
import IconButton from '@mui/material/IconButton';
import CircularProgress from '@mui/material/CircularProgress';
import InputAdornment from '@mui/material/InputAdornment';
import Select from '@mui/material/Select';
import MenuItem from '@mui/material/MenuItem';
import FormControl from '@mui/material/FormControl';
import InputLabel from '@mui/material/InputLabel';
import InputBase from '@mui/material/InputBase';
import Stack from '@mui/material/Stack';
import { styled } from '@mui/material/styles';
import Chip from '@mui/material/Chip';
import { visuallyHidden } from '@mui/utils';

import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CancelIcon from '@mui/icons-material/Cancel';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import ArrowUpwardIcon from '@mui/icons-material/ArrowUpward';
import ArrowDownwardIcon from '@mui/icons-material/ArrowDownward';
import SearchIcon from '@mui/icons-material/Search';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import ReportProblemIcon from '@mui/icons-material/ReportProblem';

import debounce from 'lodash/debounce';
import groupBy from "lodash/groupBy";

import PieChartProgress from '@/components/PieChartProgress';
import { getColorByPercentage } from "@/utils/helpers";
import StudentDetailsModal from '../modals/StudentDetailsModal';

const GroupInputSearch = styled(Paper)(() => ({
  padding: "2px 4px",
  display: "flex",
  alignItems: "center",
  border: "1px solid #e5e5e5",
  width: "100%",
  height: "39px",
  maxWidth: "300px",
  borderRadius: "8px",
}));

const StyledTableCell = styled(TableCell)(({ theme }) => ({
  [`&.${tableCellClasses.head}`]: {
    backgroundColor: theme.palette.common.black,
    borderColor: theme.palette.common.black,
    borderBottom: 'none',
    color: theme.palette.grey[400],
    minWidth: '130px',
    '.Mui-active': {
      color: theme.palette.common.white,
    },
  },
  [`&.${tableCellClasses.body}`]: {
    fontSize: 14,
  },
}));

const headCells = [
  {
    id: 'name',
    align: 'left',
    disablePadding: false,
    label: 'Tên',
  },
  {
    id: 'correct',
    align: 'center',
    disablePadding: false,
    label: 'Kết quả',
  },
  {
    id: 'accuracy',
    align: 'center',
    disablePadding: false,
    label: 'Tỉ lệ đúng',
  },
  {
    id: 'attempts',
    align: 'center',
    disablePadding: false,
    label: 'Số lần làm',
  },
  {
    id: 'other',
    align: 'right',
    disablePadding: false,
    label: '',
  },
];

const StudentList = ({
  students = [],
  quizResults = [],
  groupResults = {},
  questions = []
}) => {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const MAX_VISIBLE_ROWS = isMobile ? 20 : 40;

  const studentId = searchParams.get("student") || null;

  const [searchQuery, setSearchQuery] = useState('');
  const [display, setDisplay] = useState('best');
  const [sortBy, setSortBy] = useState('accuracy');
  const [sortDirection, setSortDirection] = useState('desc');
  const [rowHeight, setRowHeight] = useState(null);
  const [selectedStudent, setSelectedStudent] = useState(null);
  const [openModal, setOpenModal] = useState(false);

  const rowRef = useRef(null);

  useEffect(() => {
    if (rowRef.current && !rowHeight) {
      setRowHeight(rowRef.current.clientHeight);
    }
  }, [rowHeight]);

  useEffect(() => {
    if (!studentId) {
      setSelectedStudent(null);
      setOpenModal(false);
      return;
    }

    const targetStudent = students.find(s => String(s.id) === String(studentId));

    if (targetStudent && selectedStudent?.id !== targetStudent.id) {
      setSelectedStudent(targetStudent);
      setOpenModal(true);
    }
  }, [studentId, students]);

  const debouncedSearch = useCallback(
    debounce((value) => {
      setSearchQuery(value);
    }, 700),
    []
  );

  const handleSearch = useCallback((event) => {
    debouncedSearch(event.target.value);
  }, [debouncedSearch]);

  const studentsData = useMemo(() => {
    const getStudentData = (student, studentResults) => {
      let data = {
        total_correct: 0,
        total_wrong: 0,
        not_doing: 0,
        accuracy: 0,
      };

      if (studentResults.length > 0) {
        if (display === 'best') {
          studentResults.sort((a, b) => a.accuracy - b.accuracy);
          const last = studentResults[studentResults.length - 1];

          data = {
            total_correct: last.total_correct,
            total_wrong: last.total_wrong,
            not_doing: last.not_doing,
            accuracy: last.accuracy,
          };
        } else {
          studentResults.sort((a, b) => a.id - b.id);

          const selected = display === 'last'
            ? studentResults[studentResults.length - 1]
            : studentResults[0];

          data = {
            total_correct: selected.total_correct,
            total_wrong: selected.total_wrong,
            not_doing: selected.not_doing,
            accuracy: selected.accuracy,
          };
        }
      }

      return {
        ...student,
        history_count: studentResults.length,
        history: studentResults,
        ...data,
      };
    };

    if (!students.length && quizResults.length) {
      const groupedResults = groupBy(quizResults, "user_id");

      return Object.entries(groupedResults).map(([userId, studentResults]) => {
        return getStudentData(studentResults[0].user, studentResults);
      });
    }

    if (!students.length) return [];

    return students.map(student => {
      const studentResults = (groupResults[student.id] || []).slice();
      return getStudentData(student, studentResults);
    });
  }, [students, groupResults, quizResults, display]);


  const filteredStudents = useMemo(() => {
    if (!studentsData.length) return [];

    const lowerQuery = searchQuery.toLowerCase();
    const filtered = searchQuery
      ? studentsData.filter(({ name }) => name.toLowerCase().includes(lowerQuery))
      : [...studentsData];

    filtered.sort((a, b) => {
      let comparison = 0;

      switch (sortBy) {
        case 'name':
          comparison = a.name.localeCompare(b.name);
          break;
        case 'correct':
          comparison = a.total_correct - b.total_correct;
          break;
        case 'wrong':
          comparison = a.total_wrong - b.total_wrong;
          break;
        case 'unanswered':
          comparison = a.not_doing - b.not_doing;
          break;
        case 'accuracy':
          comparison = (a.accuracy || 0) - (b.accuracy || 0);
          break;
        case 'attempts':
          comparison = (a.history_count || 0) - (b.history_count || 0);
          break;
        default:
          comparison = (a.accuracy || 0) - (b.accuracy || 0);
      }

      return sortDirection === 'asc' ? comparison : -comparison;
    });

    return filtered;
  }, [studentsData, searchQuery, sortBy, sortDirection]);

  const tableContainerMaxHeight = useMemo(() => {
    if (filteredStudents.length > MAX_VISIBLE_ROWS && rowHeight) {
      return rowHeight * MAX_VISIBLE_ROWS;
    }
    return 'auto';
  }, [filteredStudents.length, rowHeight, MAX_VISIBLE_ROWS]);

  const handleSortChange = useCallback((property) => {
    if (sortBy === property) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(property);
      setSortDirection('desc');
    }
  }, [sortBy, sortDirection]);

  const handleStudentClick = useCallback((student) => {
    setSelectedStudent(student);
    setOpenModal(true);
  }, []);

  const handleCloseModal = useCallback(() => {
    setOpenModal(false);
    setSelectedStudent(null);

    if (!searchParams.has('student')) return;

    const params = new URLSearchParams(searchParams.toString());
    params.delete('student');
    const query = params.toString();
    router.push(query ? `${pathname}?${query}` : pathname);
  }, [searchParams, pathname, router]);

  const StudentRow = memo(
    forwardRef(({ student }, ref) => (
      <TableRow
        ref={ref}
        sx={{
          '&:last-child td, &:last-child th': { border: 0 },
          cursor: 'pointer',
          '&:hover': { backgroundColor: 'rgba(0, 0, 0, 0.04)' }
        }}
        onClick={() => handleStudentClick(student)}
      >
        <TableCell component="th" scope="row">
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Avatar
              src={student.avatar}
              alt={student.name}
              sx={{ width: 32, height: 32 }}
            />
            <Box>
              <h6 className="mb-0">
                {student.name}
              </h6>
              {!isMobile && (
                <Typography variant="caption" color="text.secondary">
                  {student.email}
                </Typography>
              )}
            </Box>
          </Box>
        </TableCell>
        <TableCell align="center">
          {student?.history_count > 0 ? (
            <Stack
              direction="row"
              spacing={1}
              justifyContent="center"
              alignItems="center"
            >
              <Chip
                label={student.total_correct}
                icon={<CheckCircleIcon fontSize="small" />}
                color="success"
                size="small"
                title="Câu đúng"
              />
              <Chip
                label={student.total_wrong}
                icon={<CancelIcon fontSize="small" />}
                color="error"
                size="small"
                title="Câu sai"
              />
              <Chip
                label={student.not_doing}
                icon={<ErrorOutlineIcon fontSize="small" />}
                color="warning"
                size="small"
                title="Chưa làm"
              />
            </Stack>
          ) : (
            <Chip
              label="Chưa làm"
              icon={<ReportProblemIcon fontSize="small" />}
              color="secondary"
              size="small"
              title="Chưa làm"
            />
          )}
        </TableCell>
        <TableCell align="center">
          <PieChartProgress
            value={student.accuracy || 0}
            color={getColorByPercentage(student.accuracy || 0)}
            size={45}
          />
        </TableCell>
        <TableCell align="center">
          <Typography variant="body2" color="text.secondary">
            {student?.history_count || 0}
          </Typography>
        </TableCell>
        <TableCell align="right">
          <IconButton
            size="small"
            onClick={(e) => {
              e.stopPropagation();
            }}
          >
            <MoreVertIcon fontSize="small" />
          </IconButton>
        </TableCell>
      </TableRow>
    ))
  );

  return (
    <>
      <div className="bg-light p-3 mb-3">
        <div className="row align-items-center">
          <div className="col-md-6">
            <GroupInputSearch className="mb-3 mb-md-0">
              <IconButton
                size="small"
                color="inherit"
                aria-label="search"
                sx={{ minWidth: "20px" }}
                disabled
              >
                <SearchIcon />
              </IconButton>
              <InputBase
                placeholder="Tìm kiếm học sinh"
                inputProps={{ "aria-label": "Tìm kiếm học sinh" }}
                onChange={handleSearch}
              />
            </GroupInputSearch>
          </div>
          <div className="col-md-6">
            <div className="d-flex flex-wrap justify-content-end gap-3">
              <FormControl size="small">
                <InputLabel id="attempt-filter-label">Hiển thị</InputLabel>
                <Select
                  labelId="attempt-filter-label"
                  value={display}
                  label="Hiển thị"
                  onChange={(e) => setDisplay(e.target.value)}
                  sx={{
                    '.MuiInputBase-input': { fontWeight: 400, color: '#444' }
                  }}
                >
                  <MenuItem value="best">Kết quả tốt nhất</MenuItem>
                  <MenuItem value="first">Lần làm đầu tiên</MenuItem>
                  <MenuItem value="last">Lần làm gần nhất</MenuItem>
                </Select>
              </FormControl>
              <FormControl size="small">
                <InputLabel id="sort-by-label">Sắp xếp theo</InputLabel>
                <Select
                  labelId="sort-by-label"
                  value={sortBy}
                  label="Sắp xếp theo"
                  onChange={(e) => setSortBy(e.target.value)}
                  sx={{
                    '.MuiInputBase-input': { fontWeight: 400, color: '#444' }
                  }}
                >
                  <MenuItem value="name">Tên</MenuItem>
                  <MenuItem value="correct">Thống kê</MenuItem>
                  <MenuItem value="accuracy">Tỉ lệ đúng</MenuItem>
                  <MenuItem value="attempts">Số lần làm</MenuItem>
                </Select>
              </FormControl>
            </div>
          </div>
        </div>
      </div>

      <TableContainer sx={{ maxHeight: tableContainerMaxHeight, overflowY: 'auto' }}>
        <Table stickyHeader aria-label="student results table" size={isMobile ? "small" : "medium"}>
          <TableHead>
            <TableRow>
              {headCells.map((headCell) => (
                <StyledTableCell
                  key={headCell.id}
                  align={headCell.align}
                  padding={headCell.disablePadding ? 'none' : 'normal'}
                  sortDirection={sortBy === headCell.id ? sortDirection : false}
                >
                  <TableSortLabel
                    active={sortBy === headCell.id}
                    direction={sortBy === headCell.id ? sortDirection : 'asc'}
                    onClick={() => handleSortChange(headCell.id)}
                  >
                    {headCell.label}
                    {sortBy === headCell.id ? (
                      <Box component="span" sx={visuallyHidden}>
                        {sortDirection === 'desc' ? 'sorted descending' : 'sorted ascending'}
                      </Box>
                    ) : null}
                  </TableSortLabel>
                </StyledTableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredStudents.length === 0 ? (
              <TableRow>
                <TableCell colSpan={5} align="center" sx={{ py: 3 }}>
                  <Typography variant="body1" color="text.secondary">
                    Không tìm thấy học sinh nào
                  </Typography>
                </TableCell>
              </TableRow>
            ): (
              filteredStudents.map((student, index) => (
                <StudentRow key={student.id} student={student} ref={index === 0 ? rowRef : null} />
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>

      { selectedStudent && (
        <StudentDetailsModal
          open={openModal}
          onClose={handleCloseModal}
          student={selectedStudent}
          studentResults={groupResults[selectedStudent.id] ?? []}
          questions={questions}
        />
      ) }
    </>
  );
};

export default StudentList;
