"use client";

import React, { useEffect, useState, useCallback, useMemo } from "react";
import { fetchAssignmentsReport } from "@/actions/userAction";
import { alpha, styled } from "@mui/material/styles";
import { purple } from "@mui/material/colors";
import Box from "@mui/material/Box";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TablePagination from "@mui/material/TablePagination";
import TableRow from "@mui/material/TableRow";
import Toolbar from "@mui/material/Toolbar";
import Typography from "@mui/material/Typography";
import Paper from "@mui/material/Paper";
import Checkbox from "@mui/material/Checkbox";
import IconButton from "@mui/material/IconButton";
import Button from "@mui/material/Button";
import SearchIcon from "@mui/icons-material/Search";
import Tooltip from "@mui/material/Tooltip";
import DeleteIcon from "@mui/icons-material/Delete";
import FilterListIcon from "@mui/icons-material/FilterList";
import Container from '@mui/material/Container';
import PropTypes from "prop-types";
import PieChartProgress from "@/components/PieChartProgress";
import { getColorByPercentage } from "@/utils/helpers";
import dayjs from "dayjs";
import "dayjs/locale/vi";
import { removeAssignmentsReport } from "@/actions/classroomAction";
import toast from "react-hot-toast";
import CircularProgress from "@mui/material/CircularProgress";
import { useRouter } from 'next/navigation'
import { perPage } from "@/constant";
import NoDataOverlay from "../NoDataOverlay";
import debounce from "lodash/debounce";
import InputBase from "@mui/material/InputBase";

const headCells = [
  { id: "status", numeric: false, disablePadding: true, label: "Loại"},
  { id: "title", numeric: false, disablePadding: false, label: "Tên quiz" },
  { id: "quiz_results_count", numeric: true, disablePadding: false, label: "Số người tham gia" },
  { id: "class_room", numeric: true, disablePadding: false, label: "Lớp" },
  { id: "correct_percentage", numeric: true, disablePadding: false, label: "Tỉ lệ đúng" },
  { id: "code", numeric: false, disablePadding: false, label: "Mã số" },
];

function EnhancedTableHead(props) {
  const { onSelectAllClick, numSelected, rowCount } = props;

  return (
    <TableHead>
      <TableRow>
        <TableCell padding="checkbox">
          <Checkbox
            color="primary"
            indeterminate={numSelected > 0 && numSelected < rowCount}
            checked={rowCount > 0 && numSelected === rowCount}
            onChange={onSelectAllClick}
            inputProps={{ "aria-label": "select all reports" }}
          />
        </TableCell>
        {headCells.map((headCell) => (
          <TableCell
            key={headCell.id}
            align={headCell.numeric ? "center" : "left"}
            padding={headCell.disablePadding ? "none" : "normal"}
          >
            {headCell.label}
          </TableCell>
        ))}
      </TableRow>
    </TableHead>
  );
}

EnhancedTableHead.propTypes = {
  numSelected: PropTypes.number.isRequired,
  onSelectAllClick: PropTypes.func.isRequired,
  rowCount: PropTypes.number.isRequired,
};

function EnhancedTableToolbar(props) {
  const { numSelected, onDelete } = props;

  return (
    <Toolbar
      sx={[
        { pl: { sm: 2 }, pr: { xs: 1, sm: 1 } },
        numSelected > 0 && {
          bgcolor: (theme) =>
            alpha(theme.palette.primary.main, theme.palette.action.activatedOpacity),
        },
      ]}
    >
      {numSelected > 0 ? (
        <Typography sx={{ flex: "1 1 100%" }} color="inherit" variant="subtitle1" component="div">
          {numSelected} selected
        </Typography>
      ) : (
        <Typography sx={{ flex: "1 1 100%" }} variant="h7" id="tableTitle" component="div">
          Danh sách bài tập
        </Typography>
      )}
      {numSelected > 0 ? (
        <Tooltip title="Delete">
          <IconButton onClick={onDelete}>
            <DeleteIcon />
          </IconButton>
        </Tooltip>
      ) : (
        <Tooltip title="Filter list">
          <IconButton>
            <FilterListIcon />
          </IconButton>
        </Tooltip>
      )}
    </Toolbar>
  );
}

EnhancedTableToolbar.propTypes = {
  numSelected: PropTypes.number.isRequired,
  onDelete: PropTypes.func.isRequired,
};

const ColorButton = styled(Button)(({ theme }) => ({
  color: theme.palette.getContrastText(purple[500]),
  backgroundColor: purple[700],
  "&:hover": {
    backgroundColor: purple[500],
  },
}));

const GroupInputSearch = styled(Paper)(() => ({
  padding: "2px 4px",
  display: "flex",
  alignItems: "center",
  border: "1px solid #e5e5e5",
  width: "100%",
  height: "39px",
  maxWidth: "256px",
  borderRadius: "8px",
}));

export default function AssignmentsReport() {
  const router = useRouter()
  const [reports, setReports] = useState([]);
  const [selected, setSelected] = useState([]);
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(perPage);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [keyword, setKeyword] = useState("");

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      setError(null);
      try {
        const response = await fetchAssignmentsReport({
          page: page + 1,
          limit: rowsPerPage,
        });
        if (response && response.data) {
          setReports(response.data);
          setTotal(response.total || 0);
        } else {
          throw new Error("Phản hồi từ API không hợp lệ");
        }
      } catch (err) {
        console.error("Lỗi khi lấy dữ liệu báo cáo:", err.message);
        setError("Không thể tải dữ liệu. Vui lòng thử lại sau.");
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [page, rowsPerPage]);

  const getReportStatus = (report) => {
    if (!report?.end_time) return null;

    const isDone = dayjs(report.end_time).isBefore(dayjs());
    return {
      isDone,
      label: isDone ? "Đã kết thúc" : "Đang diễn ra",
      className: isDone ? "success" : "orange",
    };
  };

  const handleSelectAllClick = (event) => {
    if (event.target.checked) {
      const newSelected = filteredReports
        .filter((report) => {
          const status = getReportStatus(report);
          return (status?.isDone || !status) && !selected.includes(report.id);
        })
        .map((report) => report.id);
      setSelected(newSelected);
      return;
    }
    setSelected([]);
  };

  const handleClick = (event, id) => {
    const report = reports.find((r) => r.id === id);
    if (!report) return;

    const status = getReportStatus(report);
    if (status && !status.isDone) {
      toast.error("Không thể xóa bài tập đang diễn ra");
      return;
    }

    const selectedIndex = selected.indexOf(id);
    let newSelected = [];

    if (selectedIndex === -1) {
      newSelected = newSelected.concat(selected, id);
    } else if (selectedIndex === 0) {
      newSelected = newSelected.concat(selected.slice(1));
    } else if (selectedIndex === selected.length - 1) {
      newSelected = newSelected.concat(selected.slice(0, -1));
    } else if (selectedIndex > 0) {
      newSelected = newSelected.concat(
        selected.slice(0, selectedIndex),
        selected.slice(selectedIndex + 1)
      );
    }
    setSelected(newSelected);
  };

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const deleteAssignment = async () => {
    if (!selected.length || loading) {
      if (loading) toast.error("Đang tải dữ liệu, vui lòng chờ...");
      return;
    }

    try {
      const response = await removeAssignmentsReport(selected);
      if (response?.status === 200) {
        setReports((prevReports) => prevReports.filter((report) => !selected.includes(report.id)));
        setSelected([]);
        setTotal((prevTotal) => prevTotal - selected.length);
        toast.success("Xóa thành công");
      } else {
        toast.error("Lỗi khi xóa báo cáo");
      }
    } catch (error) {
      console.error("Lỗi khi xóa:", error);
      toast.error("Lỗi khi xóa báo cáo");
    }
  };

  const handleCopy = (code) => {
    navigator.clipboard.writeText(code)
      .then(() => toast.success(`Copy mã thành công`))
      .catch(err => console.error('Lỗi copy:', err));
  }

  const addAssignment = useCallback(() => {
    router.push('/dashboard/quiz/create');
  }, []);

  const debouncedSearch = useCallback(
    debounce((value) => {
      setKeyword(value);
    }, 700),
    []
  );

  const handleSearch = useCallback((event) => {
    debouncedSearch(event.target.value);
  }, []);

  const filteredReports = useMemo(() => {
    return reports.filter((report) =>
      report.title.toLowerCase().includes(keyword.toLowerCase())
    );
  }, [reports, keyword]);

  return (
    <Container maxWidth="cxl">
      <Box sx={{ width: "100%" }}>
      <div className="d-flex flex-wrap align-items-center justify-content-between gap-2 mb-3">
        <div className="d-flex align-items-center gap-3">
          <h3 className="fs-16 fw-light flex-shrink-0 mb-0">
            <i className="bi bi-calendar-check"></i> Danh sách bài tập đã giao:
          </h3>
          <GroupInputSearch>
          <IconButton
            size="small"
            color="inherit"
            aria-label="search"
            sx={{ minWidth: "20px" }}
            disabled
          >
            <SearchIcon />
          </IconButton>
          <InputBase
            placeholder="Tìm bài tập"
            inputProps={{ "aria-label": "Tìm bài tập" }}
            onChange={handleSearch}
          />
        </GroupInputSearch>
        </div>
        <ColorButton onClick={addAssignment}>
          <i className="bi bi-plus-circle me-2"></i>
          Tạo bài tập
        </ColorButton>
      </div>
        <Paper sx={{ width: "100%", mb: 2, pb: loading || filteredReports.length === 0 ? 10 : 0 }}>
          <EnhancedTableToolbar numSelected={selected.length} onDelete={deleteAssignment} />
          {loading ? (
            <Box display="flex" justifyContent="center" alignItems={"center"} my={4}>
              <CircularProgress />
            </Box>
          ) : error ? (
            <div className="text-center py-4 text-danger">{error}</div>
          ) : filteredReports.length > 0 ? (
            <>
            <TableContainer>
                <Table sx={{ minWidth: 750 }} aria-labelledby="tableTitle">
                  <EnhancedTableHead
                    numSelected={selected.length}
                    onSelectAllClick={handleSelectAllClick}
                    rowCount={filteredReports.length}
                  />
                  <TableBody>
                    {filteredReports.map((report, index) => {
                      const isItemSelected = selected.includes(report.id);
                      const labelId = `enhanced-table-checkbox-${index}`;
                      const reportStatus = getReportStatus(report);

                      return (
                        <TableRow
                          hover
                          role="checkbox"
                          aria-checked={isItemSelected}
                          tabIndex={-1}
                          key={report.id}
                          selected={isItemSelected}
                          sx={{ cursor: "pointer" }}
                          onClick={() => router.push(`/dashboard/reports/${report.id}`)}
                        >
                          <TableCell padding="checkbox" onClick={(e) => e.stopPropagation()}>
                            <Checkbox
                              color="primary"
                              checked={isItemSelected}
                              onChange={(event) => handleClick(event, report.id)}
                              inputProps={{ "aria-labelledby": labelId }}
                            />
                          </TableCell>
                          <TableCell component="th" id={labelId} scope="row" padding="none">
                            <div className="px-2">
                              {reportStatus ? (
                                <div className="ass-status">
                                  <span className={`ass-status-label fs-10 ${reportStatus.className} d-flex align-items-center`}>
                                    <i className="bi bi-clock-history me-2"></i>
                                    {reportStatus.label}
                                  </span>
                                </div>
                              ) : (
                                <div className="ass-status">
                                  <span className="ass-status-label text-danger d-flex align-items-center">-- Vô thời hạn --</span>
                                </div>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            <p>- {report?.title}</p>
                            {(report.start_time || report.end_time) && (
                              <Typography
                                className="mt-2"
                                variant="body2"
                                color="text.secondary"
                                sx={{ fontSize: "10px" }}
                              >
                                {report.start_time && `${dayjs(report.start_time).format("DD/MM/YYYY HH:mm")}`}
                                {report.start_time && report.end_time && " • "}
                                {report.end_time && `${dayjs(report.end_time).format("DD/MM/YYYY HH:mm")}`}
                              </Typography>
                            )}
                          </TableCell>
                          <TableCell align="center">{report?.quiz_results_count || 0}</TableCell>
                          <TableCell align="center">{report?.classroom?.title || ""}</TableCell>
                          <TableCell align="center">
                            <PieChartProgress
                              value={report.correct_percentage || 0}
                              color={getColorByPercentage(report.correct_percentage || 0)}
                              size={45}
                            />
                          </TableCell>
                          <TableCell onClick={(e) => e.stopPropagation()}>
                            {!reportStatus?.isDone && (
                              <span
                                className="px-2 py-1 rounded"
                                style={{ backgroundColor: "#0909090d" }}
                                onClick={() => handleCopy(report?.code)}
                              >
                                {report.code}
                              </span>
                            )}
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </TableContainer>
              <TablePagination
                rowsPerPageOptions={[7, 14, 21]}
                component="div"
                count={total}
                rowsPerPage={rowsPerPage}
                page={page}
                onPageChange={handleChangePage}
                onRowsPerPageChange={handleChangeRowsPerPage}
                labelRowsPerPage="Số bản ghi mỗi trang:"
                labelDisplayedRows={({ from, to, count }) =>
                  `${from}–${to} trong tổng ${count !== -1 ? count : `hơn ${to}`}`
                }
                sx={{
                  marginTop: 0,
                  paddingBottom: 0,
                  "&.MuiTablePagination-root": { margin: 0, padding: 0 },
                  "& *": { margin: 0, padding: 0 },
                }}
              />
            </>
          ) : (
            <NoDataOverlay message="Chưa có dữ liệu báo cáo" />
          )}
        </Paper>
      </Box>
    </Container>
  );
}
