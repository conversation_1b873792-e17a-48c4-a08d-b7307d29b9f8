"use client";

import { useState, useMemo, useEffect } from 'react';
import { useRouter } from "next/navigation";
import Link from "next/link";

import Box from "@mui/material/Box";
import Card from "@mui/material/Card";
import CardContent from "@mui/material/CardContent";
import Typography from "@mui/material/Typography";
import Grid from "@mui/material/Grid";
import Avatar from "@mui/material/Avatar";
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import IconButton from '@mui/material/IconButton';
import Skeleton from '@mui/material/Skeleton';
import Button from '@mui/material/Button';
import TextField from '@mui/material/TextField';
import Collapse from '@mui/material/Collapse';
import Divider from '@mui/material/Divider';
import Container from '@mui/material/Container';

import WestIcon from '@mui/icons-material/West';
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import PeopleAltIcon from "@mui/icons-material/PeopleAlt";
import QuizIcon from "@mui/icons-material/Quiz";
import AssistantPhotoIcon from '@mui/icons-material/AssistantPhoto';

import StudentList from "./tabs/StudentList";
import QuestionList from "./tabs/QuestionList";
import TagItemPicker from "@/components/TagItemPicker";

import dayjs from "dayjs";
import "dayjs/locale/vi";
import groupBy from "lodash/groupBy";
import toast from "react-hot-toast";

import { useFetchAssignmentReportsQuery } from '@/slices/features/reportApiSlice';

import { getColorByPercentage } from "@/utils/helpers";

function CustomTabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`report-tabpanel-${index}`}
      aria-labelledby={`report-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ py: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const AssignmentReport = ({ assignmentId }) => {
  console.log('AssignmentReport');
  const router = useRouter();

  const [tabValue, setTabValue] = useState(0);

  const { data: report } = useFetchAssignmentReportsQuery({ assignmentId });

  const reportStatus = useMemo(() => {
    if (!report?.end_time) return null;

    const isDone = dayjs(report.end_time).isBefore(dayjs());

    return {
      isDone,
      label: isDone ? 'Đã kết thúc' : 'Đang diễn ra',
      className: isDone ? 'success' : 'warning',
    };
  }, [report?.end_time]);

  const stats = useMemo(() => {
    if (!report) return null;

    const students = report.classroom?.users || [];
    const quizResults = report.quiz_results || [];
    const usersFromResults = students.length === 0
      ? Object.values(groupBy(quizResults, 'user_id'))
      : [];
    const allStudents = students.length > 0 ? students : usersFromResults;
    const totalStudents = allStudents.length;
    const totalQuestions = report?.quiz?.questions_count || 0;

    let groupResults = {};
    let correctSum = 0;
    let totalSum = 0;

    for (const result of quizResults) {
      correctSum += result.total_correct ?? 0;
      totalSum += result.total ?? 0;

      if (!groupResults[result.user_id]) {
        groupResults[result.user_id] = [];
      }

      groupResults[result.user_id].push(result);
    }

    const completedCount = Object.keys(groupResults).length;

    const formatPercentage = (numerator, denominator) =>
      denominator > 0 ? `${Math.round((numerator * 1000) / denominator) / 10}%` : '0%';

    return {
      correctRate: formatPercentage(correctSum, totalSum),
      completedRate: formatPercentage(completedCount, totalStudents),
      totalStudents,
      totalQuestions,
      groupResults,
    };
  }, [report]);

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const classroomOptions = useMemo(() =>
  (report?.classrooms ?? []).map(c => ({
    value: c.id,
    label: c.title,
  })), [report?.classrooms]);

  const [openStudentDetailsModal, setOpenStudentDetailsModal] = useState(false);
  const [selectedStudent, setSelectedStudent] = useState(null);

  const [collapsed, setCollapsed] = useState(false);

  if (!stats) {
    return (
      <Box>
        <Box className="d-flex align-items-center gap-3">
          <Skeleton sx={{ width: { xs: '70%', sm: '20%' }, height: 40 }} />
          <Skeleton sx={{ width: { xs: '20%', sm: '5%' }, height: 40 }} />
        </Box>
        <Box className="bg-white p-3 mt-3">
          <Skeleton variant="rounded" sx={{ width: { xs: '70%', sm: '20%' }, height: 40, mb: 2 }} />

          <Grid container spacing={3}>
            {Array.from({ length: 4 }, (_, index) => (
              <Grid size={{ xs: 12, sm: 3 }} key={index}>
                <Skeleton variant="rounded" height={100} />
              </Grid>
            ))}
          </Grid>
        </Box>
        <Box className="bg-white p-3 mt-4">
          <Skeleton variant="rounded" sx={{ width: { xs: '40%', sm: '15%' }, height: 40, mb: 3 }} />
          {Array.from({ length: 5 }, (_, index) => (
            <Skeleton
              key={index}
              variant="rounded"
              height={60}
              sx={{ mb: 2 }}
            />
          ))}
        </Box>
      </Box>
    );
  }

  return (
    <Container maxWidth="xl">
      <div className="d-flex flex-wrap gap-2 align-items-center">
        <IconButton onClick={() => router.push(`/dashboard/classroom/${ report?.classroom?.id || '' }`)}>
          <WestIcon />
        </IconButton>
        <h1 className="h4 mb-0">{report?.quiz?.title || '__'}</h1>
        { reportStatus && (
          <div className="ass-status">
            <span className={`ass-status-label fs-14 ${reportStatus.className} d-flex align-items-center`}>
              <i className="bi bi-clock-history me-2"></i>
              {reportStatus.label}
            </span>
          </div>
        ) }
      </div>
      {(report?.start_time || report?.end_time) && (
        <Typography className="mt-2" variant="body2" color="text.secondary">
          {report.start_time && `Bắt đầu: ${dayjs(report.start_time).format('DD/MM/YYYY HH:mm')}`}
          {report.start_time && report.end_time && ' • '}
          {report.end_time && `Hết hạn: ${dayjs(report.end_time).format('DD/MM/YYYY HH:mm')}`}
        </Typography>
      )}
      {!reportStatus?.isDone && report?.code && (
        <div className="row mt-4">
          <div className="col-xl-8 offset-xl-2">
            <Card variant="outlined">
              <CardContent className="p-0">
                <Box sx={{ p: 2, bgcolor: '#7b1fa2', color: 'white' }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Box>
                      <p className="mb-0 fs-16">
                        Mời người tham gia
                      </p>
                      {report.end_time && (
                        <p className="mt-2 mb-0 text-warning">
                          Hết hạn: {dayjs(report.end_time).format('DD/MM/YYYY HH:mm')}
                        </p>
                      )}
                    </Box>
                    <Button
                      variant="text"
                      color="inherit"
                      endIcon={collapsed ? <i className="bi bi-chevron-down"></i> : <i className="bi bi-chevron-up"></i>}
                      onClick={() => setCollapsed(!collapsed)}
                      size="small"
                    >
                      {collapsed ? 'Mở rộng' : 'Thu gọn'}
                    </Button>
                  </Box>
                </Box>
                <Collapse in={!collapsed}>
                  <div className="p-3">
                    <div className="row">
                      <div className="col-md-6">
                        <p className="fs-16 fw-bold">
                          Tham gia bằng mã bài tập:
                        </p>
                        <div className="mb-3">
                          <p className="mb-1">
                            BƯỚC 1: Sử dụng bất kỳ thiết bị nào để mở
                          </p>
                          <TextField
                            fullWidth
                            value={window.location.origin}
                            InputProps={{
                              readOnly: true,
                            }}
                            variant="outlined"
                            size="small"
                          />
                        </div>
                        <div>
                          <p className="mb-1">
                            BƯỚC 2: Nhập mã tham gia
                          </p>
                          <TextField
                            fullWidth
                            value={report.code}
                            InputProps={{
                              readOnly: true,
                            }}
                            variant="outlined"
                            size="small"
                          />
                        </div>
                      </div>
                      <div className="col-md-6">
                        <p className="fs-16 fw-bold">
                          Tham gia bằng liên kết:
                        </p>
                        <div>
                          <p className="mb-1">
                            Liên kết bài tập
                          </p>
                          <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
                            <TextField
                              fullWidth
                              value={`${window.location.origin}/dashboard?gc=${report.code}`}
                              InputProps={{
                                readOnly: true,
                                sx: { height: '40px' }
                              }}
                              variant="outlined"
                              size="small"
                              sx={{ flexGrow: 1 }}
                            />
                            <Button
                              variant="contained"
                              color="secondary"
                              startIcon={<i className="bi bi-copy" style={{ fontSize: '14px' }}></i>}
                              onClick={() => {
                                navigator.clipboard.writeText(`${window.location.origin}/dashboard?gc=${report.code}`);
                                toast.success('Copy liên kết thành công!');
                              }}
                              size="small"
                              sx={{
                                height: '40px',
                                py: 0,
                                minWidth: '120px',
                                whiteSpace: 'nowrap',
                                '& .MuiButton-startIcon': {
                                  mr: 0.5
                                }
                              }}
                            >
                              Sao chép
                            </Button>
                          </Box>
                        </div>
                      </div>
                    </div>
                  </div>
                </Collapse>
              </CardContent>
            </Card>
          </div>
        </div>
      )}
      <Card variant="outlined" className="mt-4">
        <CardContent>
          <div className="d-flex gap-2 align-items-center mb-3">
            <div><i className="bi bi-list-check me-2"></i> Tổng quan</div>
            { classroomOptions.length > 0 && (
            <TagItemPicker
              options={classroomOptions}
              defaultOption={
                {
                  value: report?.classroom?.id || null,
                  label: report?.classroom?.title || '',
                }
              }
              renderDefaultItem={(option) => (
                <>
                  <span
                    className="d-inline-block rounded-circle"
                    style={{
                      backgroundColor: report?.classroom?.color || "#e91d63",
                      width: "10px",
                      height: "10px"
                    }}
                  />
                  <span className="ms-2 fs-12">{ option.label }</span>
                </>
              )}
              renderItem={(option) => <span className="px-2">{ option.label }</span>}
              onChange={(value) => {
                router.push(`/dashboard/classroom/${value}?tab=2`);
              }}
            />
            ) }
          </div>
          <Box
            sx={{
              width: '100%',
              display: 'grid',
              gridTemplateColumns: {
                xs: 'repeat(1, 1fr)',
                sm: 'repeat(2, 1fr)',
                md: 'repeat(2, 1fr)',
                lg: 'repeat(4, 1fr)',
              },
              gap: 2,
            }}
          >
            <Card variant="outlined" sx={{ height: '100%' }}>
              <CardContent sx={{ display: 'flex', alignItems: 'center', gap: 2, p: 2 }}>
                <Avatar
                  sx={{
                    bgcolor: '#f5f5f5',
                    width: 56,
                    height: 56
                  }}
                >
                  <CheckCircleIcon fontSize="large" color="success" />
                </Avatar>
                <Box>
                  <Typography variant="subtitle1" color="text.success">
                    Câu đúng
                  </Typography>
                  <Typography
                    variant="h5"
                    component="div"
                    sx={{
                      color: getColorByPercentage(parseInt(stats.correctRate))
                    }}
                  >
                    {stats.correctRate}
                  </Typography>
                </Box>
              </CardContent>
            </Card>
            <Card variant="outlined" sx={{ height: '100%' }}>
              <CardContent sx={{ display: 'flex', alignItems: 'center', gap: 2, p: 2 }}>
                <Avatar
                  sx={{
                    bgcolor: '#f5f5f5',
                    width: 56,
                    height: 56
                  }}
                >
                  <AssistantPhotoIcon fontSize="large" color="error" />
                </Avatar>
                <Box>
                  <Typography variant="subtitle1" color="text.secondary">
                    Tỷ lệ hoàn thành
                  </Typography>
                  <Typography
                    variant="h5"
                    component="div"
                    sx={{
                      color: getColorByPercentage(parseInt(stats.completedRate))
                    }}
                  >
                    {stats.completedRate}
                  </Typography>
                </Box>
              </CardContent>
            </Card>
            <Card variant="outlined" sx={{ height: '100%' }}>
              <CardContent sx={{ display: 'flex', alignItems: 'center', gap: 2, p: 2 }}>
                <Avatar
                  sx={{
                    bgcolor: '#f5f5f5',
                    width: 56,
                    height: 56
                  }}
                >
                  <PeopleAltIcon fontSize="large" color="primary" />
                </Avatar>
                <Box>
                  <Typography variant="subtitle1" color="text.secondary">
                    {classroomOptions.length > 0 ? 'Số học sinh' : 'Số người chơi'}
                  </Typography>
                  <Typography variant="h5" component="div">
                    {stats.totalStudents}
                  </Typography>
                </Box>
              </CardContent>
            </Card>
            <Card variant="outlined" sx={{ height: '100%' }}>
              <CardContent sx={{ display: 'flex', alignItems: 'center', gap: 2, p: 2 }}>
                <Avatar
                  sx={{
                    bgcolor: '#f5f5f5',
                    width: 56,
                    height: 56
                  }}
                >
                  <QuizIcon fontSize="large" color="secondary" />
                </Avatar>
                <Box>
                  <Typography variant="subtitle1" color="text.secondary">
                    Câu hỏi
                  </Typography>
                  <Typography variant="h5" component="div">
                    {stats.totalQuestions}
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Box>
        </CardContent>
      </Card>
      <Card variant="outlined" className="mt-4">
        <CardContent>
          <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <Tabs
              value={tabValue}
              onChange={handleTabChange}
              aria-label="assignment report tabs"
              sx={{
                '& .MuiTabs-indicator': {
                  backgroundColor: '#9155FD',
                },
                '& .MuiTab-root': {
                  color: 'text.secondary',
                  '&.Mui-selected': {
                    color: '#9155FD',
                    fontWeight: 600,
                  },
                },
              }}
            >
              <Tab
                label={classroomOptions.length > 0 ? 'DS Học sinh' : 'Người chơi'}
                sx={{ textTransform: 'none' }}
              />
              <Tab
                label="Câu hỏi"
                sx={{ textTransform: 'none' }}
              />
            </Tabs>
          </Box>

          <CustomTabPanel value={tabValue} index={0}>
            <StudentList
              students={report?.classroom?.users || []}
              quizResults={report?.quiz_results || {}}
              groupResults={stats?.groupResults || {}}
              questions={report?.quiz?.questions || []}
            />
          </CustomTabPanel>

          <CustomTabPanel value={tabValue} index={1}>
            <QuestionList
              questions={report?.quiz?.questions || []}
              groupResults={stats?.groupResults || {}}
              quizResults={report?.quiz_results || []}
              totalStudents={stats?.totalStudents || 0}
            />
          </CustomTabPanel>
        </CardContent>
      </Card>
    </Container>
  );
};

export default AssignmentReport;
