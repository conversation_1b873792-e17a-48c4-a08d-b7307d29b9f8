import React, { useState, useMemo, useCallback, useEffect } from 'react';

import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import Chip from '@mui/material/Chip';
import Paper from '@mui/material/Paper';
import Grid from '@mui/material/Grid';
import useMediaQuery from '@mui/material/useMediaQuery';
import { useTheme } from '@mui/material/styles';

import CloseIcon from '@mui/icons-material/Close';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CancelIcon from '@mui/icons-material/Cancel';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import AccessTimeIcon from '@mui/icons-material/AccessTime';

import { LineChart } from '@mui/x-charts/LineChart';

import dayjs from 'dayjs';
import 'dayjs/locale/vi';
import { useTranslations } from "next-intl";
import { getColorByPercentage, formatDuration } from "@/utils/helpers";

import PieChartProgress from '@/components/PieChartProgress';
import NoDataOverlay from '@/components/NoDataOverlay';
import ResultBadge from '@/components/ResultBadge';
import QuestionCard from '@/components/questions/QuestionCard';
import QuizTimingSummary from "@/components/quizzes/QuizTimingSummary";

const AttemptTabPanel = ({ children, value, index, ...other }) => (
  <div
    role="tabpanel"
    hidden={value !== index}
    id={`attempt-tabpanel-${index}`}
    aria-labelledby={`attempt-tab-${index}`}
    {...other}
  >
    {value === index && <Box pt={3}>{children}</Box>}
  </div>
);

const StatCard = ({ icon, value, label, color, isPieChart = false }) => (
  <Card variant="outlined" className="h-100">
    <CardContent className="p-2 text-center d-flex flex-column justify-content-center gap-1 h-100">
      <div>
        {isPieChart ? (
          <PieChartProgress value={value} color={color} size={35} />
        ) : (
          <Typography variant="h4" color={color} className="mb-1">
            {value}
          </Typography>
        )}
      </div>
      <Box className="d-flex align-items-center gap-2 justify-content-center">
        {icon && icon}
        <p className="mb-0">
          {label}
        </p>
      </Box>
    </CardContent>
  </Card>
);

const AccuracyLineChart = ({ results = [], color = '#4caf50', height = 250 }) => {
  const { seriesData, xAxisLabels } = useMemo(() => {
    const accuracy = [];
    const labels = [];

    results.forEach((result, index) => {
      accuracy.push(result.accuracy ?? 0);
      labels.push(`Lần ${index + 1}`);
    });

    return {
      seriesData: accuracy,
      xAxisLabels: labels,
    };
  }, [results]);

  return (
    <LineChart
      series={[{ data: seriesData, color }]}
      xAxis={[{ data: xAxisLabels, scaleType: 'band' }]}
      yAxis={[{
        min: 0,
        max: 100,
        tickValues: [0, 25, 50, 75, 100],
        valueFormatter: v => `${v}%`,
      }]}
      height={height}
      margin={{ top: 10, bottom: 30, left: 40, right: 10 }}
      slotProps={{ legend: { hidden: true } }}
    />
  );
};

const StudentDetailsModal = ({ open, onClose, student, studentResults = [], questions = [] }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const t = useTranslations("Common");

  const [activeTab, setActiveTab] = useState(studentResults.length > 0 ? studentResults.length - 1 : 0);

  const sortedResults = useMemo(() => {
    if (!studentResults.length) return [];
    return [...studentResults].sort((a, b) => a.id - b.id);
  }, [studentResults]);

  return (
    <Dialog
      open={open}
      onClose={onClose}
      fullWidth
      maxWidth="lg"
      fullScreen={isMobile}
      scroll="paper"
      aria-labelledby="student-details-dialog-title"
    >
      <DialogTitle
        id="student-details-dialog-title"
      >
        <Box className="d-flex align-items-center justify-content-between">
          <h4 className="h6 fw-normal">
            Chi tiết kết quả: <strong>{student?.name || 'Học sinh'}</strong>
          </h4>
          <IconButton
            edge="end"
            color="inherit"
            onClick={onClose}
            aria-label="close"
            size="small"
            sx={{ p: 0.5 }}
          >
            <CloseIcon fontSize="small" />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent dividers>
        {sortedResults.length > 0 ? (
          <>
            <div className="p-3 mb-3 bg-light">
              <p>
                Tỷ lệ đúng:
              </p>
              <AccuracyLineChart results={sortedResults} />
            </div>
            <Box
              sx={{
                flexGrow: 1,
                bgcolor: 'background.paper',
              }}
            >
              <Tabs
                value={activeTab}
                onChange={(e, newValue) => setActiveTab(newValue)}
                variant="scrollable"
                scrollButtons="auto"
                aria-label="student attempts tabs"
                sx={{
                  '& .MuiTabs-indicator': {
                    backgroundColor: '#9155FD',
                  },
                  '& .MuiTab-root': {
                    color: 'text.secondary',
                    '&.Mui-selected': {
                      color: '#9155FD',
                      fontWeight: 600,
                    },
                  },
                }}
              >
                {sortedResults.map((_, index) => (
                  <Tab
                    key={index}
                    label={`Lần ${index + 1}`}
                    sx={{ textTransform: 'none', minWidth: '60px' }}
                  />
                ))}
              </Tabs>
            </Box>

            {sortedResults.map((result, index) => {
              let dataLog = result.data_log;

              if (typeof dataLog === 'string') {
                try {
                  dataLog = JSON.parse(dataLog);
                } catch (e) {
                  console.warn("Lỗi JSON parse:", e);
                }
              }

              return (
                <AttemptTabPanel key={index} value={activeTab} index={index}>
                  <p className="text-primary">
                    <i className="bi bi-calendar-check text-danger"></i> Thời gian nộp bài: <mark>{result.created_at ? dayjs(result.created_at).format('DD/MM/YYYY HH:mm') : 'N/A'}</mark>
                  </p>
                  <div className="bg-light p-lg-3">
                    <div className="row mb-4">
                      <div className="col-md-6 col-lg-3">
                        <StatCard
                          isPieChart={true}
                          value={result.accuracy || 0}
                          color={getColorByPercentage(result.accuracy || 0)}
                          label="Tỉ lệ đúng"
                        />
                      </div>
                      <div className="col-md-6 col-lg-3">
                        <StatCard
                          value={`${result.total_correct || 0}/${result.total || 0}`}
                          color="success.main"
                          icon={<CheckCircleIcon color="success" fontSize="small" />}
                          label="Câu đúng"
                        />
                      </div>
                      <div className="col-md-6 col-lg-3">
                        <StatCard
                          value={`${result.total_wrong || 0}/${result.total || 0}`}
                          color="error.main"
                          icon={<CancelIcon color="error" fontSize="small" />}
                          label="Câu sai"
                        />
                      </div>
                      <div className="col-md-6 col-lg-3">
                        <StatCard
                          value={`${result.not_doing || 0}/${result.total || 0}`}
                          color="warning.main"
                          icon={<ErrorOutlineIcon color="warning" fontSize="small" />}
                          label="Chưa làm"
                        />
                      </div>
                      <div className="col-12">
                        <QuizTimingSummary quizResult={result}/>
                      </div>
                    </div>
                    { questions.map((question, index) => {
                      const saveLog = dataLog?.[question.id] || {};
                      const isCorrect = saveLog.hasOwnProperty('isCorrect') ? !!saveLog.isCorrect : null;

                      return (
                        <Card variant="outlined" key={question.id} className="p-3 pb-0 mb-4">
                          <div className="d-flex flex-wrap align-items-center justify-content-between mb-2">
                            <div className="d-flex flex-wrap align-items-center gap-2">
                              <span className="fw-normal my-1">
                                <i className="bi bi-check2 me-2"></i>
                                {t(question.type)}
                              </span>
                              <ResultBadge isCorrect={isCorrect} />
                              {saveLog.question_duration && (
                                <Chip
                                  size="small"
                                  icon={<AccessTimeIcon />}
                                  label={formatDuration(saveLog.question_duration)}
                                  variant="outlined"
                                  color="info"
                                />
                              )}
                            </div>
                            {saveLog.answered_at && (
                              <div className="d-flex flex-wrap align-items-center gap-2">
                                <p className="mb-0 fs-14 text-black-50">
                                  Trả lời lúc: <mark>{dayjs(saveLog.answered_at).format('DD/MM/YYYY HH:mm')}</mark>
                                </p>
                              </div>
                            )}
                            {question.content_json && (
                              <div className="d-flex flex-wrap align-items-center gap-2">
                                <span className="fw-normal fs-14 my-1 text-black-50">
                                  {question.content_json?.points || 1} điểm
                                </span>
                                {question.content_json.timeLimit && (
                                  <span className="fw-normal fs-14 my-1 text-black-50">
                                    <small className="mx-1"> • </small>
                                    { formatDuration(question.content_json.timeLimit) }
                                  </span>
                                )}
                              </div>
                            )}
                          </div>
                          <CardContent className="p-0 pt-3 border-top">
                            <QuestionCard question={question} showAnswer={true} questionLogs={saveLog} />
                          </CardContent>
                        </Card>
                      )
                    })}
                  </div>
                </AttemptTabPanel>
              );
            })}
          </>
        ) : <NoDataOverlay message="Học sinh chưa có kết quả làm bài" /> }
      </DialogContent>
    </Dialog>
  );
};

export default React.memo(StudentDetailsModal);
