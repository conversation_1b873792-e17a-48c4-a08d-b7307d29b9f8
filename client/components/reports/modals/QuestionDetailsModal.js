import React, { useState, useMemo, useCallback } from 'react';

import { useTheme, styled } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell, { tableCellClasses } from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Paper from '@mui/material/Paper';
import Avatar from '@mui/material/Avatar';
import InputBase from "@mui/material/InputBase";

import CloseIcon from '@mui/icons-material/Close';
import SearchIcon from '@mui/icons-material/Search';

import NoDataOverlay from '@/components/NoDataOverlay';
import ResultBadge from '@/components/ResultBadge';
import dayjs from "dayjs";

import debounce from 'lodash/debounce';
import {formatDuration} from "@/utils/helpers";
import Chip from "@mui/material/Chip";
import AccessTimeIcon from "@mui/icons-material/AccessTime";

const StyledTableCell = styled(TableCell)(({ theme }) => ({
  [`&.${tableCellClasses.head}`]: {
    backgroundColor: theme.palette.common.black,
    borderColor: theme.palette.common.black,
    borderBottom: 'none',
    color: theme.palette.grey[400],
    minWidth: '130px',
    '.Mui-active': {
      color: theme.palette.common.white,
    },
  },
  [`&.${tableCellClasses.body}`]: {
    fontSize: 14,
  },
}));

const GroupInputSearch = styled(Paper)(() => {
  return {
    padding: "2px 4px",
    display: "flex",
    alignItems: "center",
    border: "1px solid #e5e5e5",
    width: "100%",
    height: "39px",
    maxWidth: "100%",
    borderRadius: "8px",
  };
});

const QuestionDetailsModal = ({ open, onClose, question, quizResults = [] }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const [searchTerm, setSearchTerm] = useState('');

  const debouncedSearch = useCallback(
    debounce((value) => {
      setSearchTerm(value);
    }, 700),
    []
  );

  const handleSearch = useCallback((event) => {
    debouncedSearch(event.target.value);
  }, [debouncedSearch]);

  const filteredQuizResults = useMemo(() => {
    if (!searchTerm.trim()) return quizResults;

    return quizResults.filter(result =>
      result?.user?.name.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [quizResults, searchTerm]);

  return (
    <Dialog
      open={open}
      onClose={onClose}
      fullWidth
      maxWidth="md"
      fullScreen={isMobile}
      scroll="paper"
      aria-labelledby="question-details-dialog-title"
    >
      <DialogTitle id="question-details-dialog-title">
        <Box className="d-flex align-items-center justify-content-between">
          <h4 className="h6 fw-normal">
            Số lượt trả lời của học sinh
          </h4>
          <IconButton
            edge="end"
            color="inherit"
            onClick={onClose}
            aria-label="close"
            size="small"
            sx={{ p: 0.5 }}
          >
            <CloseIcon fontSize="small" />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent dividers>
        <GroupInputSearch className="mb-3">
          <IconButton
            size="small"
            color="inherit"
            aria-label="search"
            sx={{ minWidth: "20px" }}
            disabled
          >
            <SearchIcon />
          </IconButton>
          <InputBase
            fullWidth
            placeholder="Tìm kiếm lớp học"
            inputProps={{ "aria-label": "Tìm kiếm" }}
            onChange={handleSearch}
          />
        </GroupInputSearch>
        <TableContainer>
          <Table stickyHeader>
            <TableHead>
              <TableRow>
                <StyledTableCell>Học sinh</StyledTableCell>
                <StyledTableCell align="center">Kết quả</StyledTableCell>
                <StyledTableCell>Đáp án đã chọn</StyledTableCell>
                <StyledTableCell>Thời gian</StyledTableCell>
                <StyledTableCell>Trả lời lúc</StyledTableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredQuizResults.length > 0 ? (
                filteredQuizResults.map((result, index) => {
                  let dataLog = result.data_log;

                  if (typeof dataLog === 'string') {
                    try {
                      dataLog = JSON.parse(dataLog);
                    } catch (e) {
                      console.warn("Lỗi JSON parse:", e);
                    }
                  }

                  const saveLog = dataLog?.[question.id] || null;

                  return saveLog ? (
                    <TableRow
                      key={result.id}
                      sx={{
                        '&:last-child td, &:last-child th': { border: 0 },
                        '&:hover': { backgroundColor: 'rgba(0, 0, 0, 0.04)' }
                      }}
                    >
                      <TableCell>
                        <Box className="d-flex align-items-center gap-2">
                          <Avatar sx={{ width: 30, height: 30 }}></Avatar>
                          <Typography variant="body2">{result?.user?.name || '__'}</Typography>
                        </Box>
                      </TableCell>
                      <TableCell align="center">
                        <ResultBadge isCorrect={!!saveLog.isCorrect} />
                      </TableCell>
                      <TableCell>
                        <ul>
                          {saveLog.answer.map(optionIndex => {
                            const option = question?.content_json?.options?.[optionIndex];

                            return option ? (
                              <li key={optionIndex}>
                                <Box
                                  className="mb-1"
                                  dangerouslySetInnerHTML={{ __html: option.content || '' }}
                                />
                              </li>
                            ) : null;
                          })}
                        </ul>
                      </TableCell>
                      <TableCell>
                        <Chip
                          size="small"
                          icon={<AccessTimeIcon />}
                          label={formatDuration(saveLog.question_duration)}
                          variant="outlined"
                          color="info"
                        />
                      </TableCell>
                      <TableCell>
                        <mark>{ saveLog.answered_at ? dayjs(saveLog.answered_at).format('DD/MM/YYYY HH:mm') : 'N/A' }</mark>
                      </TableCell>
                    </TableRow>
                  ) : null;
                })
              ) : (
                <TableRow>
                  <TableCell colSpan={3} align="center" sx={{ py: 3 }}>
                    <NoDataOverlay />
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
      </DialogContent>
    </Dialog>
  );
};

export default QuestionDetailsModal;
