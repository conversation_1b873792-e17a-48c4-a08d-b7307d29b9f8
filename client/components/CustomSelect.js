import * as React from 'react';
import { styled, alpha } from '@mui/material/styles';
import Button from '@mui/material/Button';
import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';

import UnfoldMoreRoundedIcon from '@mui/icons-material/UnfoldMoreRounded';

const StyledMenu = styled((props) => (
  <Menu
    elevation={0}
    anchorOrigin={{
      vertical: 'bottom',
      horizontal: 'right',
    }}
    transformOrigin={{
      vertical: 'top',
      horizontal: 'right',
    }}
    {...props}
  />
))(({ theme }) => ({
  '& .MuiPaper-root': {
    borderRadius: 6,
    marginTop: theme.spacing(1),
    minWidth: 200,
    color: 'rgb(55, 65, 81)',
    boxShadow:
      'rgb(255, 255, 255) 0px 0px 0px 0px, rgba(0, 0, 0, 0.05) 0px 0px 0px 1px, rgba(0, 0, 0, 0.1) 0px 10px 15px -3px, rgba(0, 0, 0, 0.05) 0px 4px 6px -2px',
    '& .MuiMenu-list': {
      padding: '4px 0',
    },
    '& .MuiMenuItem-root': {
      '& .MuiSvgIcon-root': {
        fontSize: 18,
        color: theme.palette.text.secondary,
        marginRight: theme.spacing(1.5),
      },
      '&:active': {
        backgroundColor: alpha(
          theme.palette.primary.main,
          theme.palette.action.selectedOpacity,
        ),
      },
    },
    ...theme.applyStyles('dark', {
      color: theme.palette.grey[300],
    }),
  },
}));

const StyledButton = styled((props) => (
  <Button
    variant="outlined"
    {...props}
  />
))(({ theme }) => ({
  minWidth: '200px',
  justifyContent: 'space-between',
  border: `1px solid ${theme.palette.mode === 'dark' ? theme.palette.grey[700] : theme.palette.grey[200]}`,
  color: `${theme.palette.mode === 'dark' ? theme.palette.grey[300] : theme.palette.grey[900]}`,
  boxShadow: `0px 2px 2px ${theme.palette.mode === 'dark' ? theme.palette.grey[900] : theme.palette.grey[50]}`,
  '&:hover': {
    background: `${theme.palette.mode === 'dark' ? theme.palette.grey[800] : theme.palette.grey[50]}`,
    borderColor: `${theme.palette.mode === 'dark' ? theme.palette.grey[600] : theme.palette.grey[300]}`,
  },
}));

export default function CustomSelect({ defaultOption, options, onChange }) {
  const [anchorEl, setAnchorEl] = React.useState(null);
  const open = Boolean(anchorEl);

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleMenuItemClick = (event, value) => {
    onChange(value);
    setAnchorEl(null);
  };

  return (
    <div>
      <StyledButton
        id="demo-customized-button"
        aria-controls={open ? 'demo-customized-menu' : undefined}
        aria-haspopup="true"
        aria-expanded={open ? 'true' : undefined}
        disableElevation
        onClick={handleClick}
        endIcon={<UnfoldMoreRoundedIcon />}
      >
        { defaultOption.icon && (<span className="me-2 icon-link" dangerouslySetInnerHTML={{ __html: defaultOption.icon }} />) }
        { defaultOption.label }
      </StyledButton>
      <StyledMenu
        id="demo-customized-menu"
        MenuListProps={{
          'aria-labelledby': 'demo-customized-button',
        }}
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
      >
        {options.map((option) => (
          <MenuItem
            key={option.value}
            onClick={(event) => handleMenuItemClick(event, option.value)}
            disableRipple
            selected={defaultOption.value === option.value}
          >
            { option.icon && (<span className="me-2 icon-link" dangerouslySetInnerHTML={{ __html: option.icon }} />) }
            {option.label}
          </MenuItem>
        ))}
      </StyledMenu>
    </div>
  );
}
