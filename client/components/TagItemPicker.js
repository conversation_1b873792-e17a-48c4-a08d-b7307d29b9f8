import PropTypes from 'prop-types';

import * as React from 'react';
import { styled, alpha } from '@mui/material/styles';
import Button from '@mui/material/Button';
import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import Box from '@mui/material/Box';

import UnfoldMoreRoundedIcon from '@mui/icons-material/UnfoldMoreRounded';

const StyledMenu = styled(({ columnCount, ...other }) => (
  <Menu
    elevation={0}
    anchorOrigin={{
      vertical: 'bottom',
      horizontal: 'right',
    }}
    transformOrigin={{
      vertical: 'top',
      horizontal: 'right',
    }}
    {...other}
  />
))(({ theme, columnCount }) => ({
  '& .MuiPaper-root': {
    borderRadius: 6,
    marginTop: theme.spacing(1),
    minWidth: 180,
    color: 'rgb(55, 65, 81)',
    boxShadow:
      'rgb(255, 255, 255) 0px 0px 0px 0px, rgba(0, 0, 0, 0.05) 0px 0px 0px 1px, rgba(0, 0, 0, 0.1) 0px 10px 15px -3px, rgba(0, 0, 0, 0.05) 0px 4px 6px -2px',
    '& .MuiMenu-list': {
      padding: '5px',
      display: 'grid',
      gridTemplateColumns: `repeat(${columnCount}, 1fr)`,
      gap: '2px',
    },
    '& .MuiMenuItem-root': {
      display: 'flex',
      justifyContent: 'center',
      padding: '4px 0',
      maxWidth: '110px',
      whiteSpace: 'normal',
      '& .MuiSvgIcon-root': {
        fontSize: 18,
        color: theme.palette.text.secondary,
        marginRight: theme.spacing(1.5),
      },
      '&:active': {
        backgroundColor: alpha(
          theme.palette.primary.main,
          theme.palette.action.selectedOpacity,
        ),
      },
    },
    ...theme.applyStyles('dark', {
      color: theme.palette.grey[300],
    }),
  },
}));

const StyledButton = styled((props) => (
  <Button
    variant="outlined"
    {...props}
  />
))(({ theme }) => ({
  background: '#eee',
  minWidth: '50px',
  padding: '5px 7px',
  justifyContent: 'space-between',
  border: `1px solid ${theme.palette.mode === 'dark' ? theme.palette.grey[700] : theme.palette.grey[200]}`,
  color: `${theme.palette.mode === 'dark' ? theme.palette.grey[300] : theme.palette.grey[900]}`,
  boxShadow: `0px 2px 2px ${theme.palette.mode === 'dark' ? theme.palette.grey[900] : theme.palette.grey[50]}`,
  '&:hover': {
    background: `${theme.palette.mode === 'dark' ? theme.palette.grey[800] : theme.palette.grey[50]}`,
    borderColor: `${theme.palette.mode === 'dark' ? theme.palette.grey[600] : theme.palette.grey[300]}`,
  },
}));

export default function TagItemPicker({ defaultOption, options, renderDefaultItem, renderItem, onChange }) {
  console.log('TagItemPicker');
  const [anchorEl, setAnchorEl] = React.useState(null);
  const open = Boolean(anchorEl);

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleMenuItemClick = (event, value) => {
    onChange(value);
    setAnchorEl(null);
  };

  const columnCount = options.length > 3 ? 4 : options.length;

  return (
    <div>
      <StyledButton
        id="demo-customized-button"
        aria-controls={open ? 'demo-customized-menu' : undefined}
        aria-haspopup="true"
        aria-expanded={open ? 'true' : undefined}
        disableElevation
        onClick={handleClick}
        endIcon={ columnCount > 1 && <UnfoldMoreRoundedIcon />}
      >
        { renderDefaultItem && renderDefaultItem(defaultOption) }
      </StyledButton>
      { columnCount > 1 && (
        <StyledMenu
          id="demo-customized-menu"
          columnCount={columnCount}
          MenuListProps={{
            'aria-labelledby': 'demo-customized-button',
          }}
          anchorEl={anchorEl}
          open={open}
          onClose={handleClose}
        >
          {options.map((option, index) => (
            <MenuItem
              key={index}
              onClick={(event) => handleMenuItemClick(event, option.value)}
              disableRipple
              selected={defaultOption.value === option.value}
            >
              { renderItem && renderItem(option) }
            </MenuItem>
          ))}
        </StyledMenu>
      ) }
    </div>
  );
}

TagItemPicker.propTypes = {
  options: PropTypes.arrayOf(
    PropTypes.shape({
      value: PropTypes.any.isRequired,
      label: PropTypes.string,
    })
  ).isRequired,

  defaultOption: PropTypes.shape({
    value: PropTypes.any.isRequired,
    label: PropTypes.string,
  }).isRequired,

  renderDefaultItem: PropTypes.func.isRequired,
  renderItem: PropTypes.func.isRequired,
  onChange: PropTypes.func.isRequired,
};
