import React, { useEffect, useState, useRef } from "react";
import useSound from "@/hooks/useSound";

const Countdown = ({ initialCounter = null, onEnd }) => {
  console.log('Countdown');
  const [count, setCount] = useState(initialCounter);

  const [puffer, setPuffer] = useState(false);

  const [playCountdown] = useSound("/assets/sounds/countdown.mp3");
  const [playCountdownEnd] = useSound("/assets/sounds/countdown_end.mp3");

  const timerRef = useRef();
  const playedEndSoundRef = useRef(false);

  useEffect(() => {
    if (count === null) return;

    if (count > 0) {
      playCountdown();

      const pufferTimer = setTimeout(() => {
        setPuffer(true);
      }, 600);

      timerRef.current = setTimeout(() => {
        setCount((prevCount) => prevCount - 1);
        setPuffer(false);
      }, 1000);

      return () => {
        clearTimeout(timerRef.current);
        clearTimeout(pufferTimer);
      };
    } else {
      if (!playedEndSoundRef.current) {
        playCountdownEnd();
        playedEndSoundRef.current = true;
      }

      const endTimer = setTimeout(() => {
        setPuffer(true);
        onEnd?.();
      }, 1000);
      return () => clearTimeout(endTimer);
    }
  }, [count, onEnd, playCountdown, playCountdownEnd]);

  return (
    <div className={`countdown ${puffer ? "puffer" : ""}`}>
      <p>
        <span className="countdown-text">
          {count > 0 ? count : count === 0 ? "Bắt Đầu" : "Kết Thúc"}
        </span>
      </p>
    </div>
  );
};

export default React.memo(Countdown);
