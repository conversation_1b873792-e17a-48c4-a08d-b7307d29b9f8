"use client";

import React, { useEffect, useRef, useCallback } from "react";
import throttle from "lodash/throttle";

const InfiniteScroll = ({
  children,
  loader = "Loading...",
  fetchMore,
  hasMore,
  endMessage = "",
  className = "",
}) => {
  const pageEndRef = useRef(null);
  const isFetchingRef = useRef(false);

  // throttle giới hạn số lần gọi fetchMore (1000ms 1 lần)
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const throttledFetchMore = useCallback(
    throttle(async () => {
      if (isFetchingRef.current) return;
      isFetchingRef.current = true;
      try {
        await fetchMore();
      } finally {
        setTimeout(() => {
          isFetchingRef.current = false;
        }, 500);
      }
    }, 1000),
    [fetchMore]
  );

  useEffect(() => {
    if (typeof window === "undefined" || !window.IntersectionObserver) {
      return;
    }

    if (!hasMore) return;

    const currentPageEndRef = pageEndRef.current;

    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          throttledFetchMore();
        }
      },
      { threshold: 0.5 }
    );

    if (currentPageEndRef) observer.observe(currentPageEndRef);

    return () => {
      if (currentPageEndRef) {
        observer.unobserve(currentPageEndRef);
      }
      // Hủy bất kỳ lần gọi nào đang chờ trong throttledFetchMore
      throttledFetchMore.cancel();
    };
  }, [hasMore, throttledFetchMore]);

  return (
    <div className={className}>
      {children}
      {hasMore ? (
        <div ref={pageEndRef} className="text-center p-2">
          {loader}
        </div>
      ) : (
        endMessage
      )}
    </div>
  );
};

export default InfiniteScroll;
