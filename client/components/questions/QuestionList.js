"use client";

import React, { useState } from "react";

import Switch from '@mui/material/Switch';
import Card from "@mui/material/Card";
import CardContent from "@mui/material/CardContent";
import FormControlLabel from "@mui/material/FormControlLabel";

import QuestionCard from "./QuestionCard";

import { useTranslations } from "next-intl";

import useMathJax from '@/hooks/useMathJax';
import { formatDuration } from "@/utils/helpers";

const QuestionList = ({ questions = [], questionLogs = null }) => {
  const t = useTranslations("Common");
  const [showAnswer, setShowAnswer] = useState(false);
  const [showExplain, setShowExplain] = useState({});
  useMathJax([questions, showAnswer, showExplain]);

  const handleShowExplain = (questionId) => () => {
    setShowExplain(prev => ({
      ...prev,
      [questionId]: !prev[questionId],
    }));
  }

  return (
    <Card className="mt-4 p-0 overflow-hidden">
      <div className="border-bottom p-3 d-flex justify-content-between align-items-center">
        <h6 className="h6 text-uppercase m-0">
          {questions.length || 0} CÂU HỎI
        </h6>
        <FormControlLabel
          control={
            <Switch
              checked={showAnswer}
              onChange={(e) => setShowAnswer(e.target.checked)}
              inputProps={{ "aria-label": "Toggle answers" }}
            />
          }
          label={
            <span className="fw-normal">
              Hiển thị đáp án
            </span>
          }
        />
      </div>

      {questions.map((question, index) => (
        <div key={question.id} className={`${index > 0 ? 'border-top' : ''}`}>
          <div className="p-3">
            <div className="d-flex flex-wrap align-items-center justify-content-between mb-2">
              <span className="fw-normal my-1 text-black-50">
                {index + 1}. {t(question.type)}
              </span>
              {question.content_json && (
                <div className="my-1">
                  <span className="fw-normal fs-14 my-1 text-black-50">
                    {question.content_json?.points || 1} điểm
                  </span>
                  <span className="mx-1 text-black-50"> • </span>
                  <span className="fw-normal fs-14 my-1 text-black-50">
                    { question.content_json.timeLimit ? formatDuration(question.content_json.timeLimit) : 'Không giới hạn' }
                  </span>
                </div>
              )}
            </div>
            <CardContent className="p-0">
              <QuestionCard question={question} showAnswer={showAnswer} questionLogs={questionLogs} />
            </CardContent>
            {question.explain && (
              <div className="p-2">
                <span className="text-muted question-explain fs-14 fw-medium" onClick={handleShowExplain(question.id)}>
                  Xem đáp án {showExplain[question.id] ? <i className="bi bi-chevron-up ms-1"></i> : <i className="bi bi-chevron-down ms-1"></i>}
                </span>
                {showExplain[question.id] && (
                  <div className="box-content mt-3" dangerouslySetInnerHTML={{ __html: question.explain || '' }} />
                )}
              </div>
            )}
          </div>
        </div>
      ))}
    </Card>
  );
};

export default QuestionList;
