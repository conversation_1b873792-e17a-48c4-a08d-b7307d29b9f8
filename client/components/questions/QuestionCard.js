import React from 'react';
import Paragraph from "./show/Paragraph";
import MultipleChoice from "./show/MultipleChoice";
import Blank from "./show/Blank";

const QuestionCard = ({ question, showAnswer = false, questionLogs = null }) => {
    switch (question.type) {
        case 'QUIZX':
            return <MultipleChoice key={question.id} question={question} showAnswer={showAnswer} questionLogs={questionLogs} />;
        case 'BLANK':
        case 'DRAG_DROP':
        case 'DROPDOWN':
            return <Blank key={question.id} question={question} showAnswer={showAnswer} questionLogs={questionLogs} />;
        default:
            return <Paragraph key={question.id} question={question} />;
    }
};

export default React.memo(QuestionCard);
