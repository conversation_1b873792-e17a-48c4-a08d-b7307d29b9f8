import * as QuizX     from './QuizX.js';
import * as Blank     from './Blank.js';
import * as Paragraph from './Paragraph.js';

const MODULES = {
  QUIZX: QuizX,
  BLANK: Blank,
};

export function jsonToHtml(type, json) {
  const mod = MODULES[type] || Paragraph;

  return mod.jsonToHtml(json);
}

export function htmlToJson(type, html) {
  const mod = MODULES[type] || Paragraph;

  return mod.htmlToJson(html);
}
