export function htmlToJson(html = '') {
  if (!html.trim()) return {};

  try {
    const doc = new DOMParser()
      .parseFromString(html, 'text/html')
      .body;

    const result = {};

    const listEl = doc.querySelector('.quiz-list');
    if (listEl) {
      const items = Array.from(listEl.children);
      result.options = items.map((li, idx) => {
        const isCorrect = li.classList.contains('correctAnswer');
        if (isCorrect) {
          result.answer = result.answer || [];
          result.answer.push(idx);
        }
        return {
          content: li.innerHTML.trim(),
          isCorrect
        };
      });
      listEl.remove();
    }

    const expEl = doc.querySelector('.exp');
    if (expEl) {
      result.explain = expEl.innerHTML.trim();
      expEl.remove();
    }

    result.content = doc.innerHTML.trim();

    return result;
  } catch (err) {
    console.error('htmlToJson error:', err);
    return {};
  }
}

export function jsonToHtml(questionJson = {}) {
  let q;
  try {
    q = typeof questionJson === 'string'
      ? JSON.parse(questionJson)
      : questionJson;
  } catch {
    console.error('Invalid JSON passed to jsonToHtml');
    return '';
  }

  const contentHtml = q.content || '';

  const optionsHtml = Array.isArray(q.options) && q.options.length
    ? `<ol class="quiz-list">
        ${q.options.map((opt, idx) => {
          const cls = Array.isArray(q.answer) && q.answer.includes(idx)
            ? 'correctAnswer'
            : '';
          return `<li class="${cls}">${opt.content || ''}</li>`;
        }).join('')}
      </ol>`
    : '';

  const explainHtml = q.explain
    ? `<div class="exp">${q.explain}</div>`
    : '';

  return `${contentHtml.trim()}
    ${optionsHtml}
    ${explainHtml}`.trim();
}
