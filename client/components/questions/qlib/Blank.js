import { v4 as uuidv4 } from 'uuid';

export function htmlToJson(html = '') {
  if (!html.trim()) return {};

  try {
    const parser = new DOMParser();
    const doc = parser.parseFromString(html, 'text/html');
    const spans = Array.from(doc.querySelectorAll('span.flag-el'));

    const options = [];
    const answer  = [];

    spans.forEach(span => {
        let targetId = span.id;

        if (!targetId || spans.filter(s => s.id === targetId).length > 1) {
            targetId = uuidv4();
            span.id = targetId;
        }

        const blankText = span.getAttribute('data-blank-text');
        span.removeAttribute('data-blank-text');

        if (blankText && blankText !== '__') {
            /*const optionId  = uuidv4();

            options.push({
              id:      optionId,
              content: blankText,
            });

            answer.push({
              targetId,
              optionId: [ optionId ],
            });*/

          // Support multiple correct answers separated by ||
          const correctAnswers = blankText.split('||').map(a => a.trim()).filter(a => a);
          const optionIds = [];

          correctAnswers.forEach(answerText => {
            const optionId = uuidv4();
            options.push({
              id: optionId,
              content: answerText,
              isCorrect: true // Mark as correct answer
            });
            optionIds.push(optionId);
          });

          if (optionIds.length > 0) {
            answer.push({
              targetId,
              optionId: optionIds,
            });
          }
        }

        span.innerHTML = '___________';
    });

    const content = doc.body.innerHTML;

    return {
        content,
        options,
        answer
    };
  } catch (err) {
    console.error('htmlToJson error:', err);
    return {};
  }
}

export function jsonToHtml(questionJson = {}) {
  try {
    const { content = '', options = [], answer = [] } = typeof questionJson === 'string'
      ? JSON.parse(questionJson)
      : questionJson;

    const parser = new DOMParser();
    const doc = parser.parseFromString(content, 'text/html');

    answer.forEach(({ optionId, targetId }) => {
      const span = doc.getElementById(targetId);
      if (!span) return;

      const selected = optionId.map(id => {
        const opt = options.find(o => o.id === id);
        return opt?.content ?? '';
      }).filter(text => text);

      const text = selected.join('||');

      span.setAttribute('data-blank-text', text);
      span.innerHTML = '[ ' + text + ' ]';
    });

    return doc.body.innerHTML.trim();
  } catch {
    console.error('Invalid JSON passed to jsonToHtml');
    return '';
  }
}
