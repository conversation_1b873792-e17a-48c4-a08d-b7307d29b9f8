export function htmlToJson(html = '') {
  const content = html.replace(/[\t\r\n]/g, '').trim();

  if (!content) return {};

  return {
    content
  };
}

export function jsonToHtml(questionJson = {}) {
  let q;
  try {
    q = typeof questionJson === 'string'
      ? JSON.parse(questionJson)
      : questionJson;
  } catch {
    console.error('Invalid JSON passed to jsonToHtml');
    return '';
  }

  return q.content || '';
}
