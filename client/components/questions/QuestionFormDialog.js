"use client";

import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useDispatch } from "react-redux";

import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import AppBar from '@mui/material/AppBar';
import Toolbar from '@mui/material/Toolbar';
import IconButton from '@mui/material/IconButton';
import Slide from '@mui/material/Slide';
import Select from '@mui/material/Select';
import MenuItem from '@mui/material/MenuItem';
import FormControl from '@mui/material/FormControl';
import InputLabel from '@mui/material/InputLabel';
import Box from '@mui/material/Box';

import ChevronLeftIcon from '@mui/icons-material/ChevronLeft';
import SaveIcon from '@mui/icons-material/Save';

// third party
import * as Yup from 'yup';
import { useTranslations } from "next-intl";
import toast from "react-hot-toast";

import { useDebounceCallback } from '@/hooks/useDebounce';

import CustomSelect from '@/components/CustomSelect';

import MultipleChoice from './form/MultipleChoice';
import Blank from './form/Blank';

import { questionTypes, pointsOptions, timeOptions } from "@/constant";
import { mapDataQuestionJson } from '@/utils/helpers';
import { jsonToHtml } from '@/components/questions/qlib/index.js';

import { storeQuestion, updateQuestion } from '@/actions/questionAction';
import { setNoti } from "@/slices/notiSlice";

const Transition = React.forwardRef(function Transition(props, ref) {
  return <Slide direction="up" ref={ref} {...props} />;
});

const QuestionFormDialog = ({open, onClose, question, quizId, updatedQuestions}) => {
  console.log('QuestionFormDialog');
  const t = useTranslations("Common");
  const dispatch = useDispatch();
  const [questionData, setQuestionData] = useState(null);
  const [submiting, setSubmiting] = useState(false);
  const [selectedPoints, setSelectedPoints] = useState(1);
  const [selectedTimeLimit, setSelectedTimeLimit] = useState(0);

  useEffect(() => {
    setQuestionData(question);

    if (question && question.content_json) {
      setSelectedPoints(question?.content_json?.points || 1);
      setSelectedTimeLimit(question?.content_json?.timeLimit || 0);
    }
  }, [question]);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const questionTypeOptions = useMemo(() => {
    return Object.entries(questionTypes).map(([keyType, questionType]) => ({
      value: keyType,
      label: t(keyType),
      icon: questionType.icon
    }));
  }, []);

  const validateSchema = useCallback((qtype) => {
    let schema = null;

    switch(qtype) {
      case 'QUIZX':
        schema = Yup.object().shape({
          content: Yup.string().required('Bạn chưa nhập nội dung câu hỏi.'),
          options: Yup.array().of(
            Yup.object().shape({
              content: Yup.string().required('Bạn chưa nhập nội dung đáp án.'),
              isCorrect: Yup.boolean()
            })
          )
          .min(2, 'Câu hỏi cần tối thiểu 2 lựa chọn.')
          .max(5, 'Chỉ được tối đa 5 lựa chọn.')
          .test(
            'at-least-one-correct',
            'Bạn chưa chọn đáp án đúng cho câu hỏi.',
            (options) => options && options.some(option => option.isCorrect === true)
          ),
          answer: Yup.array().required('Bạn chưa thiết lập câu trả lời cho câu hỏi.'),
        });

        break;
      case 'BLANK':
      case 'DRAG_DROP':
      case 'DROPDOWN':
        schema = Yup.object().shape({
          content: Yup.string().required('Bạn chưa nhập nội dung câu hỏi.'),
          options: Yup.array().of(
            Yup.object().shape({
              id: Yup.string().required('Đáp án không hợp lệ (không có id).'),
              content: Yup.string().required('Bạn chưa nhập đáp án.')
            })
          ).min(1, 'Câu hỏi cần tối thiểu 1 đáp án.'),
          answer: Yup.array().of(
            Yup.object().shape({
              targetId: Yup.string().required('Lỗi thiết lập đáp án (không có targetId).'),
              optionId: Yup.array().required('Lỗi thiết lập đáp án (optionId trống).')
            })
          ).min(1, 'Bạn chưa thiết lập câu trả lời cho câu hỏi.'),
        });

        break;
      case 'PARAGRAPH':
        schema = Yup.object().shape({
          content: Yup.string().required('Bạn chưa nhập nội dung câu hỏi.'),
        });
        break;
      default:
        schema = Yup.object().shape({
          type: Yup.string().required('Loại câu hỏi không xác định.'),
        });
    }

    return schema;
  }, []);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const saveQuestion = useCallback(async () => {
    setSubmiting(true);

    if (questionData) {
      try {
        const content_json = questionData.content_json || {};
        const validateQuestionSchema = validateSchema(questionData.type);
        await validateQuestionSchema.validate(content_json, { abortEarly: false });

        questionData.content = jsonToHtml(questionData.type, content_json);

        const { data: newQue } = questionData.id
          ? await updateQuestion(quizId, questionData)
          : await storeQuestion(quizId, questionData);

        if (newQue) {
          updatedQuestions(newQue, questionData.id ? true : false);
          setQuestionData(null);
          onClose();
        } else {
          throw new Error("Đã xảy ra lỗi.");
        }
      } catch (validationErrors) {
        if (validationErrors instanceof Yup.ValidationError) {
          // Loại bỏ các thông báo giống nhau
          const uniqueMessages = [...new Set(validationErrors.inner.map((err) => err.message))];
          const errorMessage = (
            <ul style={{ marginBottom: '0px', padding: '0 20px', color: 'red' }}>
              {uniqueMessages.map((msg, index) => (
                <li key={index}>{msg}</li>
              ))}
            </ul>
          );

          toast.error(
            errorMessage,
            {
              duration: 5000,
              position: "top-center"
            }
          );
        } else {
          dispatch(setNoti(validationErrors));
        }
      } finally {
        setSubmiting(false);
      }
    }
  }, [questionData]);

  const debouncedSaveQuestion = useDebounceCallback(saveQuestion, 700);

  const handleQuestionTypeChange = useCallback((value) => {
    setQuestionData(prev =>
      mapDataQuestionJson(
        { type: value, content_json: questionTypes[value].initQue },
        prev
      )
    );
  }, []);

  const questionDataChange = useCallback((content_json) => {
    setQuestionData({ ...questionData, content_json });
  }, [questionData]);

  const renderElementForm = useCallback(() => {
    if (questionData?.type &&
        questionData?.content_json &&
        Object.keys(questionData.content_json).length > 0
    ) {
      switch (questionData.type) {
        case 'QUIZX':
          return <MultipleChoice key="QUIZX" contentJson={questionData.content_json} onChange={questionDataChange} />;
        case 'BLANK':
        case 'DRAG_DROP':
        case 'DROPDOWN':
          return <Blank key="BLANK" contentJson={questionData.content_json} onChange={questionDataChange} />;
      }
    }

    return <p className="text-white m-3">(Câu hỏi không xác định)</p>;
  }, [questionData]);

  const handleTimeLimitChange = useCallback((event) => {
    setSelectedTimeLimit(event.target.value);
    setQuestionData(prev => ({
      ...prev,
      content_json: {
        ...prev.content_json,
        timeLimit: event.target.value
      }
    }));
  }, []);

  const handlePointsChange = useCallback((event) => {
    setSelectedPoints(event.target.value);
    setQuestionData(prev => ({
      ...prev,
      content_json: {
        ...prev.content_json,
        points: event.target.value
      }
    }));
  }, []);

  return (
    <Dialog
      fullScreen
      open={open}
      onClose={onClose}
      TransitionComponent={Transition}
      disableEnforceFocus
      disableAutoFocus
    >
      { questionData && (
        <div className="bg-light">
          <AppBar sx={{ boxShadow: 'none', borderBottom: '1px solid #eee' }} color="inherit">
            <Toolbar>
              <IconButton
                size="small"
                edge="start"
                color="inherit"
                onClick={onClose}
                aria-label="close"
              >
                <ChevronLeftIcon fontSize="small" />
              </IconButton>
              <div style={{ marginLeft: '5px', flex: 1 }}>
                <CustomSelect
                  options={questionTypeOptions}
                  defaultOption={
                    {
                      value: questionData.type,
                      label: t(questionData.type),
                      icon: questionTypes[questionData.type].icon
                    }
                  }
                  onChange={handleQuestionTypeChange}
                />
              </div>
              {/* Points Selector */}
              <Box sx={{ minWidth: 100, mr: 1 }}>
                <FormControl size="small" fullWidth>
                  <InputLabel>Điểm</InputLabel>
                  <Select
                    value={selectedPoints}
                    label="Điểm"
                    onChange={handlePointsChange}
                    sx={{ '.MuiInputBase-input': { p: '8px 16px !important' } }}
                  >
                    {pointsOptions.map((option) => (
                      <MenuItem
                        key={'points-' + option.value}
                        value={option.value}
                        sx={{ fontWeight: 500 }}
                      >
                        {option.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Box>
              {/* Time Limit Selector */}
              <Box sx={{ minWidth: 100, mr: 1 }}>
                <FormControl size="small" fullWidth>
                  <InputLabel>Thời gian</InputLabel>
                  <Select
                    value={selectedTimeLimit}
                    label="Thời gian"
                    onChange={handleTimeLimitChange}
                    sx={{ '.MuiInputBase-input': { p: '8px 16px !important' } }}
                  >
                    {timeOptions.map((option) => (
                      <MenuItem
                        key={'time-' + option.value}
                        value={option.value}
                        sx={{ fontWeight: 500 }}
                      >
                        {option.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Box>
              <Button
                disabled={submiting}
                onClick={debouncedSaveQuestion}
                variant="contained"
                color="secondary"
                startIcon={<SaveIcon fontSize="small"/>}
                sx={{ borderRadius: '8px' }}
              >
                Lưu <span className="d-none d-md-inline ms-1">câu hỏi</span>
              </Button>
            </Toolbar>
          </AppBar>
          <div className="question-editor-wrapper">
            <div className="question-editor-main">
              <div className="question-editor-body">
                { renderElementForm() }
              </div>
            </div>
          </div>
        </div>
      ) }
    </Dialog>
  );
}

export default React.memo(QuestionFormDialog);
