import React, { useRef, useState, useEffect, useCallback } from 'react';

import HtmlContent from '@/components/HtmlContent';
import CKEditor from '@/components/CKEditor';
import toast from "react-hot-toast";

import { find, filter, includes, flatMap, uniq, debounce } from 'lodash';
import { jsonToHtml, htmlToJson } from '@/components/questions/qlib/index.js';
import { optionBorderColors, gameNotes } from '@/constant';

const Blank = ({ contentJson, onChange }) => {
  console.log('Blank');
  const editorRef = useRef(null);
  const inputRef = useRef(null);
  const [isAddingDistractor, setIsAddingDistractor] = useState(false);
  const [editingDistractorId, setEditingDistractorId] = useState(null);
  const [inputValue, setInputValue] = useState('');

  const MAX_OPTIONS = 10;

  console.log(jsonToHtml('BLANK', contentJson));

  useEffect(() => {
    return () => {
      if (editorRef.current) {
        editorRef.current.destroy();
        editorRef.current = null;
      }
    }
  }, []);

  // Autofocus input when it appears
  useEffect(() => {
    if ((isAddingDistractor || editingDistractorId) && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isAddingDistractor, editingDistractorId]);

  const handleEditorReady = useCallback((editor) => {
    editorRef.current = editor;
    editor.on('dataReady', () => {
      editor._isContentReady = true;
      console.log('[CKEditor] dataReady: content is safe to get');
    });
  }, []);

  const handleEditorChange = useCallback(debounce(() => {
    const editor = editorRef.current;

    if (!editor) return;

    const rawContent = editor.getData();
    const newContentJson = htmlToJson('BLANK', rawContent);

    // Preserve existing distractors when content changes
    const preservedOptions = (contentJson.options || []).map(opt => ({
      ...opt,
      isCorrect: opt.isCorrect !== false
    }));

    onChange({
      ...contentJson,
      ...newContentJson,
      options: mergeOptionsWithDistractors(newContentJson.options || [], preservedOptions)
    });
  }, 200), [contentJson]);

  const mergeOptionsWithDistractors = (correctOptions, existingOptions) => {
    const merged = [...correctOptions.map(opt => ({ ...opt, isCorrect: true }))];

    // Add existing distractors
    existingOptions.forEach(existing => {
      if (!existing.isCorrect && !merged.find(m => m.content === existing.content)) {
        merged.push(existing);
      }
    });

    return merged;
  };

  const validateDistractor = (content) => {
    const trimmed = content.trim();

    if (!trimmed) {
      toast.error('Vui lòng nhập nội dung đáp án');
      return false;
    }

    const existingOptions = contentJson.options || [];

    // Check max options limit (only when adding new, not editing)
    if (!editingDistractorId && existingOptions.length >= MAX_OPTIONS) {
      toast.error(`Chỉ được phép tối đa ${MAX_OPTIONS} tùy chọn`);
      return false;
    }

    const isDuplicate = existingOptions.some(opt =>
      opt.content.trim().toLowerCase() === trimmed.toLowerCase() &&
      opt.id !== editingDistractorId
    );

    if (isDuplicate) {
      toast.error('Đáp án này đã tồn tại');
      return false;
    }

    return true;
  };

  const saveDistractor = useCallback(() => {
    if (!validateDistractor(inputValue)) return;

    const trimmedValue = inputValue.trim();
    const existingOptions = contentJson.options || [];

    if (editingDistractorId) {
      // Edit existing distractor
      const updatedOptions = existingOptions.map(opt =>
        opt.id === editingDistractorId
          ? { ...opt, content: trimmedValue }
          : opt
      );

      onChange({
        ...contentJson,
        options: updatedOptions
      });
    } else {
      // Add new distractor
      const newOption = {
        id: Date.now() + Math.random(),
        content: trimmedValue,
        isCorrect: false
      };

      onChange({
        ...contentJson,
        options: [...existingOptions, newOption]
      });
    }

    // Reset state
    setInputValue('');
    setIsAddingDistractor(false);
    setEditingDistractorId(null);
  }, [contentJson, inputValue, editingDistractorId, onChange]);

  const cancelEdit = useCallback(() => {
    setInputValue('');
    setIsAddingDistractor(false);
    setEditingDistractorId(null);
  }, []);

  const handleAddDistractorClick = useCallback(() => {
    setInputValue('');
    setIsAddingDistractor(true);
    setEditingDistractorId(null);
  }, []);

  const handleEditDistractor = useCallback((option) => {
    setInputValue(option.content);
    setEditingDistractorId(option.id);
    setIsAddingDistractor(false);
  }, []);

  const handleDeleteDistractor = useCallback((optionId) => {
    const newOptions = (contentJson.options || []).filter(opt => opt.id !== optionId);

    onChange({
      ...contentJson,
      options: newOptions
    });
  }, [contentJson, onChange]);

  /*const handleDeleteInEditMode = useCallback(() => {
    if (editingDistractorId) {
      handleDeleteDistractor(editingDistractorId);
      // Reset input state after deletion
      setInputValue('');
      setIsAddingDistractor(false);
      setEditingDistractorId(null);
    }
  }, [editingDistractorId, handleDeleteDistractor]);*/

  const handleInputKeyDown = useCallback((e) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      saveDistractor();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      cancelEdit();
    }
  }, [saveDistractor, cancelEdit]);

  const handleInputBlur = useCallback(() => {
    // Only save on blur if there's content
    if (inputValue.trim()) {
      saveDistractor();
    } else {
      cancelEdit();
    }
  }, [inputValue, saveDistractor, cancelEdit]);

  const getCorrectOptions = () => {
    return (contentJson.options || []).filter(opt => opt.isCorrect === true);
  };

  const getDistractorOptions = () => {
    return (contentJson.options || []).filter(opt => opt.isCorrect === false);
  };

  const renderDistractorInput = () => (
    <div className="distractor-input-container d-flex align-items-center gap-2">
      <input
        ref={inputRef}
        type="text"
        className="form-control form-control-sm"
        placeholder="Thêm tùy chọn..."
        value={inputValue}
        onChange={(e) => setInputValue(e.target.value)}
        onKeyDown={handleInputKeyDown}
        onBlur={handleInputBlur}
        style={{ minWidth: '200px' }}
      />
      {inputValue.trim() && (
        <>
          <button
            type="button"
            className="btn btn-sm btn-success"
            onClick={saveDistractor}
            title="Lưu đáp án"
          >
            <i className="bi bi-arrow-return-left"></i>
          </button>
          {/*<button
            type="button"
            className="btn btn-sm btn-secondary"
            onClick={cancelEdit}
            title="Hủy"
          >
            <i className="bi bi-x-lg"></i>
          </button>*/}
        </>
      )}
    </div>
  );

  const canAddMoreOptions = () => {
    const existingOptions = contentJson.options || [];
    return existingOptions.length < MAX_OPTIONS;
  };

  const renderAddButton = () => {
    if (!canAddMoreOptions()) {
      return (
        <div className="text-center">
          <button
            id="btn-add-distractor"
            className="btn btn-outline-secondary cursor-not-allowed"
            style={{ padding: '3px 0.75rem', borderWidth: '2px' }}
            title={`Đã đạt giới hạn tối đa ${MAX_OPTIONS} tùy chọn`}
          >
            <i className="bi bi-plus-lg me-1"></i> Thêm
          </button>
        </div>
      );
    }

    return (
      <button
        id="btn-add-distractor"
        className="btn btn-outline-secondary"
        style={{ padding: '3px 0.75rem', borderWidth: '2px' }}
        onClick={handleAddDistractorClick}
      >
        <i className="bi bi-plus-lg me-1"></i> Thêm
      </button>
    );
  };

  return (
    <div className="question-editor" style={{ gridTemplateRows: 'auto 1fr' }}>
      <div className="q-content" style={{ minHeight: '350px' }}>
        <CKEditor
          initData={jsonToHtml('BLANK', contentJson)}
          name="question_blank"
          onInstanceReady={handleEditorReady}
          onChange={handleEditorChange}
        />
      </div>
      <div className="q-answer-blank position-relative">
        { contentJson?.content ? (
          <HtmlContent html={ contentJson.content } type="BLANK" />
        ) : (
          <p className="q-placeholder small">--- Chế độ xem ---</p>
        )}
        { contentJson?.options?.length > 0 && (
          <>
            <div className="w-100 d-flex gap-4 align-items-center overflow-auto scrollbar-hide flex-row">
              <div className="d-flex h-100 w-100 justify-content-end">
                <div className="d-flex flex-column gap-y-3" style={{ minWidth: '150px' }}>
                  <p className="fs-16 fw-medium mb-2 text-left" style={{ color: '#fffc' }}>Tùy chọn đúng</p>
                  <div className="q-options-blank">
                    {getCorrectOptions().map((option, index) => (
                      <div key={index} className="q-option-blank" style={{borderColor: optionBorderColors[index]}}>
                        <HtmlContent html={option.content}/>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
              <div className="d-flex h-100 w-100">
                <div className="d-flex flex-column gap-y-3">
                  <p className="fs-16 fw-medium mb-2 text-left" style={{ color: '#fffc' }}>
                    Tùy chọn không đúng
                    <i className="bi bi-question-circle-fill ms-2" data-bs-toggle="tooltip" data-bs-placement="top" title={gameNotes.BLANK}></i>
                  </p>
                  <div className="q-options-blank">
                    {getDistractorOptions().map((option, index) => (
                      <div key={option.id} className="q-option-blank distractor-option"
                           style={{ border: 'none' }}
                           onClick={() => handleEditDistractor(option)}
                           title="Click để chỉnh sửa"
                      >
                        {editingDistractorId === option.id ? (
                          renderDistractorInput()
                        ) : (
                          <div className="d-flex align-items-center justify-content-between distractor-item-wrapper position-relative">
                            <div className="flex-grow-1 cursor-pointer distractor-content">
                              <HtmlContent html={option.content} type="BLANK"/>
                            </div>
                            <button
                              type="button"
                              className="btn btn-sm btn-outline-danger ms-2 btn-delete-distractor position-absolute"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDeleteDistractor(option.id);
                              }}
                              title="Xóa đáp án"
                            >
                              <i className="bi bi-x-lg"></i>
                            </button>
                          </div>
                        )}
                      </div>
                    ))}
                    {isAddingDistractor ? renderDistractorInput() : renderAddButton()}
                  </div>
                </div>
              </div>
            </div>
            <span className="position-absolute game-note-2">
              Học sinh sẽ thấy các tùy chọn trả lời được xáo trộn
            </span>
          </>
        )}
      </div>
    </div>
  );
}

export default Blank;
