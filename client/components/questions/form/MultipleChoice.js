import React, { useState, useEffect, useCallback } from 'react';

import toast from "react-hot-toast";

import EditorDialog from '@/components/EditorDialog';
import HtmlContent from '@/components/HtmlContent';

import { optionBackgrounds, mcqPerOption, mcpMinOption } from "@/constant";
import { checkOverflow } from "@/utils/helpers";

const MultipleChoice = ({ contentJson, onChange }) => {
  console.log('MultipleChoice');
  const [openEditorDialog, setOpenEditorDialog] = useState(false);
  const [textValue, setTextValue] = useState('');
  const [elName, setElName] = useState('');

  useEffect(() => {
    checkOverflow('.q-content, .q-option-content');
  }, [contentJson]);

  const openEditor = useCallback((content, name) => {
    setTextValue(content);
    setElName(name);
    setOpenEditorDialog(true);
  }, []);

  const handleContent = useCallback((content) => {
    let newQuestionJson = contentJson || {};

    if (elName === 'question_content') {
      newQuestionJson = { ...newQuestionJson, content };
    } else if (elName.startsWith('option_content-')) {
      const index = parseInt(elName.split('-')[1], 10);
      let tmpOptions = [...newQuestionJson.options];

      tmpOptions[index] = { ...tmpOptions[index], content };

      newQuestionJson = { ...newQuestionJson, options: tmpOptions };
    }

    onChange(newQuestionJson);
  }, [elName, onChange]);

  const addOption = useCallback(() => {
    const existing = contentJson.options || [];

    if (existing.length >= mcqPerOption) {
      toast.error(`Chỉ được tối đa ${mcqPerOption} tùy chọn.`, {
        duration: 5000,
        position: 'top-center',
      });

      return;
    }

    const newOptions = [
      ...existing,
      { content: '', isCorrect: false }
    ];

    onChange({
      ...contentJson,
      options: newOptions,
    });
  }, [contentJson]);

  const delOption = useCallback((index) => {
    const existing = contentJson.options || [];

    if (existing.length <= mcpMinOption) {
      toast.error(`Phải có ít nhất ${mcpMinOption} tùy chọn.`, {
        duration: 5000,
        position: 'top-center',
      });

      return;
    }

    const newOptions = contentJson.options.filter((_, i) => i !== index);
    const newAnswers = (contentJson.answer || []).filter(value => value !== index);

    onChange({
      ...contentJson,
      options: newOptions,
      answer: newAnswers
    });
  }, [contentJson]);

  const handleIsCorrect = useCallback((index) => {
    const newOptions = contentJson.options.map((option, i) =>
      i === index ? { ...option, isCorrect: !option.isCorrect } : option
    );

    const newAnswers = newOptions[index].isCorrect
      ? [...new Set([...(contentJson.answer || []), index])]
      : (contentJson.answer || []).filter(value => value !== index);

    onChange({
      ...contentJson,
      options: newOptions,
      answer: newAnswers
    });
  }, [contentJson]);

  return (
    <>
      <div className="question-editor">
        <div
          className="q-content"
          onClick={() => openEditor(contentJson.content || '', 'question_content')}
        >
          { contentJson.content ? <HtmlContent html={ contentJson.content } /> : <p className="q-placeholder">Nhập câu hỏi vào đây</p> }
        </div>
        <div className="q-answer-quizs">
          { contentJson?.options?.length > 0 && (
            <div className="q-option-container">
              {
                contentJson.options.map((option, index) => (
                  <div
                    key={index}
                    className="q-option-item"
                    style={{ backgroundColor: optionBackgrounds[index] }}
                  >
                    <div className="q-option-action d-flex align-items-center justify-content-between">
                      <button
                        type="button"
                        className="btn-icon btn-icon-danger"
                        data-toggle="tooltip"
                        data-placement="top"
                        title="Xóa"
                        onClick={() => delOption(index)}
                      >
                        <i className="bi bi-trash3-fill"></i>
                      </button>
                      <button
                        type="button"
                        className={`btn-icon btn-icon-success rounded-circle ${option.isCorrect ? 'active' : ''}`}
                        data-toggle="tooltip"
                        data-placement="top"
                        title="Đánh dấu là câu trả lời đúng"
                        onClick={() => handleIsCorrect(index)}
                      >
                        <i className="bi bi-check-lg"></i>
                      </button>
                    </div>
                    <div className="q-option-content" onClick={() => openEditor(option.content || '', `option_content-${index}`)}>
                      { option.content ? <HtmlContent html={ option.content } /> : <p className="q-placeholder">Nhập đáp án ở đây</p> }
                    </div>
                  </div>
                ))
              }
            </div>
          ) }
          <button
            type="button"
            className="btn-icon btn-add-option fw-bold"
            data-toggle="tooltip"
            data-placement="top"
            title="Thêm tùy chọn"
            onClick={addOption}
          >
            <i className="bi bi-plus"></i> <span>Thêm tùy chọn</span>
          </button>
        </div>
      </div>
      <EditorDialog
        open={openEditorDialog}
        onClose={() => setOpenEditorDialog(false)}
        onDone={handleContent}
        textValue={textValue}
        elName={elName}
      />
    </>
  );
}

export default MultipleChoice;
