import Chip from '@mui/material/Chip';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CancelIcon from '@mui/icons-material/Cancel';

const AnswerIcon = ({ isCorrect, showAnswer = false }) => {
  if (!showAnswer) {
    return (
      <i className="bi bi-circle me-2 text-body text-opacity-75" />
    );
  }

  return isCorrect ? (
    <i className="bi bi-check-circle-fill me-2 text-success" />
  ) : (
    <i className="bi bi-x-circle me-2 text-danger" />
  );
};

const MultipleChoice = ({ question, showAnswer, questionLogs }) => {
    return (
        <div className="bg-white fs-16 p-2">
            <div className="mb-4 box-content" dangerouslySetInnerHTML={{ __html: question?.content_json?.content || '' }} />
            <div className="row row-gap-3">
                {question?.content_json?.options.map((option, index) => {
                    const isSelected = questionLogs?.answer && questionLogs.answer.includes(index);

                    let bgStyle = '';

                    if (isSelected) {
                        bgStyle = option.isCorrect ? 'bg-success bg-opacity-10' : 'bg-danger bg-opacity-10';
                    }

                    return (
                        <div key={index} className={`col-12 ${ questionLogs != null ? '' : 'col-md-6' }`}>
                            <div className={`d-flex align-items-center overflow-auto p-1 ${bgStyle}`}>
                                <AnswerIcon isCorrect={!!option.isCorrect} showAnswer={showAnswer} />
                                <div className="box-content" dangerouslySetInnerHTML={{ __html: option.content || '' }} />
                            </div>
                            { questionLogs?.optionCount && (
                                <Chip
                                    className="ms-4"
                                    label={
                                        <>
                                          <strong className="text-dark">{questionLogs.optionCount[index] ?? 0}</strong> học sinh lựa chọn
                                        </>
                                    }
                                    icon={option.isCorrect ? <CheckCircleIcon fontSize="small" /> : <CancelIcon fontSize="small" />}
                                    color={option.isCorrect ? "success" : "error"}
                                    size="small"
                                    title="Số lần hs chọn"
                                />
                            ) }
                        </div>
                    );
                })}
            </div>
        </div>
    )
}

export default MultipleChoice;
