const Blank = ({ question }) => {
  const contentJson = question?.content_json || {};
  const { content = '', options = [], answer = [] } = contentJson;

  return (
    <div className="bg-white fs-16 p-2">
      <div className="box-content mb-3" dangerouslySetInnerHTML={{ __html: content }} />
      <div className="text-muted fs-14 mb-3">Câu trả lời</div>
      <div className="">
        {answer.map((ans, idx) => {
          const selected = (ans.optionId || []).map(id => {
            return options.find(o => o.id === id);
          });

          const label = String.fromCharCode(97 + idx);
          return (
            <div key={idx} className="fs-16 mb-1">
              <span className="fw-medium"><i className="bi bi-check-lg text-success"></i> {label}. </span>
              {selected.map((option, i) => (
                <span key={option.id}>
                  <span>{option.content}</span>
                  {i < selected.length - 1 && <span className="small text-muted">&nbsp;hoặc&nbsp;</span>}
                </span>
              ))}
            </div>
          );
        })}
      </div>
    </div>
  )
}

export default Blank;
