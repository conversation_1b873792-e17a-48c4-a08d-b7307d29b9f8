import React, { useState, useCallback, useMemo } from 'react';

import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import IconButton from '@mui/material/IconButton';
import CloseIcon from '@mui/icons-material/Close';

import { useRouter } from 'next/navigation';

// third party
import { useTranslations } from "next-intl";
import { useSearchParams } from "next/navigation";
import { useSelector } from "react-redux";

import { questionTypes } from "@/constant";

const QuestionTypeDialog = ({ createQuestion, quiz }) => {
  console.log('QuestionTypeDialog');
  const t = useTranslations("Common");
  const homework = useSearchParams().get("homework");
  const router = useRouter();
  const [open, setOpen] = useState(false);
  const { user } = useSelector((state) => state.auth);

  const handleCloseDialog = useCallback(() => {
    setOpen(false);
  }, []);

  const handleOpenDialog = useCallback(() => {
    setOpen(true);
  }, []);

  const handleQuestionDialog = useCallback((initData) => {
    setOpen(false);
    createQuestion(initData);
  }, [createQuestion]);

  const questionCount = useMemo(
    () => quiz?.questions_count || quiz?.questions?.length || 0,
    [quiz]
  );

  return (
    <>
      {!homework && user && user?.role?.name === 'teacher' && questionCount > 0 && (
        <button
          className="btn btn-outline-primary2 btn-sm ms-3"
          onClick={() => router.push(`/dashboard/quiz/${quiz.id}/homework`)}
          title="Giao cho học sinh làm bài tập trên lớp hoặc làm bài tập về nhà"
        >
          <i className="bi bi-send me-2"></i>
          <strong>Giao bài</strong>
        </button>
      )}
      <button
        className="btn btn-primary2 btn-sm"
        onClick={handleOpenDialog}
        title="Thêm câu hỏi"
      >
        <i className="bi bi-plus-circle me-2"></i>
        <strong>Thêm câu hỏi</strong>
      </button>
      <Dialog
        fullWidth
        maxWidth={'sm'}
        open={open}
        onClose={handleCloseDialog}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <IconButton
          aria-label="close"
          onClick={handleCloseDialog}
          sx={{
            position: 'absolute',
            right: 8,
            top: 8,
            color: (theme) => theme.palette.grey[500],
          }}
        >
          <CloseIcon />
        </IconButton>
        <DialogTitle className="h5" id="alert-dialog-title">
          Lựa chọn dạng câu hỏi:
        </DialogTitle>
        <DialogContent>
          <div className="btn-group">
            {Object.entries(questionTypes).map(([keyType, questionType]) => {
              return (
                <button
                  key={keyType}
                  className="btn btn-icon-link"
                  onClick={() => handleQuestionDialog({ type: keyType, content_json: questionType.initQue })}
                >
                  { questionType.icon && (<span className="me-2 icon-link" dangerouslySetInnerHTML={{ __html: questionType.icon }} />) }
                  { t(keyType) }
                </button>
                );
            })}
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}

export default React.memo(QuestionTypeDialog);
