import React, { useCallback } from "react";

import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { renderTimeViewClock } from '@mui/x-date-pickers/timeViewRenderers';

const DateTimePickerField = ({ label, value, fieldName, setFieldValue }) => {
  const handleChange = useCallback(
    (time) => {
      setFieldValue(fieldName, time);
    },
    [setFieldValue]
  );

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs} adapterLocale="vi">
      <DateTimePicker
        className="w-100"
        label={label}
        value={value}
        onChange={handleChange}
        viewRenderers={{
          hours: renderTimeViewClock,
          minutes: renderTimeViewClock,
          seconds: renderTimeViewClock,
        }}
      />
    </LocalizationProvider>
  );
};

export default React.memo(DateTimePickerField);
