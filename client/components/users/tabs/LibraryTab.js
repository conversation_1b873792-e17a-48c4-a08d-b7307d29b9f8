"use client";

import { memo, useEffect, useState, useCallback } from "react";
import { useDispatch } from "react-redux";
import Link from "next/link";

import Tabs from "@mui/material/Tabs";
import Tab from "@mui/material/Tab";
import TextField from '@mui/material/TextField';
import Skeleton from "@mui/material/Skeleton";
import CircularProgress from "@mui/material/CircularProgress";

import MoreVertIcon from '@mui/icons-material/MoreVert';
import FolderIcon from "@mui/icons-material/Folder";

import NoDataOverlay from "@/components/NoDataOverlay";
import Dropdown from "@/components/Dropdown";
import QuizCard from "@/components/quizzes/QuizCard"
import InfiniteScroll from "@/components/InfiniteScroll";
import SaveToCollectionModal from "@/components/quizzes/SaveToCollectionModal";

import toast from "react-hot-toast";

import { perPage, orderOptions } from "@/constant";

import { fetchQuizzes, removeQuiz } from "@/actions/quizAction";

import { userApiSlice } from "@/slices/features/userApiSlice";

import { useConfirm } from "@/contexts/ConfirmContext";

const LibraryTab = () => {
  console.log('LibraryTab');
  const dispatch = useDispatch();
  const confirmDelete = useConfirm();

  const [tabValue, setTabValue] = useState("all");
  const [orderValue, setOrderValue] = useState("latest");

  const [quizzes, setQuizzes] = useState(null);
  const [selectedQuiz, setSelectedQuiz] = useState(null);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(false);

  const [openSaveModal, setOpenSaveModal] = useState(false);

  const handleOpenSaveModal = useCallback((quiz) => {
    setSelectedQuiz(quiz);
    setOpenSaveModal(true);
  }, []);

  const handleCloseSaveModal = useCallback(() => {
    setOpenSaveModal(false);
    setSelectedQuiz(null);
  }, []);

  const fetchData = async (signal) => {
    try {
      const res =  await fetchQuizzes(
        {
          page,
          limit: perPage,
          orderBy: orderValue,
          personal: 1,
          screen: 'library',
        },
        signal
      );

      let newData = res.data;
      if (page > 1) {
        newData = [...(quizzes || []), ...newData];
      }
      setQuizzes(newData);
      setHasMore(res.data.length >= perPage);
    } catch (error) {
      if (error?.message !== "canceled") {
        console.error('Error fetching data:', error);
      }
    }
  };

  useEffect(() => {
    if (page > 0) {
      const controller = new AbortController();
      const { signal } = controller;

      fetchData(signal);

      return () => controller.abort();
    }
  }, [page, orderValue]);

  const handleChangeTab = useCallback((event, newValue) => {
    setTabValue(newValue);
  }, []);

  const handleChangeOrder = useCallback((event) => {
    if (event?.target.value) {
      setPage(1);
      setQuizzes(null);
      setOrderValue(event.target.value);
    }
  }, []);

  const handleSave = useCallback(() => {
    try {
      dispatch(
        userApiSlice.util.invalidateTags([{ type: "User", id: "MYLIBRARY" }])
      );
    } catch (error) {
      console.error(error);
    }
  }, [])

  const deleteQuiz = useCallback(async (quiz) => {
    const ans = await confirmDelete(
      `Xóa: ${quiz.title}?`,
      "Sau khi xóa, nội dung đã xóa sẽ không còn xuất hiện trong thư viện của bạn, bao gồm cả bộ sưu tập, trong báo cáo hoặc kết quả tìm kiếm của Quizizz"
    );
    if (ans) {
      try {
        const res = await removeQuiz(quiz.id);

        if (res?.status == 200) {
          setQuizzes((prevQuizzes) =>
            prevQuizzes.filter((q) => q.id !== quiz.id)
          );

          toast.success(res.message || "Xóa thành công!", {
            position: "top-right",
          });
        }
      } catch (error) {
        toast.error(error.message || "Xóa quiz thất bại");
      }
    }
  }, [confirmDelete]);

  return (
    <>
      <div className="row justify-content-between align-items-center">
        <div className="col-12 col-sm-6 mb-1">
          <Tabs
            value={tabValue}
            onChange={handleChangeTab}
            textColor="secondary"
            indicatorColor="secondary"
            aria-label="secondary tabs example"
          >
            <Tab value="all" label="Tất cả" />
          </Tabs>
        </div>
        <div className="col-12 col-sm-6 mb-1 text-end">
          <TextField
            select
            size="small"
            value={orderValue}
            onChange={handleChangeOrder}
            slotProps={{
              select: {
                native: true,
              }
            }}
          >
            {orderOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </TextField>
        </div>
      </div>
      {quizzes == null ? (
        Array.from({ length: 3 }, (_, index) => (
          <Skeleton
            key={`skeleton-${index}`}
            variant="rounded"
            height={150}
            sx={{ mt: "20px", borderRadius: "4px" }}
          />
        ))
      ) : quizzes.length ? (
        <InfiniteScroll
          loader={<CircularProgress />}
          fetchMore={() => setPage((prev) => prev + 1)}
          hasMore={hasMore}
          endMessage={<p className="text-center">Hết!</p>}
        >
          {quizzes.map((quiz, index) => (
            <QuizCard
              key={quiz.id}
              quiz={quiz}
              actionComponent={
                <div className="position-absolute top-0 end-0 mt-1 me-1">
                  <Dropdown
                    renderToggle={({ onClick }) => (
                      <button
                        type="button"
                        onClick={onClick}
                        className="btn btn-link btn-dropdown-small dropdown-toggle"
                      >
                        <MoreVertIcon />
                      </button>
                    )}
                    placement="bottom-end"
                    renderMenu={() => (
                      <div className="dropdown-menu">
                        <Link
                          href={`/dashboard/quiz/${quiz.id}/edit`}
                          className="dropdown-item fs-14"
                        >
                          <i className="bi bi-pencil-square me-2"></i> Chỉnh sửa
                        </Link>
                        <a
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            handleOpenSaveModal(quiz);
                          }}
                          className="dropdown-item fs-14"
                          href="#"
                        >
                          { quiz?.collections_count > 0 ? (
                              <span><i className="bi bi-folder-fill me-2" style={{ color: '#ffa100' }}></i> Đã lưu</span>
                            ) : (
                              <span><i className="bi bi-folder me-2"></i> Lưu</span>
                            )
                          }
                        </a>
                        <hr className="dropdown-divider" />
                        <a
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            deleteQuiz(quiz);
                          }}
                          className="dropdown-item fs-14 text-danger"
                          href="#"
                        >
                          <i className="bi bi-trash3 me-2"></i> Xóa
                        </a>
                      </div>
                    )}
                  />
                </div>
              }
            />
          ))}
        </InfiniteScroll>
      ) : (
        <NoDataOverlay />
      )}
      {openSaveModal && selectedQuiz && (
        <SaveToCollectionModal
          open={openSaveModal}
          handleClose={handleCloseSaveModal}
          quiz={selectedQuiz}
          onSuccess={handleSave}
        />
      )}
    </>
  );
};

export default memo(LibraryTab);
