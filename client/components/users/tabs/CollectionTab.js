"use client";

import { memo, useEffect, useState, useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import Link from "next/link";

import Card from '@mui/material/Card';
import TextField from '@mui/material/TextField';
import Skeleton from "@mui/material/Skeleton";
import CircularProgress from "@mui/material/CircularProgress";

import MoreVertIcon from '@mui/icons-material/MoreVert';
import FolderIcon from "@mui/icons-material/Folder";

import Dropdown from "@/components/Dropdown";
import NoDataOverlay from "@/components/NoDataOverlay";
import QuizCard from "@/components/quizzes/QuizCard";
import InfiniteScroll from "@/components/InfiniteScroll";
import SaveToCollectionModal from "@/components/quizzes/SaveToCollectionModal";

import toast from "react-hot-toast";

import { perPage, orderOptions } from "@/constant";

import {
  deleteCollection,
  fetchCollection,
  removeQuizFromCollection
} from "@/actions/collectionAction";

import { userApiSlice } from "@/slices/features/userApiSlice";

import { useConfirm } from "@/contexts/ConfirmContext";

const CollectionTab = ({ collection, onEdit, onDelete }) => {
  console.log('CollectionTab');
  const dispatch = useDispatch();
  const currentUser = useSelector((state) => state.auth.user);
  const confirmDelete = useConfirm();

  const [orderValue, setOrderValue] = useState("latest");

  const [quizzes, setQuizzes] = useState(null);
  const [selectedQuiz, setSelectedQuiz] = useState(null);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(false);

  const [openSaveModal, setOpenSaveModal] = useState(false);

  const handleOpenSaveModal = useCallback((quiz) => {
    setSelectedQuiz(quiz);
    setOpenSaveModal(true);
  }, []);

  const handleCloseSaveModal = useCallback(() => {
    setOpenSaveModal(false);
    setSelectedQuiz(null);
  }, []);

  const fetchData = async (coll, signal) => {
    try {
      const res =  await fetchCollection(
        coll.id,
        {
          page,
          limit: perPage,
          orderBy: orderValue,
        },
        signal
      );

      const newData = res.data;

      if (newData) {
        let tableContents = newData?.table_contents || [];

        if (page > 1) {
          tableContents = [...(quizzes || []), ...tableContents];
        }

        setQuizzes(tableContents);
        setHasMore(tableContents.length >= perPage);
      } else {
        throw new Error("Đã xảy ra lỗi.");
      }
    } catch (error) {
      if (error?.message !== "canceled") {
        console.error('Error fetching data:', error);
      }
    }
  };

  useEffect(() => {
    if (page > 0) {
      const controller = new AbortController();
      const { signal } = controller;

      fetchData(collection, signal);

      return () => controller.abort();
    }
  }, [collection, page, orderValue]);

  const handleChangeOrder = useCallback((event) => {
    if (event?.target.value) {
      setPage(1);
      setQuizzes(null);
      setOrderValue(event.target.value);
    }
  }, []);

  const handleSave = useCallback(() => {
    try {
      dispatch(
        userApiSlice.util.invalidateTags([{ type: "User", id: "MYLIBRARY" }])
      );
    } catch (error) {
      console.error(error);
    }
  }, [])

  const removeCollection = useCallback(async (coll) => {
    const ans = await confirmDelete(
      `Xóa: ${coll.title}?`,
      "Gỡ bỏ bộ sưu tập này sẽ không làm mất đi danh sách đề thi đã tạo. Bạn vẫn có thể tìm thấy trong thư viện của mình."
    );
    if (ans) {
      try {
        await deleteCollection(coll.id);

        onDelete(coll);

        toast.success("Xóa bộ sưu tập thành công");
      } catch (error) {
        toast.error("Không thể xóa bộ sưu tập");
      }
    }
  }, [confirmDelete]);

  const deleteQuiz = useCallback(async (quiz) => {
    const ans = await confirmDelete(
      `Gỡ bỏ: ${quiz.title}?`,
      "Gỡ bỏ này khỏi bộ sưu tập sẽ không làm mất đi nội dung đã tạo. Bạn vẫn có thể tìm thấy trong thư viện của mình."
    );

    if (ans) {
      try {
        await removeQuizFromCollection(collection.id, quiz.id);

        setQuizzes((prevQuizzes) =>
          prevQuizzes.filter((q) => q.id !== quiz.id)
        );

        handleSave();

        toast.success("Đã gỡ bỏ khỏi bộ sưu tập!", {
          position: "top-right",
        });
      } catch (error) {
        toast.error(error.message || "Gỡ thất bại");
      }
    }
  }, [confirmDelete]);
  
  if (!collection) return <p className="text-center mt-3"><CircularProgress /></p>;

  return (
    <>
      <Card style={{ overflow: 'visible' }} className="mb-3 p-2">
        <div className="d-flex justify-content-between align-items-center">
          <div className="d-flex align-items-center">
            <FolderIcon sx={{ color: '#ffa100', mr: 1 }} />
            <h5 className="fw-medium m-0">{collection.title}</h5>
          </div>
          <Dropdown
            renderToggle={({ onClick }) => (
              <button
                type="button"
                onClick={onClick}
                className="btn btn-link btn-dropdown-small dropdown-toggle"
              >
                <MoreVertIcon />
              </button>
            )}
            placement="bottom-end"
            renderMenu={() => (
              <div className="dropdown-menu">
                <a
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    onEdit(collection);
                  }}
                  className="dropdown-item fs-14"
                  href="#"
                >
                  <i className="bi bi-pencil-square me-2"></i> Chỉnh sửa
                </a>
                <a
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    removeCollection(collection);
                  }}
                  className="dropdown-item fs-14 text-danger"
                  href="#"
                >
                  <i className="bi bi-trash3 me-2"></i> Xóa
                </a>
              </div>
            )}
          />
        </div>
      </Card>
      <div className="text-end mb-2">
        <TextField
          select
          size="small"
          value={orderValue}
          onChange={handleChangeOrder}
          slotProps={{
            select: {
              native: true,
            }
          }}
        >
          {orderOptions.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </TextField>
      </div>
      {quizzes == null ? (
        Array.from({ length: 3 }, (_, index) => (
          <Skeleton
            key={`skeleton-${index}`}
            variant="rounded"
            height={150}
            sx={{ mt: "20px", borderRadius: "4px" }}
          />
        ))
      ) : quizzes.length ? (
        <InfiniteScroll
          loader={<CircularProgress />}
          fetchMore={() => setPage((prev) => prev + 1)}
          hasMore={hasMore}
          endMessage={<p className="text-center">Hết!</p>}
        >
          {quizzes.map((quiz, index) => (
            <QuizCard
              key={quiz.id}
              quiz={quiz}
              actionComponent={
                <div className="position-absolute top-0 end-0 mt-1 me-1">
                  <Dropdown
                    renderToggle={({ onClick }) => (
                      <button
                        type="button"
                        onClick={onClick}
                        className="btn btn-link btn-dropdown-small dropdown-toggle"
                      >
                        <MoreVertIcon />
                      </button>
                    )}
                    placement="bottom-end"
                    renderMenu={() => (
                      <div className="dropdown-menu">
                        {currentUser?.id === quiz.editor_id && (
                          <Link
                            href={`/dashboard/quiz/${quiz.id}/edit`}
                            className="dropdown-item fs-14"
                          >
                            <i className="bi bi-pencil-square me-2"></i> Chỉnh sửa
                          </Link>
                        )}
                        <a
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            handleOpenSaveModal(quiz);
                          }}
                          className="dropdown-item fs-14"
                          href="#"
                        >
                          <span><i className="bi bi-folder-fill me-2" style={{ color: '#ffa100' }}></i> Đã lưu</span>
                        </a>
                        <hr className="dropdown-divider" />
                        <a
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            deleteQuiz(quiz);
                          }}
                          className="dropdown-item fs-14 text-danger"
                          href="#"
                        >
                          <i className="bi bi-x-circle me-2"></i> Gỡ bỏ
                        </a>
                      </div>
                    )}
                  />
                </div>
              }
            />
          ))}
        </InfiniteScroll>
      ) : (
        <NoDataOverlay message="Bộ sưu tập này chưa có bài quiz nào!" />
      )}
      {openSaveModal && selectedQuiz && (
        <SaveToCollectionModal
          open={openSaveModal}
          handleClose={handleCloseSaveModal}
          quiz={selectedQuiz}
          onSuccess={handleSave}
        />
      )}
    </>
  );
};

export default memo(CollectionTab);
