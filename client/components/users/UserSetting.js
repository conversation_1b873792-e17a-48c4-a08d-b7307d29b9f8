"use client";

import React from "react";
import { useSelector } from "react-redux";
import { setUser } from "@/slices/authSlice";
import { useDispatch } from "react-redux";
import * as Yup from "yup";
import { Formik } from "formik";
import toast from "react-hot-toast";

// MUI components
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import Container from "@mui/material/Container";
import Divider from "@mui/material/Divider";
import Paper from "@mui/material/Paper";
import Stack from "@mui/material/Stack";
import TextField from "@mui/material/TextField";
import Typography from "@mui/material/Typography";

// MUI icons
import AccountCircleIcon from "@mui/icons-material/AccountCircle";
import DeleteIcon from "@mui/icons-material/Delete";
import LockIcon from "@mui/icons-material/Lock";

// Actions
import { updatePassword, updateProfile } from "@/actions/userAction";

const BUTTON_STYLE = {
  mt: 2,
  backgroundColor: "rgba(136, 84, 192)",
  "&:hover": { opacity: "0.8" },
};

const BORDER_BOTTOM = { borderBottom: "3px solid #51136b" };

const UserSetting = () => {
  const dispatch = useDispatch();
  const { user } = useSelector((state) => state.auth);

  const UserNameValidationSchema = Yup.object().shape({
    name: Yup.string()
      .min(3, "Tên phải có ít nhất 3 ký tự")
      .max(30, "Tên không được vượt quá 30 ký tự")
      .required("Tên không được để trống"),
  });

  const passwordValidationSchema = Yup.object({
    currentPassword: Yup.string().required("Mật khẩu hiện tại không được để trống"),
    newPassword: Yup.string()
      .min(8, "Mật khẩu mới phải ít nhất 8 ký tự")
      .required("Mật khẩu mới không được để trống"),
    confirmPassword: Yup.string()
      .oneOf([Yup.ref('newPassword'), null], 'Mật khẩu xác nhận phải khớp')
      .required('Vui lòng xác nhận mật khẩu mới')
  });

  const handleChangeName = async (values, { setSubmitting }) => {
    if (!user) return;

    try {
      const response = await updateProfile({
        name: values.name,
        email: user.email
      });
      if (response && response.data) {
        toast.success("Cập nhật tên thành công!");
        dispatch(setUser({ user: { ...user, name: values.name }, persist: true }));
      } else {
        toast.error("Cập nhật thất bại");
      }
    } catch (error) {
      toast.error("Cập nhật thất bại");
    } finally {
      setSubmitting(false);
    }
  };

  const handleChangePassword = async (values, { setSubmitting, resetForm }) => {
    try {
      const response = await updatePassword({
        current_password: values.currentPassword,
        password: values.newPassword,
        password_confirmation: values.confirmPassword,
      });
      if (response && response.status === 200) {
        toast.success("Đổi mật khẩu thành công!");
        resetForm();
      } else {
        toast.error("Đổi mật khẩu thất bại");
      }
    } catch (error) {
      if (error.status === 422) {
        toast.error("Mật khẩu hiện tại không đúng");
      } else {
        toast.error("Đổi mật khẩu thất bại");
      }
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <Container maxWidth="sm">
      <Paper elevation={3} sx={{ p: 3, mt: 5, borderTop: "5px solid #000" }}>
        <Typography variant="h2" sx={{ mb: 3 }}>
          Cài đặt
        </Typography>

        {/* UserName Settings */}
        <Formik
          initialValues={{ name: user?.name || '', email: user?.email || '' }}
          validationSchema={UserNameValidationSchema}
          onSubmit={handleChangeName}
          enableReinitialize
        >
          {formik => (
            <Box component="form" onSubmit={formik.handleSubmit} noValidate>
              <Typography variant="h5" sx={{ mb: 2, display: "flex", alignItems: "center", gap: 1 }}>
                <AccountCircleIcon /> Tài khoản
              </Typography>
              <Divider sx={{ mb: 2 }} />
              <Stack spacing={3}>
                <TextField
                  label="Email"
                  fullWidth
                  size="small"
                  value={formik.values.email}
                  disabled
                />
                <TextField
                  label="Họ và tên"
                  name="name"
                  fullWidth
                  size="small"
                  value={formik.values.name}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.name && Boolean(formik.errors.name)}
                  helperText={formik.touched.name && formik.errors.name}
                />
                <Button
                  fullWidth
                  variant="contained"
                  type="submit"
                  disabled={formik.isSubmitting}
                  sx={[BUTTON_STYLE, BORDER_BOTTOM]}
                >
                  Lưu thay đổi
                </Button>
              </Stack>
            </Box>
          )}
        </Formik>

        {/* Password Settings */}
        <Formik
          initialValues={{ currentPassword: "", newPassword: "", confirmPassword: "" }}
          validationSchema={passwordValidationSchema}
          onSubmit={handleChangePassword}
        >
          {formik => (
            <Box component="form" onSubmit={formik.handleSubmit} noValidate sx={{ mt: 5 }}>
              <Typography variant="h5" sx={{ mb: 2, display: "flex", alignItems: "center", gap: 1 }}>
                <LockIcon /> Mật khẩu
              </Typography>
              <Divider sx={{ mb: 2 }} />
              <Stack spacing={3}>
                <TextField
                  label="Mật khẩu hiện tại"
                  name="currentPassword"
                  type="password"
                  fullWidth
                  size="small"
                  value={formik.values.currentPassword}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.currentPassword && Boolean(formik.errors.currentPassword)}
                  helperText={formik.touched.currentPassword && formik.errors.currentPassword}
                />
                <TextField
                  label="Mật khẩu mới"
                  name="newPassword"
                  type="password"
                  fullWidth
                  size="small"
                  value={formik.values.newPassword}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.newPassword && Boolean(formik.errors.newPassword)}
                  helperText={formik.touched.newPassword && formik.errors.newPassword}
                />
                <TextField
                  label="Xác nhận mật khẩu mới"
                  name="confirmPassword"
                  type="password"
                  fullWidth
                  size="small"
                  value={formik.values.confirmPassword}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.confirmPassword && Boolean(formik.errors.confirmPassword)}
                  helperText={formik.touched.confirmPassword && formik.errors.confirmPassword}
                />
                <Button
                  fullWidth
                  variant="contained"
                  type="submit"
                  disabled={formik.isSubmitting}
                  sx={[BUTTON_STYLE, BORDER_BOTTOM]}
                >
                  Cập nhật mật khẩu
                </Button>
              </Stack>
            </Box>
          )}
        </Formik>

        {/* Delete Account */}
        <Box sx={{ mt: 5 }}>
          <Typography
            variant="h5"
            sx={{ mb: 2, display: "flex", alignItems: "center", gap: 1 }}
          >
            <DeleteIcon /> Xóa tài khoản
          </Typography>
          <Divider sx={{ mb: 2 }} />
          <Typography
            variant="h5"
            align="center"
            sx={{ color: "error.main", fontWeight: "bold" }}
          >
            Nếu muốn xóa tài khoản vui lòng liên hệ admin
          </Typography>
        </Box>
      </Paper>
    </Container>
  );
};

export default UserSetting;
