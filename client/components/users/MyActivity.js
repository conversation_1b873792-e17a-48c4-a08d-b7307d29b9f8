"use client"

import React, { useState, useEffect } from "react";
import { useDispatch } from "react-redux";

import CircularProgress from "@mui/material/CircularProgress";
import Tabs from "@mui/material/Tabs";
import Tab from "@mui/material/Tab";
import Box from "@mui/material/Box";
import Container from '@mui/material/Container';

import { fetchRecentActivity } from "@/actions/userAction";

import { setNoti } from "@/slices/notiSlice";

import InfiniteScroll from "../InfiniteScroll";
import NoDataOverlay from "@/components/NoDataOverlay";
import QuizActivityCard from "@/components/cards/QuizActivityCard";
import AccessTimeIcon from "@mui/icons-material/AccessTime";

const LIMIT_ITEM = 10;

const MyActivity = () => {
  const dispatch = useDispatch();
  const [playedQuizs, setPlayedQuizs] = useState(null);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [valueTab, setValueTab] = useState(0);

  useEffect(() => {
    const controller = new AbortController();
    const { signal } = controller;

    fetchRecentActivity(
      {
        page,
        limit: LIMIT_ITEM,
        status: valueTab,
      },
      signal
    ).then((res) => {
        if (res.data) {
          const newData = res.data.completedQuiz || [];
          setPlayedQuizs([...(playedQuizs || []), ...newData]);

          if (newData.length < LIMIT_ITEM) {
            setHasMore(false);
          } else if (page == 1) {
            setHasMore(true);
          }
        } else {
          setHasMore(false);
        }
      })
      .catch((error) => {
        if (error?.message === "canceled") {
          console.log("Request was canceled");
        } else {
          dispatch(setNoti(error));
        }
      });

    return () => {
      controller.abort();
    };
  }, [page, valueTab]);

  const handleChangeTab = (event, newValueTab) => {
    setValueTab(newValueTab);
    setPage(1);
    setPlayedQuizs(null);
  };

  return (
    <Container maxWidth="cxl">
      <p className="fs-22 fw-medium text-center">Hoạt động gần đây của bạn</p>
      <Box className="w-100 mb-4">
        <Tabs
          value={valueTab}
          onChange={handleChangeTab}
          textColor="secondary"
          indicatorColor="secondary"
        >
          <Tab value={0} icon={<i className="bi bi-file-earmark-text"></i>} iconPosition="end" label="Tất cả" />
          <Tab value={1} icon={<i className="bi bi-hourglass-split"></i>} iconPosition="end" label="Đang chạy" />
          <Tab value={2} icon={<i className="bi bi-hourglass-bottom"></i>} iconPosition="end" label="Hoàn thành" />
        </Tabs>
      </Box>
      {playedQuizs === null ? (
        <div className="text-center">
          <CircularProgress />
        </div>
      ) : playedQuizs.length ? (
        <InfiniteScroll
          loader={<CircularProgress />}
          fetchMore={() => setPage((prev) => prev + 1)}
          hasMore={hasMore}
          endMessage={<p className="text-center">Hết!</p>}
        >
          <div className="d-flex flex-wrap align-items-stretch">
            {playedQuizs.map((playedQuiz) => (
              <QuizActivityCard
                playedQuiz={playedQuiz}
                key={playedQuiz.id}
                href={`/join/quiz/${playedQuiz.quiz_id}/start${playedQuiz.assignment_id ? `?assignment=${playedQuiz.assignment_id}` : ''}`}
                sx={{
                  margin: "0 10px 20px",
                  width: "calc(20% - 20px)",
                  boxSizing: "border-box",
                  "@media (max-width: 1500px)": {
                    width: "calc(25% - 20px)",
                  },
                  "@media (max-width: 1299px)": {
                    width: "calc(33.33% - 20px)",
                  },
                  "@media (max-width: 1050px)": {
                    width: "calc(50% - 20px)",
                  },
                  "@media (max-width: 500px)": {
                    width: "calc(100% - 20px)",
                  },
                }}
              />
            ))}
          </div>
        </InfiniteScroll>
      ) : (
        <NoDataOverlay />
      )}
    </Container>
  );
};

export default MyActivity;
