"use client";

import React, { useEffect, useState, useCallback } from "react";
import { useDispatch } from "react-redux";

import { useTheme } from "@mui/material/styles";
import useMediaQuery from "@mui/material/useMediaQuery";
import Typography from "@mui/material/Typography";
import List from "@mui/material/List";
import ListItem from "@mui/material/ListItem";
import ListItemButton from "@mui/material/ListItemButton";
import ListItemIcon from "@mui/material/ListItemIcon";
import ListItemText from "@mui/material/ListItemText";
import CircularProgress from "@mui/material/CircularProgress";
import Button from '@mui/material/Button';
import Box from "@mui/material/Box";
import Divider from '@mui/material/Divider';
import Skeleton from "@mui/material/Skeleton";
import Grid from '@mui/material/Grid';
import Drawer from "@mui/material/Drawer";
import IconButton from "@mui/material/IconButton";
import Container from '@mui/material/Container';

import PersonIcon from "@mui/icons-material/Person";
import FolderIcon from "@mui/icons-material/Folder";
import MenuIcon from "@mui/icons-material/Menu";

import toast from "react-hot-toast";

import NoDataOverlay from "@/components/NoDataOverlay";
import DialogCollectionForm from "@/components/collections/DialogCollectionForm";

import LibraryTab from "./tabs/LibraryTab";
import CollectionTab from "./tabs/CollectionTab";

import { userApiSlice } from "@/slices/features/userApiSlice";

const MyLibrary = () => {
  const theme = useTheme();
  const dispatch = useDispatch();
  const isMobile = useMediaQuery(theme.breakpoints.down("lg"));
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [tabbarIndex, setTabbarIndex] = useState(0);
  // const [loading, setLoading] = useState(true);
  const [selectedCollection, setSelectedCollection] = useState(null);
  const [collection, setCollection] = useState(null);

  const [openCollectionModal, setOpenCollectionModal] = useState(false);

  const { data: myLibraryData, isLoading } = userApiSlice.useFetchMyLibraryQuery();

  const toggleDrawer = useCallback(() => {
    setDrawerOpen((prev) => !prev);
  }, []);

  const handleTabbarClick = useCallback((event, index) => {
    setTabbarIndex(index);
    setSelectedCollection(null);
  }, []);

  useEffect(() => {
    if (myLibraryData) {
      setTabbarIndex(1);
    }
  }, [myLibraryData]);

  const handleOpenCollectionModal = useCallback(() => {
    setOpenCollectionModal(true);
  }, []);

  const handleCloseCollectionModal = useCallback(() => {
    setOpenCollectionModal(false);
  }, []);

  const handleCollectionSubmitSuccess = useCallback((newCollection, isEdit = false) => {
    try {
      dispatch(
        userApiSlice.util.invalidateTags([{ type: "User", id: "MYLIBRARY" }])
      );
      setSelectedCollection(newCollection);
      toast.success(isEdit ? "Cập nhật bộ sưu tập thành công!" : "Tạo bộ sưu tập thành công!");
    } catch (error) {
      toast.error("Đã xảy ra lỗi!");
    }
  }, []);

  const handleEditCollection = useCallback((coll) => {
    setCollection(coll);
    handleOpenCollectionModal();
  }, []);

  const handleDeleteCollection = useCallback((coll) => {
    try {
      dispatch(
        userApiSlice.util.invalidateTags([{ type: "User", id: "MYLIBRARY" }])
      );
      setSelectedCollection(null);
      setCollection(null);
    } catch (error) {
      toast.error("Đã xảy ra lỗi!");
    }
  }, []);

  const renderSidebarContent = () => (
    <>
      <h4 className="fs-18 text-dark text-start mb-3">
        Thư viện của tôi
      </h4>
      <List
        sx={{
          width: "100%",
          bgcolor: "transparent",
        }}
        component="nav"
      >
        <ListItem
          secondaryAction={<Typography>{myLibraryData?.totalQuizzes || 0}</Typography>}
          disablePadding
          className="my-1 mt-0"
        >
          <ListItemButton
            selected={!selectedCollection && tabbarIndex === 1}
            onClick={(event) => handleTabbarClick(event, 1)}
          >
            <ListItemIcon sx={{ minWidth: '36px' }}>
              <PersonIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText
              primary="Được tạo bởi tôi"
              // primaryTypographyProps={{ fontWeight: 500 }}
            />
          </ListItemButton>
        </ListItem>
      </List>
      <div className="d-flex justify-content-between align-items-center mt-4 mb-3">
        <h4 className="fs-17 text-dark text-start mb-0 fw-normal">
          Bộ sưu tập
        </h4>
        <Button
          size="small"
          variant="outlined"
          color="secondary"
          onClick={() => {
            setCollection(null);
            handleOpenCollectionModal();
          }}
        >
          <i className="bi bi-plus-circle me-1"></i>
          Mới
        </Button>
      </div>
      <Box
        sx={{
          maxHeight: 'calc(100vh - 300px)',
          overflowY: 'auto',
          '&::-webkit-scrollbar': {
            width: '6px',
          },
          '&::-webkit-scrollbar-track': {
            background: '#f1f1f1',
            borderRadius: '10px',
          },
          '&::-webkit-scrollbar-thumb': {
            background: '#ddd',
            borderRadius: '10px',
          },
          '&::-webkit-scrollbar-thumb:hover': {
            background: '#ccc',
          },
        }}
      >
        {myLibraryData?.collections?.length > 0 ? (
          <List sx={{ width: "100%", bgcolor: "transparent" }} className="pt-0" component="nav">
            {myLibraryData.collections.map((collection, index) => (
              <ListItem
                key={collection.id}
                secondaryAction={
                  <Typography>
                    {collection.table_contents_count || 0}
                  </Typography>
                }
                disablePadding
                className="my-1"
              >
                <ListItemButton
                  selected={selectedCollection?.id === collection.id}
                  onClick={() => setSelectedCollection(collection)}
                >
                  <ListItemIcon sx={{ minWidth: '36px' }}>
                    <FolderIcon
                      fontSize="small"
                      sx={{
                        fill: selectedCollection?.id === collection.id ? '#ffa100' : '#fffaf3',
                        stroke: selectedCollection?.id === collection.id ? '#ffa100' : '#777',
                      }}
                    />
                  </ListItemIcon>
                  <ListItemText
                    primary={collection.title}
                    primaryTypographyProps={{
                      sx: {
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap',
                        maxWidth: '180px',
                        fontWeight: 500
                      }
                    }}
                  />
                </ListItemButton>
              </ListItem>
            ))}
          </List>
        ) : (
          <div className="my-1 text-center">
            <NoDataOverlay message="Không có bộ sưu tập nào!" />
          </div>
        )}
      </Box>
    </>
  );

  if (isLoading) return <p className="text-center mt-3"><CircularProgress /></p>;

  return (
    <Container maxWidth="cxl">
      <Grid container spacing={2}>
        <Grid size={{ xs: 12, lg: 4, xl: 3 }}>
          {isMobile ? (
            <>
              <div className="d-flex gap-2 align-items-center mt-3">
                <IconButton onClick={toggleDrawer}>
                  <MenuIcon />
                </IconButton>
                <h4 className="fs-18 text-dark mb-0">
                  Thư viện của tôi
                </h4>
              </div>
              <Drawer
                open={drawerOpen}
                onClose={toggleDrawer}
                anchor="left"
                ModalProps={{ keepMounted: true }}
                sx={{
                  "& .MuiDrawer-paper": {
                    width: "80%",
                    maxWidth: 280,
                    boxShadow: theme.shadows[4],
                  },
                }}
              >
                <Box sx={{ px: 2, pt: 3 }}>
                  {renderSidebarContent()}
                </Box>
              </Drawer>
            </>
          ) : (
            <Box
              className="position-sticky"
              sx={{
                top: 100,
                zIndex: 1000,
                maxHeight: "calc(100vh - 120px)",
                overflowY: "auto",
                pr: 1,
              }}
            >
              {renderSidebarContent()}
            </Box>
          )}
        </Grid>
        <Grid size={{ xs: 12, lg: 8, xl: 9 }}>
          { selectedCollection ? <CollectionTab collection={selectedCollection} onEdit={handleEditCollection} onDelete={handleDeleteCollection} /> : (
            tabbarIndex > 0 ? (
              tabbarIndex === 1 && <LibraryTab />
            ) : (
              Array.from({ length: 3 }, (_, index) => (
                <Skeleton
                  key={`skeleton-${index}`}
                  variant="rounded"
                  height={150}
                  sx={{ mt: "20px", borderRadius: "4px" }}
                />
              ))
            )
          )}
        </Grid>
      </Grid>
      <DialogCollectionForm
        open={openCollectionModal}
        onClose={handleCloseCollectionModal}
        onSubmitSuccess={handleCollectionSubmitSuccess}
        collection={collection}
      />
    </Container>
  );
};

export default MyLibrary;
