import Image from 'next/image'
import PropTypes from 'prop-types';

export default function NextImage({
  src,
  alt,
  sizes = "200px",
  defaultImg = "/assets/images/og_image_default.jpg",
  imgStyle = {
    objectFit: 'cover',
    objectPosition: 'top',
  },
  className = "banner-wrapper",
  height
}) {
  if (height) {
    return (
      <div className={className} style={{ height: height, position: 'relative' }}>
        <Image
          src={src || defaultImg}
          alt={alt || ''}
          width={100}
          height={height}
          priority
          style={imgStyle}
        />
      </div>
    );
  }

  return (
    <div className={className}>
      <Image
        src={src || defaultImg}
        alt={alt || ''}
        sizes={sizes}
        fill
        priority
        style={imgStyle}
      />
    </div>
  );
}

NextImage.propTypes = {
  src: PropTypes.string.isRequired,
  alt: PropTypes.string,
  sizes: PropTypes.string,
  defaultImg: PropTypes.string,
  imgStyle: PropTypes.object,
  className: PropTypes.string,
  height: PropTypes.number,
  width: PropTypes.number
};
