import React, { useEffect, useState, useRef, useCallback } from "react";
import Popper from '@mui/material/Popper';
import { styled, css } from '@mui/system';
import Avatar from '@mui/material/Avatar';

const grey = {
  50: '#F3F6F9',
  100: '#E5EAF2',
  200: '#DAE2ED',
  300: '#C7D0DD',
  400: '#B0B8C4',
  500: '#9DA8B7',
  600: '#6B7A90',
  700: '#434D5B',
  800: '#303740',
  900: '#1C2025',
};

const StyledPopperDiv = styled('div')(
  ({ theme }) => css`
    background-color: ${theme.palette.mode === 'dark' ? grey[900] : '#fff'};
    border-radius: 8px;
    border: 1px solid ${theme.palette.mode === 'dark' ? grey[700] : grey[200]};
    box-shadow: ${theme.palette.mode === 'dark'
      ? `0px 4px 8px rgb(0 0 0 / 0.7)`
      : `0px 4px 8px rgb(0 0 0 / 0.1)`};
    color: ${theme.palette.mode === 'dark' ? grey[100] : grey[700]};
    font-size: 0.875rem;
    font-family: 'IBM Plex Sans', sans-serif;
    font-weight: 500;
    opacity: 1;
    margin: 1rem 0;
    position: relative;
    max-width: 330px;
    width: 100%;

    &::before,
    &::after {
      content: '';
      position: absolute;
      width: 0;
      height: 0;
      border-style: solid;
    }

    &::before {
      border-width: 8px;
      border-color: #fff transparent transparent transparent;
      bottom: -16px;
      left: calc(50% - 8px);
      z-index: 1;
    }

    &::after {
      border-width: 8px;
      border-color: #cdcdcd transparent transparent transparent;
      bottom: -17px;
      left: calc(50% - 8px);
      z-index: 0;
    }
  `,
);

const DeleteButtonPopper = React.memo(function DeleteButtonPopper({
  message = 'Bạn có chắc chắn muốn xóa không?',
  onDelete = () => {},
}) {
  const [anchorEl, setAnchorEl] = useState(null);
  const anchorElRef = useRef(null);

  const handleClick = useCallback((event) => {
    if (anchorElRef.current) {
      setAnchorEl(null);
      anchorElRef.current = null;
    } else {
      setAnchorEl(event.currentTarget);
      anchorElRef.current = event.currentTarget;
    }
  }, []);

  const handleDelete = useCallback(() => {
    setAnchorEl(null);
    anchorElRef.current = null;
    onDelete();
  }, [onDelete]);

  useEffect(() => {
    const handleClickOutside = (event) => {
      const popperEl = document.getElementById('simple-popper');
      if (
        anchorElRef.current &&
        !anchorElRef.current.contains(event.target) &&
        !(popperEl && popperEl.contains(event.target))
      ) {
        setAnchorEl(null);
        anchorElRef.current = null;
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const open = Boolean(anchorEl);
  const id = open ? 'simple-popper' : undefined;

  return (
    <>
      <button
        aria-describedby={id}
        onClick={handleClick}
        type="button"
        className="btn btn-default btn-sm text-black-50"
        style={{ padding: '0px 5px' }}
      >
        <i className="bi bi-trash3"></i>
      </button>
      <Popper id={id} open={open} anchorEl={anchorEl} placement="top" sx={{ zIndex: 1200 }}>
        <StyledPopperDiv>
          <div className="card">
            <div className="card-body d-flex align-items-center gap-3">
              <Avatar sx={{ bgcolor: '#fcdbe3', color: '#ec0b43' }}>
                <i className="bi bi-trash3"></i>
              </Avatar>
              <span className="fs-16">{message}</span>
            </div>
            <div className="card-footer bg-light p-2 text-end">
              <button
                onClick={handleClick}
                type="button"
                className="btn btn-default btn-sm mb-0 px-2 py-0"
              >
                Hủy
              </button>
              <button
                onClick={handleDelete}
                type="button"
                className="btn btn-danger btn-sm mb-0 px-2 py-0 ms-2"
              >
                Xóa
              </button>
            </div>
          </div>
        </StyledPopperDiv>
      </Popper>
    </>
  );
});

export default DeleteButtonPopper;
