/* eslint-disable @next/next/no-img-element */
"use client";

// https://codesandbox.io/p/sandbox/react-image-crop-demo-with-react-hooks-y831o?file=%2Fsrc%2FApp.tsx%3A51%2C7-51%2C25
import React, {
  useCallback,
  useState,
  useRef,
  forwardRef,
  memo,
  useImperativeHandle,
} from "react";

import Box from "@mui/material/Box";
import Paper from "@mui/material/Paper";
import Typography from "@mui/material/Typography";
import IconButton from "@mui/material/IconButton";
import CircularProgress from "@mui/material/CircularProgress";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogContent from "@mui/material/DialogContent";
import DialogActions from "@mui/material/DialogActions";
import DialogTitle from "@mui/material/DialogTitle";
import Slide from "@mui/material/Slide";
import CloudUploadIcon from "@mui/icons-material/CloudUpload";
import ImageIcon from "@mui/icons-material/Image";
import DeleteIcon from "@mui/icons-material/Delete";
import CropIcon from "@mui/icons-material/Crop";
import SaveIcon from "@mui/icons-material/Save";
import KeyboardBackspaceIcon from "@mui/icons-material/KeyboardBackspace";

import ReactCrop, {
  centerCrop,
  makeAspectCrop,
  Crop,
  PixelCrop,
  convertToPixelCrop,
} from "react-image-crop";

import { canvasPreview } from "./canvasPreview";
import { useDebounceEffect } from "../../hooks/useDebounce";

import "react-image-crop/dist/ReactCrop.css";

const imageMaxSize = 1000000000; // bytes
const acceptedFileTypes =
  "image/x-png, image/png, image/jpg, image/jpeg, image/gif";
const acceptedFileTypesArray = acceptedFileTypes.split(",").map((item) => {
  return item.trim();
});

const Transition = forwardRef(function Transition(props, ref) {
  return <Slide direction="up" ref={ref} {...props} />;
});

// This is to demonstrate how to make and center a % aspect crop
// which is a bit trickier so we use some helper functions.
function centerAspectCrop(mediaWidth, mediaHeight, aspect) {
  return centerCrop(
    makeAspectCrop(
      {
        unit: "%",
        width: 90,
      },
      aspect,
      mediaWidth,
      mediaHeight
    ),
    mediaWidth,
    mediaHeight
  );
}

export const DragDropCropImage = memo(
  forwardRef((props, ref) => {
    console.log("DragDropCropImage");

    const [img, setImg] = useState(props.imgSrc);
    const previewCanvasRef = useRef(null);
    const imgRef = useRef(null);
    const hiddenAnchorRef = useRef(null);
    const blobUrlRef = useRef("");
    const [crop, setCrop] = useState();
    const [completedCrop, setCompletedCrop] = useState();
    const [scale, setScale] = useState(1);
    const [rotate, setRotate] = useState(0);
    const [aspect, setAspect] = useState(16 / 9);

    const [startCrop, setStartCrop] = useState(false);
    const [firstCrop, setFirstCrop] = useState(true);

    const [dragOver, setDragOver] = useState(false);
    const [loading, setLoading] = useState(false);

    const [error, setError] = useState("");

    useImperativeHandle(ref, () => ({
      getImg: () => {
        if (startCrop) {
          return getCropBase64();
        }

        return img;
      },
    }));

    const handleDragOver = useCallback((event) => {
      event.preventDefault();
      setDragOver(true);
    }, []);

    const handleDragLeave = useCallback((event) => {
      event.preventDefault();
      setDragOver(false);
    }, []);

    const verifyFile = (files) => {
      setError("");

      if (files && files.length > 0) {
        const currentFile = files[0];
        const currentFileType = currentFile.type;
        const currentFileSize = currentFile.size;
        if (currentFileSize > imageMaxSize) {
          setError(
            `File này không được phép. Kích thước ${currentFileSize} bytes quá lớn.`
          );
          return false;
        }
        if (!acceptedFileTypesArray.includes(currentFileType)) {
          setError(
            `File này không được phép. Chỉ cho phép các định dạng ảnh ${acceptedFileTypes}`
          );
          return false;
        }
        return true;
      }
    };

    const handleDrop = useCallback((event) => {
      event.preventDefault();
      setDragOver(false);
      const files = event.dataTransfer.files;

      if (files && files.length > 0) {
        const isVerified = verifyFile(files);

        if (isVerified) {
          imageRender(files[0]);
        }
      }
    }, []);

    const imageRender = (file) => {
      setLoading(true);
      setCrop(undefined); // Makes crop preview update between images.

      const reader = new FileReader();

      reader.onloadend = () => {
        setLoading(false);
        const imgResult = reader.result?.toString() || "";
        setImg(imgResult);
      };

      reader.readAsDataURL(file);
    };

    const handleChange = useCallback((event) => {
      const files = event.target.files;

      if (files && files.length > 0) {
        const isVerified = verifyFile(files);
        if (isVerified) {
          imageRender(files[0]);
        }
      }
    }, []);

    const onImageLoad = (e) => {
      if (aspect) {
        const { width, height } = e.currentTarget;
        setCrop(centerAspectCrop(width, height, aspect));
      }
    };

    // Hàm hỗ trợ chuyển đổi Blob sang Base64
    const convertBlobToBase64 = (blob) => {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onloadend = () => {
          resolve(reader.result);
        };
        reader.onerror = reject;
        reader.readAsDataURL(blob);
      });
    };

    const toBlob = async () => {
      const image = imgRef.current;
      const previewCanvas = previewCanvasRef.current;
      if (!image || !previewCanvas || !completedCrop) {
        setError("Crop canvas không tồn tại.");
        return;
      }
      try {
        const scaleX = image.naturalWidth / image.width;
        const scaleY = image.naturalHeight / image.height;

        const offscreen = new OffscreenCanvas(
          completedCrop.width * scaleX,
          completedCrop.height * scaleY
        );
        const ctx = offscreen.getContext("2d");
        if (!ctx) {
          throw new Error("Không có context 2D.");
        }

        ctx.drawImage(
          previewCanvas,
          0,
          0,
          previewCanvas.width,
          previewCanvas.height,
          0,
          0,
          offscreen.width,
          offscreen.height
        );
        // ảnh khác domain sẽ ko dùng đc convertToBlob theo chính sách cors
        return await offscreen.convertToBlob({
          type: "image/png",
        });
      } catch (err) {
        console.error(err);
        setError("Failed to create blob.");
        return "";
      }
    };

    const getCropBase64 = () => {
      // C1:
      const image = imgRef.current;
      const previewCanvas = previewCanvasRef.current;

      if (!image || !previewCanvas || !completedCrop) {
        setError("Crop canvas does not exist");

        return "";
      }
      // ảnh khác domain sẽ ko dùng đc toDataURL theo chính sách cors
      const dataUrlBase64 = previewCanvas.toDataURL();

      return dataUrlBase64 || "";

      // C2:
      // const blob = await toBlob();

      // if (!blob) {
      //   setError("Đã xảy ra lỗi. Vui lòng thử lại");
      //   return;
      // }

      // try {
      //   const base64 = await convertBlobToBase64(blob);

      //   // Bạn có thể sử dụng base64 ở đây, ví dụ: setImgSrc(base64) hoặc gửi nó lên server
      //   return base64;
      // } catch (error) {
      //   console.error("Lỗi khi chuyển đổi Blob sang Base64:", error);
      //   setError("Lỗi khi chuyển đổi Blob sang Base64.");
      //   return "";
      // }
    };

    const onDownloadCropClick = async () => {
      const blob = await toBlob();

      if (!blob) {
        setError("Lỗi khi tải xuống ảnh đã crop");
        return;
      }

      if (blobUrlRef.current) {
        URL.revokeObjectURL(blobUrlRef.current);
      }

      blobUrlRef.current = URL.createObjectURL(blob);

      if (hiddenAnchorRef.current) {
        hiddenAnchorRef.current.href = blobUrlRef.current;
        hiddenAnchorRef.current.click();
      }
    };

    useDebounceEffect(
      async () => {
        if (
          completedCrop?.width &&
          completedCrop?.height &&
          imgRef.current &&
          previewCanvasRef.current
        ) {
          try {
            await canvasPreview(
              imgRef.current,
              previewCanvasRef.current,
              completedCrop,
              scale,
              rotate
            );
          } catch (err) {
            console.error(err);
            setError("Lỗi khi vẽ lên canvas.");
          }
        }
      },
      100,
      [completedCrop, scale, rotate]
    );

    const handleStartCrop = useCallback(() => {
      setStartCrop(true);
    }, []);

    const handleStopCrop = useCallback(() => {
      setAspect(undefined);
      setStartCrop(false);
    }, []);

    const deleteImage = useCallback(() => {
      setImg(null);
      setAspect(16 / 9);
      setStartCrop(false);
      setFirstCrop(true);
    }, []);

    const done = () => {
      setAspect(16 / 9);
      setStartCrop(false);
      setFirstCrop(true);
    };

    const handleCompletedCrop = (c) => {
      setCompletedCrop(c);

      if (firstCrop) {
        if (aspect) {
          setAspect(undefined);
        } else {
          setAspect(16 / 9);

          if (imgRef.current) {
            const { width, height } = imgRef.current;
            const newCrop = centerAspectCrop(width, height, 16 / 9);
            setCrop(newCrop);
            setCompletedCrop(convertToPixelCrop(newCrop, width, height));
          }
        }

        setFirstCrop(false);
      }
    };

    return (
      <>
        {error && <p className="text-danger">{error}</p>}
        <Box
          className="drag-drop-crop"
          style={{
            backgroundColor: startCrop ? "rgba(0, 0, 0, 0.5)" : "#ececec",
            ...props?.sx
          }}
        >
          {img ? (
            <Box>
              {startCrop ? (
                <Box className="text-center">
                  <Button
                    variant="contained"
                    className="btn-small btn-float-left"
                    color="secondary"
                    aria-label="crop"
                    onClick={handleStopCrop}
                  >
                    <KeyboardBackspaceIcon fontSize="small" />
                  </Button>
                  <ReactCrop
                    crop={crop}
                    onChange={(_, percentCrop) => setCrop(percentCrop)}
                    onComplete={handleCompletedCrop}
                    aspect={aspect}
                  >
                    <img
                      ref={imgRef}
                      alt="Crop Image"
                      src={img}
                      // crossOrigin="anonymous" // Thêm thuộc tính này để xử lý CORS
                      style={{
                        transform: `scale(${scale}) rotate(${rotate}deg)`,
                      }}
                      onLoad={onImageLoad}
                    />
                  </ReactCrop>
                </Box>
              ) : (
                <Box>
                  <Button
                    variant="contained"
                    className="btn-small btn-float-left"
                    color="secondary"
                    aria-label="crop"
                    onClick={handleStartCrop}
                  >
                    <CropIcon fontSize="small" />
                  </Button>
                  <Button
                    variant="contained"
                    className="btn-small btn-float-right"
                    color="error"
                    aria-label="delete"
                    onClick={deleteImage}
                  >
                    <DeleteIcon fontSize="small" />
                  </Button>
                  <img
                    alt="Preview Image"
                    src={img}
                    style={{
                      transform: `scale(${scale}) rotate(${rotate}deg)`,
                    }}
                  />
                </Box>
              )}
            </Box>
          ) : (
            <Paper
              variant="outlined"
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
              className="h-100 w-100"
              style={{
                border: "1px dashed rgb(170, 170, 170)",
                background: dragOver ? "#dedede" : "#ececec",
                cursor: "pointer",
              }}
            >
              <input
                accept="image/*"
                style={{ display: "none" }}
                id="raised-button-file"
                multiple
                type="file"
                onChange={handleChange}
              />
              <label htmlFor="raised-button-file">
                <Box display="flex" flexDirection="column" alignItems="center">
                  <IconButton
                    color="primary"
                    aria-label="upload picture"
                    component="span"
                  >
                    <ImageIcon style={{ fontSize: 60 }} />
                  </IconButton>
                  <Typography className="p-1 text-center fs-14 text-muted">
                    {props?.subtitle || "Kéo thả ảnh hoặc click để chọn ảnh"}
                  </Typography>
                </Box>
              </label>
            </Paper>
          )}
          {loading && (
            <CircularProgress
              color="secondary"
              size={50}
              style={{
                position: "absolute",
                transform: "translate(-50%, -50%)",
                zIndex: 10,
              }}
            />
          )}
        </Box>
        {!!completedCrop && (
          <div className="h-100 w-100 text-center d-none">
            <canvas
              ref={previewCanvasRef}
              style={{
                border: "1px solid #dedede",
                objectFit: "contain",
                width: completedCrop.width,
                height: completedCrop.height,
                backgroundColor: "#eee",
              }}
            />
          </div>
        )}
      </>
    );
  })
);

DragDropCropImage.displayName = "DragDropCropImage";

const DialogDragDropCropImage = ({
  open,
  onClose,
  imgSrc = null,
  setImgSrc = () => {},
}) => {
  console.log("DialogDragDropCropImage");
  const cropRef = useRef();

  const handleSave = () => {
    if (cropRef.current) {
      const newImgBase64 = cropRef.current.getImg();
      setImgSrc(newImgBase64);
    }
  };

  return (
    <Dialog
      fullWidth={true}
      maxWidth={"sm"}
      open={open || false}
      TransitionComponent={Transition}
      keepMounted
      onClose={onClose}
      disableEscapeKeyDown={true}
    >
      <DialogTitle className="h5" sx={{ textAlign: "center" }}>
        Tải lên một hình ảnh
      </DialogTitle>
      <DialogContent>
        <DragDropCropImage imgSrc={imgSrc} ref={cropRef} />
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} color="error" size="small">
          Hủy
        </Button>
        <Button
          size="small"
          onClick={handleSave}
          variant="contained"
          color="secondary"
          startIcon={<SaveIcon size="small" />}
        >
          Save
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default memo(DialogDragDropCropImage);
