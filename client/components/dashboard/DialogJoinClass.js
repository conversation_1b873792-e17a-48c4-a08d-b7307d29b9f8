"use client";

import React from "react";

import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import Button from '@mui/material/Button';
import Box from '@mui/material/Box';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';

import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import Image from "next/image";

const DialogJoinClass = ({
  open,
  onClose,
  classRoomInfo,
  userName,
  onUserNameChange,
  onJoinClassRoom,
  disabled = false,
  joinButtonText = "Tham gia trò chơi",
  backButtonText = "Quay lại",
  isLoading = false,
  headerTitle = "Bạn đang tham gia lớp học:",
  inputLabel = "Tên của bạn",
  inputHelperText = "Nhập tên của bạn để tham gia lớp học."
}) => {
  const handleUserNameChange = (event) => {
    if (onUserNameChange) {
      onUserNameChange(event.target.value);
    }
  };

  const handleClose = () => {
    if (onClose) {
      onClose();
    }
  };

  const handleJoinClassRoom = () => {
    if (onJoinClassRoom) {
      onJoinClassRoom();
    }
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="xs"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 3,
          overflow: 'hidden',
          boxShadow: 6,
          paddingTop: 0
        }
      }}
    >
      <Box sx={{ width: '100%', backgroundColor: '#743468' }}>
        <Image
          src="/assets/images/classes_signup_upsell.png"
          width={512}
          height={200}
          alt="Class signup"
          style={{ width: '100%', height: 'auto', display: 'block' }}
        />
      </Box>
      <DialogContent sx={{ px: 3, pt: 3, pb: 0 }}>
        <Typography align="center" gutterBottom sx={{ fontSize: '20px' }}>
          {headerTitle}
        </Typography>
        <Typography
          variant="h4"
          align="center"
          sx={{ mb: 3 }}
        >
          {classRoomInfo?.classroom?.title || "(Không có thông tin lớp học)"}
        </Typography>

        <Typography paragraph sx={{ mb: 2 }}>
          {inputHelperText}
        </Typography>

        <TextField
          fullWidth
          label={inputLabel}
          variant="outlined"
          value={userName || ""}
          onChange={handleUserNameChange}
          sx={{ mb: 2 }}
          disabled={disabled || isLoading}
        />
      </DialogContent>

      <DialogActions sx={{ px: 3, py: 3, justifyContent: 'space-between' }}>
        <Button
          onClick={handleClose}
          color="inherit"
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 1
          }}
          disabled={disabled}
        >
          <ArrowBackIcon sx={{ fontSize: 18 }} />
          {backButtonText}
        </Button>

        <Button
          variant="contained"
          onClick={handleJoinClassRoom}
          disabled={disabled || isLoading || !userName?.trim()}
          sx={{
            backgroundColor: '#ede6f6',
            color: '#9f57eb',
            fontWeight: 600,
            '&:hover': {
              backgroundColor: '#e0d0f0',
              color: '#985ed6'
            },
            '&:disabled': {
              backgroundColor: '#f5f5f5',
              color: '#9e9e9e'
            }
          }}
        >
          {isLoading ? "Đang tham gia..." : joinButtonText}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default DialogJoinClass;
