"use client";

import Link from "next/link";
import { useRouter } from "next/navigation";

import Button from "@mui/material/CardActionArea";
import Card from "@mui/material/Card";
import CardActionArea from "@mui/material/CardActionArea";
import CardContent from "@mui/material/CardContent";

import FormatListBulletedIcon from "@mui/icons-material/FormatListBulleted";
import SchoolIcon from "@mui/icons-material/School";
import TipsAndUpdatesIcon from "@mui/icons-material/TipsAndUpdates";
import ManageAccountsIcon from "@mui/icons-material/ManageAccounts";
import PeopleIcon from "@mui/icons-material/People";
import BusinessCenterIcon from "@mui/icons-material/BusinessCenter";
import CalendarMonthIcon from "@mui/icons-material/CalendarMonth";

import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation } from "swiper/modules";
import "swiper/css";
import "swiper/css/navigation";

import { timeElapsedString } from "@/utils/helpers";
import QuizActivityCard from "@/components/cards/QuizActivityCard";

const DisplayUserData = ({ initialDatas = [] }) => {
  console.log("DisplayUserData");
  const router = useRouter();

  return (
    <>
      {initialDatas?.completedQuiz?.length > 0 && (
        <div className="mb-5">
          <div className="d-flex align-items-center justify-content-between mb-2">
            <h4 className="featured-section-title mb-0">Hoạt động gần đây</h4>
            <Link
              href="/dashboard/my-activity"
              className="see-all-link"
            >
              Xem tất cả
            </Link>
          </div>
          <Swiper
            slidesPerView={5}
            spaceBetween={20}
            navigation={true}
            modules={[Navigation]}
            breakpoints={{
              0: {
                slidesPerView: 1.3,
              },
              450: {
                slidesPerView: 2,
              },
              639: {
                slidesPerView: 2.5,
              },
              900: {
                slidesPerView: 3.5,
              },
              1200: {
                slidesPerView: 4.2,
              },
              1400: {
                slidesPerView: 5,
              },
            }}
            className="mySwiper"
          >
            {initialDatas.completedQuiz.map((quizInfo) => {
              const {quiz = null} = quizInfo;

              return quiz ? (
                <SwiperSlide className="h-auto" key={quizInfo.id}>
                  <QuizActivityCard
                    playedQuiz={quizInfo}
                    key={quizInfo.id}
                    href={
                      quizInfo.status == 2
                        ? `/join/quiz/${quiz.id}/start?action=replay&quizResultId=${quizInfo.id}&gameType=${quizInfo.type}`
                        : `/join/pre-game/${quiz.id}/running?quizResultId=${quizInfo.id}`
                    }
                  />
                </SwiperSlide>
              ) : null;
            })}
          </Swiper>
        </div>
      )}

      {initialDatas?.myAssignments?.length > 0 && (
        <div className="mb-5">
          <div className="d-flex align-items-center justify-content-between mb-2">
            <h4 className="featured-section-title mb-0">Bài tập được giao gần đây</h4>
            <button
              className="see-more-btn"
              onClick={() => router.push(`/dashboard/my-classroom`)}
            >
              Xem tất cả
            </button>
          </div>
          <Swiper
            slidesPerView={3}
            spaceBetween={20}
            navigation={true}
            modules={[Navigation]}
            breakpoints={{
              0: {
                slidesPerView: 1,
              },
              639: {
                slidesPerView: 1.5,
              },
              900: {
                slidesPerView: 2,
              },
              1200: {
                slidesPerView: 2.5,
              },
              1400: {
                slidesPerView: 3,
              },
            }}
            className="mySwiper mySwiper-sm mb-4"
          >
            {initialDatas.myAssignments.map((assignment) => {
              const quizInfo = assignment;
              const quiz = assignment.quiz;

              return (
                <SwiperSlide className="h-auto" key={assignment.id}>
                  <Card>
                    <CardActionArea component={Link} href={`/join/quiz/${assignment.quiz_id}/start?assignment=${assignment.id}`}>
                      <CardContent sx={{ padding: "24px 16px" }}>
                        <div className="d-flex align-items-center">
                          <div className="ass-status me-2">
                            <span
                              className={`ass-status-label fs-12 ${
                                assignment.quiz_results_count
                                  ? "success"
                                  : "danger"
                              } d-flex align-items-center`}
                            >
                              <FormatListBulletedIcon className="me-1 fs-16" />
                              {assignment.quiz_results_count
                                ? "Đã làm"
                                : "Chưa làm"}
                            </span>
                          </div>
                          <h4 className="quiz-name fs-18 text-limit mb-0">
                            {assignment.subtitle || assignment.title}
                          </h4>
                        </div>
                        <div
                          className="d-flex flex-wrap align-items-center gap-3 mt-3"
                        >
                          <Button
                            variant="outlined"
                            size="small"
                            color="secondary"
                            className="d-flex align-items-center"
                            sx={{ width: "auto" }}
                          >
                            <SchoolIcon className="me-2 fs-16" />
                            <span
                              className="fs-12"
                              style={{ marginTop: "1px" }}
                            >
                              {assignment.classroom?.title || "__"}
                            </span>
                          </Button>
                          {assignment.classroom?.author && (
                            <span className="d-flex fs-12">
                              <ManageAccountsIcon
                                className="me-1 fs-16"
                                sx={{ color: "#03A9F4" }}
                              />
                              Giáo viên:
                              <strong className="ms-1">
                                {assignment.classroom?.author?.name || "__"}
                              </strong>
                            </span>
                          )}
                          <span className="fs-12 d-flex">
                            <TipsAndUpdatesIcon
                              className="me-1 fs-16"
                              sx={{ color: "#ffc107" }}
                            />
                            Số lần làm:
                            <strong className="ms-1">
                              {quizInfo?.quiz_results_count || 0}
                            </strong>
                          </span>
                        </div>
                      </CardContent>
                    </CardActionArea>
                  </Card>
                </SwiperSlide>
              );
            })}
          </Swiper>
        </div>
      )}

      {initialDatas?.classrooms?.length > 0 && (
        <div className="mb-5">
          <div className="d-flex align-items-center justify-content-between mb-2">
            <h4 className="featured-section-title mb-0">Lớp học của bạn</h4>
            <button
              className="see-more-btn"
              onClick={() => router.push(`/dashboard/classroom`)}
            >
              Xem tất cả
            </button>
          </div>
          <Swiper
            slidesPerView={3}
            spaceBetween={20}
            navigation={true}
            modules={[Navigation]}
            breakpoints={{
              0: {
                slidesPerView: 1,
              },
              639: {
                slidesPerView: 1.5,
              },
              900: {
                slidesPerView: 2,
              },
              1200: {
                slidesPerView: 2.5,
              },
              1400: {
                slidesPerView: 3,
              },
            }}
            className="mySwiper mySwiper-sm mb-5"
          >
            {initialDatas.classrooms.map((classroom) => {
              return (
                <SwiperSlide className="h-auto" key={classroom.id}>
                  <Card>
                    <CardActionArea
                      component={Link}
                      href={`/dashboard/classroom/${classroom.id}`}
                    >
                      <CardContent sx={{ padding: "24px 16px", borderTop: `5px solid ${classroom?.color || "#eee"}` }}>
                        <div className="d-flex align-items-center">
                          <div className="me-2">
                            <span
                              className={`classroom-label fs-12 success d-flex align-items-center`}
                            >
                              <FormatListBulletedIcon className="me-1 fs-16" />
                              {classroom.grade?.title}
                            </span>
                          </div>
                          <h4 className="quiz-name text-limit fs-18 mb-0 fw-bold" style={{ color: classroom?.color || "#eee" }}>
                            {classroom.title}
                          </h4>
                        </div>
                        <div
                          className="d-flex flex-wrap align-items-center gap-3 mt-3"
                        >
                          <span className="fs-12 d-flex">
                            <PeopleIcon
                              className="me-1"
                              sx={{ fontSize: "1rem" }}
                            />
                            <span>
                              {classroom.classroom_user_count || 0} học sinh
                            </span>
                          </span>
                          <span className="fs-12 d-flex">
                            <BusinessCenterIcon
                              className="me-1"
                              sx={{ fontSize: "1rem", color: "#03A9F4" }}
                            />
                            <span>{classroom.classroom_quiz_count || 0} bài tập</span>
                          </span>
                          <span className="fs-12 d-flex">
                            <CalendarMonthIcon
                              className="me-1"
                              sx={{ fontSize: "1rem" }}
                            />
                            <span>
                              {timeElapsedString(classroom.created_at)}
                            </span>
                          </span>
                        </div>
                      </CardContent>
                    </CardActionArea>
                  </Card>
                </SwiperSlide>
              );
            })}
          </Swiper>
        </div>
      )}
    </>
  );
};

export default DisplayUserData;
