"use client";

import React, { useState, useEffect } from "react";
import Link from "next/link";

import Card from "@mui/material/Card";
import CardActionArea from "@mui/material/CardActionArea";
import CardActions from "@mui/material/CardActions";
import CardContent from "@mui/material/CardContent";
import CardMedia from "@mui/material/CardMedia";
import CircularProgress from "@mui/material/CircularProgress";

import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation } from "swiper/modules";
import "swiper/css";
import "swiper/css/navigation";

import NextImage from "@/components/NextImage";
import InfiniteScroll from "@/components/InfiniteScroll";

import { fetchSubjects } from "@/actions/subjectAction";
import Image from "next/image";

const LIMIT_ITEM = 2; // change follow SubjectController

const DisplaySubjects = ({ initialDatas = [] }) => {
  console.log("DisplaySubjects");
  const [subjectsWithQuiz, setSubjectsWithQuiz] = useState(initialDatas);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(!(initialDatas.length < LIMIT_ITEM));

  // eslint-disable-next-line react-hooks/exhaustive-deps
  useEffect(() => {
    if (page > 1 && hasMore) {
      const fetchData = async () => {
        try {
          const res = await fetchSubjects({
            page,
            quiz: true,
            t: Date.now(),
          });

          if (res.data && res.data.length > 0) {
            const newData = [...subjectsWithQuiz, ...res.data];

            setSubjectsWithQuiz(newData);

            if (res.data.length < LIMIT_ITEM) {
              setHasMore(false);
            }
          } else {
            setHasMore(false);
          }
        } catch (error) {
          setHasMore(false);
          console.error(error);
        }
      };

      fetchData();
    }
  }, [page, hasMore]);

  return subjectsWithQuiz.length ? (
    <InfiniteScroll
      loader={<CircularProgress />}
      fetchMore={() => setPage((prev) => prev + 1)}
      hasMore={hasMore}
    >
      {subjectsWithQuiz.map((subjectWithQuiz, index) => (
        <div className="mb-5" key={index}>
          <div className="d-flex align-items-center justify-content-between mb-1">
            <h4 className="featured-section-title mb-0">
              <Image
                className="me-2 mb-1"
                src={subjectWithQuiz.banner || "/assets/images/icons/starboy.png"}
                alt={subjectWithQuiz.title}
                width={24}
                height={ subjectWithQuiz.banner ? 24 : 27}
              />
              {subjectWithQuiz.title || "__"}
            </h4>
            <Link
              href={`/topic/${subjectWithQuiz.slug}-${subjectWithQuiz.id}`}
              className="see-all-link"
            >
              Xem thêm
            </Link>
          </div>
          <Swiper
            slidesPerView={5}
            spaceBetween={20}
            navigation={true}
            modules={[Navigation]}
            breakpoints={{
              0: {
                slidesPerView: 1.3,
              },
              450: {
                slidesPerView: 2,
              },
              639: {
                slidesPerView: 2.5,
              },
              900: {
                slidesPerView: 3.5,
              },
              1200: {
                slidesPerView: 4.2,
              },
              1400: {
                slidesPerView: 5,
              },
            }}
            className="mySwiper"
          >
            {subjectWithQuiz.quiz.map((quiz) => (
              <SwiperSlide className="h-auto" key={quiz.id}>
                <Card
                  className="h-100 shadow"
                >
                  <CardActionArea component={Link} href={`/quiz/${quiz.slug}-${quiz.id}`}>
                    <CardMedia>
                      <NextImage
                        src={quiz.banner}
                        alt={quiz.title}
                      />
                    </CardMedia>
                    <CardContent sx={{ padding: "10px" }}>
                      <div className="d-flex align-items-center justify-content-between mb-2">
                        <span className="badge bg-light text-dark">{ quiz.type_text }</span>
                        <span className="text-black-50 fs-12 text-limit text-center" style={{'--lines': 1}}>
                          {subjectWithQuiz.title} - {quiz.grade?.title || ''}
                        </span>
                      </div>
                      <h4 className="quiz-name text-limit">
                        {quiz.title || "__"}
                      </h4>
                    </CardContent>
                  </CardActionArea>
                  <CardActions
                    className="justify-content-between text-black-50 fs-14"
                    sx={{ padding: "5px 10px" }}
                  >
                    <span>{quiz.questions_count} câu hỏi</span>
                    <span>{quiz.view} lượt thi</span>
                  </CardActions>
                </Card>
              </SwiperSlide>
            ))}
          </Swiper>
        </div>
      ))}
    </InfiniteScroll>
  ) : (
    ""
  );
};

export default DisplaySubjects;
