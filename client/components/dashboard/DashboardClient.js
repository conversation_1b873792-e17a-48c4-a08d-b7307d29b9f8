"use client";

import React, { useState, useEffect } from 'react';
import { useSelector } from "react-redux";
import WelcomeDialogSystem from '@/components/welcome/WelcomeDialogSystem';

const DashboardClient = ({
  children,
  availableRoles = ['student', 'teacher']
}) => {
  const { user, loading } = useSelector((state) => state.auth);
  const [showWelcomeDialog, setShowWelcomeDialog] = useState(false);

  useEffect(() => {
    // Check if user needs to see welcome dialog
    if (!loading && user) {
      // Check if user has no roles assigned
      const hasRoles = user.roles && user.roles.length > 0;

      if (!hasRoles) {
        setShowWelcomeDialog(true);
      }
    }
  }, [user, loading]);

  const handleWelcomeComplete = () => {
    setShowWelcomeDialog(false);
    // Optionally refresh the page or update user data
    window.location.reload();
  };

  return (
    <>
      {children}
      {showWelcomeDialog && (
        <WelcomeDialogSystem
          user={user}
          availableRoles={availableRoles}
          onComplete={handleWelcomeComplete}
        />
      )}
    </>
  );
};

export default DashboardClient;
