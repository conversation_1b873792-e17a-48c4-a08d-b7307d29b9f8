"use client";

import Avatar from "@mui/material/Avatar";

import { Swiper, SwiperSlide } from "swiper/react";
import Link from 'next/link'
import "swiper/css";
import "swiper/css/pagination";

import { Pagination } from "swiper/modules";
import {optionBackgrounds} from "@/constant";

const ListSubjects = ({ subjects = [] }) => {
  if (subjects.length == 0) return null;

  return (
    <div className="mb-4 px-md-5">
      <Swiper
        slidesPerView={9}
        spaceBetween={10}
        pagination={{
          clickable: true,
        }}
        modules={[Pagination]}
        breakpoints={{
          0: {
            slidesPerView: 2,
          },
          375: {
            slidesPerView: 3,
          },
          639: {
            slidesPerView: 5,
          },
          900: {
            slidesPerView: 6,
          },
          1200: {
            slidesPerView: 7,
          },
          1400: {
            slidesPerView: 9,
          },
        }}
        className="mySwiper-base"
      >
        {subjects.map((subject, index) => (
          <SwiperSlide key={subject.id}>
            <Link
              className="text-dark fw-normal"
              style={{
                textAlign: "center",
                display: "inline-block",
                padding: "10px 0",
                margin: "10px 0",
              }}
              href={`/topic/${subject.slug}-${subject.id}`}
            >
              <Avatar
                sx={{
                  margin: "10px auto",
                  width: 32,
                  height: 32,
                  padding: "12px",
                  color: "#fff",
                  background: subject.banner ? "#f8f9fa" : optionBackgrounds[index % 5]
              }}
                alt={subject.title || ""}
                src={subject?.banner}
              >
                {subject.title?.charAt(0)}
              </Avatar>
              <p className="fs-17">{subject.title || "__"}</p>
            </Link>
          </SwiperSlide>
        ))}
      </Swiper>
    </div>
  );
};

export default ListSubjects;
