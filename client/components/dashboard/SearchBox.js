"use client";

import React, { useState, useCallback, useEffect } from "react";
import Paper from "@mui/material/Paper";
import InputBase from "@mui/material/InputBase";
import Button from "@mui/material/Button";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import Logo from "@/components/Logo";
import { fetchQuizByCode } from "@/actions/quizAction";
import { useDispatch, useSelector } from "react-redux";
import { useRouter } from "next/navigation";
import { classroomApiSlice } from "@/slices/features/classroomApiSlice";
import { storeStudent } from "@/actions/classroomAction";
import DialogJoinClass from "./DialogJoinClass";

const SearchBox = () => {
  const { user } = useSelector((state) => state.auth);
  const [userName, setUserName] = useState("");
  const [codeValue, setCodeValue] = useState("");
  const [errorMsg, setErrorMsg] = useState("");
  const [classRoomInfo, setClassRoomInfo] = useState(null);
  const [isAutoSearchTriggered, setIsAutoSearchTriggered] = useState(false);
  const [isAutoSearching, setIsAutoSearching] = useState(false);
  const router = useRouter();
  const dispatch = useDispatch();

  // Helper function to clean up URL parameters after successful auto-search
  const cleanUpUrlParams = useCallback(() => {
    if (typeof window === 'undefined') return;

    try {
      const url = new URL(window.location);
      if (url.searchParams.has('gc')) {
        url.searchParams.delete('gc');
        // Update URL without triggering a page reload
        window.history.replaceState({}, '', url.toString());
      }
    } catch (error) {
      console.error('SearchBox - Error cleaning up URL params:', error);
    }
  }, []);

  // Extract gc parameter from URL and auto-trigger search
  useEffect(() => {
    const extractGcFromUrl = () => {
      try {
        // Check if we're in the browser environment
        if (typeof window === 'undefined') return null;

        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get('gc');
      } catch (error) {
        return null;
      }
    };

    const handleAutoSearch = async (gcValue) => {
      if (!gcValue || isAutoSearchTriggered) return;

      // Set the code value first
      setCodeValue(gcValue);
      setIsAutoSearchTriggered(true);

      // Clear any existing error messages
      setErrorMsg("");

      // Trigger the search after a small delay to ensure state is updated
      setTimeout(async () => {
        try {
          setIsAutoSearching(true);

          const response = await fetchQuizByCode(gcValue);
          const { classroom, quiz_id, id } = response.data;

          if (!classroom || classroom.users_count === 1) {
            // Clean up URL before redirecting
            cleanUpUrlParams();
            router.push(`/join/quiz/${quiz_id}/start?assignment=${id}`);
          } else {
            setClassRoomInfo(response.data);
            setUserName(user?.name || "");
            // Clean up URL after successful search
            cleanUpUrlParams();
          }
        } catch (error) {
          setErrorMsg(error.message || "Không tìm thấy quiz với mã này");
        } finally {
          setIsAutoSearching(false);
        }
      }, 100);
    };

    // Only run on component mount
    const gcParam = extractGcFromUrl();
    if (gcParam && !isAutoSearchTriggered) {
      handleAutoSearch(gcParam);
    }
  }, [user, router, isAutoSearchTriggered, cleanUpUrlParams]); // Dependencies for the effect

  const submitSearchQuiz = async () => {
    if (!codeValue?.trim()) {
      setErrorMsg("Vui lòng nhập mã tham gia.");
      return;
    }

    try {
      // Clear any existing error messages
      setErrorMsg("");

      const response = await fetchQuizByCode(codeValue);
      const { classroom, quiz_id, id } = response.data;

      if (!classroom || classroom.users_count === 1) {
        router.push(`/join/quiz/${quiz_id}/start?assignment=${id}`);
      } else {
        setClassRoomInfo(response.data);
        setUserName(user?.name || "");
      }
    } catch (error) {
      setErrorMsg(error.message || "Không tìm thấy quiz với mã này");
    }
  };

  const joinClassRoom = useCallback(async () => {
    if (!classRoomInfo.classroom.id) return;

    try {
      storeStudent(classRoomInfo.classroom.id, {
        name: userName,
        email: user?.email,
      });

      dispatch(
        classroomApiSlice.util.invalidateTags([
          { type: "Classroom", id: `STUDENTS-${classRoomInfo.classroom.id}` }
        ])
      );

      router.push(`/join/quiz/${classRoomInfo.quiz_id}/start?assignment=${classRoomInfo.id}`);
    } catch (error) {
      console.error("Lỗi khi tham gia lớp học");
    }
  }, [classRoomInfo, userName, dispatch, router]);

  return (
    <>
      <div className="m-auto my-3 my-md-5 text-center">
        <Logo />
      </div>
      <Paper
        className="mb-4 mx-auto position-relative"
        component="form"
        sx={{
          p: "2px 4px",
          display: "flex",
          alignItems: "center",
          borderRadius: "0.75rem",
          borderWidth: "2px",
          borderStyle: "solid",
          borderColor: errorMsg ? "#ec0b43" : "#09090933",
          width: "100%",
          maxWidth: "700px",
        }}
      >
        <InputBase
          className="input-large joinGame-input"
          sx={{ ml: 1, flex: 1 }}
          placeholder={isAutoSearching ? "Đang tìm kiếm..." : "Nhập mã tham gia"}
          inputProps={{ "aria-label": "Nhập mã tham gia" }}
          value={codeValue}
          onChange={(e) => setCodeValue(e.target.value)}
          disabled={isAutoSearching}
        />
          {errorMsg && (
            <Box sx={{ pr: 1 }}>
              <i className="bi bi-info-circle-fill" style={{ color: '#ec0b43', fontSize: '24px', paddingRight: '10px' }} />
            </Box>
          )}
        <Button
          className="joinGame-button"
          variant="contained"
          size="large"
          type="button"
          sx={{ m: "5px" }}
          aria-label="search"
          onClick={submitSearchQuiz}
          disabled={isAutoSearching}
        >
          {isAutoSearching ? "Đang tìm..." : "Tham gia"}
        </Button>
      </Paper>
      {errorMsg && (
        <p className="mx-auto error-message">{errorMsg}</p>
      )}
      {isAutoSearching && (
        <Box sx={{ textAlign: 'center', mt: 2 }}>
          <Typography variant="body2" color="primary">
            Đang tự động tìm kiếm quiz với mã: {codeValue}
          </Typography>
        </Box>
      )}
      <DialogJoinClass
        open={classRoomInfo != null}
        onClose={() => setClassRoomInfo(null)}
        classRoomInfo={classRoomInfo}
        userName={userName}
        onUserNameChange={setUserName}
        onJoinClassRoom={joinClassRoom}
      />
    </>
  );
};

export default SearchBox;
