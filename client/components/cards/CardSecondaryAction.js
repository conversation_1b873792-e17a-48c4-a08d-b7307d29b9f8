import PropTypes from 'prop-types';
import Link from 'next/link'

// material-ui
import ButtonBase from '@mui/material/ButtonBase';

export default function CardSecondaryAction({ title, link, icon }) {
  return (
    <ButtonBase disableRipple>
      <Link href={link}>
        { title || '' }
        {icon && icon}
      </Link>
    </ButtonBase>
  );
}

CardSecondaryAction.propTypes = {
  title: PropTypes.string,
  link: PropTypes.string,
  icon: PropTypes.oneOfType([PropTypes.node, PropTypes.string])
};
