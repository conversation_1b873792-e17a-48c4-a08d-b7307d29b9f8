import React from "react";
import Link from "next/link";

import Card from "@mui/material/Card";
import CardActionArea from "@mui/material/CardActionArea";
import CardContent from "@mui/material/CardContent";
import CardMedia from "@mui/material/CardMedia";
import CardActions from "@mui/material/CardActions";

import { timeElapsedString } from "@/utils/helpers";
import NextImage from "@/components/NextImage";

const QuizActivityCard = ({ playedQuiz, href, sx }) => {
  const {
    quiz = {},
    total = 0,
    total_correct = 0,
    total_wrong = 0,
  } = playedQuiz;

  const accuracyPercentage = total > 0 ? (total_correct / total) * 100 : 0;
  const notDoing = total - total_correct - total_wrong;

  return (
    <Card className="shadow" sx={sx}>
      <CardActionArea
        component={Link}
        href={href}
      >
        <CardMedia>
          <NextImage
            src={quiz.banner}
            alt={quiz.title}
          />
          {playedQuiz.assignment_id && (
            <div className="game-type-info">
              <span className="game-type-icon">
                <i className="bi bi-person-workspace"></i>
              </span> &nbsp; Đ<PERSON><PERSON><PERSON> giao
            </div>
          )}
        </CardMedia>
        <CardContent sx={{ padding: "10px" }}>
          <div className="d-flex align-items-center justify-content-between mb-2">
            <span className="badge bg-light text-dark">{quiz.type_text}</span>
            <span className="text-black-50 fs-12 text-limit text-center" style={{'--lines': 1}}>
              {quiz.subject?.title} - {quiz.grade?.title}
            </span>
          </div>
          <h4 className="quiz-name text-limit" style={{minHeight: "43px"}}>
            {quiz.title || "__"}
          </h4>
          <div className="d-flex flex-wrap gap-2 align-items-center justify-content-between text-black-50 fs-12">
            <span>{quiz.questions_count} câu hỏi</span>
            <span>{timeElapsedString(playedQuiz.created_at)}</span>
          </div>
        </CardContent>
      </CardActionArea>
      <CardActions
        sx={{
          justifyContent: "space-between",
          padding: "5px 10px",
        }}
      >
        <div
          className={`q-progress m-0 ${
            playedQuiz.status === 2
              ? accuracyPercentage < 50
                ? "q-progress-danger"
                : accuracyPercentage > 80
                ? "q-progress-success"
                : "q-progress-warning"
              : ""
          }`}
        >
          <div className="q-progress-main">
            <div
              className="q-progress-bar q-progress-active"
              data-percent={accuracyPercentage}
              style={{
                width: `${accuracyPercentage}%`,
                transition: "none 0s ease 0s",
              }}
            >
              <span className="q-progress-label mx-3">
                {playedQuiz.status === 2
                  ? `Độ chính xác: ${Math.round(accuracyPercentage)}%`
                  : notDoing === total
                  ? "Bạn chưa làm câu nào"
                  : `Hoàn thành ${total - notDoing} câu`}
              </span>
            </div>
          </div>
        </div>
      </CardActions>
    </Card>
  );
};

export default QuizActivityCard;
