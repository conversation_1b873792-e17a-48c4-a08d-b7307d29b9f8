import React, { useState, useEffect } from "react";

import { styled, useTheme } from '@mui/material/styles';
import Button from '@mui/material/Button';
import Drawer from '@mui/material/Drawer';
import IconButton from '@mui/material/IconButton';
import Badge from '@mui/material/Badge';
import Box from '@mui/material/Box';

import ChevronLeftIcon from "@mui/icons-material/ChevronLeft";

import CheckboxList from "@/components/CheckboxList";

const StyledBadge = styled(Badge)(({ theme }) => ({
  '& .MuiBadge-badge': {
    right: -22,
    top: '50%',
    transform: 'translateY(-50%)',
    border: `2px solid ${(theme.vars ?? theme).palette.background.paper}`,
    padding: '0 4px',
  },
}));

function SearchBar({ open, onClose, dataFilter, defaultFilter, onApply, onClear }) {
  console.log('SearchBar');
  const [localFilter, setLocalFilter] = useState(defaultFilter);

  useEffect(() => {
    setLocalFilter(defaultFilter);
  }, [defaultFilter]);

  const handleChangeCheckbox = (key, value, checked) => {
    setLocalFilter((prev) => {
      const prevValues = prev[key] || [];
      const newValues = checked ? [...prevValues, value] : prevValues.filter((v) => v !== value);
      return { ...prev, [key]: newValues };
    });
  };

  const handleApply = () => {
    onApply(localFilter);
  };

  return (
    <Drawer anchor="right" open={open} onClose={onClose}
      PaperProps={{ className: "w-100 w-lg-50" }}
    >
      <Box className="px-3 py-2 bg-white h-100">
        <Box className="d-flex align-items-center mb-2">
          <IconButton className="me-3" onClick={onClose}>
            <ChevronLeftIcon />
          </IconButton>
          <h4 className="m-0 h5">Bộ lọc</h4>
        </Box>
        <Box className="row border-top" sx={{ height: "calc(100% - 110px)" }}>
          <Box className="col-12 h-100">
            <Box className="d-flex flex-column flex-shrink-0 h-100 p-3 overflow-auto">
              {dataFilter && (
                <>
                  {dataFilter?.grades?.length > 0 && (
                    <div className="border rounded-3 p-3 mb-3">
                      <p className="mb-2">
                        <StyledBadge
                          badgeContent={localFilter?.grades.length || 0}
                          color="secondary"
                        >
                          <span>Lớp</span>
                        </StyledBadge>
                      </p>
                      <CheckboxList
                        options={dataFilter.grades}
                        selectedValues={localFilter.grades}
                        onChange={(value, checked) =>
                          handleChangeCheckbox("grades", value, checked)
                        }
                      />
                    </div>
                  )}
                  {dataFilter?.subjects?.length > 0 && (
                    <div className="border rounded-3 p-3 mb-3">
                      <p className="mb-2">
                        <StyledBadge
                          badgeContent={localFilter?.subjects.length || 0}
                          color="secondary"
                        >
                          <span>Môn</span>
                        </StyledBadge>
                      </p>
                      <CheckboxList
                        options={dataFilter.subjects}
                        selectedValues={localFilter.subjects}
                        onChange={(value, checked) =>
                          handleChangeCheckbox("subjects", value, checked)
                        }
                      />
                    </div>
                  )}
                  {dataFilter?.books?.length > 0 && (
                    <div className="border rounded-3 p-3">
                      <p className="mb-2">
                        <StyledBadge
                          badgeContent={localFilter?.books.length || 0}
                          color="secondary"
                        >
                          <span>Sách</span>
                        </StyledBadge>
                      </p>
                      <CheckboxList
                        options={dataFilter.books}
                        selectedValues={localFilter.books}
                        onChange={(value, checked) =>
                          handleChangeCheckbox("books", value, checked)
                        }
                      />
                    </div>
                  )}
                </>
              )}
            </Box>
          </Box>
        </Box>
        <Box className="border-top p-3 d-flex justify-content-end gap-3">
          <Button variant="contained" color="error" onClick={onClear}>
            Xóa hết
          </Button>
          <Button variant="contained" color="secondary" onClick={handleApply}>
            Áp dụng các bộ lọc
          </Button>
        </Box>
      </Box>
    </Drawer>
  );
}

export default React.memo(SearchBar);
