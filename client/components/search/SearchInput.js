"use client";

import React, { useMemo, useState, useEffect } from "react";
import Autocomplete from "@mui/material/Autocomplete";
import TextField from "@mui/material/TextField";
import InputAdornment from "@mui/material/InputAdornment";
import CircularProgress from "@mui/material/CircularProgress";
import Box from "@mui/material/Box";

import SearchIcon from "@mui/icons-material/Search";

import debounce from "lodash/debounce";

async function fetchKeyword(query) {
  // Giả lập delay 500ms
  return new Promise((resolve) => {
    setTimeout(() => {
      const topFilms = [
        { title: "The Shawshank Redemption", id: 1 },
        { title: "The Godfather", id: 2 },
        { title: "The Godfather: Part II", id: 3 },
        { title: "The Dark Knight", id: 4 },
        { title: "12 Angry Men", id: 5 },
        { title: "Schindler's List", id: 6 },
        { title: "Pulp Fiction", id: 7 },
        { title: "The Lord of the Rings: The Return of the King", id: 8 },
        { title: "The Good, the Bad and the Ugly", id: 9 },
        { title: "Fight Club", id: 10 },
        { title: "The Lord of the Rings: The Fellowship of the Ring", id: 11 },
        { title: "Star Wars: Episode V - The Empire Strikes Back", id: 12 },
        { title: "Forrest Gump", id: 13 },
        { title: "Inception", id: 14 },
        { title: "The Lord of the Rings: The Two Towers", id: 15 },
      ];
      const filtered =
        query.trim() === ""
          ? topFilms.slice(0, 5)
          : topFilms.filter((film) =>
              film.title.toLowerCase().includes(query.toLowerCase())
            ).slice(0, 5);
      resolve(filtered);
    }, 100);
  });
}

const SearchInput = React.memo(function SearchInput({
  initialValue,
  onSearch,
}) {
  console.log('SearchInput');
  const [inputValue, setInputValue] = useState(initialValue);
  const [open, setOpen] = useState(false);
  const [options, setOptions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [isFirstRender, setIsFirstRender] = useState(true);

  useEffect(() => {
    setInputValue(initialValue);
  }, [initialValue]);

  const fetchKeywordHandler = async () => {
    setLoading(true);
    const result = await fetchKeyword(inputValue);
    setOptions(result);
    setLoading(false);
  };

  const debouncedFetch = useMemo(() => debounce(fetchKeywordHandler, 700), [inputValue]);

  useEffect(() => {
    if (inputValue) {
      debouncedFetch();
    }
    return () => {
      debouncedFetch.cancel();
    };
  }, [inputValue, debouncedFetch]);

  const handleOpen = () => {
    setOpen(true);

    if (options.length == 0) fetchKeywordHandler();
  };

  const handleClose = () => setOpen(false);

  const handleInputChange = (event, newInputValue) => {
    setInputValue(newInputValue);
  };

  const handleChange = (newKeyword) => {
    onSearch(newKeyword);
    handleClose();
  };

  return (
    <Autocomplete
      sx={{ width: { xs: "100%", md: 400, lg: 500 } }}
      open={open}
      freeSolo
      size="small"
      value={inputValue ? { title: inputValue } : null}
      onOpen={handleOpen}
      onClose={handleClose}
      isOptionEqualToValue={(option, value) => option.title === value.title}
      getOptionLabel={(option) => option.title || ""}
      options={options}
      loading={loading}
      popupIcon={null}
      onInputChange={handleInputChange}
      renderOption={(props, option, { selected }) => {
        const { key, ...optionProps } = props;
        return (
          <li key={key} {...optionProps} onClick={() => handleChange(option.title)}>
            <Box
              component={SearchIcon}
              sx={{ width: 17, height: 17, mr: '5px', ml: '-2px' }}
            />
            <Box>
              {option.title}
            </Box>
          </li>
        );
      }}
      renderInput={(params) => (
        <TextField
          {...params}
          placeholder="Search..."
          onKeyDown={(e) => {
            if (e.key === "Enter") {
              e.preventDefault();
              const newValue = e.target.value;

              if (newValue.trim() !== initialValue.trim()) {
                onSearch(newValue);
              }

              handleClose();
            }
          }}
          slotProps={{
            input: {
              ...params.InputProps,
              startAdornment: (
                <>
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                  {params.InputProps.startAdornment}
                </>
              ),
              endAdornment: (
                <>
                  {loading ? <CircularProgress color="inherit" size={20} /> : null}
                  {params.InputProps.endAdornment}
                </>
              ),
            },
          }}
          sx={{
            "& .MuiOutlinedInput-root": {
              borderRadius: "5px",
              "& fieldset": { borderRadius: "5px" },
              "& input": { height: "2em" },
            },
          }}
        />
      )}
    />
  );
});

export default SearchInput;
