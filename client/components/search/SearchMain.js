"use client";

import React, { useState, useEffect, useRef } from "react";
import { useSelector } from "react-redux";
import Link from "next/link";
import { useRouter } from "next/navigation";

import Card from "@mui/material/Card";
import CardContent from "@mui/material/CardContent";
import CircularProgress from "@mui/material/CircularProgress";
import IconButton from "@mui/material/IconButton";
import MoreVertIcon from '@mui/icons-material/MoreVert';
import AppBar from '@mui/material/AppBar';
import Toolbar from '@mui/material/Toolbar';
import Box from '@mui/material/Box';
import Button from "@mui/material/Button";

import { useTranslations } from "next-intl";
import { toast } from "react-hot-toast";

import Dropdown from "@/components/Dropdown";
import NoDataOverlay from "@/components/NoDataOverlay";
import InfiniteScroll from "@/components/InfiniteScroll";
import QuizMedia from "@/components/quizzes/QuizMedia";
import QuestionCard from "@/components/questions/QuestionCard";

import useMathJax from '@/hooks/useMathJax';
import { copyQuiz } from "@/actions/quizAction";

export default function SearchMain({ quizzes, hasMore, fetchMore }) {
  const t = useTranslations("Common");
  const router = useRouter();
  const { user } = useSelector((state) => state.auth);

  const [quizSelected, setQuizSelected] = useState(null);
  const containerRef = useRef(null);
  const [isLoading, setIsLoading] = useState(false);

  useMathJax([quizSelected]);

  useEffect(() => {
    if (quizzes?.length > 0) {
      setQuizSelected(quizzes[0]);
    }
  }, [quizzes]);


  const handleCopyQuiz = async () => {
    if (isLoading || !quizSelected) return;

    if (user) {
      setIsLoading(true);

      try {
        const { data: newQuiz } = await copyQuiz(quizSelected.id);
        toast.success("Sao chép quiz thành công");
        router.push(`/dashboard/quiz/${newQuiz.id}/edit`);
      } catch (error) {
        console.error(error);
        toast.error("Không thể sao chép quiz. Vui lòng thử lại sau.");
        setIsLoading(false);
      }
    } else {
      router.push(`/login?backUrl=${encodeURIComponent(`/quiz/${quizSelected.slug}-${quizSelected.id}`)}`);
    }
  };

  return (
    <div className="position-relative pt-4">
      {Array.isArray(quizzes) ? (
        quizzes.length == 0 ? (
          <NoDataOverlay />
        ) : (
          <div className="row">
            <div className="col-lg-5 col-xl-4 mb-3">
              <InfiniteScroll
                loader={<CircularProgress />}
                className="px-1 overflow-auto media-group media-lg-group pb-lg-5"
                fetchMore={fetchMore}
                hasMore={hasMore}
                endMessage={<p className="text-center">Hết!</p>}
              >
                {quizzes.map((quizItem) => (
                  <QuizMedia
                    key={quizItem.id}
                    quiz={quizItem}
                    bannerWidth="75px"
                    bannerHeight="75px"
                    active={quizSelected && quizSelected.id == quizItem.id}
                    onQuizSelected={(item) => setQuizSelected(item)}
                  />
                ))}
              </InfiniteScroll>
            </div>
            <div ref={containerRef} className="col-lg-7 col-xl-8">
              {quizSelected ? (
                <div style={{ height: 'calc(100vh - 100px)', border: '1px solid #8854c0', borderRadius: '6px' }} className="sticky-top">
                  <Card className="d-flex flex-column h-100"
                    sx={{
                      overflow: 'hidden'
                    }}>
                    <AppBar
                      position="relative"
                      color="default"
                      elevation={0}
                      sx={{
                        borderBottom: '1px solid rgba(0, 0, 0, 0.12)',
                        backgroundColor: 'white',
                      }}
                    >
                      <Toolbar className="p-3">
                        <div style={{
                          maxWidth: 'calc(100% - 120px)',
                          overflow: 'hidden'
                        }}>
                          <h5 className="fw-bold mb-1" style={{
                            overflow: 'hidden',
                            lineHeight: '1.4',
                            wordBreak: 'break-word'
                          }}>
                            {quizSelected.title}
                          </h5>
                          <p className="text-muted mb-0">{quizSelected.questions.length} câu hỏi</p>
                        </div>
                        <Box sx={{ flexGrow: 1 }} />
                        <Dropdown
                          renderToggle={({ onClick }) => (
                            <IconButton
                              size="small"
                              onClick={onClick}
                            >
                              <MoreVertIcon />
                            </IconButton>
                          )}
                          placement="bottom-end"
                          renderMenu={() => (
                            <div className="dropdown-menu dropdown-menu-2 p-1">
                              <Link href={`/quiz/${quizSelected.slug}-${quizSelected.id}`} className="dropdown-item fs-14">
                                <i className="bi bi-info-circle me-2"></i> Xem chi tiết
                              </Link>
                              <Button
                                onClick={handleCopyQuiz}
                                disabled={isLoading}
                                className="dropdown-item fs-14 text-start"
                              >
                                {isLoading ? (
                                  <CircularProgress size={16} color="inherit" className="me-2" />
                                ) : (
                                  <i className="bi bi-pencil-square me-2"></i>
                                )}
                                Sao chép và chỉnh sửa
                              </Button>
                            </div>
                          )}
                        />
                        <Dropdown
                          className="ms-2"
                          renderToggle={({ onClick }) => (
                            <button
                              className="btn btn-primary2 btn-sm dropdown-toggle"
                              type="button"
                              onClick={onClick}
                            >
                              <i className="bi bi-play-circle me-1"></i> Chơi
                            </button>
                          )}
                          placement="bottom-end"
                          renderMenu={() => (
                            <div className="dropdown-menu dropdown-menu-2 p-1">
                              <Link href={`/join/quiz/${quizSelected.id}/start`} className="dropdown-item fs-14">
                                <i className="bi bi-play-circle me-2"></i> Thi thử
                              </Link>
                              <Link href={`/dashboard/quiz/${quizSelected.id}/homework`} className="dropdown-item fs-14">
                                <i className="bi bi-send-plus me-2"></i> Giao bài
                              </Link>
                            </div>
                          )}
                        />
                      </Toolbar>
                    </AppBar>
                    {quizSelected?.questions?.length ? (
                      <div className="overflow-auto flex-grow-1">
                        {quizSelected.questions.map((question, index) => (
                          <div key={question.id} className={`${index > 0 ? 'border-top' : ''}`}>
                            <div className="p-3">
                              <div className="d-flex flex-wrap align-items-center mb-2">
                                <span className="fw-normal my-1 text-muted fs-14">
                                  {index + 1}. {t(question.type)}
                                </span>
                              </div>
                              <CardContent className="p-0">
                                <QuestionCard question={question} showAnswer={true} />
                              </CardContent>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : <NoDataOverlay message="Danh sách câu hỏi trống!" /> }
                  </Card>
                </div>
              ) : (
                <div className="fs-20 text-center py-5 text-black-50 border border-warning rounded bg-white">
                  <p className="mt-3">Chọn một quiz để xem nội dung ở đây</p>
                </div>
              )}
            </div>
          </div>
        )
      ) : (
        <p className="text-center py-2"><CircularProgress /></p>
      )}
    </div>
  );
}
