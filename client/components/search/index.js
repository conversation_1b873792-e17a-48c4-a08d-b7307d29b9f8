"use client";

import React, { useMemo, useEffect, useState, useCallback } from "react";
import { useRouter, useSearchParams } from 'next/navigation'

import { styled, useTheme } from '@mui/material/styles';
import GlobalStyles from "@mui/material/GlobalStyles";
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import FormControl from "@mui/material/FormControl";
import Select from "@mui/material/Select";
import OutlinedInput from "@mui/material/OutlinedInput";
import MenuItem from "@mui/material/MenuItem";
import Checkbox from "@mui/material/Checkbox";
import ListItemText from "@mui/material/ListItemText";
import Container from '@mui/material/Container';

import TuneIcon from '@mui/icons-material/Tune';

import LogoSection from '@/layout/MainLayout/LogoSection';
import CheckboxList from '@/components/CheckboxList';
import SearchMain from "./SearchMain";
import SearchBar from "./SearchBar";
import SearchInput from "./SearchInput";

import { perPage, orderOptions } from '@/constant';
import { buildQueryString } from '@/utils/helpers';
import { fetchOptions } from "@/actions/onlyClientAction";
import { fetchQuizzes } from '@/actions/quizAction';

import debounce from "lodash/debounce";
import { useTranslations } from "next-intl";

const Header = styled("header")(({ theme }) => [
  {
    position: "sticky",
    top: 0,
    transition: theme.transitions.create("top"),
    zIndex: theme.zIndex.appBar,
    backgroundColor: "rgba(255,255,255,0.8)",
    backdropFilter: "blur(8px)",
    borderBottom: `1px solid ${(theme.vars || theme).palette.divider}`,
  },
]);

const MainContentStyled = styled('main')(({ theme }) => ({
  backgroundColor: theme.palette.grey[100],
  minWidth: '1%',
  width: '100%',
  minHeight: 'calc(100vh - 62px)',
}));

const HEIGHT = 60;
const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
      minwidth: 150,
    },
  },
};

const INIT_FILTER = {
  grades: [],
  subjects: [],
  books: [],
};

export default function SearchApp(props) {
  console.log('SearchApp');
  const t = useTranslations("Common");
  const searchParams = useSearchParams();
  const router = useRouter();
  const theme = useTheme();

  const initialKeyword = searchParams.get("q") || "";
  const [keyword, setKeyword] = useState(initialKeyword);

  const initialFilter = useMemo(
    () => ({
      grades: searchParams.get("grades") ? searchParams.get("grades").split(",").map(Number) : [],
      subjects: searchParams.get("subjects") ? searchParams.get("subjects").split(",").map(Number) : [],
      books: searchParams.get("books") ? searchParams.get("books").split(",").map(Number) : [],
    }),
    [searchParams]
  );

  const [filter, setFilter] = useState(() => ({ ...INIT_FILTER, ...initialFilter }));
  const [openSidebar, setOpenSidebar] = useState(false);

  const [page, setPage] = useState(1);
  const [quizzes, setQuizzes] = useState(null);
  const [hasMore, setHasMore] = useState(false);

  const { data: dataFilter } = fetchOptions("grades,subjects,books");

  const handleSearch = (newKeyword) => {
    if (newKeyword.trim() === keyword.trim()) {
      return;
    }

    setQuizzes([]);
    setPage(1);
    setKeyword(newKeyword);
  }

  const fetchData = async (params) => {
    try {
      const filterParam = { ...filter };

      Object.keys(filterParam).forEach((key) => {
        if (filterParam[key].length > 0) {
          filterParam[key] = filterParam[key].join(",");
        }
      });

      const res = await fetchQuizzes({
        limit: perPage,
        q: keyword,
        ...filterParam,
        ...params
      });

      let newData = res.data;
      if (page > 1) {
        newData = [...(quizzes || []), ...newData];
      }
      setQuizzes(newData);
      setHasMore(res.data.length >= perPage);
    } catch (error) {
      console.error("Error:", error);
    }
  };

  useEffect(() => {
    const queryString = buildQueryString({ keyword, filter });

    if (searchParams.toString() !== queryString) {
      router.push(`/search?${queryString}`, undefined, { shallow: true });
    }
  }, [keyword, filter, router, searchParams]);

  useEffect(() => {
    if (page > 1) fetchData({ page });
  }, [page]);

  useEffect(() => {
    fetchData({ page: 1 });
  }, [keyword, filter]);

  const handleToggleSidebar = () => setOpenSidebar((prev) => !prev);

  const handleChangeSelect = useCallback((event, key) => {
    const { value } = event.target;
    setFilter(prev => ({
      ...prev,
      [key]: typeof value === "string" ? value.split(",") : value,
    }));
  }, []);

  return (
    <>
      <Header>
        <GlobalStyles
          styles={{
            ":root": {
              "--MuiDocs-header-height": `${HEIGHT}px`,
            },
          }}
        />
        <Container maxWidth="cxl" className="d-flex align-items-center justify-content-between text-center gap-2 py-2" sx={{ minHeight: HEIGHT }}>
          <div className="d-none d-lg-inline me-3">
            <LogoSection />
          </div>
          <SearchInput
            initialValue={keyword}
            onSearch={handleSearch}
          />
          <div className="d-flex align-items-center text-center gap-2 ms-auto flex-shrink-0">
            {dataFilter && Object.keys(dataFilter).map((key, index) => (
              dataFilter[key].length > 0 && (
                <FormControl
                  key={index}
                  sx={{
                    ...theme.typography.customInput,
                    margin: 0,
                    '.MuiInputBase-inputSizeSmall': {
                      paddingTop: '5px!important',
                      paddingBottom: '5px!important',
                    },
                  }}
                  size="small"
                  className="d-none d-sm-block"
                >
                  <Select
                    id={`select-${key}`}
                    multiple
                    displayEmpty
                    value={filter[key]}
                    onChange={(event) => handleChangeSelect(event, key)}
                    input={<OutlinedInput label="Tag" />}
                    renderValue={(selected) => {
                      return <span className="text-capitalize fw-light">{t(key)}</span>;
                    }}
                    MenuProps={MenuProps}
                    inputProps={{ 'aria-label': 'Without label' }}
                  >
                    {dataFilter[key].map((option) => (
                      <MenuItem key={`${key}-${option.value}`} value={option.value}>
                        <Checkbox size="small" checked={filter[key].indexOf(option.value) > -1} />
                        <ListItemText primary={option.label} />
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              )
            ))}
            <Button
              onClick={handleToggleSidebar}
              sx={{ borderColor: '#bdbdbd', background: '#f8fafc' }}
              color="inherit"
              className="flex-shrink-0"
              variant="outlined"
              size="small"
              startIcon={<TuneIcon />}
            >Bộ lọc</Button>
          </div>
        </Container>
      </Header>
      <MainContentStyled>
        <Container maxWidth="cxl">
          <SearchMain
            quizzes={quizzes}
            hasMore={hasMore}
            fetchMore={() => setPage((prev) => prev + 1)}
          />
        </Container>
      </MainContentStyled>
      <SearchBar
        open={openSidebar}
        onClose={handleToggleSidebar}
        dataFilter={dataFilter}
        defaultFilter={filter}
        onApply={(newFilter) => {
          setFilter(newFilter);
          handleToggleSidebar();
        }}
        onClear={() => {
          setFilter(INIT_FILTER);
          handleToggleSidebar();
        }}
      />
    </>
  );
}
