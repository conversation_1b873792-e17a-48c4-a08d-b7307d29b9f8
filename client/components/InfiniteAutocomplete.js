import React, { useState, useEffect, useCallback, useRef } from "react";
import Box from "@mui/material/Box";
import TextField from "@mui/material/TextField";
import Autocomplete from "@mui/material/Autocomplete";
import { debounce, throttle } from "lodash";
import { perPage } from "@/constant";

export default function InfiniteAutocomplete({
  name,
  value,
  placeholder,
  readOnly,
  setFieldValue,
  loadData,
}) {
  const listboxRef = useRef(null);
  const [page, setPage] = useState(1);
  const [loading, setLoading] = useState(false);
  const [options, setOptions] = useState([]);
  const [keyword, setKeyword] = useState("");
  const [hasMore, setHasMore] = useState(true);
  const [selectedOption, setSelectedOption] = useState(null);

  const debouncedFetch = useCallback(
    debounce((searchKeyword) => {
      setPage(1);
      setOptions([]);
      fetchData(1, searchKeyword);
    }, 500),
    []
  );

  useEffect(() => {
    if (keyword) {
      debouncedFetch(keyword);
    }
  }, [keyword, debouncedFetch]);

  useEffect(() => {
    if (page > 1 || keyword === "") {
      fetchData(page, keyword);
    }
  }, [page]);

  const fetchData = useCallback(
    async (pageNumber, searchKeyword) => {
      if (loading || (!hasMore && pageNumber !== 1)) return;

      setLoading(true);

      try {
        const res = await loadData({
          page: pageNumber,
          limit: perPage,
          keyword: searchKeyword.trim().replace(/\s+/g, " "),
          id: value,
          orderBy: "oldest",
        });

        const newOptions = res.data || [];
        setOptions((prevOptions) =>
          pageNumber === 1 ? newOptions : [...prevOptions, ...newOptions]
        );

        if (value && !selectedOption) {
          setSelectedOption((prevSelectedOption) =>
            newOptions.find((opt) => opt.id === value)
          );
        }

        if (newOptions.length < perPage) {
          setHasMore(false);
        } else {
          setHasMore(true);
        }
      } finally {
        setLoading(false);
      }
    },
    [hasMore, loadData, value]
  );

  const handleScroll = useCallback(
    throttle(() => {
      const listboxNode = listboxRef.current;
      if (!listboxNode) return; // Ensure listboxNode is not null

      if (
        listboxNode.scrollHeight -
          (listboxNode.scrollTop + listboxNode.clientHeight) <
          10 &&
        hasMore &&
        !loading
      ) {
        setPage((prevPage) => prevPage + 1);
      }
    }, 300),
    [hasMore, loading]
  );

  const handleInputChange = (event, newInputValue) => {
    if (event && event.type === "change") {
      setKeyword(newInputValue);
    }
  };

  const bgReadOnly = readOnly
    ? { ".MuiInputBase-root, .MuiInputBase-input": { background: "#e9ecef" } }
    : {};

  return (
    <Autocomplete
      options={options}
      value={selectedOption}
      autoHighlight
      readOnly={readOnly}
      getOptionLabel={(option) => option.title}
      isOptionEqualToValue={(option, value) => option.id === value.id}
      onChange={(event, option) => {
        setSelectedOption(option);
        setFieldValue?.(name, option?.id || null);
      }}
      onInputChange={handleInputChange}
      sx={{
        ".MuiInputBase-input": { padding: "4px !important" },
        ...bgReadOnly,
      }}
      renderOption={(props, option) => (
        <Box component="li" {...props} key={option.id}>
          {option.title}
        </Box>
      )}
      renderInput={(params) => <TextField {...params} label={placeholder} />}
      ListboxProps={{
        ref: listboxRef,
        onScroll: handleScroll,
        style: { maxHeight: "200px", overflowY: "auto" },
      }}
      loading={loading}
    />
  );
}
