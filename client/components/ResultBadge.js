const ResultBadge = ({ isCorrect = null }) => {
  if (isCorrect === null) {
    return (
      <span className="badge bg-secondary fs-12">
        <i className="bi bi-exclamation-circle me-1"></i> Ch<PERSON><PERSON> làm
      </span>
    );
  }

  const badgeClass = isCorrect ? 'bg-success' : 'bg-danger';
  const iconClass = isCorrect ? 'bi-check-circle-fill' : 'bi-x-circle-fill';
  const text = isCorrect ? 'Đúng' : 'Sai';

  return (
    <span className={`badge ${badgeClass} fs-12`}>
      <i className={`bi ${iconClass} me-1`}></i> {text}
    </span>
  );
};

export default ResultBadge;
