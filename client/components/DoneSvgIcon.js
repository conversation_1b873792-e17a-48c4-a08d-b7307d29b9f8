import React from 'react';

const DoneSvgIcon = () => (
  <svg
    height="80"
    viewBox="0 0 107 123"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M95.5 2V8" stroke="#75A4FE" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M95.5 16V22" stroke="#75A4FE" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M85.5 12H91.5" stroke="#75A4FE" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M99.5 12H105.5" stroke="#75A4FE" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M12.5 105V109" stroke="#75A4FE" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M12.5 117V121" stroke="#75A4FE" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M4.5 113H8.5" stroke="#75A4FE" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M16.5 113H20.5" stroke="#75A4FE" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round" />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M83.7114 88.1263C85.1021 86.4232 86.3521 84.6421 87.4614 82.7987C89.9011 78.7443 91.6596 74.3884 92.7343 69.8975C93.8213 65.3555 94.2088 60.6755 93.8943 56.0296C93.6198 51.9756 92.8108 47.9475 91.4653 44.0598C90.1305 40.2028 88.2678 36.484 85.8753 33.015C84.4497 30.9479 82.8359 28.9696 81.0338 27.1036C78.3878 24.364 75.4856 22.0219 72.402 20.0787C69.0887 17.9908 65.5659 16.3634 61.9264 15.1984C57.3685 13.7393 52.6275 13.0054 47.8859 13C44.1965 12.9959 40.5068 13.4328 36.9025 14.3125C32.8447 15.3028 28.8953 16.8543 25.1766 18.9692C21.9076 20.8284 18.817 23.123 15.988 25.8547C12.4328 29.2875 9.55299 33.1572 7.35124 37.2985C5.19563 41.3531 3.68992 45.6682 2.83656 50.0887C1.95737 54.6429 1.77061 59.3091 2.27894 63.9178C2.9378 69.8911 4.76435 75.7677 7.7644 81.1785C9.68404 84.6407 12.0842 87.9123 14.9663 90.8964C19.5416 95.6338 24.8833 99.1826 30.6045 101.535C37.5619 104.397 45.0806 105.489 52.4647 104.8C58.662 104.221 64.7645 102.387 70.361 99.2902"
      fill="white"
    />
    <path
      d="M83.7114 88.1263C85.1021 86.4232 86.3521 84.6421 87.4614 82.7987C89.9011 78.7443 91.6596 74.3884 92.7343 69.8975C93.8213 65.3555 94.2088 60.6755 93.8943 56.0296C93.6198 51.9756 92.8108 47.9475 91.4653 44.0598C90.1305 40.2028 88.2678 36.484 85.8753 33.015C84.4497 30.9479 82.8359 28.9696 81.0338 27.1036C78.3878 24.364 75.4856 22.0219 72.402 20.0787C69.0887 17.9908 65.5659 16.3634 61.9264 15.1984C57.3685 13.7393 52.6275 13.0054 47.8859 13C44.1965 12.9959 40.5068 13.4328 36.9025 14.3125C32.8447 15.3028 28.8953 16.8543 25.1766 18.9692C21.9076 20.8284 18.817 23.123 15.988 25.8547C12.4328 29.2875 9.55299 33.1572 7.35124 37.2985C5.19563 41.3531 3.68992 45.6682 2.83656 50.0887C1.95737 54.6429 1.77061 59.3091 2.27894 63.9178C2.9378 69.8911 4.76435 75.7677 7.7644 81.1785C9.68404 84.6407 12.0842 87.9123 14.9663 90.8964C19.5416 95.6338 24.8833 99.1826 30.6045 101.535C37.5619 104.397 45.0806 105.489 52.4647 104.8C58.662 104.221 64.7645 102.387 70.361 99.2902"
      stroke="#1F64E7"
      strokeWidth="2.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path fillRule="evenodd" clipRule="evenodd" d="M73.7015 97.3782C76.6075 95.5081 79.1912 93.1749 80.8722 91.2913L73.7015 97.3782Z" fill="white" />
    <path d="M73.7015 97.3782C76.6075 95.5081 79.1912 93.1749 80.8722 91.2913" stroke="#1F64E7" strokeWidth="2.5" strokeLinecap="round" />
    <path d="M90 61.617C89.714 61.6221 89.4273 61.6246 89.14 61.6246C63.8865 61.6246 43.2087 41.9362 41.4759 17C21.3752 20.1706 6 37.6526 6 58.7434C6 82.0811 24.8255 101 48.0478 101C70.3092 101 88.5301 83.6145 90 61.617Z" fill="#E8F0FE" />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M32.0233 57.3729C30.8001 56.2433 28.9025 56.33 27.785 57.5666C26.6676 58.8032 26.7534 60.7214 27.9767 61.8511L42.0957 74.889C43.8083 76.4704 46.4649 76.349 48.0293 74.6178C48.0947 74.5442 48.0947 74.5442 48.1578 74.4687L68.3067 49.9716C69.366 48.6837 69.1919 46.7716 67.9179 45.7008C66.6439 44.63 64.7524 44.806 63.6931 46.0939L44.75 69.125L32.0233 57.3729Z"
      fill="white"
      stroke="#1F64E7"
      strokeWidth="2.5"
      strokeLinecap="round"
    />
    <path fillRule="evenodd" clipRule="evenodd" d="M33.8239 21.4131C31.8737 22.1447 29.9688 23.0338 28.1284 24.0806C25.2686 25.7072 22.5648 27.7149 20.0899 30.1049C19.0754 31.0846 18.1237 32.105 17.235 33.161M14.4851 36.8378C13.7785 37.9057 13.1281 39.0004 12.5342 40.1176C12.0956 40.9428 11.6877 41.7803 11.3106 42.6285" fill="white" />
    <path
      d="M34.263 22.5835C34.9093 22.341 35.2367 21.6204 34.9942 20.974C34.7517 20.3277 34.0312 20.0003 33.3848 20.2428L34.263 22.5835ZM28.1284 24.0806L28.7464 25.1671L28.1284 24.0806ZM20.0899 30.1049L19.2216 29.2057L20.0899 30.1049ZM16.2786 32.3562C15.8341 32.8844 15.902 33.6729 16.4302 34.1174C16.9584 34.5619 17.7469 34.4941 18.1914 33.9659L16.2786 32.3562ZM15.5275 37.5277C15.9085 36.9519 15.7507 36.1764 15.1749 35.7954C14.5992 35.4144 13.8237 35.5723 13.4427 36.148L15.5275 37.5277ZM12.5342 40.1176L13.638 40.7044L13.638 40.7044L12.5342 40.1176ZM10.1684 42.1206C9.8879 42.7515 10.1719 43.4902 10.8027 43.7707C11.4335 44.0511 12.1723 43.7671 12.4528 43.1363L10.1684 42.1206ZM33.3848 20.2428C31.3735 20.9973 29.4086 21.9143 27.5104 22.994L28.7464 25.1671C30.5289 24.1532 32.374 23.2921 34.263 22.5835L33.3848 20.2428ZM27.5104 22.994C24.5604 24.672 21.7724 26.7424 19.2216 29.2057L20.9582 31.0041C23.3573 28.6874 25.9768 26.7425 28.7464 25.1671L27.5104 22.994ZM19.2216 29.2057C18.176 30.2154 17.195 31.2673 16.2786 32.3562L18.1914 33.9659C19.0525 32.9427 19.9747 31.9538 20.9582 31.0041L19.2216 29.2057ZM13.4427 36.148C12.7138 37.2494 12.0431 38.3785 11.4305 39.5309L13.638 40.7044C14.2132 39.6222 14.8431 38.562 15.5275 37.5277L13.4427 36.148ZM11.4305 39.5309C10.978 40.382 10.5573 41.2459 10.1684 42.1206L12.4528 43.1363C12.818 42.3148 13.2131 41.5036 13.638 40.7044L11.4305 39.5309Z"
      fill="#75A4FE"
    />
  </svg>
);

export default DoneSvgIcon;
