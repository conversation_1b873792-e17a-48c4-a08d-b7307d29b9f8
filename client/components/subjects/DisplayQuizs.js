"use client";

import { useState, useEffect } from "react";
import Link from "next/link";

import { styled } from "@mui/material/styles";
import Card from "@mui/material/Card";
import CardActionArea from "@mui/material/CardActionArea";
import CardContent from "@mui/material/CardContent";
import CardMedia from "@mui/material/CardMedia";
import CardActions from "@mui/material/CardActions";
import CircularProgress from "@mui/material/CircularProgress";

import NextImage from "@/components/NextImage";
import InfiniteScroll from "@/components/InfiniteScroll";

import { fetchQuizsOfSubject } from "@/actions/subjectAction";

const CustomCard = styled(Card)(({ theme }) => ({
  margin: "0 10px 20px",
  width: "calc(20% - 20px)",
  boxSizing: "border-box",
  "@media (max-width: 1500px)": {
    width: "calc(25% - 20px)",
  },
  "@media (max-width: 1299px)": {
    width: "calc(33.33% - 20px)",
  },
  "@media (max-width: 1050px)": {
    width: "calc(50% - 20px)",
  },
  "@media (max-width: 500px)": {
    width: "calc(100% - 20px)",
  },
}));

const LIMIT_ITEM = 10;

const DisplayQuizs = ({ initialQuizzes = [], subject }) => {
  console.log("DisplayQuizs");
  const [quizDatas, setQuizDatas] = useState(initialQuizzes);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(!(initialQuizzes.length < LIMIT_ITEM));

  useEffect(() => {
    if (page > 1 && hasMore) {
      const fetchData = async () => {
        try {
          const res = await fetchQuizsOfSubject(subject.id, { page, limit: LIMIT_ITEM });

          if (res?.data?.quizzes?.length > 0) {
            const newData = [...quizDatas, ...res.data.quizzes];

            setQuizDatas(newData);

            if (res.data.quizzes.length < LIMIT_ITEM) {
              setHasMore(false);
            }
          } else {
            setHasMore(false);
          }
        } catch (error) {
          setHasMore(false);
          console.error(error);
        }
      };

      fetchData();
    }
  }, [page, hasMore]);

  return quizDatas.length > 0 ? (
    <InfiniteScroll
      loader={<CircularProgress />}
      fetchMore={() => setPage((prev) => prev + 1)}
      hasMore={hasMore}
      endMessage={<p className="text-center">Hết!</p>}
    >
      <h1 className="h4 mb-4">
        <span className="fs-16 fw-normal">Tổng hợp bài quiz môn: </span>
        {quizDatas[0].subject?.title || ""}
      </h1>
      <div className="d-flex flex-wrap align-items-stretch">
        {quizDatas.map((quiz) => {
          return (
            <CustomCard className="shadow" key={quiz.id}>
              <CardActionArea
                component={Link}
                href={`/quiz/${quiz.slug}-${quiz.id}`}
              >
                <CardMedia className="p-1">
                  <NextImage
                    src={quiz.banner}
                    alt={quiz.title}
                  />
                </CardMedia>
                <CardContent sx={{ padding: "10px" }}>
                  <div className="d-flex align-items-center justify-content-between mb-2">
                    <span className="badge bg-light text-dark">{ quiz.type_text }</span>
                    <span className="text-black-50 fs-12 text-limit text-center" style={{'--lines': 1}}>
                      {quiz.subject?.title} - {quiz.grade?.title || ''}
                    </span>
                  </div>
                  <h4 className="quiz-name text-limit">
                    {quiz.title || "__"}
                  </h4>
                </CardContent>
              </CardActionArea>
              <CardActions
                className="justify-content-between text-black-50 fs-14"
                sx={{ padding: "5px 10px" }}
              >
                <span>{quiz.questions_count} câu hỏi</span>
                <span>{quiz.view} lượt thi</span>
              </CardActions>
            </CustomCard>
          );
        })}
      </div>
    </InfiniteScroll>
  ) : null;
};

export default DisplayQuizs;
