"use client";

import React, { useState, useEffect, useRef, memo } from 'react';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import LinearProgress from '@mui/material/LinearProgress';

// Auto Progress Bar Component
const AutoProgressBar = memo(({
  isVisible,
  isCorrect,
  onComplete,
  onSkip,
  duration = 5000
}) => {
  console.log('🚀 AutoProgressBar render:', { isVisible, isCorrect, duration });
  const [progress, setProgress] = useState(0);
  const [timeLeft, setTimeLeft] = useState(Math.ceil(duration / 1000));
  const intervalRef = useRef(null);
  const timeoutRef = useRef(null);

  useEffect(() => {
    console.log('🔄 AutoProgressBar useEffect:', { isVisible, duration });
    if (isVisible) {
      console.log('✅ Starting progress animation');
      setProgress(0);
      setTimeLeft(Math.ceil(duration / 1000));

      console.log('time', timeLeft);

      // Progress animation
      const progressInterval = setInterval(() => {
        setProgress(prev => {
          console.log('===hhhh 1', prev);
          const newProgress = prev + (100 / (duration / 100));

          console.log('===ffffffffffffffffff 1', newProgress);

          return newProgress >= 100 ? 100 : newProgress;
        });

        console.log('===AutoProgressBar 111', progress);
      }, 100);

      console.log('===========AutoProgressBar 11111', progress);

      // Countdown timer
      const countdownInterval = setInterval(() => {
        setTimeLeft(prev => {
          const newTime = prev - 1;
          return newTime <= 0 ? 0 : newTime;
        });
        console.log('===AutoProgressBar 44', countdownInterval);
      }, 1000);

      console.log('==========AutoProgressBar 44', countdownInterval);

      // Auto complete
      const completeTimeout = setTimeout(() => {
        onComplete();
      }, duration);

      intervalRef.current = progressInterval;
      timeoutRef.current = completeTimeout;

      // Cleanup function
      return () => {
        clearInterval(progressInterval);
        clearInterval(countdownInterval);
        clearTimeout(completeTimeout);
      };
    }
  }, [isVisible, duration, onComplete]);

  useEffect(() => {
    const handleKeyPress = (event) => {
      console.log('==handleKeyPress', event);

      if (isVisible && event.code === 'Space') {
        event.preventDefault();
        onSkip();
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [isVisible, onSkip]);

  if (!isVisible) {
    console.log('❌ AutoProgressBar not visible, returning null');
    return null;
  }

  console.log('✅ AutoProgressBar rendering with:', { progress, timeLeft, isCorrect });

  const progressColor = isCorrect ? '#2ecc71' : '#e74c3c';
  const containerClass = `auto-progress-container ${isCorrect ? 'auto-progress-correct' : 'auto-progress-wrong'}`;

  console.log('🎨 Styling:', { progressColor, containerClass });

  return (
    <Box
      className={containerClass}
      role="progressbar"
      aria-valuenow={progress}
      aria-valuemin={0}
      aria-valuemax={100}
      aria-label={`Chuyển câu tiếp theo trong ${timeLeft} giây`}
      sx={{
        width: '100%',
        padding: '16px',
        backgroundColor: isCorrect ? 'rgba(46, 204, 113, 0.1)' : 'rgba(231, 76, 60, 0.1)',
        borderRadius: '8px',
        marginTop: '16px',
        border: `1px solid ${progressColor}20`,
        minHeight: '80px' // Đảm bảo có chiều cao tối thiểu
      }}
    >
      <div className="progress-header">
        <div className="progress-text">
          Chuyển câu tiếp theo trong <strong>{timeLeft}</strong> giây
        </div>
        <Button
          variant="outlined"
          size="small"
          onClick={onSkip}
          className="skip-button"
        >
          Tiếp tục (Space)
        </Button>
      </div>

      <LinearProgress
        variant="determinate"
        value={progress}
        className="progress-bar"
        sx={{
          backgroundColor: 'rgba(255, 255, 255, 0.2)',
          '& .MuiLinearProgress-bar': {
            backgroundColor: progressColor,
          }
        }}
      />
    </Box>
  );
});

AutoProgressBar.displayName = "AutoProgressBar";

export default AutoProgressBar;
