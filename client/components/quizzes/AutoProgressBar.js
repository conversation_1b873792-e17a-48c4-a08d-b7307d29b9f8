"use client";

import React, { useState, useEffect, memo } from 'react';
import LinearProgress from '@mui/material/LinearProgress';

// Simple Auto Progress Bar Component
const AutoProgressBar = memo(({
  isVisible,
  onComplete,
  progressColor = '#fff',
  duration = 3000
}) => {
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    if (!isVisible) return;

    // Reset progress
    setProgress(0);

    // Progress animation - cập nhật mỗi 100ms để mượt mà
    const progressInterval = setInterval(() => {
      setProgress(prev => {
        const increment = (100 / duration) * 100; // 100ms intervals
        const newProgress = prev + increment;

        if (newProgress >= 100) {
          clearInterval(progressInterval);
        }

        return newProgress;
      });
    }, 100);

    // Auto complete sau duration
    const completeTimeout = setTimeout(() => {
      onComplete && onComplete();
    }, duration);

    // Cleanup function
    return () => {
      clearInterval(progressInterval);
      clearTimeout(completeTimeout);
    };
  }, [isVisible, duration]);

  if (!isVisible) return null;

  return (
    <div style={{
      display: 'flex',
      justifyContent: 'center'
    }}>
      <LinearProgress
        variant="determinate"
        value={progress}
        sx={{
          width: '30px',
          backgroundColor: 'transparent',
          borderRadius: '1rem',
          '& .MuiLinearProgress-bar': {
            backgroundColor: progressColor,
            borderRadius: '1rem',
          }
        }}
      />
    </div>
  );
});

AutoProgressBar.displayName = "AutoProgressBar";

export default AutoProgressBar;
