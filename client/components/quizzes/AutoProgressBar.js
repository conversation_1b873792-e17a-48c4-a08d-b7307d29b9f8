"use client";

import React, { useState, useEffect, useRef, memo } from 'react';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import LinearProgress from '@mui/material/LinearProgress';

// Auto Progress Bar Component
const AutoProgressBar = memo(({
  isVisible,
  isCorrect,
  onComplete,
  onSkip,
  duration = 5000
}) => {
  console.log('🚀 AutoProgressBar render:', { isVisible, isCorrect, duration });
  const [progress, setProgress] = useState(0);
  const [timeLeft, setTimeLeft] = useState(Math.ceil(duration / 1000));
  const intervalRef = useRef(null);
  const timeoutRef = useRef(null);

  useEffect(() => {
    console.log('🔄 AutoProgressBar useEffect:', { isVisible, duration });
    if (isVisible) {
      console.log('✅ Starting progress animation');
      setProgress(0);
      setTimeLeft(Math.ceil(duration / 1000));

      console.log('time', timeLeft);

      // Progress animation - cập nhật mỗi 100ms
      const progressInterval = setInterval(() => {
        setProgress(prev => {
          // Tính toán: mỗi 100ms tăng (100 / (duration/100)) = (100 * 100) / duration
          const increment = (100 * 100) / duration; // Ví dụ: duration=5000 -> increment=2
          const newProgress = prev + increment;
          console.log('📊 Progress:', prev.toFixed(1), '->', newProgress.toFixed(1));
          return newProgress >= 100 ? 100 : newProgress;
        });
      }, 100);

      // Countdown timer - cập nhật mỗi giây
      const countdownInterval = setInterval(() => {
        setTimeLeft(prev => {
          const newTime = prev - 1;
          console.log('⏰ Countdown:', prev, '->', newTime);
          return newTime <= 0 ? 0 : newTime;
        });
      }, 1000);

      // Auto complete sau duration
      const completeTimeout = setTimeout(() => {
        console.log('🏁 Auto complete triggered after', duration, 'ms');
        onComplete();
      }, duration);

      console.log('🔧 Timers created:', {
        progressInterval: !!progressInterval,
        countdownInterval: !!countdownInterval,
        completeTimeout: !!completeTimeout
      });

      // Cleanup function
      return () => {
        console.log('🧹 Cleaning up timers');
        clearInterval(progressInterval);
        clearInterval(countdownInterval);
        clearTimeout(completeTimeout);
      };
    }
  }, [isVisible, duration]); // Loại bỏ onComplete khỏi dependencies

  useEffect(() => {
    const handleKeyPress = (event) => {
      console.log('==handleKeyPress', event);

      if (isVisible && event.code === 'Space') {
        event.preventDefault();
        onSkip();
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [isVisible, onSkip]);

  if (!isVisible) {
    console.log('❌ AutoProgressBar not visible, returning null');
    return null;
  }

  console.log('✅ AutoProgressBar rendering with:', { progress, timeLeft, isCorrect });

  const progressColor = isCorrect ? '#2ecc71' : '#e74c3c';
  const containerClass = `auto-progress-container ${isCorrect ? 'auto-progress-correct' : 'auto-progress-wrong'}`;

  console.log('🎨 Styling:', { progressColor, containerClass });

  return (
    <Box
      className={containerClass}
      role="progressbar"
      aria-valuenow={progress}
      aria-valuemin={0}
      aria-valuemax={100}
      aria-label={`Chuyển câu tiếp theo trong ${timeLeft} giây`}
      sx={{
        width: '100%',
        padding: '16px',
        backgroundColor: isCorrect ? 'rgba(46, 204, 113, 0.1)' : 'rgba(231, 76, 60, 0.1)',
        borderRadius: '8px',
        marginTop: '16px',
        border: `1px solid ${progressColor}20`,
        minHeight: '80px' // Đảm bảo có chiều cao tối thiểu
      }}
    >
      <div className="progress-header">
        <div className="progress-text">
          Chuyển câu tiếp theo trong <strong>{timeLeft}</strong> giây
        </div>
        <Button
          variant="outlined"
          size="small"
          onClick={onSkip}
          className="skip-button"
        >
          Tiếp tục (Space)
        </Button>
      </div>

      <LinearProgress
        variant="determinate"
        value={progress}
        className="progress-bar"
        sx={{
          backgroundColor: 'rgba(255, 255, 255, 0.2)',
          '& .MuiLinearProgress-bar': {
            backgroundColor: progressColor,
          }
        }}
      />
    </Box>
  );
});

AutoProgressBar.displayName = "AutoProgressBar";

export default AutoProgressBar;
