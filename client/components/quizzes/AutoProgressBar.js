"use client";

import React, { useState, useEffect, useRef, memo } from 'react';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import LinearProgress from '@mui/material/LinearProgress';

// Auto Progress Bar Component
const AutoProgressBar = memo(({
  isVisible,
  isCorrect,
  onComplete,
  onSkip,
  duration = 5000
}) => {
  console.log('AutoProgressBar', isVisible, duration);
  const [progress, setProgress] = useState(0);
  const [timeLeft, setTimeLeft] = useState(Math.ceil(duration / 1000));
  const intervalRef = useRef(null);
  const timeoutRef = useRef(null);

  useEffect(() => {
    if (isVisible) {
      setProgress(0);
      setTimeLeft(Math.ceil(duration / 1000));

      // Progress animation
      const progressInterval = setInterval(() => {
        setProgress(prev => {
          const newProgress = prev + (100 / (duration / 100));
          return newProgress >= 100 ? 100 : newProgress;
        });
      }, 100);

      console.log('AutoProgressBar 11', progress);

      // Countdown timer
      const countdownInterval = setInterval(() => {
        setTimeLeft(prev => {
          const newTime = prev - 1;
          return newTime <= 0 ? 0 : newTime;
        });
      }, 1000);

      console.log('AutoProgressBar 44', countdownInterval);

      // Auto complete
      const completeTimeout = setTimeout(() => {
        onComplete();
      }, duration);

      intervalRef.current = progressInterval;
      timeoutRef.current = completeTimeout;

      // Cleanup function
      return () => {
        clearInterval(progressInterval);
        clearInterval(countdownInterval);
        clearTimeout(completeTimeout);
      };
    }
  }, [isVisible, duration, onComplete]);

  useEffect(() => {
    const handleKeyPress = (event) => {
      console.log('==handleKeyPress', event);

      if (isVisible && event.code === 'Space') {
        event.preventDefault();
        onSkip();
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [isVisible, onSkip]);

  if (!isVisible) return null;

  const progressColor = isCorrect ? '#2ecc71' : '#e74c3c';
  const containerClass = `auto-progress-container ${isCorrect ? 'auto-progress-correct' : 'auto-progress-wrong'}`;

  return (
    <Box
      className={containerClass}
      role="progressbar"
      aria-valuenow={progress}
      aria-valuemin={0}
      aria-valuemax={100}
      aria-label={`Chuyển câu tiếp theo trong ${timeLeft} giây`}
    >
      <div className="progress-header">
        <div className="progress-text">
          Chuyển câu tiếp theo trong <strong>{timeLeft}</strong> giây
        </div>
        <Button
          variant="outlined"
          size="small"
          onClick={onSkip}
          className="skip-button"
        >
          Tiếp tục (Space)
        </Button>
      </div>

      <LinearProgress
        variant="determinate"
        value={progress}
        className="progress-bar"
        sx={{
          backgroundColor: 'rgba(255, 255, 255, 0.2)',
          '& .MuiLinearProgress-bar': {
            backgroundColor: progressColor,
          }
        }}
      />
    </Box>
  );
});

AutoProgressBar.displayName = "AutoProgressBar";

export default AutoProgressBar;
