"use client";

import React, { useEffect, useState, useCallback, useRef, memo } from "react";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { useSelector, useDispatch } from 'react-redux';

import AppBar from '@mui/material/AppBar';
import Box from '@mui/material/Box';
import Toolbar from '@mui/material/Toolbar';
import IconButton from '@mui/material/IconButton';
import Avatar from '@mui/material/Avatar';

import MusicNoteIcon from '@mui/icons-material/MusicNote';
import MusicOffIcon from '@mui/icons-material/MusicOff';
import CloseIcon from '@mui/icons-material/Close';
import ArrowRightIcon from '@mui/icons-material/ArrowRight';
import ArrowLeftIcon from '@mui/icons-material/ArrowLeft';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import FullscreenIcon from '@mui/icons-material/Fullscreen';
import FullscreenExitIcon from '@mui/icons-material/FullscreenExit';

import QuizGameIntro from './QuizGameIntro';
import QuizGameTimer from './QuizGameTimer';
import QuizResult from "@/components/quizzes/QuizResult";
import BlankQuestion from "@/components/quizzes/games/BlankQuestion";
import DragAndDropQuestion from "@/components/quizzes/games/DragAndDropQuestion";
import DropdownQuestion from "@/components/quizzes/games/DropdownQuestion";
import AutoProgressBar from './AutoProgressBar';
import HtmlContent from '../HtmlContent';
import Loader from '../Loader';

import useSound from '@/hooks/useSound';
import useQuizTiming from '@/hooks/useQuizTiming';
import { setNoti } from "@/slices/notiSlice";

import { optionBackgrounds } from "@/constant";

import { soloProceed, soloEnd, soloJoin } from '@/actions/quizResultAction';

const MultipleChoiceQuestion = memo(({
  questionData,
  answer,
  onAnswer,
  isSubmit = false,
  onSubmitAnswer,
  showAutoProgress = false,
  onAutoProgressComplete,
  ...props
}) => {

  const activeOption = (index) => {
    if (!isSubmit && typeof onAnswer === 'function') {
      let newAnswer = [];

      if (questionData.kind === 'MCQ') {
        // Multiple Choice Question - chỉ cho phép chọn 1 đáp án
        if (answer.includes(index)) {
          // Bỏ chọn nếu click vào đáp án đã chọn
          newAnswer = [];
        } else {
          // Chọn đáp án mới, bỏ tất cả đáp án cũ
          newAnswer = [index];
        }

        onAnswer(newAnswer);

        // Tự động submit cho MCQ khi chọn đáp án
        if (newAnswer.length > 0 && typeof onSubmitAnswer === 'function') {
          // Delay nhỏ để user thấy được đáp án được chọn trước khi submit
          setTimeout(() => {
            onSubmitAnswer(newAnswer);
          }, 300);
        }
      } else {
        // Multiple Select Question - cho phép chọn nhiều đáp án
        if (answer.includes(index)) {
          newAnswer = answer.filter((i) => i !== index);
        } else {
          newAnswer = [...answer, index];
        }

        onAnswer(newAnswer);
      }
    }
  };

  const optionClassName = (index) => {
    const isActive = answer.includes(index);

    if (isSubmit) {
      const isCorrect = questionData.answer ? questionData.answer.includes(index) : undefined;

      if (isActive) {
        return `active ${isCorrect !== undefined ? (isCorrect ? 'isCorrect' : 'isWrong') : ''}`;
      } else {
        return isCorrect ? 'isCorrect' : 'disable';
      }
    } else {
      return isActive ? 'active' : '';
    }
  }

  return (
    <div className={`question-box q-mode-v1 ${isSubmit ? 'submitting' : 'slideDown'}`}>
      <div className="py-3 px-md-3 h-50">
        <div className="q-content">
          <div className="q-content-text">
            <div className="q-label absolute px-3 py-1 border flex flex-row align-middle">
              <p className="mb-0">
                <span data-cy="current-question-number" role="current-question-number">{props.currentQuestionIndex+1}</span>/
                <span data-cy="total-question-number" role="total-question-number">{props.totalQuestion}</span>
              </p>
            </div>
            <HtmlContent html={ questionData.content } />
          </div>
        </div>
        { questionData.kind === 'MSQ' && (
          <div className="msq-text mb-1 d-flex blur-20 justify-content-center text-sm text-ds-light-500 border-ds-light-500-20 mx-auto px-3 py-1 rounded-full" title="Multiple Select Question">
            Chọn tất cả các đáp án đúng (MSQ)
          </div>
        )}
      </div>
      <div className="q-answer py-3 px-md-3 h-50">
        { questionData?.options?.length > 0 && (
          <div className="q-option-container">
            {
              questionData.options.map((option, index) => (
                <div
                  key={index}
                  className={`q-option-item ${isSubmit ? 'disabled' : 'cursor-pointer'} ${ optionClassName(index) }`}
                  style={{
                    backgroundColor: optionBackgrounds[index],
                    pointerEvents: isSubmit ? 'none' : 'auto'
                  }}
                  onClick={() => activeOption(index)}
                >
                  <div className="q-option-action d-flex align-items-center justify-content-end">
                    <span className="btn-icon icon-check">
                      <i className="bi bi-check-lg"></i>
                    </span>
                  </div>
                  <div className="q-option-content">
                    <HtmlContent html={ option.content } />
                  </div>
                </div>
              ))
            }
          </div>
        )}
        {showAutoProgress && (
          <AutoProgressBar
            isVisible={showAutoProgress}
            onComplete={onAutoProgressComplete}
          />
        )}
      </div>
    </div>
  );
});

MultipleChoiceQuestion.displayName = "MultipleChoiceQuestion";

const MultipleChoiceFlashcard = memo(({questionData, onFlipFlashCard, onNextQuestion, isSubmit = false}) => {
  console.log('MultipleChoiceFlashcard');
  const [showOption, setShowOption] = useState(false);
  const flashcardRef = useRef(null);

  useEffect(() => {
    if (isSubmit) {
      handleToggleFlip();
    }
  }, [isSubmit]);


  useEffect(() => {
    const handleKeyDown = (e) => {
      if (isSubmit && e.key === 'ArrowRight') {
        nextFlashcard();
      }
    };

    window.addEventListener('keydown', handleKeyDown);

  }, [isSubmit]);

  const handleToggleFlip = () => {
    if (flashcardRef.current) {
      const child = flashcardRef.current.children[0];
      if (child) {
        child.classList.toggle('is-flipped');
      }
    }
  };

  const flipFlashCard = () => {
    if (isSubmit) {
      handleToggleFlip();
    } else {
      onFlipFlashCard();
    }
  }

  const nextFlashcard = () => {
    if (isSubmit && flashcardRef.current) {
      flashcardRef.current.classList.add('fadeOutLeft');

      setTimeout(() => {
        onNextQuestion();
      }, 300);
    }
  }

  const getCorrectText = (questionData) => {
    if (!questionData || !Array.isArray(questionData.options) || !Array.isArray(questionData.answer)) {
      return `<p class="fs-16 text-muted">Đang cập nhật ...</p>`;
    }

    return questionData.answer
      .map(index => questionData.options[index]?.content || '')
      .join(', ');
  };

  return (
    <div ref={flashcardRef} className={`flashcard-container ${isSubmit ? '' : 'fadeInRight'}`}>
      <div className="flashcard-box">
        <div className="flashcard">
          <div className="flashcard-content flashcard-front" onClick={flipFlashCard}>
            { showOption ? (
              <div className="q-answer">
                <button
                  className="btn-rt btn btn-link btn-sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    setShowOption(false)
                  }}
                >
                  <i className="bi bi-info-circle"></i> Xem câu hỏi
                </button>
                { questionData?.options?.length > 0 && (
                  <div className="q-option-container row">
                    {
                      questionData.options.map((option, index) => (
                        <div
                          key={index}
                          className="col-12 col-md-6 mb-3 q-option-item"
                        >
                          <div className="q-option-content">
                            <HtmlContent html={ option.content } />
                          </div>
                        </div>
                      ))
                    }
                  </div>
                ) }
              </div>
            ) : (
              <div className="q-content">
                <button
                  className="btn-rt btn btn-link btn-sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    setShowOption(true)
                  }}
                >
                  <i className="bi bi-info-circle"></i> Xem tùy chọn
                </button>
                <HtmlContent html={ questionData.content } />
              </div>
            ) }
          </div>
          <div className="flashcard-content flashcard-back" onClick={flipFlashCard}>
            <div className="answer-container">
              <div className="q-content">
                <HtmlContent html={ getCorrectText(questionData) } />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="flashcard-action mt-3">
        <button className="btn btn-warning2 btn-lg fs-5 me-3 px-lg-5" onClick={flipFlashCard}>
          <i className="bi bi-grid-fill me-2"></i> Lật úp
        </button>
        <button onClick={nextFlashcard} className={`btn btn-default btn-lg fs-5 px-lg-5 ${isSubmit ? '' : 'disabled'}` }>
          Kế tiếp <i className="bi bi-arrow-right ms-1"></i>
        </button>
      </div>
    </div>
  );
});

MultipleChoiceFlashcard.displayName = "MultipleChoiceFlashcard";

const QuizGame = ({ initQuizInfo }) => {
  console.log('QuizGame');
  const dispatch = useDispatch();
  const router = useRouter();
  const searchParams = useSearchParams();

  const gameType = searchParams.get("gameType") || 'solo';
  const [quizInfo, setQuizInfo] = useState(initQuizInfo);
  const { user } = useSelector((state) => state.auth);

  const [start, setStart] = useState(true);
  const [endQuiz, setEndQuiz] = useState(false);
  const [timer, setTimer] = useState(0);

  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [questions, setQuestions] = useState([]);
  const [activeQuestion, setActiveQuestion] = useState(null);
  const [answer, setAnswer] = useState([]);
  const [answered, setAnswered] = useState(null);
  const [isSubmit, setIsSubmit] = useState(false);
  const [showAutoProgress, setShowAutoProgress] = useState(false);

  // Theme and fullscreen states
  const [selectedTheme, setSelectedTheme] = useState('classic');
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showFullscreenNotification, setShowFullscreenNotification] = useState(false);

  const playAudioBgRef = useRef(null);
  const stopAudioBgRef = useRef(null);

  const [playAudioBg, { stop: stopAudioBg }] = useSound('/assets/sounds/quiz_bg2.mp3', {
    loop: true,
    onload: () => console.log('Sound loaded!'),
  });
  const [playAudioCorrect] = useSound('/assets/sounds/correct_answer.mp3');
  const [playAudioWrong] = useSound('/assets/sounds/wrong_answer.mp3');

  // Timing functionality
  const {
    startQuiz,
    startQuestion,
    endQuestion,
    endQuiz: endQuizTiming,
    getQuestionDuration,
    totalDuration,
    isQuizActive
  } = useQuizTiming();

  // Theme functionality
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const savedTheme = localStorage.getItem('quiz-theme');
      if (savedTheme) {
        setSelectedTheme(savedTheme);
      }
    }
  }, []);

  // Fullscreen functionality
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
      if (document.fullscreenElement) {
        setShowFullscreenNotification(true);
        setTimeout(() => setShowFullscreenNotification(false), 1000);
      }
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => document.removeEventListener('fullscreenchange', handleFullscreenChange);
  }, []);

  useEffect(() => {
    if (quizInfo?.quiz?.questions?.length > 0) {
      setQuestions(quizInfo.quiz.questions);
      setActiveQuestion(quizInfo.quiz.questions[0]);
    }
  }, [quizInfo]);

  useEffect(() => {
    if (questions[currentQuestionIndex]) {
      setAnswer([]);
      setIsSubmit(false);
      setActiveQuestion(questions[currentQuestionIndex]);

      // Start timing for this question
      if (isQuizActive && !isSubmit) {
        startQuestion(questions[currentQuestionIndex].id);
        setTimer(questions[currentQuestionIndex].content_json?.timeLimit || 0);
      }
    }
  }, [currentQuestionIndex, questions, isQuizActive, startQuestion]);

  useEffect(() => {
    return () => {
      stopAudioBg(); // Stop the audio when the component unmounts
    };
  }, [stopAudioBg]);

  useEffect(() => {
    const handleBeforeUnload = (event) => {
      if (!start && !endQuiz) {
        event.preventDefault();
        event.returnValue = '';

        return '';
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [start, endQuiz]);

  const toggleFullscreen = async () => {
    try {
      if (!document.fullscreenElement) {
        await document.documentElement.requestFullscreen();
      } else {
        await document.exitFullscreen();
      }
    } catch (error) {
      console.error('Fullscreen error:', error);
    }
  };

  const rejoinGame = async () => {
    try {
      const res = await soloJoin({
        quizId: quizInfo.quiz_id,
        gameType,
        assignmentId: quizInfo.assignment_id,
        quizResult: quizInfo.id
      });

      if (res?.data) {
        setTimeout(() => {
          router.push(`/join/game/${res.data.id}?gameType=${gameType}`);
        }, 1000);
      } else {
        throw new Error("Đã xảy ra lỗi.");
      }
    } catch (errors) {
      dispatch(setNoti(errors));
    }
  }

  const stopAudioBG = () => {
    stopAudioBg && stopAudioBg();
    stopAudioBgRef.current.classList.add('d-none');
    playAudioBgRef.current.classList.remove('d-none');
  }

  const playAudioBG = () => {
    playAudioBg && playAudioBg();
    stopAudioBgRef.current.classList.remove('d-none');
    playAudioBgRef.current.classList.add('d-none');
  }

  const startGame = () => {
    setStart(false);
    playAudioBg && playAudioBg();

    // Start quiz timing
    startQuiz();

    // Start timing for the first question
    if (questions[0]) {
      startQuestion(questions[0].id);
    }
  };

  const endGame = async () => {
    if (endQuiz) {
      try {
        // End quiz timing and get total duration
        const finalDuration = endQuizTiming();

        const res = await soloEnd(quizInfo.id, {
          timer: 0,
          total_duration: finalDuration
        });

        if (res.data) {
          setQuizInfo(res.data);
        }
      } catch (errors) {
        dispatch(setNoti(errors));
      }
    }
  };

  const nextQuestion = (currentQuestionIdx) => {
    setTimer(0);
    setShowAutoProgress(false);

    if (currentQuestionIdx + 1 === questions.length) {
      console.log('Quiz is incomplete');
      setEndQuiz(true);
    } else {
      setCurrentQuestionIndex(currentQuestionIdx + 1);
    }
  };

  const handleGoBack = () => {
    const fallbackUrl = quizInfo.quiz
      ? `/quiz/${quizInfo.quiz.slug}-${quizInfo.quiz.id}`
      : '/';
    const ref = document.referrer;

    if (
      ref &&
      ref !== location.href &&
      new URL(ref).origin === location.origin
    ) {
      router.back();
    } else {
      router.push(fallbackUrl);
    }
  };

  // Auto progress handlers - sử dụng useCallback để tránh re-render
  const handleAutoProgressComplete = useCallback(() => {
    setShowAutoProgress(false);
    nextQuestion(currentQuestionIndex);
  }, [currentQuestionIndex]);

  const onAnswer = (answered) => {
    setShowAutoProgress(false); // Reset khi user bắt đầu trả lời câu mới
    setAnswer(answered);
  };

  const submitAnswer = async (answerOfUser) => {
    if (
      activeQuestion?.user_answer ||
      isSubmit ||
      (gameType !== 'flashcard' && answerOfUser.length == 0)
    ) return;

    setIsSubmit(true);
    setTimer(0);

    try {
      // End timing for the current question and get duration
      const questionDuration = endQuestion(activeQuestion.id);

      const res = await soloProceed(quizInfo.id, {
        questionId: activeQuestion.id,
        answer: answerOfUser,
        timer: 0,
        question_duration: questionDuration
      });

      if (res.data) {
        if (res.data.question?.answer) {
          const updatedActiveQuestion = {
            ...activeQuestion,
            content_json: {
              ...activeQuestion.content_json,
              answer: res.data.question.answer,
            },
            user_answer: answerOfUser,
          };

          setActiveQuestion(updatedActiveQuestion);

          setQuestions((prevQuestions) => {
            return prevQuestions.map((question) =>
              question.id === activeQuestion.id ? updatedActiveQuestion : question
            );
          });
        }

        if (gameType !== 'flashcard') {
          if (res.data.isCorrect) {
            playAudioCorrect && playAudioCorrect();
            setAnswered(true)
          } else {
            playAudioWrong && playAudioWrong();
            setAnswered(false)
          }

          // Hiển thị thanh tiến trình tự động chuyển câu
          setShowAutoProgress(true);

          setTimeout(() => {
            setAnswered(null);
          }, 1500);
        }
      } else {
        throw new Error("Đã xảy ra lỗi.");
      }
    } catch (errors) {
      dispatch(setNoti(errors));
    } finally {
      setIsSubmit(false);
    }
  }

  const onEndTimer = () => {
    nextQuestion(currentQuestionIndex);
  };

  const renderQuestion = () => {
    if (activeQuestion?.type && activeQuestion.content_json && Object.keys(activeQuestion.content_json).length > 0) {
      switch (activeQuestion.type) {
        case 'QUIZX':
          return (
            <MultipleChoiceQuestion
              key={activeQuestion.id}
              isSubmit={activeQuestion.user_answer ? true : isSubmit}
              answer={activeQuestion.user_answer || answer}
              onAnswer={(answered) => activeQuestion.user_answer ? undefined : onAnswer(answered)}
              questionData={activeQuestion.content_json}
              currentQuestionIndex={currentQuestionIndex}
              totalQuestion={questions.length}
              onSubmitAnswer={(answered) => activeQuestion.user_answer ? undefined : submitAnswer(answered)}
              showAutoProgress={showAutoProgress}
              onAutoProgressComplete={handleAutoProgressComplete}
            />
          );
        case 'BLANK':
          return (
            <BlankQuestion
              key={activeQuestion.id}
              isSubmit={activeQuestion.user_answer ? true : isSubmit}
              answer={activeQuestion.user_answer || answer}
              onAnswer={(answered) => activeQuestion.user_answer ? undefined : onAnswer(answered)}
              questionData={activeQuestion.content_json}
              currentQuestionIndex={currentQuestionIndex}
              totalQuestion={questions.length}
            />
          );
        case 'DRAG_DROP':
          return (
            <DragAndDropQuestion
              key={activeQuestion.id}
              isSubmit={activeQuestion.user_answer ? true : isSubmit}
              answer={activeQuestion.user_answer || answer}
              onAnswer={(answered) => activeQuestion.user_answer ? undefined : onAnswer(answered)}
              questionData={activeQuestion.content_json}
              currentQuestionIndex={currentQuestionIndex}
              totalQuestion={questions.length}
            />
          );
        case 'DROPDOWN':
          return (
            <DropdownQuestion
              key={activeQuestion.id}
              isSubmit={activeQuestion.user_answer ? true : isSubmit}
              answer={activeQuestion.user_answer || answer}
              onAnswer={(answered) => activeQuestion.user_answer ? undefined : onAnswer(answered)}
              questionData={activeQuestion.content_json}
              currentQuestionIndex={currentQuestionIndex}
              totalQuestion={questions.length}
            />
          );
      }
    }

    return <p className="text-white text-center m-3 fs-18 pt-5">( Câu hỏi đang cập nhật... )</p>;
  };

  const renderFlashcard = () => {
    if (activeQuestion?.type && activeQuestion.content_json && Object.keys(activeQuestion.content_json).length > 0) {
      switch (activeQuestion.type) {
        case 'QUIZX':
          return (
            <MultipleChoiceFlashcard
              key={activeQuestion.id}
              isSubmit={activeQuestion.user_answer ? true : isSubmit}
              onFlipFlashCard={() => submitAnswer([])}
              onNextQuestion={() => nextQuestion(currentQuestionIndex)}
              questionData={activeQuestion.content_json}
            />
          );
      }
    }

    return <p className="text-white text-center m-3 fs-18 pt-5">( Flashcard đang cập nhật... )</p>;
  }

  const renderContentGame = () => {
    switch (gameType) {
      case 'flashcard':
        return renderFlashcard();
      default:
        return renderQuestion();
    }
  };

  if (!initQuizInfo) return <Loader />;

  return (
    <Box
      className={`quiz-game theme-${selectedTheme}`}
      sx={{
        backgroundColor: gameType === 'flashcard' ? '#000' : '#461a42',
        color: '#fff',
        height: quizInfo.status > 1 ? 'auto' : '100vh',
        display: 'flex',
        flexDirection: 'column'
      }}
    >
      <AppBar
        position="fixed"
        sx={{
          boxShadow: 'none',
          backgroundColor: gameType === 'solo' ? '#09090933' : '#000',
          color: '#fff',
          padding: '.5rem 0'
        }}
      >
        <Toolbar variant="dense">
          <button className="btn-icon-md me-2 cursor-pointer" onClick={handleGoBack}>
            <CloseIcon />
          </button>
          <button ref={stopAudioBgRef} className="btn-icon-md me-2 cursor-pointer" onClick={stopAudioBG}>
            <MusicNoteIcon />
          </button>
          <button ref={playAudioBgRef} className="btn-icon-md me-2 cursor-pointer d-none" onClick={playAudioBG}>
            <MusicOffIcon />
          </button>
          {/*{ timer > 0 && (
            <QuizGameTimer counter={currentQuestionIndex} initValue={timer} onEnd={onEndTimer} />
          ) }*/}

          <Box sx={{ flexGrow: 1 }} />

          <IconButton
            className="btn-icon-md ms-2 cursor-pointer icon-full-screen"
            color="inherit"
            onClick={toggleFullscreen}
            sx={{ color: '#fff' }}
          >
            {isFullscreen ? <FullscreenExitIcon /> : <FullscreenIcon />}
          </IconButton>
        </Toolbar>
      </AppBar>
      <div style={{ minHeight: 'calc(100vh - 64px)', height: '100%', paddingTop: '64px', paddingBottom: '64px', overflowY: 'hidden', }}>
        { quizInfo.status > 1 ? (
          <div className="h-100 d-flex flex-column">
            <QuizResult
              quizResult={quizInfo}
              questions={questions}
              gameType={gameType}
              onPlayAgain={rejoinGame}
            />
          </div>
        ) : (
          <>
            { start && (
              <QuizGameIntro initialCounter={3} onEnd={startGame} />
            ) }
            { endQuiz && (
              <QuizGameIntro initialCounter={-1} onEnd={endGame} />
            ) }
            { !start && !endQuiz && (
              <div className="question-wrapper px-2 py-4 h-100 w-100">
                { renderContentGame() }
              </div>
            ) }
          </>
        ) }
      </div>
      <AppBar
        position="fixed"
        sx={{
          boxShadow: 'none',
          backgroundColor: gameType === 'solo' ? '#09090920' : '#000',
          color: '#fff',
          padding: '.5rem 0',
          top: 'auto',
          bottom: 0
        }}
      >
        <Toolbar variant="dense">
          { user  && (
            <IconButton color="inherit">
              <Avatar alt={ user.name } src={ user.avatar } />
              <span className="ms-2 fs-18">{ user.name }</span>
            </IconButton>
          ) }
          <Box sx={{ flexGrow: 1 }} />
          {!endQuiz && (gameType !== 'flashcard') && (
            <div className="d-flex align-items-start">
              { (isSubmit || activeQuestion?.user_answer) && (
                <>
                  { currentQuestionIndex > 0 && (
                    <button className="btn-icon-md ms-2 cursor-pointer" onClick={() => nextQuestion(currentQuestionIndex - 2)}>
                      <ArrowLeftIcon fontSize="large" />
                    </button>
                  ) }
                  <button className="btn-icon-md ms-2 cursor-pointer" onClick={() => nextQuestion(currentQuestionIndex)}>
                    <ArrowRightIcon fontSize="large" />
                  </button>
                </>
              ) }
              <button
                className="btn-icon-md submit-button ms-2 cursor-pointer"
                onClick={() => submitAnswer(answer)}
                disabled={answer.length === 0 || (answer.length > 0 && isSubmit)}
              >
                <span className="me-2 fs-18 fw-bold">NỘP</span>
                {isSubmit ? (
                  <i className="bi bi-hourglass-split"></i>
                ) : (
                  <ArrowForwardIcon />
                )}
              </button>
            </div>
          )}
        </Toolbar>
      </AppBar>
      <div className={`slogan-noti ${answered === false ? "show slogan-danger" : answered === true ? "show slogan-success" : ""}`}>
        {answered === false && (
          <>
            <div className="d-flex">
              <i className="bi bi-x-circle me-2 fs-5"></i>Không chính xác
            </div>
            <span className="close-item-icon" onClick={() => setAnswered(null)}>
              <i className="bi bi-x-lg"></i>
            </span>
          </>
        )}

        {answered === true && (
          <>
            <div className="d-flex">
              <i className="bi bi-check-circle me-2 fs-5"></i>Chính xác
            </div>
            <span className="close-item-icon" onClick={() => setAnswered(null)}>
              <i className="bi bi-x-lg"></i>
            </span>
          </>
        )}
      </div>
      {/* Fullscreen Notification */}
      {showFullscreenNotification && (
        <Box
          sx={{
            position: 'fixed',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            color: '#fff',
            padding: '12px 24px',
            borderRadius: '8px',
            zIndex: 9999,
            fontSize: '16px',
            fontWeight: 'bold'
          }}
        >
          Nhấn ESC để thoát fullscreen
        </Box>
      )}
    </Box>
  );
}

export default QuizGame;
