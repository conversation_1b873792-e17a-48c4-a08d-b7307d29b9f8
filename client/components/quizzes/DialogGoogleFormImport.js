"use client";

import React, { memo, useState, useEffect } from "react";
import { useRouter } from "next/navigation";

import Box from "@mui/material/Box";
import Dialog from "@mui/material/Dialog";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import Typography from "@mui/material/Typography";
import IconButton from "@mui/material/IconButton";
import LinearProgress from "@mui/material/LinearProgress";

import CloseIcon from "@mui/icons-material/Close";
import FormIcon from "@mui/icons-material/Assignment";
import FileIcon from "@mui/icons-material/Description";

import toast from "react-hot-toast";
import {importGoogleForm} from "@/actions/quizAction";
import {useFakeProgress} from "@/hooks/useFakeProgress";

const GOOGLE_CLIENT_ID = process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID;
const GOOGLE_API_KEY = process.env.NEXT_PUBLIC_GOOGLE_API_KEY;

// Global flags to track script loading status
let isGisLoading = false;
let isPickerLoading = false;
let gisLoaded = false;
let pickerLoaded = false;

const DialogGoogleFormImport = ({ open, onClose, quiz = {}, onImportSuccess }) => {
  const router = useRouter();
  const [isImporting, setIsImporting] = useState(false);
  const [selectedForm, setSelectedForm] = useState(null);
  const [isLoadingScripts, setIsLoadingScripts] = useState(false);
  const [showMainDialog, setShowMainDialog] = useState(true);

  // Sử dụng useFakeProgress thay vì importProgress state
  const { progress, start, reset } = useFakeProgress({
    max: 90,
    interval: 150,
    step: 12,
    autoStart: false, // Không tự chạy, sẽ start manual
  });

  // Initialize Google APIs when dialog opens
  useEffect(() => {
    if (open) {
      setShowMainDialog(true);
      loadGoogleAPIs();
    }
  }, [open]);

  const loadGoogleAPIs = async () => {
    setIsLoadingScripts(true);

    try {
      // Load both scripts in parallel - silently
      await Promise.all([
        loadGisScript(),
        loadPickerScript()
      ]);

      // Immediately show Google's native account selection
      await initializeAndShowGoogleAuth();
    } catch (error) {
      console.error('Error loading Google APIs:', error);
      onClose();
    } finally {
      setIsLoadingScripts(false);
    }
  };

  const initializeAndShowGoogleAuth = () => {
    return new Promise((resolve, reject) => {
      try {
        // Initialize and immediately request token - shows Google's native dialog
        const tokenClient = window.google.accounts.oauth2.initTokenClient({
          client_id: GOOGLE_CLIENT_ID,
          scope: 'https://www.googleapis.com/auth/drive.readonly',
          callback: (tokenResponse) => {
            if (tokenResponse.access_token) {
              showPicker(tokenResponse.access_token);
              resolve();
            } else {
              reject(new Error('Failed to get access token'));
            }
          },
          error_callback: (error) => {
            console.error('OAuth2 error:', error);
            onClose();
            reject(error);
          }
        });

        // Immediately show Google's native account selection dialog
        tokenClient.requestAccessToken({
          prompt: 'select_account'
        });
      } catch (error) {
        reject(error);
      }
    });
  };

  const loadGisScript = () => {
    return new Promise((resolve, reject) => {
      // Check if already loaded
      if (window.google?.accounts && gisLoaded) {
        resolve();
        return;
      }

      // Check if already loading
      if (isGisLoading) {
        // Wait for loading to complete
        const checkLoaded = setInterval(() => {
          if (gisLoaded) {
            clearInterval(checkLoaded);
            resolve();
          }
        }, 100);
        return;
      }

      // Start loading
      isGisLoading = true;

      const script = document.createElement('script');
      script.src = 'https://accounts.google.com/gsi/client';
      script.async = true;
      script.defer = true;

      script.onload = () => {
        gisLoaded = true;
        isGisLoading = false;
        resolve();
      };

      script.onerror = () => {
        isGisLoading = false;
        reject(new Error('Failed to load Google Identity Services script'));
      };

      document.head.appendChild(script);
    });
  };

  const loadPickerScript = () => {
    return new Promise((resolve, reject) => {
      // Check if already loaded
      if (window.google?.picker && pickerLoaded) {
        resolve();
        return;
      }

      // Check if already loading
      if (isPickerLoading) {
        // Wait for loading to complete
        const checkLoaded = setInterval(() => {
          if (pickerLoaded) {
            clearInterval(checkLoaded);
            resolve();
          }
        }, 100);
        return;
      }

      // Start loading
      isPickerLoading = true;

      const script = document.createElement('script');
      script.src = 'https://apis.google.com/js/picker.js';
      script.async = true;
      script.defer = true;

      script.onload = () => {
        pickerLoaded = true;
        isPickerLoading = false;
        resolve();
      };

      script.onerror = () => {
        isPickerLoading = false;
        reject(new Error('Failed to load Google Picker script'));
      };

      document.head.appendChild(script);
    });
  };

  const showPicker = (accessToken) => {
    try {
      // Ẩn dialog chính khi hiển thị Google Picker
      setShowMainDialog(false);

      const picker = new window.google.picker.PickerBuilder()
        .addView(
          new window.google.picker.DocsView(window.google.picker.ViewId.FORMS)
            .setIncludeFolders(false)
            .setSelectFolderEnabled(false)
        )
        .setOAuthToken(accessToken)
        .setDeveloperKey(GOOGLE_API_KEY)
        .setCallback((data) => handlePickerCallback(data, accessToken, picker))
        .setTitle('Chọn Google Form')
        .build();

      picker.setVisible(true);
    } catch (error) {
      console.error('Error showing picker:', error);
      toast.error('Không thể hiển thị Google Picker. Vui lòng thử lại.');
      setShowMainDialog(true); // Hiện lại dialog chính nếu có lỗi
      onClose();
    }
  };

  const handlePickerCallback = (data, accessToken, picker) => {
    if (data.action === window.google.picker.Action.PICKED) {
      const file = data.docs[0];

      // Ẩn Google Picker ngay lập tức
      if (picker) {
        picker.setVisible(false);
      }

      // Hiển thị lại dialog chính với trạng thái importing
      setShowMainDialog(true);

      setSelectedForm({
        id: file.id,
        name: file.name,
        accessToken: accessToken
      });

      // Start import process immediately
      startImport(file, accessToken);
    } else if (data.action === window.google.picker.Action.CANCEL) {
      // Đóng picker và hiển thị lại dialog chính
      if (picker) {
        picker.setVisible(false);
      }
      setShowMainDialog(true);
      onClose();
    }
  };

  const startImport = async (file, accessToken) => {
    setIsImporting(true);

    // Bắt đầu fake progress
    start();

    try {
      const data = {
        form_id: file.id,
        quiz_id: quiz?.id || null,
        access_token: accessToken,
      }

      // Call backend API to import the Google Form
      const response = await importGoogleForm(data);

      // Hoàn thành progress về 100%

      setTimeout(() => {
        if (response.status === 200) {
          if (onImportSuccess && response?.imported_questions) {
            onImportSuccess(response.imported_questions);
          }
          toast.success(response.message || 'Import Google Form thành công', {position: "top-right"});
          setIsImporting(false);
          onClose();
        } else {
          toast.error(response.message || 'Có lỗi xảy ra khi import');
          onClose();
        }
      }, 300);
    } catch (error) {
      console.error('Import error:', error);
      toast.error(error.message || 'Có lỗi xảy ra khi import Google Form');
      onClose();
    }
  };

  const handleClose = () => {
    if (!isImporting) {
      setSelectedForm(null);
      reset(); // Reset progress khi đóng
      setShowMainDialog(true);
      onClose();
    }
  };

  return (
    <Dialog
      open={open && showMainDialog}
      onClose={isImporting ? undefined : handleClose}
      maxWidth="sm"
      fullWidth
      slotProps={{
        paper: {
          sx: {
            borderRadius: 3,
            minHeight: 250,
            zIndex: 1300 // Đảm bảo z-index thấp hơn Google Picker
          }
        }
      }}
    >
      <DialogTitle
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'flex-start',
          pb: 1
        }}
      >
        <Box sx={{ display: 'flex' }}>
          <Box mb={1}>
            <FileIcon className="file-icon-bg" />
          </Box>
          <Box ml={2}>
            <Typography variant="h6" gutterBottom fontWeight="bold" className="mb-1">
              {isImporting ? 'Đang nhập nội dung' : 'Kết nối Google Form'}
            </Typography>
            <Typography variant="body2" color="text.secondary" className="fs-13">
              {isImporting ? 'Quá trình này có thể mất vài giây...' : 'Chọn tài khoản Google để tiếp tục...'}
            </Typography>
          </Box>
        </Box>

        {!isImporting && (
          <IconButton aria-label="close" onClick={handleClose}>
            <CloseIcon />
          </IconButton>
        )}
      </DialogTitle>

      <DialogContent>
        {isImporting ? (
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <Box sx={{ width: '100%', mb: 2 }}>
              <LinearProgress
                className="determinate-progress"
                variant="determinate"
                value={progress}
                sx={{
                  height: 16,
                  backgroundColor: '#e5e5e5',
                  borderRadius: 4,
                  '& .MuiLinearProgress-bar': {
                    backgroundColor: '#8854c0'
                  }
                }}
              />
            </Box>
            <Typography variant="caption" color="text.secondary">
              {Math.round(progress)}% hoàn thành
            </Typography>

            {selectedForm && (
              <Typography variant="body2" sx={{ mt: 2 }}>
                Đang import: <strong>{selectedForm.name}</strong>
              </Typography>
            )}
          </Box>
        ) : (
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <FormIcon sx={{ fontSize: 48, color: '#4285f4', mb: 3 }} />
            <Typography variant="h6" gutterBottom fontWeight="600">
              Kết nối với Google
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 4 }}>
              Cửa sổ đăng nhập Google sẽ mở trong giây lát...
            </Typography>
          </Box>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default memo(DialogGoogleFormImport);
