"use client";

import React, { useEffect, useState } from "react";
import toast from "react-hot-toast";
import { useRouter } from "next/navigation";
import { importQuizByFile } from "@/actions/quizAction";
import {
  useFileImport,
  FileUploadArea,
  SelectedFileDisplay,
  ValidationErrorsDisplay
} from "@/components/shared/FileImport";
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import CircularProgress from "@mui/material/CircularProgress";
import Typography from "@mui/material/Typography";
import InputIcon from "@mui/icons-material/Input";
import ExcelIcon from "@mui/icons-material/TableView";
import FileIcon from "@mui/icons-material/Description";

const QuizImportFile = ({ quiz, onClose, onImportSuccess, fileType = "word" }) => {
  const router = useRouter();
  const [isUploading, setIsUploading] = useState(false);

  // Use the centralized file import hook
  const {
    selectedFile,
    isDragOver,
    validationErrors,
    fileInputRef,
    fileConfig,
    handleFileInputChange,
    handleDrop,
    handleDragOver,
    handleDragLeave,
    removeSelectedFile,
    triggerFileInput,
    hasFile,
    hasErrors,
    setErrors,
    resetState
  } = useFileImport({
    fileType: fileType,
    onFileSelect: (file) => {
      console.log('📁 QuizImportFile: File selected:', file);
    },
    onFileRemove: () => {
      console.log('🗑️ QuizImportFile: File removed');
    }
  });

  const handleImport = async () => {
    if (!selectedFile) {
      toast.error("Vui lòng chọn file để import");
      return;
    }

    setIsUploading(true);
    setErrors([]);

    try {
      const formData = new FormData();
      formData.append("file", selectedFile);
      formData.append("file_type", fileType);
      formData.append("quiz_id", quiz?.id || null);

      const response = await importQuizByFile(formData);

      if (response && response?.imported_questions) {
        if (onImportSuccess && response.imported_questions) {
          onImportSuccess(response.imported_questions);
        }

        toast.success(response.message, {position: "top-right"});
        // Reset the file selection and errors
        resetState();
        onClose();

        // Nếu đang ở route khác edit thì redirect sang route edit
        if (!window.location.pathname.includes('/edit')) {
          router.push(`/dashboard/quiz/${response.quiz.id}/edit`);
        }
      } else {
        toast.error("Có lỗi xảy ra");
        console.log(response.message);
      }
    } catch (error) {
      toast.error(error.message || "Có lỗi xảy ra!");
      console.error("Import error:", error);
      setErrors(Array.isArray(error) ? error : [error]);
    } finally {
      setIsUploading(false);
    }
  }

  const handleCancel = () => {
    resetState();
    setIsUploading(false);

    if (onClose) {
      onClose();
    }
  }

  return (
    <Box>
      {/* Selected File Display - Show when file is selected */}
      {hasFile && (
        <SelectedFileDisplay
          file={selectedFile}
          onRemove={removeSelectedFile}
          fileConfig={fileConfig}
          fileType={fileType}
          sx={{p: '2rem 1rem'}}
        />
      )}

      {/* File Upload Area - Hidden when file is selected */}
      {!hasFile && (
        <FileUploadArea
          isDragOver={isDragOver}
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onClick={triggerFileInput}
          onFileInputChange={handleFileInputChange}
          fileInputRef={fileInputRef}
          fileConfig={fileConfig}
          title="Kéo thả file hoặc click để chọn file"
          icon={ fileType === "excel" ? (<ExcelIcon className="file-icon file-excel" />) : (<FileIcon className="file-icon file-word" />)}
        />
      )}

      {/* Validation Errors Display */}
      {hasErrors && (
        <ValidationErrorsDisplay errors={validationErrors} />
      )}

      {isUploading && (
        <Box
          sx={{
            border: '2px solid #red',
            borderRadius: 1,
            p: 2,
            mt: 2,
            backgroundColor: '#fff',
            color: '#000',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            flexDirection: 'column'
          }}
        >
          <CircularProgress color="secondary" size={30} sx={{mb: 3}}/>
          <Typography variant="h6" gutterBottom>
            Đang xử lý file...
          </Typography>
          <Typography variant="caption" color="text.secondary">
            Vui lòng giữ trang này mở và không tắt trình duyệt trong quá trình import.
          </Typography>
        </Box>
      )}

      {/* Import Button */}
      <Box className="text-center text-md-end" mt={3}>
        <Button
          variant="outlined"
          color="inherit"
          size="small"
          onClick={handleCancel}
          sx={{ mr: 2 }}
        >
          Hủy
        </Button>
        <Button
          variant="contained"
          color="secondary"
          size="small"
          onClick={handleImport}
          disabled={!selectedFile || isUploading}
          startIcon={isUploading ? <CircularProgress size={20} /> : <InputIcon size="small" />}
        >
          {isUploading ? "Đang import..." : "Xác nhận import"}
        </Button>
      </Box>
    </Box>
  )
}

export default QuizImportFile
