"use client";

import React, { useState } from 'react';
import HtmlContent from '../../HtmlContent';
import AutoProgressBar from '../AutoProgressBar';

const MCQDemo = () => {
  const [answer, setAnswer] = useState([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmit, setIsSubmit] = useState(false);
  const [submitResult, setSubmitResult] = useState(null);
  const [showAutoProgress, setShowAutoProgress] = useState(false);

  const questionData = {
    kind: 'MCQ',
    content: '<p><strong>Câu hỏi demo:</strong> Thủ đô của Việt Nam là gì?</p>',
    options: [
      { content: '<p><PERSON><PERSON></p>' },
      { content: '<p>Hà Nội</p>' },
      { content: '<p>Đà Nẵng</p>' },
      { content: '<p><PERSON><PERSON><PERSON></p>' },
    ],
    answer: [1], // H<PERSON>ội là đáp án đúng
  };

  const optionBackgrounds = [
    '#e74c3c', // Đỏ
    '#3498db', // Xanh dương
    '#f39c12', // Cam
    '#2ecc71', // Xanh lá
  ];

  const activeOption = (index) => {
    if (!isSubmit && !isSubmitting) {
      let newAnswer = [];

      if (questionData.kind === 'MCQ') {
        // Multiple Choice Question - chỉ cho phép chọn 1 đáp án
        if (answer.includes(index)) {
          // Bỏ chọn nếu click vào đáp án đã chọn
          newAnswer = [];
        } else {
          // Chọn đáp án mới, bỏ tất cả đáp án cũ
          newAnswer = [index];
        }

        // Tự động submit cho MCQ khi chọn đáp án
        setAnswer(newAnswer);
        if (newAnswer.length > 0) {
          // Delay nhỏ để user thấy được đáp án được chọn trước khi submit
          setTimeout(() => {
            submitAnswer(newAnswer);
          }, 300);
        }
      }
    }
  };

  const submitAnswer = async (answerOfUser) => {
    if (isSubmit || isSubmitting || answerOfUser.length === 0) return;

    setIsSubmitting(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Check if answer is correct
      const isCorrect = questionData.answer.some(correctIndex =>
        answerOfUser.includes(correctIndex)
      );

      setSubmitResult(isCorrect);
      setIsSubmit(true);

      // Hiển thị thanh tiến trình
      console.log('🔥 Demo: Setting showAutoProgress to true, isCorrect:', isCorrect);
      setShowAutoProgress(true);

    } catch (error) {
      console.error('Submit error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const resetDemo = () => {
    setAnswer([]);
    setIsSubmitting(false);
    setIsSubmit(false);
    setSubmitResult(null);
    setShowAutoProgress(false);
  };

  const handleAutoProgressComplete = () => {
    setShowAutoProgress(false);
    // Trong demo, chỉ reset để có thể thử lại
    resetDemo();
  };

  const handleAutoProgressSkip = () => {
    setShowAutoProgress(false);
    // Trong demo, chỉ reset để có thể thử lại
    resetDemo();
  };

  const optionClassName = (index) => {
    const isActive = answer.includes(index);

    if (isSubmit) {
      const isCorrect = questionData.answer ? questionData.answer.includes(index) : undefined;

      if (isActive) {
        return `active ${isCorrect !== undefined ? (isCorrect ? 'isCorrect' : 'isWrong') : ''}`;
      } else {
        return isCorrect ? 'isCorrect' : 'disable';
      }
    } else {
      return isActive ? 'active' : '';
    }
  };

  return (
    <div style={{
      maxWidth: '800px',
      margin: '0 auto',
      padding: '20px',
      fontFamily: 'Arial, sans-serif'
    }}>
      <h1>MCQ Auto Submit Demo</h1>

      <div className={`question-box q-mode-v1 ${isSubmit ? '' : 'slideDown'} ${isSubmitting ? 'submitting' : ''}`}
           style={{
             backgroundColor: '#461a42',
             color: '#fff',
             borderRadius: '10px',
             padding: '20px',
             marginBottom: '20px'
           }}>

        <div className="py-3 px-md-3 h-50">
          <div className="q-content">
            <div className="q-content-text">
              <div className="q-label" style={{
                position: 'absolute',
                top: '10px',
                right: '10px',
                background: 'rgba(255,255,255,0.2)',
                padding: '5px 10px',
                borderRadius: '15px',
                fontSize: '14px'
              }}>
                <p className="mb-0">1/1</p>
              </div>
              <HtmlContent html={questionData.content} />
            </div>
          </div>
        </div>

        {questionData.kind === 'MCQ' && isSubmitting && (
          <div style={{
            textAlign: 'center',
            marginBottom: '10px',
            padding: '8px 16px',
            backgroundColor: 'rgba(255,255,255,0.1)',
            borderRadius: '20px',
            fontSize: '14px',
            animation: 'pulse 1.5s ease-in-out infinite'
          }}>
            <i className="bi bi-hourglass-split" style={{ marginRight: '8px' }}></i>
            Đang gửi câu trả lời...
          </div>
        )}

        <div className="q-answer py-3 px-md-3 h-50">
          {questionData?.options?.length > 0 && (
            <div className="q-option-container">
              {questionData.options.map((option, index) => (
                <div
                  key={index}
                  className={`q-option-item ${isSubmitting ? 'disabled' : 'cursor-pointer'} ${optionClassName(index)}`}
                  style={{
                    backgroundColor: optionBackgrounds[index],
                    opacity: isSubmitting ? 0.7 : 1,
                    pointerEvents: isSubmitting ? 'none' : 'auto',
                    margin: '10px 0',
                    padding: '15px',
                    borderRadius: '8px',
                    cursor: isSubmitting ? 'not-allowed' : 'pointer',
                    transition: 'all 0.3s ease',
                    position: 'relative',
                    border: answer.includes(index) ? '3px solid #fff' : '3px solid transparent'
                  }}
                  onClick={() => activeOption(index)}
                >
                  <div style={{
                    position: 'absolute',
                    top: '10px',
                    right: '10px',
                    width: '24px',
                    height: '24px',
                    borderRadius: '50%',
                    backgroundColor: 'rgba(255,255,255,0.3)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    {isSubmitting && answer.includes(index) ? (
                      <i className="bi bi-hourglass-split"></i>
                    ) : (
                      <i className="bi bi-check-lg"></i>
                    )}
                  </div>
                  <div>
                    <HtmlContent html={option.content} />
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Auto Progress Bar */}
        <AutoProgressBar
          isVisible={showAutoProgress}
          isCorrect={submitResult}
          onComplete={handleAutoProgressComplete}
          onSkip={handleAutoProgressSkip}
          duration={5000}
        />
      </div>

      {/* Status Display */}
      <div style={{ textAlign: 'center', marginBottom: '20px' }}>
        <p><strong>Trạng thái:</strong></p>
        <p>Đáp án đã chọn: {answer.length > 0 ? `Option ${answer[0] + 1}` : 'Chưa chọn'}</p>
        <p>Đang submit: {isSubmitting ? 'Có' : 'Không'}</p>
        <p>Đã submit: {isSubmit ? 'Có' : 'Không'}</p>
        <p>Hiển thị thanh tiến trình: {showAutoProgress ? 'Có' : 'Không'}</p>
        {submitResult !== null && (
          <p style={{
            color: submitResult ? '#2ecc71' : '#e74c3c',
            fontWeight: 'bold',
            fontSize: '18px'
          }}>
            {submitResult ? '✅ Chính xác!' : '❌ Không chính xác!'}
          </p>
        )}
      </div>

      {/* Reset Button */}
      <div style={{ textAlign: 'center' }}>
        <button
          onClick={resetDemo}
          style={{
            padding: '10px 20px',
            backgroundColor: '#8854C0',
            color: '#fff',
            border: 'none',
            borderRadius: '5px',
            cursor: 'pointer',
            fontSize: '16px'
          }}
        >
          Reset Demo
        </button>
      </div>

      {/* Instructions */}
      <div style={{
        marginTop: '30px',
        padding: '20px',
        backgroundColor: '#f8f9fa',
        borderRadius: '8px',
        color: '#333'
      }}>
        <h3>Hướng dẫn sử dụng:</h3>
        <ol>
          <li>Click vào một đáp án để chọn</li>
          <li>Hệ thống sẽ tự động submit sau 300ms</li>
          <li>Trong khi đang submit, bạn không thể click vào đáp án khác</li>
          <li>Sau khi submit xong, kết quả sẽ được hiển thị</li>
          <li><strong>Thanh tiến trình sẽ xuất hiện và tự động reset demo sau 5 giây</strong></li>
          <li>Bạn có thể bỏ qua bằng cách click "Tiếp tục" hoặc nhấn phím Space</li>
          <li>Click "Reset Demo" để thử lại bất cứ lúc nào</li>
        </ol>

        <h4>Tính năng chính:</h4>
        <ul>
          <li>✅ Chỉ cho phép chọn một đáp án (radio button behavior)</li>
          <li>✅ Tự động submit khi chọn đáp án</li>
          <li>✅ Hiển thị trạng thái loading khi đang submit</li>
          <li>✅ Disable các option khi đang submit</li>
          <li>✅ Visual feedback với animation và màu sắc</li>
          <li>✅ <strong>Thanh tiến trình tự động chuyển câu sau 5 giây</strong></li>
          <li>✅ <strong>Màu xanh lá nếu đúng, màu đỏ nếu sai</strong></li>
          <li>✅ <strong>Có thể bỏ qua bằng nút "Tiếp tục" hoặc phím Space</strong></li>
          <li>✅ <strong>Responsive trên mobile với accessibility support</strong></li>
        </ul>
      </div>

      <style jsx>{`
        @keyframes pulse {
          0%, 100% { opacity: 0.7; }
          50% { opacity: 1; }
        }

        .q-option-item.active {
          transform: scale(1.02);
        }

        .q-option-item.isCorrect {
          border-color: #2ecc71 !important;
          box-shadow: 0 0 10px rgba(46, 204, 113, 0.5);
        }

        .q-option-item.isWrong {
          border-color: #e74c3c !important;
          box-shadow: 0 0 10px rgba(231, 76, 60, 0.5);
        }

        .q-option-item.disable {
          opacity: 0.5;
        }
      `}</style>
    </div>
  );
};

export default MCQDemo;
