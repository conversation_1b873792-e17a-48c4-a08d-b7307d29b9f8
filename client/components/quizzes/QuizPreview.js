import React from 'react'
import Logo from '@/components/Logo'

export default function QuizPreview({ quiz, print = false, showAnswerLabel = false }) {
  if (!quiz) {
    return (
      <div className="d-flex justify-content-center align-items-center p-5">
        <div className="text-center text-muted fs-4">Không có dữ liệu</div>
      </div>
    )
  }

  return (
    <div className="quiz-preview-container pt-0 min-vh-100">
      <div className="print-header">
        <div className="header-left">
          <div className="logo-container">
            <Logo width={82} height={22} />
          </div>
          <h1 className="logo-title">{quiz.title}</h1>
        </div>
        <div className="header-right">
          {print && (
            <div className="info-line">
                <div className="info-label">Tên :</div>
                <div className="info-value"></div>
            </div>
          )}
          <div className="info-line">
            <div className="info-label">LỚP :</div>
            <div className="info-value">{print ? "" : quiz.grade?.title || ''}</div>
          </div>
          <div className="info-line">
            <div className="info-label">MÔN :</div>
            <div className="info-value">{quiz.subject?.title || ''}</div>
          </div>
          <div className="info-line mb-0">
            <div className="info-label">SỐ CÂU :</div>
            <div className="info-value">{quiz.questions?.length || 0}</div>
          </div>
        </div>
      </div>

      <div className="questions-container">
        {quiz.questions?.map((question, qIndex) => (
          <div key={qIndex} className="question-item">
            <span className="question-number">{qIndex + 1}.</span>
            <div
              className="question-content"
              dangerouslySetInnerHTML={{ __html: question.content_json?.content || '' }}
            />

            {question.content_json?.options && (
              <div className="options-container">
                {question.content_json.options.map((option, oIndex) => (
                  <div key={oIndex} className="option-item">
                    {showAnswerLabel && (
                      <div className="option-box">
                        {String.fromCharCode(65 + oIndex)}<span className="print-dot d-none">.</span>
                      </div>
                    )}
                    <div
                      className="box-content"
                      dangerouslySetInnerHTML={{ __html: option.content || '' }}
                    />
                  </div>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  )
}
