import React, { useState, useEffect, useRef, useCallback } from "react";
import ShutterAnimated from "../ShutterAnimated";
import Countdown from "../Countdown";

const QuizGameIntro = ({ initialCounter = -1, onEnd }) => {
  const [shutterOpen, setShutterOpen] = useState(false);
  const [countdownRunning, setCountdownRunning] = useState(false);
  const shutterEndedRef = useRef(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setCountdownRunning(true);
    }, 200);
    return () => clearTimeout(timer);
  }, []);

  const handleShutterEnd = useCallback(() => {
    if (shutterOpen && !shutterEndedRef.current) {
      shutterEndedRef.current = true;
      onEnd?.();
    }
  }, [shutterOpen, onEnd]);

  const handleCountdownEnd = useCallback(() => {
    // setTimeout để đảm bảo cập nhật state khi render kết thúc
    setTimeout(() => {
      setShutterOpen(true);
    }, 0);
  }, []);

  return (
    <>
      <ShutterAnimated open={shutterOpen} onEnd={handleShutterEnd} />
      {countdownRunning && (
        <Countdown initialCounter={initialCounter} onEnd={handleCountdownEnd} />
      )}
    </>
  );
};

export default React.memo(QuizGameIntro);
