"use client";

import React, { useState, useEffect, useMemo } from "react";
import { useDispatch } from "react-redux";
import { useRouter, useSearchParams } from 'next/navigation';

import AppBar from '@mui/material/AppBar';
import Box from '@mui/material/Box';
import Toolbar from '@mui/material/Toolbar';
import IconButton from '@mui/material/IconButton';

import Popover from '@mui/material/Popover';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import Radio from '@mui/material/Radio';
import Avatar from '@mui/material/Avatar';

import CloseIcon from '@mui/icons-material/Close';
import DirectionsRunIcon from '@mui/icons-material/DirectionsRun';
import PaletteIcon from '@mui/icons-material/Palette';

import useSound from "@/hooks/useSound";
import { setNoti } from "@/slices/notiSlice";
import { useSelector } from "react-redux";
import toast from "react-hot-toast";

import QuizMedia from "./QuizMedia";
import RejoinLoadingDialog from "./RejoinLoadingDialog";
import Loader from '../Loader';

import { soloJoin, fetchQuizResult } from '@/actions/quizResultAction';
import { fetchAssignment } from "@/actions/quizAction";
import { THEMES } from '@/constant'

const QuizStart = ({ initQuiz }) => {
  console.log('QuizStart');
  const [quiz, setQuiz] = useState(initQuiz);
  const [quizSummary, setQuizSummary] = useState(null);
  const [loadingQuizResult, setLoadingQuizResult] = useState(false);
  const [quizResultError, setQuizResultError] = useState(null);
  const dispatch = useDispatch();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [open, setOpen] = React.useState(false);
  const { user } = useSelector((state) => state.auth);
  const [publish, setPublish] = useState(true);
  const [showShareInput, setShowShareInput] = useState(false);
  const [assignment, setAssignment] = useState(null);
  const [canPlay, setCanPlay] = useState(false);
  const [themeAnchorEl, setThemeAnchorEl] = useState(null);
  const [selectedTheme, setSelectedTheme] = useState('classic'); // Fix hydration: luôn bắt đầu với 'classic'
  const [isClient, setIsClient] = useState(false); // Track client-side mounting

  const themePopoverOpen = Boolean(themeAnchorEl);

  useEffect(() => {
    setIsClient(true);
    if (typeof window !== 'undefined') {
      const savedTheme = localStorage.getItem('quiz-theme');
      if (savedTheme) {
        setSelectedTheme(savedTheme);
      }
    }
  }, []);

  const isEditable = useMemo(
    () => user && quiz?.editor_id === user?.id,
    [quiz, user]
  );

  const [play, { stop }] = useSound("/assets/sounds/waiting_bg.mp3", {
    loop: true,
    onload: () => console.log('Sound loaded!'),
  });

  const handleClose = () => {
    setOpen(false);
  };

  const handleThemeButtonClick = (event) => {
    setThemeAnchorEl(event.currentTarget);
  };

  const handleThemePopoverClose = () => {
    setThemeAnchorEl(null);
  };

  const handleThemeChange = (themeKey) => {
    setSelectedTheme(themeKey);
    if (typeof window !== 'undefined') {
      localStorage.setItem('quiz-theme', themeKey);
    }
    // Không đóng popover, để user có thể chọn theme khác
  };

  // Fetch quiz result when quizResultId is present
  useEffect(() => {
    const quizResultId = searchParams.get('quizResultId');

    if (quizResultId && !quizSummary) {
      setLoadingQuizResult(true);
      setQuizResultError(null);

      fetchQuizResult(quizResultId)
        .then((response) => {
          if (response?.data) {
            setQuizSummary(response.data);
          }
        })
        .catch((error) => {
          console.error('Error fetching quiz result:', error);
          setQuizResultError(error);

          if (error?.status === 403 || error?.status === 401) {
            toast.error("Bạn không có quyền truy cập kết quả này.");
          } else {
            toast.error("Không thể tải kết quả quiz. Vui lòng thử lại.");
          }
        })
        .finally(() => {
          setLoadingQuizResult(false);
        });
    }
  }, [searchParams, quizSummary]);

  useEffect(() => {
    play();

    return () => {
      stop();
    };
  }, [play, stop]);

  const startGame = async (gameType = 'solo') => {
    setOpen(true);

    if (quiz?.status === 0 && !isEditable) {
      setPublish(false);
      toast.error("Quiz này chưa được xuất bản. Vui lòng liên hệ với người tạo quiz để biết thêm chi tiết.");
      return;
    }

    const assignmentId = searchParams.get('assignment');

    if (assignmentId) {
      try {
        if (!assignment) {
          console.log('fetchAssignment');
          const res = await fetchAssignment(assignmentId);
          setAssignment(res?.data);

          if (res?.data?.is_expired) {
            toast.error("Bài thi này đã kết thúc. Vui lòng liên hệ với giáo viên để biết thêm chi tiết.");
            return;
          }
        } else if (assignment?.is_expired) {
          toast.error("Bài thi này đã kết thúc. Vui lòng liên hệ với giáo viên để biết thêm chi tiết.");
          return;
        }

        setCanPlay(true);
      } catch (errors) {
        if (errors?.status === 401) {
          toast.error("Bạn không có quyền truy cập bài thi này.");
          return;
        }
      }
    } else {
      setCanPlay(true);
    }

    try {
      const res = await soloJoin({
        quizId: quiz.id,
        gameType,
        assignmentId: assignmentId,
        quizResultId: searchParams.get('quizResultId')
      });

      if (res?.data) {
        setTimeout(() => {
          router.push(`/join/game/${res.data.id}?gameType=${gameType}`);
        }, 1000);
      } else {
        throw new Error("Đã xảy ra lỗi.");
      }
    } catch (errors) {
      dispatch(setNoti(errors));
      setOpen(false);
    }
  };

  const handleGoBack = () => {
    const fallback = `/quiz/${quiz.slug}-${quiz.id}`;
    const ref = document.referrer;

    if (
      ref &&
      ref !== location.href &&
      new URL(ref).origin === location.origin
    ) {
      router.back();
    } else {
      router.push(fallback);
    }
  };

  const handleShareClick = (e) => {
    setShowShareInput(true);
    handleCopyLink()
  };

  const handleCopyLink = () => {
    if (navigator?.clipboard) {
      navigator.clipboard
        .writeText(window.location.href + (searchParams.get('assignment') ? '&' : '?') + 'studentShare=true')
        .then(() => {
          toast.success('Liên kết chia sẻ được sao chép vào clipboard.', {
            position: "bottom-center",
            icon: '👏',
            style: { fontSize: '14px', borderRadius: '20px', backgroundColor: '#9a42926e', color: '#fff' }
          });

          // Set the input value and select it
          setTimeout(() => {
            const shareInput = document.getElementById('quiz-info-share-input');
            if (shareInput) {
              shareInput.value = `${window.location.href}${searchParams.get('assignment') ? '&' : '?'}studentShare=true`;
              shareInput.focus();
              shareInput.select();
            }
          }, 100);
        })
        .catch((err) => console.error("Lỗi copy:", err));
    } else {
      toast.error('Trình duyệt của bạn không hỗ trợ copy.');
    }
  };

  if (!quiz) return <Loader />;

  // Add loading state display
  if (loadingQuizResult) {
    return (
      <Box
        className={`position-relative theme-${selectedTheme}`}
        sx={{
          flexGrow: 1,
          minHeight: '100vh',
          backgroundColor: '#000',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <Loader />
      </Box>
    );
  }

  // Show error state if quiz result failed to load but continue with normal flow
  if (quizResultError && searchParams.get('quizResultId')) {
    console.warn('Quiz result could not be loaded, continuing without summary');
  }

  return (
    <Box
      className={`position-relative theme-${selectedTheme}`}
      sx={{
        flexGrow: 1,
        minHeight: '100vh',
        backgroundColor: '#000',
      }}
    >
      <AppBar
        position="static"
        sx={{
          position: 'relative',
          boxShadow: 'none',
          backgroundColor: '#09090933',
          color: '#fff',
          padding: '.5rem 0'
        }}
        color="transparent"
      >
        <Toolbar variant="dense">
          <IconButton
            className="btn-icon-md me-2 cursor-pointer"
            edge="start"
            color="inherit"
            onClick={handleGoBack}
            sx={{ color: '#fff' }}
          >
            <CloseIcon />
          </IconButton>
          <IconButton
            className="btn-icon-md me-2 cursor-pointer"
            color="inherit"
            onClick={handleThemeButtonClick}
            sx={{ color: '#fff' }}
          >
            <PaletteIcon /> &nbsp;Chủ đề
          </IconButton>
        </Toolbar>
      </AppBar>
      <div className="my-5 pre-game-screen">
        <div className="mx-auto" style={{ maxWidth: '700px' }}>
          {assignment?.is_expired && (
            <h2 className="h5 text-center text-warning mb-4">
              Bài thi đã kết thúc. Vui lòng liên hệ với giáo viên để biết thêm chi tiết.
            </h2>
          )}
          {quizSummary && quizSummary.status == 1 ? (
            (() => {
              const {
                total = 0,
                total_correct = 0,
                total_wrong = 0,
              } = quizSummary;

              const accuracyPercentage = total > 0 ? ((total_correct + total_wrong) / total) * 100 : 0;
              const notDoing = total - total_correct - total_wrong;

              return (
                <div className="bg-ds-dark rounded-4 p-4">
                  <div className="mb-4">
                    <div className="q-progress q-progress-danger my-0">
                      <div className="q-progress-label2 text-white fs-20 position-relative">
                        <span
                          className="d-inline-block text-end"
                          style={{ width: `${accuracyPercentage}%`, color: '#FFEB3B' }}
                        >
                          <DirectionsRunIcon />
                        </span>
                        <i
                          className="bi bi-flag-fill"
                          style={{ position: 'absolute', right: 0, fontSize: '16px', color: '#ffc107' }}
                        />
                      </div>
                      <div className="q-progress-main bg-white">
                        <div
                          className="q-progress-bar q-progress-active rounded-0"
                          style={{ width: `${accuracyPercentage}%` }}
                        />
                      </div>
                      <div className="q-progress-label2 d-flex justify-content-between text-white">
                        <span>Bắt đầu</span>
                        <span>Kết thúc</span>
                      </div>
                    </div>

                    <p className="text-white fs-16 text-center fw-medium">
                      {notDoing === total
                        ? 'Bạn chưa làm câu nào'
                        : notDoing === 0
                          ? `Bạn làm đúng ${total_correct} câu`
                          : `Bạn còn ${notDoing} câu chưa làm`}
                    </p>
                  </div>

                  <button
                    className="primary-button resume-game w-100 mt-2 cursor-pointer"
                    onClick={() => startGame('solo')}
                    disabled={assignment?.is_expired}
                  >
                    <i className="bi bi-play-fill me-2 fs-3" /> Tiếp tục làm
                  </button>
                </div>
              );
            })()
          ) : (
            <div className="bg-ds-dark rounded-4 p-4">
              <button
                className="primary-button w-100 cursor-pointer"
                onClick={() => startGame('solo')}
                disabled={assignment?.is_expired}
              >
                <i className="bi bi-play-fill me-2 fs-3 align-middle"></i> { quiz.my_played ? 'Làm lại' : 'Bắt Đầu' }
              </button>
              <button
                className="secondary-button w-100 mt-3 cursor-pointer"
                onClick={() => startGame('flashcard')}
                disabled={assignment?.is_expired}
              >
                <i className="bi bi-credit-card-fill me-2"></i> Flashcard
              </button>
            </div>
          )}

          <div className="quiz-and-host-info-card p-4 mt-5">
            <QuizMedia quiz={quiz} />
            <button
              className="btn btn-sm w-100 mt-3 share-button bg-ds-light"
              onClick={handleShareClick}
              style={{ display: showShareInput ? 'none' : 'block' }}
            >
              <i className="bi bi-share-fill me-2"></i> Chia sẻ
            </button>
            <input
              id="quiz-info-share-input"
              className="form-control mt-3 quiz-info-share-input"
              onClick={handleCopyLink}
              readOnly
              style={{
                display: showShareInput ? 'block' : 'none',
                backgroundColor: '#f8f9fa',
                border: '1px solid #dee2e6',
                fontSize: '14px'
              }}
            />
          </div>
        </div>
        { publish && canPlay && (
          <RejoinLoadingDialog open={open} onClose={handleClose} />
        )}
      </div>

      {/* Theme Selection Popover */}
      <Popover
        open={themePopoverOpen}
        anchorEl={themeAnchorEl}
        onClose={handleThemePopoverClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
        slotProps={{
          paper: {
            sx: {
              width: 200,
              backgroundColor: '#222222',
              color: '#fff',
              borderRadius: 2,
              border: '2px solid #fff3',
              mt: 1,
            }
          }
        }}
      >
        <Box sx={{ py: 2 }}>
          <List dense sx={{ py: 0 }}>
            {THEMES.map((theme) => (
              <ListItem
                key={theme.key}
                button="true"
                onClick={() => handleThemeChange(theme.key)}
                secondaryAction={
                  <Radio
                    checked={selectedTheme === theme.key}
                    sx={{
                      color: '#fff',
                      '&.Mui-checked': {
                        color: '#4caf50',
                      },
                      padding: '4px',
                    }}
                  />
                }
                sx={{
                  borderRadius: 1,
                  mb: 0.5,
                  backgroundColor: selectedTheme === theme.key ? 'rgba(76, 175, 80, 0.1)' : 'transparent',
                  '&:hover': {
                    backgroundColor: 'rgba(255, 255, 255, 0.05)',
                  },
                }}
              >
                <ListItemIcon sx={{ minWidth: 40 }}>
                  <Avatar
                    sx={{
                      width: 32,
                      height: 32,
                      backgroundColor: selectedTheme === theme.key ? '#4caf50' : '#555',
                    }}
                  >
                    {theme.icon}
                  </Avatar>
                </ListItemIcon>
                <ListItemText
                  primary={theme.name}
                  slotProps={{
                    primary: {
                      sx: { color: '#fff !important', fontSize: '12px', fontWeight: 600, fontFamily: 'Quicksand, Helvetica, Arial, sans-serif' }
                    }
                  }}
                />
              </ListItem>
            ))}
          </List>
        </Box>
      </Popover>
    </Box>
  );
};

export default QuizStart;
