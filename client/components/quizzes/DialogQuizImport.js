"use client";

import React, {forwardRef, memo} from "react";

import Box from "@mui/material/Box";
import Dialog from "@mui/material/Dialog";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import Typography from "@mui/material/Typography";
import IconButton from "@mui/material/IconButton";
import CloseIcon from "@mui/icons-material/Close";
import QuizImportFile from "@/components/quizzes/QuizImportFile";
import FileIcon from "@mui/icons-material/Description";
import Slide from "@mui/material/Slide";

const Transition = forwardRef(function Transition(props, ref) {
  return <Slide direction="up" ref={ref} {...props} />;
});

const DialogQuizImport = ({ open, onClose, onImportSuccess, quiz = {}, fileType = "word" }) => {
  console.log("DialogQuizImport");

  return (
    <>
      <Dialog
        open={open}
        onClose={onClose}
        slots={{ transition: Transition}}
        keepMounted
        fullWidth={true}
        maxWidth="md"
      >
        <DialogTitle
          className="h5 mb-0"
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          <Box sx={{ display: 'flex' }}>
            <Box mb={1}>
              <FileIcon className="file-icon-bg" />
            </Box>
            <Box ml={2}>
              <Typography variant="h6" gutterBottom fontWeight="bold" className="mb-1">
                Nhập câu hỏi từ { fileType === "word" ? "file word" : "bảng tính" }
              </Typography>
              <Typography variant="body2" color="text.secondary" className="fs-12">
                Vui lòng tải lên { fileType === "word" ? "file word" : "bảng tính excel" } theo mẫu
              </Typography>
            </Box>
          </Box>
          <IconButton aria-label="close" onClick={onClose}>
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent>
          <QuizImportFile
            quiz={quiz}
            onClose={onClose}
            onImportSuccess={onImportSuccess}
            fileType={fileType}
          />
        </DialogContent>
      </Dialog>
    </>
  );
};

export default memo(DialogQuizImport);
