"use client";

import React, { useState, useMemo } from 'react'
import Logo from '@/components/Logo'
import AntSwitch from "@/components/ui/AntSwitch";

import Box from '@mui/material/Box'
import Typography from '@mui/material/Typography'
import FormControlLabel from '@mui/material/FormControlLabel'
import Paper from '@mui/material/Paper'
import Divider from '@mui/material/Divider'
import Button from '@mui/material/Button'
import ButtonGroup from '@mui/material/ButtonGroup'
import Collapse from '@mui/material/Collapse'

import ExpandMoreIcon from '@mui/icons-material/ExpandMore'
import ExpandLessIcon from '@mui/icons-material/ExpandLess'

import { useTheme } from '@mui/material/styles'

export default function QuizPrint({ quiz, print = true }) {
  const theme = useTheme()

  // Print settings state
  const [printSettings, setPrintSettings] = useState({
    shuffleQuestions: false,
    shuffleAnswers: false,
    showAnswerKey: false,
    showInstructorName: true,
    showAnswerChoices: true,
    showAnswerLabel: false,
    fontSize: 'M'
  })

  // Expanded settings state
  const [expandedSettings, setExpandedSettings] = useState(false)

  // Handle print setting changes
  const handleSettingChange = (setting) => (event) => {
    console.log('handleSettingChange', setting, event.target.checked !== undefined ? event.target.checked : event.target.value)
    setPrintSettings(prev => ({
      ...prev,
      [setting]: event.target.checked !== undefined ? event.target.checked : event.target.value
    }))
  }

  // Handle font size change
  const handleFontSizeChange = (size) => {
    setPrintSettings(prev => ({
      ...prev,
      fontSize: size
    }))
  }

  // Toggle expanded settings
  const toggleExpandedSettings = () => {
    setExpandedSettings(prev => !prev)
  }

  if (!quiz) {
    return (
      <div className="d-flex justify-content-center align-items-center p-5">
        <div className="text-center text-muted fs-4">Không có dữ liệu</div>
      </div>
    )
  }

  // Process questions based on settings
  const processedQuestions = useMemo(() => {
    if (!quiz.questions) return []

    let questions = [...quiz.questions]

    // Shuffle questions if enabled
    if (printSettings.shuffleQuestions) {
      questions = questions.sort(() => Math.random() - 0.5)
    }

    // Shuffle answers within each question if enabled
    if (printSettings.shuffleAnswers) {
      questions = questions.map(question => ({
        ...question,
        content_json: {
          ...question.content_json,
          options: question.content_json?.options ?
            [...question.content_json.options].sort(() => Math.random() - 0.5) :
            question.content_json?.options
        }
      }))
    }

    return questions
  }, [quiz.questions, printSettings.shuffleQuestions, printSettings.shuffleAnswers])

  // Font size classes
  const getFontSizeClass = () => {
    switch (printSettings.fontSize) {
      case 'S': return 'font-size-small'
      case 'L': return 'font-size-large'
      case 'XL': return 'font-size-extra-large'
      default: return 'font-size-medium'
    }
  }

  // Font Size Selector Component
  const FontSizeSelector = () => {
    const fontSizes = ['S', 'M', 'L', 'XL']

    return (
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <Typography variant="body2" sx={{ minWidth: 'fit-content', color: '#495057' }}>
          Cỡ chữ:
        </Typography>
        <ButtonGroup variant="outlined" size="small">
          {fontSizes.map((size) => (
            <Button
              key={size}
              onClick={() => handleFontSizeChange(size)}
              variant={printSettings.fontSize === size ? 'contained' : 'outlined'}
              sx={{
                minWidth: '40px',
                backgroundColor: printSettings.fontSize === size ? theme.palette.secondary.main : 'white',
                borderColor: printSettings.fontSize === size ? theme.palette.secondary.main : '#0909091a',
                color: printSettings.fontSize === size ? 'white' : "#222"
              }}
            >
              {size}
            </Button>
          ))}
        </ButtonGroup>
      </Box>
    )
  }

  return (
    <>
      {/* Print Settings Header - Moved outside quiz-preview-container */}
      {print && (
        <Paper className="print-settings-header" elevation={1} sx={{ mb: 3, p: 2 }}>
          {/* Header with Logo and Title */}
          <Box className="d-flex align-items-center mb-3">
            <Logo width={48} height={11} />
            <Typography variant="h6" sx={{ ml: 2, mb: '0 !important', fontWeight: 'bold' }}>
              Đề có thể in miễn phí
            </Typography>
          </Box>

          <Divider />

          {/* Compact Print Configuration Options */}
          <Box sx={{ display: 'flex', flexWrap: 'wrap', alignItems: 'center', gap: 2 }}>
            {/* Main Settings Row */}
            <FormControlLabel
              control={
                <AntSwitch
                  checked={printSettings.shuffleQuestions}
                  onChange={handleSettingChange('shuffleQuestions')}
                  color="primary"
                  size="small"
                />
              }
              label="Trộn câu hỏi"
              sx={{ margin: 0, gap: 1 }}
            />

            <FormControlLabel
              control={
                <AntSwitch
                  checked={printSettings.shuffleAnswers}
                  onChange={handleSettingChange('shuffleAnswers')}
                  color="primary"
                  size="small"
                />
              }
              label="Trộn đáp án"
              sx={{ margin: 0, gap: 1 }}
            />

            <FormControlLabel
              control={
                <AntSwitch
                  checked={printSettings.showAnswerKey}
                  onChange={handleSettingChange('showAnswerKey')}
                  color="primary"
                  size="small"
                />
              }
              label="Bảng đáp án"
              sx={{ margin: 0, gap: 1 }}
            />

            {/* Font Size Selector */}
            <FontSizeSelector />

            {/* Expand/Collapse Button */}
            <Button
              onClick={toggleExpandedSettings}
              variant="text"
              size="small"
              color="secondary"
              endIcon={expandedSettings ? <ExpandLessIcon /> : <ExpandMoreIcon />}
              sx={{ ml: 'auto', minWidth: 'fit-content' }}
            >
              {expandedSettings ? 'Thu gọn' : 'Thêm cài đặt'}
            </Button>
          </Box>

          {/* Expanded Settings */}
          <Collapse in={expandedSettings}>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', alignItems: 'center', gap: 2, pt: 2, mt: 2, borderTop: 1, borderColor: 'divider' }}>
              <FormControlLabel
                control={
                  <AntSwitch
                    checked={printSettings.showAnswerChoices}
                    onChange={handleSettingChange('showAnswerChoices')}
                    color="primary"
                    size="small"
                  />
                }
                label="Hiển thị các phương án trả lời"
                sx={{ margin: 0, gap: 1 }}
              />
              <FormControlLabel
                 control={
                   <AntSwitch
                      checked={printSettings.showAnswerLabel}
                      onChange={handleSettingChange('showAnswerLabel')}
                      color="primary"
                      size="small"
                   />
                 }
                 label="Thêm label A, B, C, D trước đáp án"
                 sx={{ margin: 0, gap: 1 }}
              />
              <FormControlLabel
                 control={
                   <AntSwitch
                      checked={printSettings.showInstructorName}
                      onChange={handleSettingChange('showInstructorName')}
                      color="primary"
                      size="small"
                   />
                 }
                 label="Tên giáo viên"
                 sx={{ margin: 0, gap: 1 }}
              />
            </Box>
          </Collapse>
        </Paper>
      )}

      {/* Quiz Preview Container */}
      <div className={`quiz-preview-container pt-0 min-vh-100 ${getFontSizeClass()}`}>
        <div className="print-header">
          <div className="header-left">
            <div className="logo-container">
              <Logo width={82} height={22} />
            </div>
            <h1 className="logo-title">{quiz.title}</h1>
            <div className="mt-1 fs-14">
              <small>Số lượng câu hỏi: {quiz.questions?.length || 0}</small><br />
              {printSettings.showInstructorName && quiz.editor && (
                <small>Tên giáo viên: {quiz.editor?.name || ''}</small>
              )}
            </div>
          </div>
          <div className="header-right">
            {print && (
              <div className="info-line">
                  <div className="info-label">Họ tên :</div>
                  <div className="info-value"></div>
              </div>
            )}
            <div className="info-line">
              <div className="info-label">Lớp học :</div>
              <div className="info-value">{print ? "" : quiz.grade?.title || ''}</div>
            </div>
            <div className="info-line">
              <div className="info-label">Môn học :</div>
              <div className="info-value">{quiz.subject?.title || ''}</div>
            </div>
            {print && (
               <div className="info-line mb-0">
                 <div className="info-label">Ngày :</div>
                 <div className="info-value"></div>
               </div>
            )}
          </div>
        </div>

        <div className="questions-container">
          {processedQuestions?.map((question, qIndex) => (
            <div key={qIndex} className="question-item">
              <span className="question-number">{qIndex + 1}.</span>
              <div
                className="question-content"
                dangerouslySetInnerHTML={{ __html: question.content_json?.content || '' }}
              />

              {printSettings.showAnswerChoices && question.content_json?.options && (
                <div className="options-container">
                  {question.content_json.options.map((option, oIndex) => (
                    <div key={oIndex} className="option-item">
                      {printSettings.showAnswerLabel && (
                         <div className="option-box">
                           {String.fromCharCode(65 + oIndex)}<span className="print-dot d-none">.</span>
                         </div>
                      )}
                      <div
                        className="box-content"
                        dangerouslySetInnerHTML={{ __html: option.content || '' }}
                      />
                    </div>
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Answer Key Section */}
        {printSettings.showAnswerKey && processedQuestions.length > 0 && (
          <div className="answer-key-section">
            <Typography variant="h6" sx={{ mt: 4, mb: 2, fontWeight: 'bold', textAlign: 'center' }}>
              BẢNG ĐÁP ÁN
            </Typography>
            <Box className="answer-key-grid">
              {processedQuestions.map((question, qIndex) => {
                const correctAnswerIndex = question.content_json?.options?.findIndex(
                  option => option.isCorrect
                )
                const correctAnswer = correctAnswerIndex !== -1 ?
                  String.fromCharCode(65 + correctAnswerIndex) : '?'

                return (
                  <Box key={qIndex} className="answer-key-item">
                    <span className="question-num">{qIndex + 1}.</span>
                    <span className="answer">{correctAnswer}</span>
                  </Box>
                )
              })}
            </Box>
          </div>
        )}
      </div>
    </>
  )
}
