"use client";

import React, { useRef, useMemo, useCallback, useState } from "react";
import { useDispatch } from "react-redux";
import { useRouter, useSearchParams } from 'next/navigation';

import MainCard from '@/components/cards/MainCard';
import Card from "@mui/material/Card";
import CardContent from "@mui/material/CardContent";
import CardMedia from "@mui/material/CardMedia";
import Avatar from "@mui/material/Avatar";
import Box from "@mui/material/Box";
import FormControl from "@mui/material/FormControl";
import FormHelperText from "@mui/material/FormHelperText";
import InputLabel from "@mui/material/InputLabel";
import OutlinedInput from "@mui/material/OutlinedInput";
import Radio from '@mui/material/Radio';
import RadioGroup from '@mui/material/RadioGroup';
import FormControlLabel from '@mui/material/FormControlLabel';
import Button from "@mui/material/Button";
import TextField from '@mui/material/TextField';
import Autocomplete from '@mui/material/Autocomplete';
import Container from '@mui/material/Container';
import EditIcon from "@mui/icons-material/Edit";
import CircularProgress from "@mui/material/CircularProgress";

import ChevronRightIcon from '@mui/icons-material/ChevronRight';

// third party
import * as Yup from "yup";
import { Formik } from "formik";
import { useTranslations } from "next-intl";
import { toast } from "react-hot-toast";
import dayjs from 'dayjs';
import 'dayjs/locale/vi';

import { storeQuiz, updateQuiz, copyQuiz, assignQuiz } from "@/actions/quizAction";
import { fetchOptions } from "@/actions/onlyClientAction";
import { setNoti } from "@/slices/notiSlice";

import { delay, isBase64 } from "@/utils/helpers";

import NextImage from "@/components/NextImage";
import { DragDropCropImage } from "../DragDropCropImage";
import ClassroomPicker from "../classrooms/ClassroomPicker";
import DateTimePickerField from "../DateTimePickerField";
import EditableText from "../EditableText";
import DialogQuizForm from "./DialogQuizForm";
import DialogDragDropCropImage from "../DragDropCropImage";

import { useSelector } from "react-redux";
import Typography from '@mui/material/Typography'

const GradeSubjectSelect = React.memo(
  ({ gradeAndSubject, values, errors, touched, handleChange }) => {
    const t = useTranslations("Common");

    return (
      <div className="row">
        {Object.keys(gradeAndSubject).map((key) => {
          let key_name = "";
          switch (key) {
            case "subjects":
              key_name = "subject_id";
              break;
            case "grades":
              key_name = "grade_id";
              break;
            default:
              key_name = key;
          }
          return (
            <div className="col-md-6 mb-4" key={key}>
              <FormControl
                fullWidth
                error={Boolean(touched[key_name] && errors[key_name])}
              >
              <Autocomplete
                id={`autocomplete-${key_name}-unique`}
                options={gradeAndSubject[key]}
                getOptionLabel={(option) => option.label}
                noOptionsText="Không tìm thấy kết quả phù hợp"
                onChange={(event, newValue) => {
                  handleChange({
                    target: {
                      name: key_name,
                      value: newValue ? newValue.value : "",
                    },
                  });
                }}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label={t(key)}
                  />
                )}
                />
                {touched[key_name] && errors[key_name] && (
                  <FormHelperText error>{errors[key_name]}</FormHelperText>
                )}
              </FormControl>
            </div>
          );
        })}
      </div>
    );
  }
);

GradeSubjectSelect.displayName = "GradeSubjectSelect";

const QuizCreate = ({ quiz = {} }) => {
  console.log("QuizCreate");
  const dispatch = useDispatch();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [openDialogQuizFrom, setOpenDialogQuizFrom] = useState(false);
  const [openDialogCropImage, setOpenDialogCropImage] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const classroomParam = searchParams.get('classroom');
  const { data: gradeAndSubject } = fetchOptions("grades,subjects");

  const cropRef = useRef();
  const memoizedCropImage = useMemo(() => (
    <DragDropCropImage
      imgSrc={quiz.banner}
      ref={cropRef}
      sx={{ maxWidth: '200px', height: '200px', margin: '0 auto' }}
    />
  ), [quiz.banner]);

  const { user } = useSelector((state) => state.auth);
  const isEditable = useMemo(
    () => user && quiz?.editor_id === user?.id,
    [quiz, user]
  );

  const editQuiz = useCallback(
    async (data, callback) => {
      try {
        if (data.title) {
          data.slug = data.title;
        }

        const { data: newQuiz } = await updateQuiz({ ...quiz, ...data });

        if (newQuiz) {
          await fetch(
            `/api/revalidate?path=${encodeURIComponent(
              `/quiz/${newQuiz.slug}-${newQuiz.id}`
            )}&path=${encodeURIComponent(`/quiz/${quiz.slug}-${quiz.id}`)}`
          );

          toast.success("Sửa Quiz thành công");

          if (typeof callback === "function") {
            callback();
          }

          router.refresh();

          return;

        } else {
          throw new Error("Đã xảy ra lỗi. Không thể cập nhật quiz");
        }
      } catch (error) {
        console.error(error);
        toast.error("Đã xảy ra lỗi. Vui lòng thử lại");
      }
    },
    [quiz, router]
  );

  const handleOpenDialogQuizFrom = useCallback(() => {
    setOpenDialogQuizFrom(true);
  }, []);

  const handleCloseDialogQuizFrom = useCallback(() => {
    setOpenDialogQuizFrom(false);
  }, []);

  const handleOpenDialogCropImage = useCallback(() => {
    setOpenDialogCropImage(true);
  }, []);

  const handleCloseDialogCropImage = useCallback(() => {
    setOpenDialogCropImage(false);
  }, []);

  const handleCopyQuiz = async () => {
    if (isLoading) return;

    setIsLoading(true);

    try {
      const { data: newQuiz } = await copyQuiz(quiz.id);
      toast.success("Sao chép quiz thành công");
      router.push(`/dashboard/quiz/${newQuiz.id}/edit`);
    } catch (error) {
      console.error(error);
      toast.error("Không thể sao chép quiz. Vui lòng thử lại sau.");
      setIsLoading(false);
    }
  };

  return (
    <Container>
      <MainCard>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h6" color="initial">{quiz?.id ? "Giao bài tập" : "Tạo quiz và giao bài tập"}</Typography>
        <button className="btn btn-link" type="button" onClick={() => router.back()}>
            Quay lại <ChevronRightIcon fontSize="small" />
        </button>
      </Box>
      {quiz?.id && (
        <Card className="mt-3 p-3">
          <div className="d-flex flex-md-row flex-column gap-3">
            <CardMedia className="quiz-banner show m-auto bg-body-secondary rounded-3">
              <NextImage
                imgStyle={{
                  objectFit: "contain",
                  objectPosition: "center",
                }}
                className="h-100"
                src={quiz.banner}
                alt={quiz.title}
              />
              {isEditable && (
                <Button
                  className="btn-small"
                  variant="contained"
                  color="secondary"
                  aria-label="edit"
                  onClick={handleOpenDialogCropImage}
                  size="small"
                >
                  <EditIcon fontSize="small" />
                </Button>
              )}
            </CardMedia>
            <CardContent
              sx={{ flex: "1", overflow: "hidden" }}
              className="text-start p-1 p-md-3"
            >
              <div className="d-flex justify-content-between align-items-center mb-1">
                <span className="badge bg-light text-dark">{quiz.type_text}</span>
              </div>
              <div className="mt-2">
                <div className="">
                  <div>
                    <div className="position-relative mb-1">
                      <div
                        className="quiz-title"
                        style={{
                          minHeight: "47px",
                          maxWidth: "calc(100% - 50px)",
                          overflow: "hidden",
                        }}
                      >
                      {isEditable ? (
                        <EditableText
                          key={quiz.title}
                          value={quiz.title || ""}
                          emptyInit={true}
                          onFocus={(value) => console.log("on focus: ", value)}
                          onBlur={(value) => {
                            console.log("on blur: ", value);
                            editQuiz({ title: value });
                          }}
                          style={{
                            wordBreak: "break-word",
                          }}
                          textVariant="h1"
                          className="h4 fw-bold"
                        />
                      ) : (
                        <h4>{quiz.title || ""}</h4>
                      )}
                      </div>
                    </div>
                    <div className="d-flex align-items-center flex-wrap align-content-stretch gap-1">
                      {quiz.editor && (
                        <span className="d-flex gap-2 align-items-center me-3">
                          <Avatar src={quiz.editor?.avatar} alt={quiz.editor?.name} sx={{ width: 24, height: 24 }}>
                            {quiz.editor.name.charAt(0)}
                          </Avatar>
                          <span style={{ fontSize: '0.875rem', color: '#6D6D6D' }}>{quiz.editor.name}</span>
                        </span>
                      )}
                      <span className="text-black-50 fs-14 me-3">
                        <i className="bi bi-patch-question me-1"></i>
                        {quiz.questions_count || quiz.questions?.length || 0} câu
                        hỏi
                      </span>
                      {quiz.subject && (
                        <span className="text-black-50 fs-14 me-3">
                          <i className="bi bi-file-earmark-medical me-1"></i>
                          {quiz.subject.title}
                        </span>
                      )}
                      {quiz.grade && (
                        <span className="text-black-50 fs-14 me-3">
                          <i className="bi bi-mortarboard-fill me-1"></i>
                          {quiz.grade.title}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              </div>
              <Box sx={{ pt: 1 }}>
                {isEditable && (
                  <button
                      className="btn btn-primary4 active5 btn-sm"
                      onClick={handleOpenDialogQuizFrom}
                      aria-hidden={false}
                    >
                      <i className="bi bi-gear-fill me-2"></i>
                      <span>Chỉnh sửa</span>
                    </button>
                )}
                {user && !isEditable && (
                  <button
                    onClick={handleCopyQuiz}
                    disabled={isLoading}
                    className="btn btn-primary4 active5 btn-sm"
                  >
                    {isLoading ? (
                      <CircularProgress size={20} color="inherit" className="me-2" />
                    ) : (
                      <i className="bi bi-pencil-square me-1"></i>
                    )}
                    <span>Sao chép và chỉnh sửa</span>
                  </button>
                )}
              </Box>
            </CardContent>
          </div>
          </Card>
        )}
        <Formik
            initialValues={{
              title: quiz.title || "Bài Quiz không có tiêu đề",
              grade_id: quiz.grade_id || "",
              subject_id: quiz.subject_id || "",
              classrooms: quiz.classrooms?.map(c => c.id) || (classroomParam ? [parseInt(classroomParam)] : []),
              start_time: null,
              end_time: null,
              show_answer: 1,
            }}
            validationSchema={Yup.object().shape({
              title: Yup.string()
                .min(3)
                .max(500)
                .required("Tiêu đề không được để trống"),
              grade_id: Yup.number()
                .required("Grade is required")
                .positive()
                .integer(),
              subject_id: Yup.number()
                .required("Subject is required")
                .positive()
                .integer(),
              classrooms: classroomParam ?
                Yup.array().min(1, "Bạn chưa chọn lớp") :
                Yup.array().nullable(),
              start_time: Yup.date()
                .nullable(),
              end_time: Yup.date()
                .nullable()
                .min(Yup.ref("start_time"), "Thời gian kết thúc phải sau thời gian bắt đầu"),
            })}
            onSubmit={async (values, { setErrors, setSubmitting }) => {
              try {
                await delay(400);

                let banner_base64 = null;

                if (cropRef.current) {
                  banner_base64 = cropRef.current.getImg();
                }

                const formattedValues = {
                  ...values,
                  start_time: values.start_time
                    ? dayjs(values.start_time).format("YYYY-MM-DD HH:mm:ss")
                    : null,
                  end_time: values.end_time
                    ? dayjs(values.end_time).format("YYYY-MM-DD HH:mm:ss")
                    : null,
                  is_assign: 1,
                };

                if (isBase64(banner_base64)) {
                  formattedValues.banner_base64 = banner_base64;
                } else if (banner_base64 === null) {
                  formattedValues.remove_banner = 1;
                }

                if (quiz.id) {
                  formattedValues.id = quiz.id;

                  const {data: newQuiz} = await assignQuiz(formattedValues);

                  await fetch(`/api/revalidate?path=${encodeURIComponent(`/quiz/${newQuiz.slug}-${newQuiz.id}`)}&path=${encodeURIComponent(`/quiz/${quiz.slug}-${quiz.id}`)}`);

                  toast.success("Thành công");

                  if (newQuiz?.classroom_quiz?.length > 0) {
                    router.push(`/dashboard/reports/${newQuiz.classroom_quiz[0].id}`);
                  } else {
                    router.push(`/quiz/${newQuiz.slug}-${newQuiz.id}`);
                  }
                } else {
                  const res = await storeQuiz(formattedValues);

                  toast.success("Thêm quiz và giao bài thành công");

                  router.push(`/dashboard/quiz/${res.data.id}/edit?homework=1`);
                }
              } catch (err) {
                console.log(err);
                if (err?.status == 422) {
                  setErrors(err.errors);
                } else {
                  dispatch(setNoti(err));
                }
              } finally {
                setSubmitting(false);
              }
            }}
          >
            {({
              errors,
              handleBlur,
              handleChange,
              handleSubmit,
              isSubmitting,
              touched,
              values,
              setFieldValue,
            }) => {
              return (
                <form noValidate onSubmit={handleSubmit}>
                  {!quiz?.id && (
                    <div className="d-flex flex-wrap gap-4 mt-3">
                      {memoizedCropImage}
                      <Box className="d-flex flex-column gap-4 flex-fill mt-2">
                        <FormControl
                          fullWidth
                          error={Boolean(touched.title && errors.title)}
                        >
                          <InputLabel htmlFor="outlined-adornment-title">
                            Tiêu đề
                          </InputLabel>
                          <OutlinedInput
                            id="outlined-adornment-title"
                            type="text"
                            label="Tiêu đề"
                            value={values.title}
                            name="title"
                            onBlur={handleBlur}
                            onChange={handleChange}
                            inputProps={{}}
                          />
                          {touched.title && errors.title && (
                            <FormHelperText
                              error
                              id="standard-weight-helper-text-title"
                            >
                              {errors.title}
                            </FormHelperText>
                          )}
                        </FormControl>

                        {gradeAndSubject && (
                          <GradeSubjectSelect
                            gradeAndSubject={gradeAndSubject}
                            values={values}
                            errors={errors}
                            touched={touched}
                            handleChange={handleChange}
                          />
                        )}
                      </Box>
                    </div>
                  )}
                <ClassroomPicker
                  defaultSelected={values.classrooms}
                  fieldName="classrooms"
                  setFieldValue={setFieldValue}
                />
                {touched.classrooms && errors.classrooms && (
                  <FormHelperText error>
                    {errors.classrooms}
                  </FormHelperText>
                )}
                <div className="row mt-4">
                  <div className="col-sm-2">
                    <p>Thời gian nộp bài:</p>
                  </div>
                  <div className="col-sm-5 mb-3">
                    <DateTimePickerField
                      label="Từ"
                      value={values.start_time}
                      fieldName="start_time"
                      setFieldValue={setFieldValue}
                    />
                    {touched.start_time && errors.start_time && (
                      <FormHelperText error>{errors.start_time}</FormHelperText>
                    )}
                  </div>
                  <div className="col-sm-5 mb-3">
                    <DateTimePickerField
                      label="Đến"
                      value={values.end_time}
                      fieldName="end_time"
                      setFieldValue={setFieldValue}
                    />
                    {touched.end_time && errors.end_time && (
                      <FormHelperText error>{errors.end_time}</FormHelperText>
                    )}
                  </div>
                </div>
                <div className="row align-items-center">
                  <div className="col-sm-2">
                    <p className="mb-0">Cho xem kết quả:</p>
                  </div>
                  <div className="col-sm-10">
                    <FormControl>
                      <RadioGroup
                        row
                        aria-labelledby="show_answer"
                        name="show_answer"
                        value={values.show_answer}
                        onChange={handleChange}
                      >
                        <FormControlLabel value="0" control={<Radio />} label="Không" />
                        <FormControlLabel value="1" control={<Radio />} label="Có" />
                      </RadioGroup>
                    </FormControl>
                  </div>
                </div>
                <Box className="text-center mt-4">
                  <Button
                    variant="outlined"
                    type="button"
                    color="inherit"
                    sx={{ mr: 2 }}
                  >
                    Hủy
                  </Button>
                  <Button
                    disableElevation
                    disabled={isSubmitting}
                    type="submit"
                    variant="contained"
                    color="secondary"
                    endIcon={isSubmitting ? <CircularProgress size={18} color="inherit" /> : null}
                  >
                    Lưu
                  </Button>
                </Box>
              </form>
              );
            }}
        </Formik>
      </MainCard>
      <DialogQuizForm
        quiz={quiz}
        open={openDialogQuizFrom}
        onClose={handleCloseDialogQuizFrom}
      />
      {openDialogCropImage && (
        <DialogDragDropCropImage
          open={openDialogCropImage}
          onClose={handleCloseDialogCropImage}
          imgSrc={quiz.banner}
          setImgSrc={(value) => {
            if (value) {
              if (isBase64(value)) {
                editQuiz({ banner_base64: value }, handleCloseDialogCropImage);
              } else {
                handleCloseDialogCropImage();
              }
            } else {
              editQuiz({ remove_banner: 1 });
            }
          }}
        />
      )}
    </Container>
  );
};

export default QuizCreate;
