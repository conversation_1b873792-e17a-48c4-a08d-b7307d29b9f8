"use client";

import React, { useState } from 'react';
import AutoProgressBar from '../AutoProgressBar';

const AutoProgressDebug = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [isCorrect, setIsCorrect] = useState(true);

  const handleStart = (correct) => {
    console.log('🔥 Debug: Starting AutoProgressBar with isCorrect:', correct);
    setIsCorrect(correct);
    setIsVisible(true);
  };

  const handleComplete = () => {
    console.log('✅ Debug: onComplete called');
    setIsVisible(false);
  };

  const handleSkip = () => {
    console.log('⏭️ Debug: onSkip called');
    setIsVisible(false);
  };

  const handleReset = () => {
    console.log('🔄 Debug: Reset');
    setIsVisible(false);
  };

  return (
    <div style={{ 
      maxWidth: '800px', 
      margin: '0 auto', 
      padding: '20px',
      fontFamily: 'Arial, sans-serif',
      backgroundColor: '#461a42',
      color: '#fff',
      minHeight: '100vh'
    }}>
      <h1>AutoProgressBar Debug</h1>
      
      <div style={{ marginBottom: '20px' }}>
        <h3>Controls:</h3>
        <button 
          onClick={() => handleStart(true)}
          style={{
            padding: '10px 20px',
            margin: '5px',
            backgroundColor: '#2ecc71',
            color: '#fff',
            border: 'none',
            borderRadius: '5px',
            cursor: 'pointer'
          }}
        >
          Start Correct (Green)
        </button>
        
        <button 
          onClick={() => handleStart(false)}
          style={{
            padding: '10px 20px',
            margin: '5px',
            backgroundColor: '#e74c3c',
            color: '#fff',
            border: 'none',
            borderRadius: '5px',
            cursor: 'pointer'
          }}
        >
          Start Wrong (Red)
        </button>
        
        <button 
          onClick={handleReset}
          style={{
            padding: '10px 20px',
            margin: '5px',
            backgroundColor: '#95a5a6',
            color: '#fff',
            border: 'none',
            borderRadius: '5px',
            cursor: 'pointer'
          }}
        >
          Reset
        </button>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h3>State:</h3>
        <p>isVisible: {isVisible ? 'true' : 'false'}</p>
        <p>isCorrect: {isCorrect ? 'true' : 'false'}</p>
      </div>

      <div style={{ 
        border: '2px dashed #fff', 
        padding: '20px', 
        borderRadius: '8px',
        minHeight: '150px'
      }}>
        <h3>AutoProgressBar Container:</h3>
        {isVisible ? (
          <div>
            <p>✅ AutoProgressBar should appear below:</p>
            <AutoProgressBar
              isVisible={isVisible}
              isCorrect={isCorrect}
              onComplete={handleComplete}
              onSkip={handleSkip}
              duration={5000}
            />
          </div>
        ) : (
          <p>❌ AutoProgressBar is not visible (isVisible = false)</p>
        )}
      </div>

      <div style={{ marginTop: '20px' }}>
        <h3>Instructions:</h3>
        <ol>
          <li>Click "Start Correct" or "Start Wrong" to show the progress bar</li>
          <li>Check console logs for debug information</li>
          <li>Watch for the progress bar animation</li>
          <li>Test the "Tiếp tục" button and Space key</li>
          <li>Wait 5 seconds for auto completion</li>
        </ol>
      </div>

      <style jsx>{`
        .auto-progress-container {
          width: 100%;
          padding: 16px;
          border-radius: 8px;
          margin-top: 16px;
          backdrop-filter: blur(10px);
        }
        
        .auto-progress-correct {
          background-color: rgba(46, 204, 113, 0.1);
          border: 1px solid rgba(46, 204, 113, 0.2);
        }
        
        .auto-progress-wrong {
          background-color: rgba(231, 76, 60, 0.1);
          border: 1px solid rgba(231, 76, 60, 0.2);
        }
        
        .progress-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 8px;
        }
        
        .progress-text {
          color: #fff;
          font-size: 14px;
          font-weight: 500;
        }
        
        .skip-button {
          min-width: auto;
          padding: 4px 12px;
          font-size: 12px;
          border-radius: 16px;
          text-transform: none;
        }
        
        .progress-bar {
          height: 6px;
          border-radius: 3px;
          overflow: hidden;
        }
      `}</style>
    </div>
  );
};

export default AutoProgressDebug;
