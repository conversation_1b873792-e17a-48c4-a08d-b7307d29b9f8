"use client";

import React, { useState, useEffect } from 'react';

const IntervalTest = () => {
  const [count, setCount] = useState(0);
  const [isRunning, setIsRunning] = useState(false);
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    let interval;
    
    if (isRunning) {
      console.log('🚀 Starting interval test');
      
      interval = setInterval(() => {
        setCount(prev => {
          const newCount = prev + 1;
          console.log('📊 Count update:', prev, '->', newCount);
          return newCount;
        });
        
        setProgress(prev => {
          const newProgress = prev + 2; // 2% mỗi 100ms = 100% trong 5 giây
          console.log('📈 Progress update:', prev, '->', newProgress);
          return newProgress >= 100 ? 100 : newProgress;
        });
      }, 100);
      
      console.log('✅ Interval created:', interval);
    }

    return () => {
      if (interval) {
        console.log('🧹 Cleaning up interval:', interval);
        clearInterval(interval);
      }
    };
  }, [isRunning]);

  const startTest = () => {
    console.log('🔥 Starting test');
    setCount(0);
    setProgress(0);
    setIsRunning(true);
  };

  const stopTest = () => {
    console.log('⏹️ Stopping test');
    setIsRunning(false);
  };

  const resetTest = () => {
    console.log('🔄 Resetting test');
    setIsRunning(false);
    setCount(0);
    setProgress(0);
  };

  return (
    <div style={{ 
      padding: '20px', 
      fontFamily: 'Arial, sans-serif',
      backgroundColor: '#f0f0f0',
      minHeight: '100vh'
    }}>
      <h1>setInterval Test</h1>
      
      <div style={{ marginBottom: '20px' }}>
        <button 
          onClick={startTest}
          disabled={isRunning}
          style={{
            padding: '10px 20px',
            margin: '5px',
            backgroundColor: isRunning ? '#ccc' : '#2ecc71',
            color: '#fff',
            border: 'none',
            borderRadius: '5px',
            cursor: isRunning ? 'not-allowed' : 'pointer'
          }}
        >
          Start Test
        </button>
        
        <button 
          onClick={stopTest}
          disabled={!isRunning}
          style={{
            padding: '10px 20px',
            margin: '5px',
            backgroundColor: !isRunning ? '#ccc' : '#e74c3c',
            color: '#fff',
            border: 'none',
            borderRadius: '5px',
            cursor: !isRunning ? 'not-allowed' : 'pointer'
          }}
        >
          Stop Test
        </button>
        
        <button 
          onClick={resetTest}
          style={{
            padding: '10px 20px',
            margin: '5px',
            backgroundColor: '#95a5a6',
            color: '#fff',
            border: 'none',
            borderRadius: '5px',
            cursor: 'pointer'
          }}
        >
          Reset
        </button>
      </div>

      <div style={{ 
        padding: '20px', 
        backgroundColor: '#fff', 
        borderRadius: '8px',
        marginBottom: '20px'
      }}>
        <h3>Results:</h3>
        <p><strong>Count:</strong> {count}</p>
        <p><strong>Progress:</strong> {progress.toFixed(1)}%</p>
        <p><strong>Is Running:</strong> {isRunning ? 'Yes' : 'No'}</p>
        
        <div style={{
          width: '100%',
          height: '20px',
          backgroundColor: '#ddd',
          borderRadius: '10px',
          overflow: 'hidden',
          marginTop: '10px'
        }}>
          <div style={{
            width: `${progress}%`,
            height: '100%',
            backgroundColor: '#2ecc71',
            transition: 'width 0.1s ease'
          }}></div>
        </div>
      </div>

      <div style={{ 
        padding: '20px', 
        backgroundColor: '#fff', 
        borderRadius: '8px'
      }}>
        <h3>Expected Behavior:</h3>
        <ul>
          <li>Count should increase by 1 every 100ms</li>
          <li>Progress should increase by 2% every 100ms</li>
          <li>Progress should reach 100% in 5 seconds (50 intervals)</li>
          <li>Check console for detailed logs</li>
        </ul>
      </div>
    </div>
  );
};

export default IntervalTest;
