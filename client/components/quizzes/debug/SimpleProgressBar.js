"use client";

import React, { useState, useEffect } from 'react';

const SimpleProgressBar = ({ isVisible, isCorrect, onComplete, duration = 5000 }) => {
  const [progress, setProgress] = useState(0);
  const [timeLeft, setTimeLeft] = useState(Math.ceil(duration / 1000));

  console.log('🚀 SimpleProgressBar render:', { isVisible, isCorrect, progress, timeLeft });

  useEffect(() => {
    console.log('🔄 SimpleProgressBar useEffect triggered:', { isVisible, duration });
    
    if (!isVisible) {
      console.log('❌ Not visible, returning early');
      return;
    }

    console.log('✅ Setting up timers');
    
    // Reset states
    setProgress(0);
    setTimeLeft(Math.ceil(duration / 1000));

    // Progress timer - update every 100ms
    const progressTimer = setInterval(() => {
      setProgress(prev => {
        const increment = (100 / duration) * 100; // 100ms intervals
        const newProgress = prev + increment;
        console.log('📊 Progress:', prev.toFixed(1), '->', newProgress.toFixed(1));
        
        if (newProgress >= 100) {
          console.log('🏁 Progress completed, calling onComplete');
          onComplete && onComplete();
          return 100;
        }
        return newProgress;
      });
    }, 100);

    // Countdown timer - update every second
    const countdownTimer = setInterval(() => {
      setTimeLeft(prev => {
        const newTime = prev - 1;
        console.log('⏰ Time:', prev, '->', newTime);
        return newTime <= 0 ? 0 : newTime;
      });
    }, 1000);

    console.log('🔧 Timers created:', { progressTimer, countdownTimer });

    // Cleanup
    return () => {
      console.log('🧹 Cleaning up timers');
      clearInterval(progressTimer);
      clearInterval(countdownTimer);
    };
  }, [isVisible, duration]); // Removed onComplete from deps

  if (!isVisible) {
    console.log('❌ Not rendering (not visible)');
    return null;
  }

  const progressColor = isCorrect ? '#2ecc71' : '#e74c3c';
  const bgColor = isCorrect ? 'rgba(46, 204, 113, 0.1)' : 'rgba(231, 76, 60, 0.1)';

  console.log('✅ Rendering progress bar');

  return (
    <div style={{
      width: '100%',
      padding: '16px',
      backgroundColor: bgColor,
      borderRadius: '8px',
      marginTop: '16px',
      border: `2px solid ${progressColor}`,
      minHeight: '80px'
    }}>
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '8px',
        color: '#fff'
      }}>
        <div>
          Chuyển câu tiếp theo trong <strong>{timeLeft}</strong> giây
        </div>
        <button
          onClick={() => {
            console.log('⏭️ Skip button clicked');
            onComplete && onComplete();
          }}
          style={{
            padding: '4px 12px',
            backgroundColor: 'transparent',
            border: `1px solid ${progressColor}`,
            color: progressColor,
            borderRadius: '16px',
            cursor: 'pointer'
          }}
        >
          Tiếp tục
        </button>
      </div>
      
      <div style={{
        width: '100%',
        height: '8px',
        backgroundColor: 'rgba(255, 255, 255, 0.2)',
        borderRadius: '4px',
        overflow: 'hidden'
      }}>
        <div style={{
          width: `${progress}%`,
          height: '100%',
          backgroundColor: progressColor,
          transition: 'width 0.1s ease'
        }}></div>
      </div>
      
      <div style={{ 
        marginTop: '8px', 
        fontSize: '12px', 
        color: '#fff',
        opacity: 0.7
      }}>
        Progress: {progress.toFixed(1)}%
      </div>
    </div>
  );
};

export default SimpleProgressBar;
