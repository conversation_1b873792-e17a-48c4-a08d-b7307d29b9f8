"use client"

import React, { useEffect, useState, useCallback, useImperativeHandle, forwardRef } from "react";
import { useRouter } from "next/navigation"

import { styled, useTheme } from '@mui/material/styles';
import Paper from "@mui/material/Paper";
import Button from "@mui/material/Button";
import InputBase from "@mui/material/InputBase";
import Drawer from "@mui/material/Drawer";
import Stack from "@mui/material/Stack";
import FormControl from "@mui/material/FormControl";
import Select from "@mui/material/Select";
import OutlinedInput from "@mui/material/OutlinedInput";
import MenuItem from "@mui/material/MenuItem";
import Checkbox from "@mui/material/Checkbox";
import ListItemText from "@mui/material/ListItemText";
import Chip from "@mui/material/Chip";
import Radio from "@mui/material/Radio";
import RadioGroup from "@mui/material/RadioGroup";
import FormControlLabel from "@mui/material/FormControlLabel";
import FormLabel from "@mui/material/FormLabel";
import CircularProgress from "@mui/material/CircularProgress";
import IconButton from "@mui/material/IconButton";

import SearchIcon from '@mui/icons-material/Search';
import ChevronLeftIcon from '@mui/icons-material/ChevronLeft';

// third-party
import { useTranslations } from "next-intl";
import debounce from "lodash/debounce";
import toast from "react-hot-toast";

import QuestionCard from "../questions/QuestionCard";
import InfiniteScroll from "../InfiniteScroll";
import NoDataOverlay from "../NoDataOverlay";
import QuizMedia from "./QuizMedia";
import DialogQuizForm from "./DialogQuizForm";
import DialogDragDropCropImage from "../DragDropCropImage";
import AntSwitch from "@/components/ui/AntSwitch";

import { perPage } from "@/constant";
import useMathJax from '../../hooks/useMathJax';
import { fetchQuizzes, duplicateQuestions, removeQuiz } from "@/actions/quizAction";
import { fetchOptions } from "@/actions/onlyClientAction";

import { useConfirm } from "@/contexts/ConfirmContext";

const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
      minWidth: 150,
    },
  },
};

const Item = styled(Paper)(({ theme }) => ({
  backgroundColor: theme.palette.mode === 'dark' ? '#1A2027' : '#fff',
  ...theme.typography.body2,
  padding: theme.spacing(0),
  textAlign: 'center',
  color: theme.palette.text.secondary,
}));

const QuizSearchSidebar =  forwardRef(({ quiz, onDupQuestions }, ref) => {
  console.log('QuizSearchSidebar');
  const t = useTranslations("Common");
  const theme = useTheme();
  const router = useRouter();
  const confirmDelete = useConfirm();

  // drawer on/off
  const [openSidebar, setOpenSidebar] = useState(false);
  const [openDialogQuizFrom, setOpenDialogQuizFrom] = useState(false);
  const [openDialogCropImage, setOpenDialogCropImage] = useState(false);
  const [page, setPage] = useState(0);
  const [flag, setFlag] = useState(0);
  const [quizzes, setQuizzes] = useState([]);
  const [hasMore, setHasMore] = useState(false);
  const [quizSelected, setQuizSelected] = useState(null);
  const [displayType, setDisplayType] = useState(0);
  const [keyword, setKeyword] = useState('');
  const [filter, setFilter] = useState({
    'grades': [],
    'subjects': [],
  });
  const [adding, setAdding] = useState(false);
  const [firstOpen, setFirstOpen] = useState(false);
  const { data: gradeAndSubject } = fetchOptions("grades,subjects");

  useMathJax([quizSelected]);

  useImperativeHandle(ref, () => ({
    toggle: handleToggleSidebar,
  }));

  const handleToggleSidebar = () => {
    if (page == 0) {
      setPage(1);
    }
    setOpenSidebar(!openSidebar);
  };

  const fetchData = async () => {
    try {
      const { data } = await fetchQuizzes({
        page,
        limit: perPage,
        grades: filter.grades.join(','),
        subjects: filter.subjects.join(','),
        q: !firstOpen ? "" : keyword,
        personal: displayType == 1,
      });

      if (data) {
        setQuizzes((prev) => [...prev, ...data]);
        setFirstOpen(true);
        if (data.length < perPage) {
          setHasMore(false);
        } else {
          setHasMore(true);
        }
      }
    } catch (error) {
      console.error('Error fetching data:', error);
    }
  };

  const resetState = useCallback(() => {
    setQuizzes([]);
    setPage(1);
    setHasMore(false);
    setFlag(pre => pre + 1);
  }, [flag]);

  useEffect(() => {
    if (page > 0) fetchData();
  }, [page, flag]);

  const debouncedSearch = useCallback(
    debounce((value) => {
      setKeyword(value);
      resetState();
    }, 700),
    []
  );

  const handleSearch = useCallback((event) => {
    debouncedSearch(event.target.value);
  }, []);

  const handleChangeOptionFilter = useCallback((event, key) => {
    const {
      target: { value },
    } = event;

    setFilter(prevFilter => ({
      ...prevFilter,
      [key]: typeof value === 'string' ? value.split(',') : value,
    }));

    resetState();
  }, [filter]);

  const handleDeleteFilter = useCallback((key, value) => {
    setFilter((prevFilter) => {
      const newFilter = {
        ...prevFilter,
        [key]: prevFilter[key].filter((item) => item !== value),
      };

      return newFilter;
    });

    resetState();
  }, [filter]);

  const handleChangeDisplayType = useCallback((event) => {
    if (event.target.type === 'checkbox') {
      setDisplayType(event.target.checked ? 0 : 1);
    } else if (event.target.type === 'radio') {
      setDisplayType(event.target.value);
    }

    resetState();
  }, [displayType]);

  const addQuestions = useCallback(async (e, duplicateData, number = 1) => {
    try {
      const buttonRect = e.currentTarget.getBoundingClientRect();
      let boxElement = document.querySelector('#fly-box_last-qlist');
      let flyX, flyY;

      setAdding(true);

      const { data: newQues } = await duplicateQuestions(quiz.id, duplicateData);

      if (newQues) {
        onDupQuestions(newQues);

        if (boxElement) {
          const boxRect = boxElement.getBoundingClientRect();

          flyX = boxRect.left - buttonRect.left;
          flyY = boxRect.top - buttonRect.top;
        } else {
          // Mặc định đến chính giữa dưới màn hình nếu không tìm thấy box
          flyX = window.innerWidth / 2 - buttonRect.left - buttonRect.width / 2;
          flyY = window.innerHeight - buttonRect.top - buttonRect.height / 2;
        }

        const flyItem = document.createElement('div');
        flyItem.classList.add('fly-item');
        flyItem.innerHTML = number;
        document.body.appendChild(flyItem);

        // Thiết lập vị trí cho fly item
        flyItem.style.left = `${buttonRect.left}px`;
        flyItem.style.top = `${buttonRect.top}px`;

        flyItem.style.setProperty('--fly-x', `${flyX}px`);
        flyItem.style.setProperty('--fly-y', `${flyY}px`);
        flyItem.classList.add('send-to-box');

        setTimeout(() => {
          flyItem.remove();
          if (boxElement) {
            boxElement.classList.add('shake');
            setTimeout(() => {
              boxElement.classList.remove('shake');
            }, 500);
          }
        }, 1000);
      } else {
        throw new Error("Đã xảy ra lỗi. Không thể thêm câu hỏi");
      }
    } catch (error) {
      toast.error(error?.message || error.getMessage());
    } finally {
      setAdding(false);
    }
  }, []);

  const deleteQuiz = useCallback(async () => {
    const ans = await confirmDelete(
      "Xóa Quizz?",
      "Bạn có chắc chắn muốn xóa quiz này không?"
    );

    if (ans) {
      try {
        const res = await removeQuiz(quiz.id);

        if (res) {
          toast.success("Xóa thành công");
          router.push("/dashboard/my-library");
        }
      } catch (error) {
        console.error(error);
        toast.error("Đã xảy ra lỗi. Vui lòng thử lại");
      }
    }
  }, [quiz.id, confirmDelete, router]);

  const handleOpenDialogQuizFrom = useCallback(() => {
    setOpenDialogQuizFrom(true);
  }, []);

  const handleCloseDialogQuizFrom = useCallback(() => {
    setOpenDialogQuizFrom(false);
  }, []);

  const handleOpenDialogCropImage = useCallback(() => {
    setOpenDialogCropImage(true);
  }, []);

  const handleCloseDialogCropImage = useCallback(() => {
    setOpenDialogCropImage(false);
  }, []);

  return (
    <>
      <div className="d-flex justify-content-between align-items-center">
        <h4 className="py-2 px-1 fs-18 fw-medium">Tìm kiếm câu hỏi từ Thư viện 2048.vn</h4>
        <Stack direction="row" sx={{ alignItems: "center", gap: 1 }}>
          <button
            className="btn btn-sm btn-icon text-danger"
            onClick={deleteQuiz}
          >
            <i className="bi bi-trash3" />
          </button>
          <button
            className="btn btn-sm btn-icon text-dark"
            onClick={handleOpenDialogQuizFrom}
            aria-hidden={false}
          >
            <i className="bi bi-gear-fill"></i>
          </button>
        </Stack>
      </div>
      <Paper
        className="mb-2"
        component="form"
        sx={{
          p: '2px 4px',
          display: 'flex',
          alignItems: 'center',
          border: '1px solid #e5e5e5',
          width: '100%',
        }}
        onClick={handleToggleSidebar}
      >
          <InputBase
            sx={{ ml: 1, flex: 1 }}
            placeholder="Nhập tên chủ đề"
            inputProps={{ 'aria-label': 'Nhập tên chủ đề' }}
            defaultValue={quiz.title || ''}
          />
          <Button
            variant="outlined"
            size="small"
            color="secondary"
            type="button"
            sx={{ m: '5px' }}
            aria-label="search"
          >
            <SearchIcon /> Tìm kiếm
          </Button>
      </Paper>
      <Drawer
        anchor="right"
        onClose={handleToggleSidebar}
        open={openSidebar}
        PaperProps={{
          sx: {
            width: {
              xs: '100%',
              sm: '100%',
              md: '100%',
              lg: '85%',
              xl: '70%',
            },
          },
        }}
      >
        <div className="p-3 bg-white h-100">
          <div className="d-flex align-items-center mb-1">
            <IconButton className="me-3" onClick={handleToggleSidebar}>
              <ChevronLeftIcon />
            </IconButton>
            <Paper
              component="form"
              onSubmit={(e) => {
                e.preventDefault();
                debouncedSearch(keyword);
              }}
              sx={{
                p: '0',
                display: 'flex',
                alignItems: 'center',
                border: '1px solid #e5e5e5',
                width: '100%',
              }}
            >
                <InputBase
                  sx={{ ml: 1, flex: 1 }}
                  placeholder="Tìm kiếm từ hàng triệu câu hỏi"
                  inputProps={{ 'aria-label': 'Tìm kiếm từ hàng triệu câu hỏi' }}
                  defaultValue={keyword}
                  onChange={handleSearch}
                />
                <Button variant="contained" className="btn-primary2" size="small" type="button" sx={{ m: '5px' }} aria-label="search" onClick={() => debouncedSearch(keyword)}>
                  <SearchIcon /> Tìm kiếm
                </Button>
            </Paper>
          </div>
          <Stack className="d-flex d-md-none px-3 pt-3 pb-2" direction="row" spacing={1} sx={{ alignItems: "center" }}>
            <span>Chỉ hiển thị từ Thư viện của tôi</span>
            <AntSwitch checked={displayType == 0} onChange={handleChangeDisplayType} />
          </Stack>
          <Stack
            className="d-none d-md-flex mx-2 my-3"
            direction={{ xs: 'column', sm: 'row' }}
            spacing={{ xs: 1, sm: 2, md: 3 }}
            sx={{ alignItems: "center" }}
          >
            <Item className="border-0">Lọc theo:</Item>
            {gradeAndSubject && Object.keys(gradeAndSubject).map((key, index) => (
              <Item key={index}>
                <FormControl sx={{
                  minWidth: 130,
                  ...theme.typography.customInput,
                  margin: 0,
                  '.MuiInputBase-inputSizeSmall': {
                    paddingTop: '5px!important',
                    paddingBottom: '5px!important',
                  },
                }} size="small">
                  <Select
                    id="select-grade_id"
                    multiple
                    displayEmpty
                    value={filter[key]}
                    onChange={(event) => handleChangeOptionFilter(event, key)}
                    input={<OutlinedInput label="Tag" />}
                    renderValue={(selected) => {
                      return <span className="text-capitalize fw-light">{t(key)}</span>;
                    }}
                    MenuProps={MenuProps}
                    inputProps={{ 'aria-label': 'Without label' }}
                  >
                    {gradeAndSubject[key].map((option) => (
                      <MenuItem key={`g-${option.value}`} value={option.value}>
                        <Checkbox size="small" checked={filter[key].indexOf(option.value) > -1} />
                        <ListItemText primary={option.label} />
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Item>
            ))}
          </Stack>
          <div className="row height-cutom1">
            <div className="col-12 col-md-5 col-lg-4 mt-1 h-md-100 bg-hl">
              <div className="d-flex flex-column flex-shrink-0 h-100">
                <div className="mb-1">
                  {Object.keys(filter).map((key, index) => (
                    filter[key].length > 0 && filter[key].map((value, index) => {
                      const label = gradeAndSubject[key].find(item => item.value === value)?.label;
                      return (
                        <Chip key={index} size="small" sx={{ margin: '5px', borderRadius: '5px', '& .MuiChip-deleteIcon': { fontSize: '14px' } }} label={label} onDelete={() => handleDeleteFilter(key, value)} />
                      );
                    })
                  ))}
                </div>
                <div className="px-1 d-none d-md-block">
                  <FormControl className="w-100 mb-3 px-3 bg-white" sx={{ border: '1px solid #eee', padding: '20px 20px 10px', borderRadius: '10px' }}>
                    <FormLabel id="controlled-radio-buttons-group">Hiển thị nội dung từ:</FormLabel>
                    <RadioGroup
                      aria-labelledby="controlled-radio-buttons-group"
                      name="display-type"
                      value={displayType}
                      onChange={handleChangeDisplayType}
                    >
                      <FormControlLabel value="0" control={<Radio />} label="Cộng đồng" />
                      <FormControlLabel value="1" control={<Radio />} label="Thư viện của tôi" />
                    </RadioGroup>
                  </FormControl>
                </div>
                <InfiniteScroll
                  loader={<CircularProgress />}
                  className="px-1 overflow-auto media-group media-md-group"
                  fetchMore={() => setPage((prev) => prev + 1)}
                  hasMore={hasMore}
                  endMessage={(quizzes.length == 0 || quizzes.length == 1 && quizzes[0].id === quiz.id) ? <NoDataOverlay className="m-auto" message="Không tìm thấy quiz" /> : ''}
                >
                  {quizzes.map((quizItem) => {
                    if (quizItem.id === quiz.id) {
                      return null;
                    }
                    return (
                      <QuizMedia
                        key={quizItem.id}
                        quiz={quizItem}
                        bannerWidth="150px"
                        bannerHeight="150px"
                        active={quizSelected && quizSelected.id === quizItem.id}
                        onQuizSelected={(item) => setQuizSelected(item)}
                      />
                    );
                  })}
                </InfiniteScroll>
              </div>
            </div>
            <div className="col-12 col-md-7 col-lg-8 mt-1 h-100" style={{ backgroundColor: '#f2f2f2' }}>
              <div className="d-flex flex-column flex-shrink-0 h-100 pb-3 overflow-auto">
                { quizSelected ? (
                  quizSelected.questions && quizSelected.questions.length ? (
                    <>
                      <div className="bg-white border rounded mt-3 p-3 d-flex justify-content-between align-items-center gap-3">
                        <div className="d-flex align-items-baseline gap-2">
                          <i className="bi bi-calendar-check text-primary"></i>
                          <div>
                            <h4 className="fs-18 mb-1" style={{
                              wordBreak: "break-word",
                              overflowWrap: "break-word"
                            }}>
                              { quizSelected.title }
                            </h4>
                            <p className="text-muted small mb-0">{ quizSelected.questions.length } câu hỏi</p>
                          </div>
                        </div>
                        <button
                            onClick={(e) => addQuestions(e, {quiz_id: quizSelected.id}, quizSelected.questions.length)}
                            type="button"
                            className="btn btn-primary2 btn-sm flex-shrink-0"
                            disabled={adding}
                          >
                            <i className="bi bi-plus-circle-fill me-2"></i>
                            {`Thêm tất cả (${quizSelected.questions.length})`}
                          </button>
                      </div>
                      {quizSelected.questions.map((question, index) => (
                        <div key={question.id} className="bg-white border rounded mt-3 px-1">
                          <div className="d-flex flex-wrap align-items-center justify-content-between px-2 pb-1 pt-2">
                            <span className="fw-normal fs-14 my-1">
                              <i className="bi bi-check2 me-2"></i>
                              { t(question.type) }
                            </span>
                            <div className="my-1">
                              <button
                                onClick={(e) => addQuestions(e, {question_id: question.id})}
                                type="button"
                                className="btn btn-outline-primary2 btn-sm d-block"
                                style={{ padding: '3px 10px' }}
                                disabled={adding}
                              >
                                <i className="bi bi-plus-circle-fill me-2"></i>
                                Thêm câu hỏi
                              </button>
                            </div>
                          </div>
                          <QuestionCard question={question} showAnswer={true} />
                        </div>
                      ))}
                    </>
                  ) : <NoDataOverlay />
                ) : (
                  <p className="fs-20 text-center py-5 text-black-50">Chọn một quiz để xem trước ở đây.</p>
                ) }
              </div>
            </div>
          </div>
        </div>
      </Drawer>
      <DialogQuizForm
        quiz={quiz}
        open={openDialogQuizFrom}
        onClose={handleCloseDialogQuizFrom}
      />
      {openDialogCropImage && (
        <DialogDragDropCropImage
          open={openDialogCropImage}
          onClose={handleCloseDialogCropImage}
          imgSrc={quiz.banner}
          setImgSrc={(value) => {
            if (value) {
              if (isBase64(value)) {
                editQuiz({ banner_base64: value }, handleCloseDialogCropImage);
              } else {
                handleCloseDialogCropImage();
              }
            } else {
              editQuiz({ remove_banner: 1 });
            }
          }}
        />
      )}
    </>
  );
});

QuizSearchSidebar.displayName = QuizSearchSidebar;

export default React.memo(QuizSearchSidebar);
