import React from "react";
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import LinearProgress from '@mui/material/LinearProgress';
import Image from 'next/image';

const RejoinLoadingDialog = React.memo(({ open, onClose }) => {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      PaperProps={{
        sx: {
          top: "5%",
          margin: 0,
          position: "absolute",
          backgroundColor: "transparent",
          boxShadow: "none",
        },
      }}
    >
      <DialogContent className="text-white text-center fs-18">
        <Image
          className="mb-3"
          src="/assets/images/rejoin_game.png"
          alt="Rejoin game"
          width={350}
          height={350}
        />
        <LinearProgress color="secondary" />
      </DialogContent>
    </Dialog>
  );
});

RejoinLoadingDialog.displayName = "RejoinLoadingDialog";

export default RejoinLoadingDialog;
