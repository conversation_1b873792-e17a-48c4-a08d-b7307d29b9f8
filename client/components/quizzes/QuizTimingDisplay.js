import React from 'react';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import Chip from '@mui/material/Chip';
import Divider from '@mui/material/Divider';
import Typography from '@mui/material/Typography';

import AccessTimeIcon from '@mui/icons-material/AccessTime';
import SpeedIcon from '@mui/icons-material/Speed';
import AvTimerIcon from '@mui/icons-material/AvTimer';
import CheckIcon from '@mui/icons-material/Check';
import CancelIcon from '@mui/icons-material/Cancel';
import HtmlContent from '../HtmlContent';

import { formatDuration } from "@/utils/helpers";

/**
 * Component to display quiz timing information
 */
const QuizTimingDisplay = ({ quizResult, questions = [] }) => {
  // Parse data_log to get question timing information
  const getQuestionTimingData = () => {
    if (!quizResult?.data_log) return [];

    let dataLog = quizResult.data_log;
    if (typeof dataLog === 'string') {
      try {
        dataLog = JSON.parse(dataLog);
      } catch (e) {
        console.warn("Error parsing data_log:", e);
        return [];
      }
    }

    return Object.entries(dataLog).map(([questionId, questionData]) => ({
      questionId: parseInt(questionId),
      duration: questionData.question_duration || 0,
      isCorrect: questionData.isCorrect,
      answeredAt: questionData.answered_at
    }));
  };

  const questionTimingData = getQuestionTimingData();
  const totalDuration = quizResult?.total_duration || 0;
  const averageDuration = questionTimingData.length > 0
    ? Math.round(totalDuration / questionTimingData.length)
    : 0;

  // Calculate fastest and slowest questions
  const sortedByDuration = [...questionTimingData].sort((a, b) => a.duration - b.duration);
  const fastestQuestion = sortedByDuration[0];
  const slowestQuestion = sortedByDuration[sortedByDuration.length - 1];

  if (!quizResult || totalDuration === 0) {
    return null;
  }

  return (
    <Card sx={{ mt: 2 }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <AccessTimeIcon sx={{ mr: 1, color: 'primary.main' }} />
          <Typography variant="h6" className="mb-0" sx={{ fontWeight: 'bold' }}>
            Thời gian làm bài
          </Typography>
        </Box>

        {/* Overall timing stats */}
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, mb: 3 }}>
          <Chip
            icon={<AvTimerIcon />}
            label={`Tổng thời gian: ${formatDuration(totalDuration)}`}
            color="primary"
            variant="outlined"
          />
          <Chip
            icon={<SpeedIcon />}
            label={`Trung bình: ${formatDuration(averageDuration)}/câu`}
            color="secondary"
            variant="outlined"
          />
          {fastestQuestion && (
            <Chip
              icon={<SpeedIcon />}
              label={`Nhanh nhất: ${formatDuration(fastestQuestion.duration)}`}
              color="success"
              variant="outlined"
            />
          )}
          {slowestQuestion && (
            <Chip
              icon={<SpeedIcon />}
              label={`Chậm nhất: ${formatDuration(slowestQuestion.duration)}`}
              color="warning"
              variant="outlined"
            />
          )}
        </Box>

        <Divider sx={{ my: 2 }} />

        {/* Individual question timing */}
        <Typography variant="subtitle2" sx={{ mb: 2, fontWeight: 'bold' }}>
          Thời gian từng câu hỏi:
        </Typography>

        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
          {questionTimingData.map((questionData, index) => {
            const question = questions.find(q => q.id === questionData.questionId);
            const questionNumber = question?.index + 1 || index + 1;

            return (
              <Box
                key={questionData.questionId}
                sx={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  p: 1,
                  borderRadius: 1,
                  backgroundColor: questionData.isCorrect ? 'success.light' : 'error.light',
                  color: questionData.isCorrect ? 'success.contrastText' : 'error.contrastText',
                  opacity: 0.8
                }}
              >
                <Typography variant="body2">
                  Câu {questionNumber}
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                    {formatDuration(questionData.duration)}
                  </Typography>
                  <Chip
                    size="small"
                    label={questionData.isCorrect ? 'Đúng' : 'Sai'}
                    color={questionData.isCorrect ? 'success' : 'error'}
                    sx={{ minWidth: 50 }}
                  />
                </Box>
              </Box>
            );
          })}
        </Box>

        {/* Quiz completion time */}
        {quizResult.completed_at && (
          <Box sx={{ mt: 2, pt: 2, borderTop: 1, borderColor: 'divider' }}>
            <Typography variant="body2" color="text.secondary">
              Hoàn thành lúc: {new Date(quizResult.completed_at).toLocaleString('vi-VN')}
            </Typography>
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

export default QuizTimingDisplay;
