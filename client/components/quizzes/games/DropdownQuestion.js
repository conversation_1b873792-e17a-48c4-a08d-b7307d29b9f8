import React, { useState, useCallback, useRef, useEffect } from 'react';
import HtmlContent from '@/components/HtmlContent';

const DropdownQuestion = ({ questionData, answer, onAnswer, isSubmit = false, ...props }) => {
  const contentRef = useRef(null);
  const { content = '', options = [], answer: correctAnswer = [] } = questionData;

  const handleSelectChange = useCallback((targetId, selectedOptionId) => {
    console.log('==== handleSelectChange333', targetId, selectedOptionId);
    if (isSubmit) return;

    // const newAnswer = [...(answer || [])];

    // Đảm bảo answer luôn là array
    const currentAnswer = Array.isArray(answer) ? answer : [];
    const newAnswer = [...currentAnswer];

    console.log('==== handleSelectChange444', targetId, selectedOptionId, answer, newAnswer);

    const existingIndex = newAnswer.findIndex(a => a && a.targetId && String(a.targetId) === String(targetId));

    console.log('==== handleSelectChange555', targetId, selectedOptionId, existingIndex, newAnswer);

    if (!selectedOptionId || selectedOptionId === '') {
      console.log('==== handleSelectChange666', targetId, selectedOptionId, existingIndex);
      // Xóa đáp án nếu chọn placeholder
      if (existingIndex >= 0) {
        newAnswer.splice(existingIndex, 1);
      }
    } else {
      // Thêm hoặc cập nhật đáp án
      const answerData = {
        targetId: String(targetId),
        optionId: [String(selectedOptionId)],
        type: 'option'
      };

      if (existingIndex >= 0) {
        newAnswer[existingIndex] = answerData;
      } else {
        newAnswer.push(answerData);
      }
    }

    console.log('==== handleSelectChange final:', newAnswer);
    onAnswer(newAnswer);
  }, [answer, isSubmit, onAnswer]);

  const getAnswerForTarget = useCallback((targetId) => {
    console.log('=== getAnswerForTarget called:', { targetId, answer });
    if (!Array.isArray(answer)) return null;

    const userAnswer = answer?.find(a => {
      const match = a && a.targetId && String(a.targetId) === String(targetId);
      console.log('=== comparing:', { aTargetId: a?.targetId, targetId, match });
      return match;
    });

    console.log('=== getAnswerForTarget result:', { targetId, userAnswer });

    if (!userAnswer || !userAnswer.optionId || userAnswer.optionId.length === 0) {
      return null;
    }

    return String(userAnswer.optionId[0]);
  }, [answer]);

  const getCorrectAnswerForTarget = useCallback((targetId) => {
    if (!Array.isArray(correctAnswer)) return null;

    const correctAnswerData = correctAnswer?.find(a => a && a.targetId && String(a.targetId) === String(targetId));

    if (!correctAnswerData || !correctAnswerData.optionId || correctAnswerData.optionId.length === 0) {
      return null;
    }

    return String(correctAnswerData.optionId[0]);
  }, [correctAnswer]);

  const isAnswerCorrect = useCallback((targetId) => {
    if (!isSubmit) return null;

    const userAnswerId = getAnswerForTarget(targetId);
    const correctAnswerId = getCorrectAnswerForTarget(targetId);

    if (!userAnswerId || !correctAnswerId) return false;
    return userAnswerId === correctAnswerId;
  }, [isSubmit, getAnswerForTarget, getCorrectAnswerForTarget]);

  const renderDropdownHTML = useCallback((targetId) => {
    const selectedValue = getAnswerForTarget(targetId) || '';
    const isCorrect = isAnswerCorrect(targetId);

    let selectClass = 'form-select form-select-sm dropdown-answer';

    if (selectedValue && selectedValue !== '') {
      selectClass += ' option-filled';
    }

    if (isSubmit) {
      if (isCorrect === true) {
        selectClass += ' is-valid bg-success text-white';
      } else if (isCorrect === false) {
        selectClass += ' is-invalid bg-danger text-white';
      }
    }

    const optionsHTML = options.map(option =>
      `<option value="${option.id}" ${selectedValue === option.id ? 'selected' : ''}>${option.content}</option>`
    ).join('');

    return `
      <select
        id="dropdown-${targetId}"
        class="${selectClass}"
        data-target-id="${targetId}"
        ${isSubmit ? 'disabled' : ''}
        style="min-width: 120px; display: inline-block; margin: 0 4px;"
      >
        <option value="" style="color: #808080">Chọn đáp án</option>
        ${optionsHTML}
      </select>
    `;
  }, [options, getAnswerForTarget, isAnswerCorrect, isSubmit]);

  const renderContent = useCallback(() => {
    if (!content) return '';

    const parser = new DOMParser();
    const doc = parser.parseFromString(content, 'text/html');
    const spans = Array.from(doc.querySelectorAll('span.flag-el'));

    spans.forEach(span => {
      const targetId = span.id;
      if (targetId) {
        // Thay thế nội dung span bằng dropdown HTML
        span.innerHTML = renderDropdownHTML(targetId);
        span.className = 'flag-el dropdown-container';
      }
    });

    return doc.body.innerHTML;
  }, [content, renderDropdownHTML]);

  // Effect để re-render khi dependencies thay đổi
  useEffect(() => {
    console.log('DropdownQuestion Debug33 re-render:');
    if (!contentRef.current) return;

    // Re-render content when answer, isSubmit, or options change
    contentRef.current.innerHTML = renderContent();
  }, [renderContent, answer, isSubmit]);

  // Effect để attach event listeners sau khi render
  useEffect(() => {
    console.log('=== DropdownQuestion attach event listeners 888:');
    if (!contentRef.current) return;

    const handleSelectChangeEvent = (e) => {
      console.log('=== DropdownQuestion handleSelectChangeEvent999:');
      const select = e.target;
      if (select.classList.contains('dropdown-answer')) {
        const targetId = select.getAttribute('data-target-id');
        if (targetId) {
          console.log('=== DropdownQuestion handleSelectChangeEvent1000000:', targetId, select.value);
          handleSelectChange(targetId, select.value);
        }
      }
    };

    // Attach event listener to container
    contentRef.current.addEventListener('change', handleSelectChangeEvent);

    // Cleanup function
    return () => {
      if (contentRef.current) {
        console.log('=== DropdownQuestion cleanup event listeners 7777777:');
        contentRef.current.removeEventListener('change', handleSelectChangeEvent);
      }
    };
  }, [handleSelectChange]);

  // Debug logging (remove in production)
  useEffect(() => {
    console.log('DropdownQuestion Debug44:', {
      answer,
      correctAnswer,
      options: options.length,
      isSubmit
    });
  }, [answer, correctAnswer, options.length, isSubmit]);

  return (
    <div className={`question-box2 dropdown-question q-mode-v1 ${isSubmit ? '' : 'slideDown'}`}>
      <div className="py-3 px-md-3 mt-3 h-100">
        <div className="q-content">
          <div className="q-content-text">
            <div className="q-label absolute px-3 py-1 border flex flex-row align-middle">
              <p className="mb-0">
                <span data-cy="current-question-number" role="current-question-number">{props.currentQuestionIndex+1}</span>/
                <span data-cy="total-question-number" role="total-question-number">{props.totalQuestion}</span>
              </p>
            </div>
            <div
              ref={contentRef}
              dangerouslySetInnerHTML={{ __html: renderContent() }}
            />
          </div>
        </div>

        {isSubmit && (
          <div className="mt-3">
            <div className="d-flex align-items-center">
              <span className="badge bg-success me-2">
                <i className="bi bi-check-circle me-1"></i>
                Đúng
              </span>
              <span className="badge bg-danger">
                <i className="bi bi-x-circle me-1"></i>
                Sai
              </span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default DropdownQuestion;
