import React, { useState, useCallback } from 'react';

const DragAndDropQuestion = ({ questionData, answer, onAnswer, isSubmit = false, ...props }) => {
  const [draggedOption, setDraggedOption] = useState(null);
  const [selectedOption, setSelectedOption] = useState(null); // Thêm state cho click mode

  const { content = '', options = [], answer: correctAnswer = [] } = questionData;

  const handleDragStart = (e, option) => {
    if (isSubmit) return;
    setDraggedOption(option);
    e.dataTransfer.effectAllowed = 'move';
    hideDragDropPlayerEducation();
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  const handleDrop = (e, targetId) => {
    e.preventDefault();
    if (isSubmit || !draggedOption) return;

    const newAnswer = [...(answer || [])];
    const existingIndex = newAnswer.findIndex(a => a.targetId === targetId);

    if (existingIndex >= 0) {
      // Thay thế đáp án hiện tại
      newAnswer[existingIndex] = {
        targetId: targetId,
        optionId: [draggedOption.id],
        type: 'option'
      };
    } else {
      // Thêm đáp án mới
      newAnswer.push({
        targetId: targetId,
        optionId: [draggedOption.id],
        type: 'option'
      });
    }

    onAnswer(newAnswer);
    setDraggedOption(null);
  };

  // Logic click: chọn option trước, sau đó click vào chỗ trống
  const handleOptionClick = (option) => {
    if (isSubmit) return;

    if (selectedOption?.id === option.id) {
      // Bỏ chọn nếu click vào option đã chọn
      setSelectedOption(null);
    } else {
      // Chọn option mới
      setSelectedOption(option);
    }
  };

  const handleBlankClick = (targetId) => {
    if (isSubmit || !selectedOption) return;

    const newAnswer = [...(answer || [])];
    const existingIndex = newAnswer.findIndex(a => a.targetId === targetId);

    if (existingIndex >= 0) {
      // Thay thế đáp án hiện tại
      newAnswer[existingIndex] = {
        targetId: targetId,
        optionId: [selectedOption.id],
        type: 'option'
      };
    } else {
      // Thêm đáp án mới
      newAnswer.push({
        targetId: targetId,
        optionId: [selectedOption.id],
        type: 'option'
      });
    }

    onAnswer(newAnswer);
    setSelectedOption(null); // Reset sau khi điền
  };

  const getAnswerForTarget = (targetId) => {
    const userAnswer = answer?.find(a => a.targetId === targetId);
    if (!userAnswer) return null;

    return options.find(opt => opt.id === userAnswer.optionId[0]);
  };

  const getCorrectAnswerForTarget = (targetId) => {
    const correctAnswerData = correctAnswer?.find(a => a.targetId === targetId);
    if (!correctAnswerData) return null;

    return options.find(opt => opt.id === correctAnswerData.optionId[0]);
  };

  const isAnswerCorrect = (targetId) => {
    if (!isSubmit) return null;

    const userAnswer = getAnswerForTarget(targetId);
    const correctAnswerData = getCorrectAnswerForTarget(targetId);

    if (!userAnswer || !correctAnswerData) return false;
    return userAnswer.id === correctAnswerData.id;
  };

  const isOptionUsedCorrectly = (optionId) => {
    if (!isSubmit) return null;

    // Tìm tất cả các chỗ mà option này được sử dụng
    const usedTargets = answer?.filter(a => a.optionId[0] === optionId) || [];

    // Kiểm tra xem có ít nhất một chỗ sử dụng đúng không
    return usedTargets.some(userAnswer => {
      const correctAnswerData = correctAnswer?.find(c => c.targetId === userAnswer.targetId);
      return correctAnswerData && correctAnswerData.optionId[0] === optionId;
    });
  };

  const hideDragDropPlayerEducation = () => {
    const dragDropPlayerEducation = document.querySelector('.drag-drop-player-education');
    if (dragDropPlayerEducation) {
      dragDropPlayerEducation.remove();
    }
  };

  const renderContent = () => {
    const parser = new DOMParser();
    const doc = parser.parseFromString(content, 'text/html');
    const spans = Array.from(doc.querySelectorAll('span.flag-el'));

    spans.forEach(span => {
      const targetId = span.id;
      const userAnswer = getAnswerForTarget(targetId);
      const isCorrect = isAnswerCorrect(targetId);

      if (userAnswer) {
        let className = 'blank-filled';
        if (isSubmit) {
          className += isCorrect ? ' correct-answer' : ' wrong-answer';
        }
        span.innerHTML = `<span class="${className}">${userAnswer.content}</span>`;
      } else {
        span.innerHTML = '<span class="blank-empty"> ..... </span>';
      }

      span.setAttribute('data-target-id', targetId);
      span.className = 'flag-el drop-zone';

      // Thêm class cho drop-zone khi submit
      if (isSubmit && userAnswer) {
        span.className += isCorrect ? ' drop-zone-correct' : ' drop-zone-wrong';
      }

      // Thêm cursor pointer cho click mode
      if (!isSubmit) {
        span.style.cursor = 'pointer';
      }
    });

    return doc.body.innerHTML;
  };

  return (
    <div
      className={`question-box2 blank-question drag-drop-question q-mode-v1 ${isSubmit ? '' : 'slideDown'}`}
      onClick={hideDragDropPlayerEducation}
    >
      <div className="py-3 px-md-3 h-50">
        <div className="q-content">
          <div className="q-content-text">
            <div className="q-label absolute px-3 py-1 border flex flex-row align-middle">
              <p className="mb-0">
                <span data-cy="current-question-number" role="current-question-number">{props.currentQuestionIndex+1}</span>/
                <span data-cy="total-question-number" role="total-question-number">{props.totalQuestion}</span>
              </p>
            </div>
            <div
              dangerouslySetInnerHTML={{ __html: renderContent() }}
              onDragOver={handleDragOver}
              onDrop={(e) => {
                const targetId = e.target.closest('.flag-el')?.getAttribute('data-target-id');
                if (targetId) handleDrop(e, targetId);
              }}
              onClick={(e) => {
                // Xử lý click vào chỗ trống
                const targetId = e.target.closest('.flag-el')?.getAttribute('data-target-id');
                if (targetId) handleBlankClick(targetId);
              }}
            />
          </div>
        </div>
      </div>

      <div className="q-answer text-center py-3 px-md-3 h-50">
        <div className="drag-drop-player-education">
          <img src="https://cf.quizizz.com/join/img/education/close_hand.png" alt="grab_handle"/>
        </div>
        <div
          className="dragdrop-options-grid position-relative d-flex flex-column gap-2 rounded-lg blur-20 text-ds-light-500">
          <div className="game-note position-sticky text-xl font-bold w-100">
            Kéo các ô này và thả chúng vào đúng chỗ trống ở trên
          </div>
          <div className="d-flex flex-grow-1 justify-content-center overflow-auto">
            <div className="d-flex flex-wrap gap-4 justify-content-center align-items-start my-auto">
              {options.map((option, index) => {
                   const isUsedCorrectly = isOptionUsedCorrectly(option.id);
                   let optionClass = `option-placeholder rounded-lg ${selectedOption?.id === option.id ? 'selected' : ''}`;

                   if (isSubmit && isUsedCorrectly !== null) {
                     optionClass += isUsedCorrectly ? ' option-correct' : ' option-wrong';
                   }

                   return (
                      <div
                         key={option.id}
                         className={optionClass}
                         data-cy={`dnd-option-text-${option.content}`}
                         draggable={!isSubmit}
                         onDragStart={(e) => handleDragStart(e, option)}
                         onClick={() => handleOptionClick(option)}
                      >
                        <div
                           className="drag-option position-relative d-flex align-items-center font-bold text-xl rounded-lg grabbable"
                           draggable="true">
                          <div className="dnd-option-icon">
                            <i className="bi bi-grip-vertical icon-fas-grip-dots-vertical text-ds-dark-200"></i>
                          </div>
                          <div className="w-100 h-100" data-cy="read-aloud-container">
                            <div id="optionText" className="resizeable-text">
                              <div className="text-container w-100">
                                <div className="resizeable gap-x-2 dnd-option-text text-ds-light-500">
                                  {option.content}
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                   )
                 }
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DragAndDropQuestion;
