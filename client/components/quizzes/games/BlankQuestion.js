import React, { useState, useCallback, useRef, useEffect } from 'react';

const BlankQuestion = ({ questionData, answer, onAnswer, isSubmit = false, ...props }) => {
  const [activeInput, setActiveInput] = useState(null); // Track input đang active
  const [inputValues, setInputValues] = useState({}); // Lưa tất cả input values
  const [hasAutoFocused, setHasAutoFocused] = useState(false); // Track if we've auto-focused
  const contentRef = useRef(null);

  const { content = '', options = [], answer: correctAnswer = [] } = questionData;
  const MAX_INPUT_LENGTH = 100;

  // Autofocus first blank field on component mount
  useEffect(() => {
    if (isSubmit || hasAutoFocused) return;

    const parser = new DOMParser();
    const doc = parser.parseFromString(content, 'text/html');
    const spans = Array.from(doc.querySelectorAll('span.flag-el'));

    // Find first blank that doesn't have an answer
    const firstEmptyBlank = spans.find(span => {
      const targetId = span.id;
      return !answer?.find(a => a.targetId === targetId);
    });

    if (firstEmptyBlank) {
      const targetId = firstEmptyBlank.id;
      setActiveInput(targetId);
      setInputValues(prev => ({
        ...prev,
        [targetId]: ''
      }));
      setHasAutoFocused(true);
    }
  }, [content, answer, isSubmit, hasAutoFocused]);

  const handleBlankClick = (targetId) => {
    if (isSubmit) return;

    // Chế độ tự nhập - hiển thị input
    const currentAnswer = getAnswerForTarget(targetId);
    const currentValue = currentAnswer?.text || '';

    setInputValues(prev => ({
      ...prev,
      [targetId]: currentValue
    }));
    setActiveInput(targetId);
  };

  const handleInputSubmit = (targetId) => {
    const inputValue = inputValues[targetId] || '';
    const trimmedValue = inputValue.trim();

    if (!trimmedValue) {
      // Xóa đáp án nếu input rỗng
      const newAnswer = (answer || []).filter(a => a.targetId !== targetId);
      onAnswer(newAnswer);
    } else {
      // Lưu đáp án tự nhập
      const newAnswer = [...(answer || [])];
      const existingIndex = newAnswer.findIndex(a => a.targetId === targetId);

      if (existingIndex >= 0) {
        newAnswer[existingIndex] = {
          targetId: targetId,
          text: trimmedValue,
          type: 'input'
        };
      } else {
        newAnswer.push({
          targetId: targetId,
          text: trimmedValue,
          type: 'input'
        });
      }

      onAnswer(newAnswer);
    }

    // Clear input state
    setInputValues(prev => {
      const newValues = { ...prev };
      delete newValues[targetId];
      return newValues;
    });

    // Find next empty blank field and autofocus
    const parser = new DOMParser();
    const doc = parser.parseFromString(content, 'text/html');
    const spans = Array.from(doc.querySelectorAll('span.flag-el'));

    // Get updated answer list (including the one we just submitted)
    const updatedAnswer = trimmedValue ?
      [...(answer || []).filter(a => a.targetId !== targetId), {
        targetId: targetId,
        text: trimmedValue,
        type: 'input'
      }] :
      (answer || []).filter(a => a.targetId !== targetId);

    // Find next blank that doesn't have an answer
    const nextEmptyBlank = spans.find(span => {
      const spanTargetId = span.id;
      return spanTargetId !== targetId && !updatedAnswer.find(a => a.targetId === spanTargetId);
    });

    if (nextEmptyBlank) {
      const nextTargetId = nextEmptyBlank.id;
      setActiveInput(nextTargetId);
      setInputValues(prev => ({
        ...prev,
        [nextTargetId]: ''
      }));
      setHasAutoFocused(true);
    } else {
      setActiveInput(null);
    }
  };

  const handleInputCancel = (targetId) => {
    setInputValues(prev => {
      const newValues = { ...prev };
      delete newValues[targetId];
      return newValues;
    });
    setActiveInput(null);
  };

  const getAnswerForTarget = (targetId) => {
    const userAnswer = answer?.find(a => a.targetId === targetId);
    if (!userAnswer) return null;

    return { text: userAnswer.text, type: 'input' };
  };

  const isAnswerCorrect = (targetId) => {
    if (!isSubmit) return false;

    const userAnswer = getAnswerForTarget(targetId);
    if (!userAnswer) return false;

    const correctAnswerObj = correctAnswer?.find(a => a.targetId === targetId);
    if (!correctAnswerObj) return false;

    // Get correct options for this target
    const correctOptions = (correctAnswerObj.optionId || []).map(optionId =>
      options.find(opt => opt.id === optionId)?.content
    ).filter(Boolean);

    // Check if user's text matches any correct option (case insensitive)
    return correctOptions.some(correctText =>
      correctText.toLowerCase().trim() === userAnswer.text.toLowerCase().trim()
    );
  };

  // Get correct answers display text
  const getCorrectAnswersDisplay = () => {
    if (!isSubmit || !correctAnswer) return '';

    const correctTexts = correctAnswer.map(answerObj => {
      const correctOptions = (answerObj.optionId || []).map(optionId =>
        options.find(opt => opt.id === optionId)?.content
      ).filter(Boolean);

      return correctOptions.join(' | ');
    }).filter(Boolean);

    return correctTexts.join('\n');
  };

  const renderContent = () => {
    const parser = new DOMParser();
    const doc = parser.parseFromString(content, 'text/html');
    const spans = Array.from(doc.querySelectorAll('span.flag-el'));

    spans.forEach(span => {
      const targetId = span.id;
      const userAnswer = getAnswerForTarget(targetId);
      const isCorrect = isAnswerCorrect(targetId);

      if (activeInput === targetId) {
        // Hiển thị input field - KHÔNG dùng dangerouslySetInnerHTML
        span.innerHTML = '<span class="blank-input-placeholder">...</span>';
      } else if (userAnswer) {
        // Hiển thị đáp án đã điền
        let className = userAnswer.type === 'input' ? 'blank-filled-input' : 'blank-filled';
        let iconHtml = '';

        if (isSubmit) {
          className += isCorrect ? ' blank-correct' : ' blank-incorrect';
          iconHtml = isCorrect ?
            ' <i class="bi bi-check-lg text-white ms-2"></i>' :
            ' <i class="bi bi-x-lg text-white ms-2"></i>';
        }
        span.innerHTML = `<span class="${className}">${userAnswer.text}${iconHtml}</span>`;
      } else {
        // Chỗ trống
        span.innerHTML = '<span class="blank-empty"> ..... </span>';
      }

      span.setAttribute('data-target-id', targetId);
      span.className = 'flag-el drop-zone';

      // Thêm cursor pointer cho click mode
      if (!isSubmit) {
        span.style.cursor = 'pointer';
      }
    });

    return doc.body.innerHTML;
  };

  // Check if all blank fields are filled
  const areAllBlanksFilled = () => {
    const parser = new DOMParser();
    const doc = parser.parseFromString(content, 'text/html');
    const spans = Array.from(doc.querySelectorAll('span.flag-el'));

    return spans.every(span => {
      const targetId = span.id;
      return answer?.find(a => a.targetId === targetId);
    });
  };

  // Render input fields riêng biệt, không qua dangerouslySetInnerHTML
  const renderInputOverlay = () => {
    if (isSubmit || !activeInput || !contentRef.current) return null;

    const targetSpan = contentRef.current.querySelector(`[data-target-id="${activeInput}"]`);
    if (!targetSpan) return null;

    const rect = targetSpan.getBoundingClientRect();
    const containerRect = contentRef.current.getBoundingClientRect();

    return (
      <input
        type="text"
        id={`input-${activeInput}`}
        className="blank-input-overlay"
        value={inputValues[activeInput] || ''}
        placeholder="Gõ câu trả lời của bạn..."
        maxLength={MAX_INPUT_LENGTH}
        autoFocus
        autoComplete="off"
        style={{
          // position: 'absolute',
          width: Math.max(rect.width, 100),
          height: rect.height + 16,
          borderRadius: '4px',
          minWidth: '300px',
          zIndex: 1000
        }}
        onChange={(e) => {
          const value = e.target.value;
          if (value.length <= MAX_INPUT_LENGTH) {
            setInputValues(prev => ({
              ...prev,
              [activeInput]: value
            }));
          }
        }}
        onKeyDown={(e) => {
          if (e.key === 'Enter') {
            e.preventDefault();
            handleInputSubmit(activeInput);
          } else if (e.key === 'Escape') {
            e.preventDefault();
            handleInputCancel(activeInput);
          }
        }}
        onBlur={() => {
          // Delay để tránh conflict với click events
          setTimeout(() => {
            if (activeInput) {
              handleInputSubmit(activeInput);
            }
          }, 100);
        }}
      />
    );
  };

  return (
    <div className={`question-box2 blank-question q-mode-v1 ${isSubmit ? '' : 'slideDown'}`}>
      <div className="py-3 px-md-3 h-50">
        <div className="q-content">
          <div className="q-content-text" ref={contentRef}>
            <div className="q-label absolute px-3 py-1 border flex flex-row align-middle">
              <p className="mb-0">
                <span data-cy="current-question-number" role="current-question-number">{props.currentQuestionIndex+1}</span>/
                <span data-cy="total-question-number" role="total-question-number">{props.totalQuestion}</span>
              </p>
            </div>
            <div
              dangerouslySetInnerHTML={{ __html: renderContent() }}
              onClick={(e) => {
                // Xử lý click vào chỗ trống
                const targetId = e.target.closest('.flag-el')?.getAttribute('data-target-id');
                if (targetId) handleBlankClick(targetId);
              }}
            />
          </div>
        </div>
      </div>

      <div className="q-answer text-center py-3 px-md-3 h-50">
        <div className="dragdrop-options-grid position-relative d-flex flex-column gap-2 rounded-lg blur-20 text-ds-light-500">
          <div className="game-note position-sticky text-xl font-bold w-100">
            Click vào chỗ trống để nhập đáp án. Nhấn Enter để xác nhận, Esc để hủy.
          </div>
          <div className="d-flex flex-grow-1 justify-content-center overflow-auto">
            <div className="flex-wrap gap-4 justify-content-center align-items-start my-auto">
              {renderInputOverlay()}
              {isSubmit && (
                <div className="correct-answers-display p-3 bg-light rounded text-start fs-20">
                  <div className="fw-bold text-success mb-2">
                    <i className="bi bi-check-circle-fill me-2"></i>
                    Đáp án đúng:
                  </div>
                  <div className="text-dark">
                    {getCorrectAnswersDisplay() ? (
                      <pre className="my-0" style={{ whiteSpace: 'pre-line', fontFamily: 'inherit' }}>
                        {getCorrectAnswersDisplay()}
                      </pre>
                    ) : (
                      'Không có đáp án'
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>

          {activeInput && (
            <div className="mt-2">
              <span>
                Đang nhập ... {(inputValues[activeInput] || '').length} / {MAX_INPUT_LENGTH} ký tự
              </span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default BlankQuestion;
