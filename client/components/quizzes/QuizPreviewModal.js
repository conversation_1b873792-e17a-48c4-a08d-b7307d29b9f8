"use client";

import React, { useState, useEffect, useRef } from 'react';
import { useTheme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import Dialog           from '@mui/material/Dialog';
import DialogContent    from '@mui/material/DialogContent';
import IconButton       from '@mui/material/IconButton';
import Box              from '@mui/material/Box';
import Typography       from '@mui/material/Typography';
import CircularProgress from '@mui/material/CircularProgress';
import CloseIcon from '@mui/icons-material/Close';
import Link from 'next/link';
import Avatar from '@mui/material/Avatar';
import Button from '@mui/material/Button';

import ShareMenu from '@/components/shared/ShareMenu';

const QuizPreviewModal = ({ open, onClose, quiz, print = false }) => {
  const theme = useTheme();
  const fullScreen = useMediaQuery(theme.breakpoints.down('md'));
  const [loading, setLoading] = useState(true);
  const printIframeRef = useRef(null);
  const qrContainerRef = useRef(null);

  useEffect(() => {
    if (open) {
      setLoading(true);
    }
  }, [open]);


  const handlePrint = () => {
    if (printIframeRef.current) {
      printIframeRef.current.contentWindow.print();
    }
  };

  if (!open || !quiz) return null;

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      fullScreen={fullScreen}
      PaperProps={{
        sx: {
          height: '100vh',
          overflow: 'hidden',
          paddingTop: 3,
          paddingBottom: 3,
        }
      }}
    >
      <Box className="position-absolute" sx={{ right: 10, top: 10, zIndex: 10 }}>
        <IconButton onClick={onClose} aria-label="close" size="small">
          <CloseIcon />
        </IconButton>
      </Box>
      <DialogContent className="p-0 d-flex h-100" sx={{
        flexDirection: { xs: 'column', md: 'row' },
        overflow: 'hidden',
        bgcolor: '#f5f5f5'
      }}>
        <Box className="position-relative" sx={{
          flex: 1,
          height: { xs: '65%', md: '100%' },
          p: { xs: 1, md: 2 }
        }}>
          {loading && (
            <Box className="position-absolute d-flex align-items-center justify-content-center" sx={{
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              bgcolor: 'rgba(255,255,255,0.7)'
            }}>
              <CircularProgress />
            </Box>
          )}
          <Box sx={{
            height: '100%',
            bgcolor: 'white',
            borderRadius: '8px',
            overflow: 'hidden',
            boxShadow: '0 0 10px rgba(0,0,0,0.05)'
          }}>
            <iframe
              ref={printIframeRef}
              src={`/print/quiz/${quiz.id}?print=${print}`}
              title={quiz.title}
              className="w-100 h-100 border-0"
              onLoad={() => setLoading(false)}
              style={{
                overflowY: 'auto',
                backgroundColor: '#fff'
              }}
            />
          </Box>
        </Box>
        <Box className="p-3" sx={{
          width: { xs: '100%', md: '350px' },
          bgcolor: 'white'
        }}>
          <h2 className="h5">
            {quiz.title}
          </h2>
          <Box className="d-flex flex-wrap mt-2" sx={{ fontSize: '0.75rem', color: '#6D6D6D' }}>
            <Box className="d-flex align-items-center gap-1 w-50">
              <i className="bi bi-patch-question"></i>
              <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.75rem', color: '#6D6D6D' }}>
                {quiz.questions_count || quiz.questions?.length || 0} câu hỏi
              </Typography>
            </Box>
            <Box className="d-flex align-items-center gap-1 w-50">
              <i className="bi bi-bar-chart"></i>
              <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.75rem', color: '#6D6D6D' }}>
                {quiz.view || 0} lượt thi
              </Typography>
            </Box>
          </Box>
          <Box className="d-flex flex-wrap mt-2" sx={{ fontSize: '0.75rem', color: '#6D6D6D' }}>
            {quiz.subject && (
              <Box className="d-flex align-items-center gap-1 w-50">
                <i className="bi bi-file-earmark-medical"></i>
                <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.75rem', color: '#6D6D6D' }}>
                  {quiz.subject.title}
                </Typography>
              </Box>
            )}
            {quiz.grade && (
              <Box className="d-flex align-items-center gap-1 w-50">
                <i className="bi bi-mortarboard"></i>
                <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.75rem', color: '#6D6D6D' }}>
                  {quiz.grade.title}
                </Typography>
              </Box>
            )}
          </Box>
          <Box className="d-flex align-items-center mt-3" sx={{ gap: 1 }}>
            <Avatar src={quiz.editor?.avatar} alt={quiz.editor?.name} sx={{ width: 24, height: 24 }}>
              {quiz.editor.name.charAt(0)}
            </Avatar>
            <span style={{ fontSize: '0.875rem', color: '#6D6D6D' }}>{quiz.editor.name}</span>
          </Box>
          <Box className="mt-3 d-flex flex-wrap gap-2">
            { print ? (
              <Button className="btn btn-primary2 flex-grow-1" onClick={handlePrint} title="In / Tải xuống">
                <i className="bi bi-printer me-2"></i>
                In / Tải xuống
              </Button>
            ) : (
              <Link href={`/quiz/${quiz.slug}-${quiz.id}`} className="btn btn-primary2 flex-grow-1 w-100" title="Xem chi tiết">
                <i className="bi bi-info-circle me-2"></i>
                Xem chi tiết
              </Link>
            )}
            <Link href={`/join/quiz/${quiz.id}/start`} className="btn btn-play-game flex-grow-1 w-100" title="Chơi như một trò chơi">
              <i className="bi bi-stars me-2"></i>
              { print ? 'Chơi như một trò chơi' : 'Bắt đầu ngay' }
            </Link>
            <ShareMenu
              quiz={quiz}
              qrContainerRef={qrContainerRef}
              widthClass="btn-share-game w-100"
            />
          </Box>
        </Box>
      </DialogContent>
    </Dialog>
  );
};

export default QuizPreviewModal;
