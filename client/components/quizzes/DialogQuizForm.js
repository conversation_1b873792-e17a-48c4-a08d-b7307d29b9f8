"use client";

import React, { forwardRef, useRef, memo, useState } from "react";
import { useDispatch } from "react-redux";
import { useRouter } from "next/navigation";

import Box from "@mui/material/Box";
import Dialog from "@mui/material/Dialog";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import FormControl from "@mui/material/FormControl";
import FormHelperText from "@mui/material/FormHelperText";
import InputLabel from "@mui/material/InputLabel";
import OutlinedInput from "@mui/material/OutlinedInput";
import Button from "@mui/material/Button";
import Slide from "@mui/material/Slide";
import TextField from '@mui/material/TextField';
import Autocomplete from '@mui/material/Autocomplete';
import Tabs from "@mui/material/Tabs";
import Tab from "@mui/material/Tab";
import CircularProgress from "@mui/material/CircularProgress";
import Typography from "@mui/material/Typography";
import IconButton from "@mui/material/IconButton";
import CloseIcon from "@mui/icons-material/Close";

import * as Yup from "yup";
import { Formik } from "formik";
import { useTranslations } from "next-intl";
import { toast } from "react-hot-toast";

import { storeQuiz, updateQuiz } from "@/actions/quizAction";
import { fetchOptions } from "@/actions/onlyClientAction";
import { setNoti } from "@/slices/notiSlice";

import { isBase64, delay } from "@/utils/helpers";

import { DragDropCropImage } from "../DragDropCropImage";
import QuizImportFile from "@/components/quizzes/QuizImportFile";

const Transition = forwardRef(function Transition(props, ref) {
  return <Slide direction="up" ref={ref} {...props} />;
});

const DialogQuizForm = ({ open, onClose, quiz = {}, onSubmit, courseId = null, tocId = null, defaultTabValue = 0 }) => {
  console.log("DialogQuizForm");
  const dispatch = useDispatch();
  const t = useTranslations("Common");
  const router = useRouter();
  const [tabValue, setTabValue] = useState(defaultTabValue || 0);

  const { data: gradeAndSubject } = fetchOptions(
    open ? "grades,subjects" : null
  );

  const handleChangeTab = (event, newValue) => {
    setTabValue(newValue);
  };

  function CustomTabPanel(props) {
    const { children, value, index, ...other } = props;

    return (
      <div
        role="tabpanel"
        hidden={value !== index}
        id={`simple-tabpanel-${index}`}
        aria-labelledby={`simple-tab-${index}`}
        {...other}
      >
        {value === index && <Box sx={{ p: 1 }}>{children}</Box>}
      </div>
    );
  }

  const cropRef = useRef();

  return (
    <>
      <Dialog
        open={open}
        onClose={onClose}
        TransitionComponent={Transition}
        keepMounted
        fullWidth={true}
        maxWidth="md"
      >
        <DialogTitle
          className="h5 mb-0"
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center" }}>
            <Typography component="h5" variant="h6">Cài đặt Quiz</Typography>
          </Box>
          <IconButton
            aria-label="close"
            onClick={onClose}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent>
          <Formik
            initialValues={{
              title: quiz.title || "Bài Quiz không có tiêu đề",
              grade_id: quiz.grade_id || "",
              subject_id: quiz.subject_id || "",
            }}
            validationSchema={Yup.object().shape({
              title: Yup.string()
                .min(3)
                .max(500)
                .required("Tiêu đề không được để trống"),
              grade_id: Yup.number()
                .required("Vui lòng chọn lớp")
                .positive()
                .integer(),
              subject_id: Yup.number()
                .required("Vui lòng chọn môn")
                .positive()
                .integer(),
            })}
            onSubmit={async (values, { setErrors, setSubmitting }) => {
              try {
                await delay(400);

                let banner_base64 = null;

                if (cropRef.current) {
                  banner_base64 = cropRef.current.getImg();
                }

                if (quiz.id) {
                  let newData = {
                    ...quiz,
                    ...values,
                  };

                  if (isBase64(banner_base64)) {
                    newData.banner_base64 = banner_base64;
                  } else if (banner_base64 === null) {
                    newData.remove_banner = 1;
                  }

                  const {data: newQuiz} = await updateQuiz(newData);

                  await fetch(`/api/revalidate?path=${encodeURIComponent(`/quiz/${newQuiz.slug}-${newQuiz.id}`)}&path=${encodeURIComponent(`/quiz/${quiz.slug}-${quiz.id}`)}`);

                  toast.success("Cập nhật quiz thành công");
                  router.refresh();
                  onClose();
                } else {
                  const submitData = {
                    ...values,
                    banner_base64,
                  };

                  if (courseId) {
                    submitData.course_id = courseId;
                  }

                  if (tocId) {
                    submitData.toc_id = tocId;
                  }

                  const res = await storeQuiz(submitData);

                  toast.success("Thêm mới quiz thành công");

                  if (onSubmit) {
                    await onSubmit(res);
                  } else {
                    router.push(`/dashboard/quiz/${res.data.id}/edit`);
                  }

                  onClose();

                  return res;
                }
              } catch (err) {
                console.log(err);
                if (err?.status == 422) {
                  setErrors(err.errors);
                } else {
                  dispatch(setNoti(err));
                }
              } finally {
                setSubmitting(false);
              }
            }}
          >
            {({
              errors,
              handleBlur,
              handleChange,
              handleSubmit,
              isSubmitting,
              touched,
              values,
            }) => (
              <form noValidate onSubmit={handleSubmit}>
                <Tabs
                  value={tabValue}
                  onChange={handleChangeTab}
                  sx={{ mb: 2 }}
                >
                  <Tab label="Tạo thủ công" />
                  <Tab label="Import file Word" />
                  <Tab label="Import file Excel" />
                </Tabs>
                <CustomTabPanel value={tabValue} index={0}>
                  <div className="row align-items-start">
                    <div className="col-12 col-md-6">
                      <Box className="d-flex flex-column gap-4 py-3">
                        <FormControl
                          fullWidth
                          error={Boolean(touched.title && errors.title)}
                        >
                          <InputLabel htmlFor="outlined-adornment-title">
                            Tiêu đề
                          </InputLabel>
                          <OutlinedInput
                            id="outlined-adornment-title"
                            type="text"
                            label="Tiêu đề"
                            value={values.title}
                            name="title"
                            onBlur={handleBlur}
                            onChange={handleChange}
                            inputProps={{}}
                          />
                          {touched.title && errors.title && (
                            <FormHelperText
                              error
                              id="standard-weight-helper-text-title"
                            >
                              {errors.title}
                            </FormHelperText>
                          )}
                        </FormControl>

                        {gradeAndSubject &&
                          Object.keys(gradeAndSubject).map((key, index) => {
                            let key_name = "";

                            switch (key) {
                              case "subjects":
                                key_name = "subject_id";
                                break;
                              case "grades":
                                key_name = "grade_id";
                                break;
                              default:
                                key_name = key;
                            }

                            return (
                              <FormControl
                                key={key}
                                fullWidth
                                error={Boolean(
                                  touched[key_name] && errors[key_name]
                                )}
                              >
                              <Autocomplete
                                id={`autocomplete-${key_name}-unique`}
                                options={gradeAndSubject[key]}
                                getOptionLabel={(option) => option.label}
                                noOptionsText="Không tìm thấy kết quả phù hợp"
                                value={values[key_name] ? gradeAndSubject[key].find((item => item.value === values[key_name])) : null}
                                onChange={(event, newValue) => {
                                  handleChange({
                                    target: {
                                      name: key_name,
                                      value: newValue ? newValue.value : "",
                                    },
                                  });
                                }}
                                renderInput={(params) => (
                                  <TextField
                                    {...params}
                                    label={t(key)}
                                  />
                                )}
                                />
                                {touched[key_name] && errors[key_name] && (
                                  <FormHelperText error>
                                    {errors[key_name]}
                                  </FormHelperText>
                                )}
                              </FormControl>
                            );
                          })
                        }
                      </Box>
                    </div>
                    <div className="col-12 col-md-6">
                      {open && (
                        <DragDropCropImage
                          imgSrc={quiz.banner || ""}
                          ref={cropRef}
                          subtitle='Thêm ảnh bìa'
                        />
                      )}
                    </div>
                  </div>
                  <Box className="text-center text-md-end" sx={{ mt: 3 }}>
                    <Button
                      variant="outlined"
                      type="button"
                      onClick={onClose}
                      color="inherit"
                      sx={{ mr: 2 }}
                    >
                      Hủy
                    </Button>
                    <Button
                      disableElevation
                      disabled={isSubmitting}
                      type="submit"
                      variant="contained"
                      color="secondary"
                      onClick={handleSubmit}
                      endIcon={isSubmitting ? <CircularProgress size={18} color="inherit" /> : null}
                    >
                      {quiz.id ? "Cập nhật" : "Tạo mới"}
                    </Button>
                  </Box>
                </CustomTabPanel>
                <CustomTabPanel value={tabValue} index={1}>
                  <QuizImportFile quiz={quiz} onClose={onClose} />
                </CustomTabPanel>
                <CustomTabPanel value={tabValue} index={2}>
                  <QuizImportFile quiz={quiz} onClose={onClose} fileType="excel" />
                </CustomTabPanel>
              </form>
            )}
          </Formik>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default memo(DialogQuizForm);
