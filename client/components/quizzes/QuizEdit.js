"use client";

import React, { useState, useCallback, useRef, useEffect, useMemo } from "react";
import { useRouter } from "next/navigation";
import { useTranslations } from "next-intl";

import {styled, useTheme} from "@mui/material/styles";
import useMediaQuery from "@mui/material/useMediaQuery";
import AppBar from '@mui/material/AppBar';
import Toolbar from '@mui/material/Toolbar';
import Box from "@mui/material/Box";
import Card from "@mui/material/Card";
import CardContent from "@mui/material/CardContent";
import CircularProgress from "@mui/material/CircularProgress";
import Tooltip from '@mui/material/Tooltip';
import IconButton from '@mui/material/IconButton';
import Container from '@mui/material/Container';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import Select from '@mui/material/Select';
import MenuItem from '@mui/material/MenuItem';
import FormControl from '@mui/material/FormControl';
import InputLabel from '@mui/material/InputLabel';
import Accordion from '@mui/material/Accordion';
import AccordionSummary from '@mui/material/AccordionSummary';
import AccordionDetails from '@mui/material/AccordionDetails';

import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import WestIcon from '@mui/icons-material/West';
import SettingsIcon from '@mui/icons-material/Settings';
import VisibilityIcon from '@mui/icons-material/Visibility';
import SaveIcon from '@mui/icons-material/Save';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';

import toast from "react-hot-toast";

import useMathJax from '@/hooks/useMathJax';
import localStorageHelper from "@/utils/localStorageHelper";

import QuizSearchSidebar from "./QuizSearchSidebar";
import QuestionCard from "../questions/QuestionCard";
import DeleteButtonPopper from "../DeleteButtonPopper";
import QuestionTypeDialog from "../questions/QuestionTypeDialog";
import QuestionFormDialog from "../questions/QuestionFormDialog";
import NoDataOverlay from "@/components/NoDataOverlay";

import { duplicateQuestions } from "@/actions/quizAction";
import { removeQuestion } from "@/actions/questionAction";
import DialogQuizForm from "@/components/quizzes/DialogQuizForm";
import DialogQuizImport from "./DialogQuizImport";
import { formatDuration } from "@/utils/helpers";
import { updateQuiz } from "@/actions/quizAction";
import DialogGoogleFormImport from "./DialogGoogleFormImport";

const MainContentStyled = styled('main')(({ theme }) => ({
  backgroundColor: theme.palette.grey[100],
  minWidth: '1%',
  width: '100%',
  minHeight: 'calc(100vh - 62px)',
}));

const StickyQuizAppBar = React.memo(({ questionCount, createQuestion, onDrawer, quiz }) => {
  const theme = useTheme();
  const matchDownMD = useMediaQuery(theme.breakpoints.down("md"));
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const threshold = matchDownMD ? 59 : 68;

  const [isSticky, setIsSticky] = useState(false);
  const appBarRef = useRef(null);

  useEffect(() => {
    const handleScroll = () => {
      if (appBarRef.current) {
        const { top } = appBarRef.current.getBoundingClientRect();
        setIsSticky(top <= threshold);
      }
    };

    window.addEventListener("scroll", handleScroll);
    handleScroll();

    return () => window.removeEventListener("scroll", handleScroll);
  }, [threshold]);

  const onDrawerClick = (e) => {
    e.stopPropagation();
    onDrawer();
  };

  return (
    <AppBar
      ref={appBarRef}
      className="mt-4 header-quiz-edit"
      component="div"
      position="sticky"
      color="inherit"
      sx={{
        top: 56,
        background: isSticky ? "#fff" : "transparent",
        boxShadow: isSticky
          ? "0px 2px 4px -1px rgba(0,0,0,0.2),0px 4px 5px 0px rgba(0,0,0,0.14),0px 1px 10px 0px rgba(0,0,0,0.12)"
          : "none",
        transition: "background 0.3s, box-shadow 0.3s",
        zIndex: 1000,
        borderRadius: '0 0 8px 8px',
        py: isSticky ? 1 : 0,
      }}
    >
      <Toolbar className="gap-2">
        <h5 className="m-0 fs-18">{questionCount} câu hỏi</h5>

        <Box sx={{ flexGrow: 1 }} />
        { isSticky && !isMobile &&
          <button className="btn btn-primary4 btn-sm" onClick={onDrawerClick} title="Tìm kiếm câu hỏi">
            <i className="bi bi-search me-2"></i>
            <strong>Tìm kiếm câu hỏi</strong>
          </button>
        }
        <QuestionTypeDialog createQuestion={createQuestion} quiz={quiz}/>
      </Toolbar>
    </AppBar>
  );
});

StickyQuizAppBar.displayName = 'StickyQuizAppBar';

const QuizEditHeader = React.memo(({ quiz, onSettingsClick }) => {
  const theme = useTheme();
  const router = useRouter();
  const matchDownMD = useMediaQuery(theme.breakpoints.down("md"));
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const threshold = matchDownMD ? 59 : 68;

  const [publish, setPublish] = useState(quiz?.status === 1);
  const [isSticky, setIsSticky] = useState(false);
  const appBarRef = useRef(null);

  useEffect(() => {
    const handleScroll = () => {
      if (appBarRef.current) {
        const { top } = appBarRef.current.getBoundingClientRect();
        setIsSticky(top <= threshold);
      }
    };

    window.addEventListener("scroll", handleScroll);
    handleScroll();

    return () => window.removeEventListener("scroll", handleScroll);
  }, [threshold]);

  const handleTitleClick = () => {
    onSettingsClick();
  };

  const handlePublish = useCallback((quiz) => async () => {
    if (!quiz) return;

    if (quiz.questions.length <= 0) {
      toast.error("Chưa có câu hỏi nào trong quiz này. Vui lòng thêm câu hỏi trước khi xuất bản.");
    } else {
      try {
        const response = await updateQuiz({
          id: quiz.id,
          title: quiz.title,
          status: 1
        });
        if (response && response.data) {
          setPublish(pre => !pre);
        }

        toast.success('Cập nhật trạng thái quiz thành công');
      } catch (error) {
        console.error("Error publishing quiz:", error);
        toast.error("Có lỗi xảy ra khi xuất bản quiz.");
      }
    }
  }, [router]);

  const questionCount = useMemo(
    () => quiz?.questions_count || quiz?.questions?.length || 0,
    [quiz]
  );

  return (
    <AppBar
      ref={appBarRef}
      component="div"
      position="sticky"
      color="inherit"
      sx={{
        top: 0,
        background: isSticky ? "#fff" : "transparent",
        boxShadow: isSticky
          ? "0px 2px 4px -1px rgba(0,0,0,0.2),0px 4px 5px 0px rgba(0,0,0,0.14),0px 1px 10px 0px rgba(0,0,0,0.12)"
          : "none",
        transition: "background 0.3s, box-shadow 0.3s",
        zIndex: 1000,
      }}
    >
      <Toolbar sx={{ justifyContent: 'space-between' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <IconButton onClick={() => router.push(`/quiz/${quiz.slug}-${quiz.id}`)}>
            <WestIcon />
          </IconButton>
          <Typography
            className="m-0 d-none d-md-block"
            variant="h6"
            component="button"
            onClick={handleTitleClick}
            sx={{
              background: 'none',
              border: 'none',
              cursor: 'pointer',
              fontWeight: 600,
              fontSize: '1rem',
              color: 'inherit',
              '&:hover': {
                color: 'grey.600',
                background: '#0909090d',
                borderRadius: '.25rem',
              }
            }}
          >
            {quiz.title}
          </Typography>
        </Box>

        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="outlined"
            startIcon={<SettingsIcon />}
            onClick={onSettingsClick}
            size="small"
            color="grey"
            title="Chỉnh sửa cài đặt"
          >
            Cài đặt
          </Button>
          <Button
            variant="contained"
            startIcon={<SaveIcon />}
            onClick={handlePublish(quiz)}
            size="small"
            color={ publish ? 'error' : 'secondary' }
            title={ (publish ? 'Ngừng xuất bản' : 'Xuất bản') + ' bài kiểm tra' }
          >
            { publish ? 'Ngừng xuất bản' : 'Xuất bản' }
          </Button>
        </Box>
      </Toolbar>
    </AppBar>
  );
});

QuizEditHeader.displayName = 'QuizEditHeader';

const BulkUpdateSection = React.memo(() => {
  const [timeLimit, setTimeLimit] = useState('');
  const [points, setPoints] = useState('');

  const timeOptions = [
    { value: 30, label: '30 giây' },
    { value: 60, label: '1 phút' },
    { value: 120, label: '2 phút' },
    { value: 300, label: '5 phút' },
    { value: 600, label: '10 phút' },
  ];

  const pointOptions = [
    { value: 1, label: '1 điểm' },
    { value: 2, label: '2 điểm' },
    { value: 3, label: '3 điểm' },
    { value: 5, label: '5 điểm' },
    { value: 10, label: '10 điểm' },
  ];

  return (
    <Accordion defaultExpanded>
      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
        <Typography variant="subtitle2" fontWeight={600}>
          Câu hỏi cập nhật hàng loạt
        </Typography>
      </AccordionSummary>
      <AccordionDetails>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          <FormControl size="small" fullWidth>
            <InputLabel>Thời gian</InputLabel>
            <Select
              value={timeLimit}
              label="Thời gian"
              onChange={(e) => setTimeLimit(e.target.value)}
            >
              {timeOptions.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <FormControl size="small" fullWidth>
            <InputLabel>Điểm</InputLabel>
            <Select
              value={points}
              label="Điểm"
              onChange={(e) => setPoints(e.target.value)}
            >
              {pointOptions.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Box>
      </AccordionDetails>
    </Accordion>
  );
});

BulkUpdateSection.displayName = 'BulkUpdateSection';

const ImportSection = React.memo(({ onImportWord, onImportExcel, onImportGoogleForm }) => {
  return (
    <Box
      className="import-actions-container mb-4 d-none d-lg-flex transition-all duration-200"
      sx={{
        display: 'flex',
        flexDirection: 'column',
        background: '#fff',
        borderRadius: 2,
        border: '1px solid #09090933',
        position: 'sticky',
        top: 68,
        zIndex: 1000,
      }}
    >
      <Typography variant="subtitle2" fontWeight={600} className="section-title d-flex align-items-center mb-0">
        Nhập từ
      </Typography>
      <Box
        className="import-spreadsheet-button import-quiz-btn cursor-pointer"
        onClick={onImportGoogleForm}
      >
        <div>
          <i className="icon bi bi-file-earmark-medical-fill" style={{ color: '#8854c0' }}></i>
          <span className="text-sm font-semibold text-dark-1">Biểu mẫu Google</span>
        </div>
        <i className="bi bi-chevron-right d-inline-flex align-items-center text-ds-dark-200"></i>
      </Box>
      <Box
        className="import-google-forms-button import-quiz-btn cursor-pointer"
        onClick={onImportExcel}
      >
        <div>
          <i className="icon bi bi-file-earmark-spreadsheet-fill file-excel"></i>
          <span className="text-sm font-semibold text-dark-1">Bảng tính</span>
        </div>
        <i className="bi bi-chevron-right d-inline-flex align-items-center text-ds-dark-200"></i>
      </Box>
      <Box
        className="import-word-button import-quiz-btn cursor-pointer"
        onClick={onImportWord}
      >
        <div>
          <i className="icon bi bi-file-earmark-word-fill file-word"></i>
          <span className="text-sm font-semibold text-dark-1">File Word</span>
        </div>
        <i className="bi bi-chevron-right d-inline-flex align-items-center text-ds-dark-200"></i>
      </Box>
    </Box>
  );
});

ImportSection.displayName = 'ImportSection';

const QuizEdit = ({ initQuiz }) => {
  console.log('QuizEdit');
  const t = useTranslations("Common");

  const [quiz, setQuiz] = useState(initQuiz);
  const [questionSelected, setQuestionSelected] = useState(null);
  const [openQuestionFormDialog, setOpenQuestionFormDialog] = useState(false);
  const [duplicatingQuestionId, setDuplicatingQuestionId] = useState(null);
  const [openDialogQuizFrom, setOpenDialogQuizFrom] = useState(false);
  const [openDialogQuizImport, setOpenDialogQuizImport] = useState(false);
  const [fileType, setFileType] = useState('word');
  const [openDialogGoogleForm, setOpenDialogGoogleForm] = useState(false);

  useMathJax([quiz]);

  const drawerRef = useRef(null);

  const onDrawer = useCallback(() => {
    if (drawerRef.current) {
      drawerRef.current.toggle();
    }
  }, []);

  const handleCloseQuestionFormDialog = useCallback(() => {
    setQuestionSelected(null);
    setOpenQuestionFormDialog(false);
  }, []);

  const createQuestion = useCallback((questionData) => {
    setQuestionSelected(questionData);
    setOpenQuestionFormDialog(true);
  }, []);

  const editQuestion = useCallback((question) => {
    setQuestionSelected(question);
    setOpenQuestionFormDialog(true);
  }, []);

  const handleDupQuestions = useCallback(async (newQuestions) => {
    setQuiz((prevQuiz) => ({
      ...prevQuiz,
      questions: [...prevQuiz.questions, ...newQuestions],
      questions_count: (prevQuiz.questions_count || 0) + newQuestions.length
    }));

    toast.success("Thêm thành công " + newQuestions.length + " câu hỏi");

    await revalidateQuizPage();
  }, []);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const updatedQuestions = useCallback(async (newQuestion, isUpdate = false) => {
    setQuiz((prevQuiz) => {
      let newQuestions;

      if (isUpdate) {
        newQuestions = prevQuiz.questions.map((question) =>
          question.id === newQuestion.id ? newQuestion : question
        );
      } else {
        newQuestions = [...prevQuiz.questions, newQuestion];
      }

      return { ...prevQuiz, questions: newQuestions };
    });

    toast.success(`${isUpdate ? 'Cập nhật câu hỏi' : 'Thêm câu hỏi'} thành công`);

    await revalidateQuizPage();
  }, []);

  const deleteQuestion = useCallback(async (questionId) => {
    try {
      await removeQuestion(quiz.id, questionId);

      setQuiz((prevQuiz) => ({
        ...prevQuiz,
        questions: prevQuiz.questions.filter((que) => que.id !== questionId),
        questions_count: (prevQuiz.questions_count || 0) - 1
      }));

      toast.success("Xóa câu hỏi thành công");

      await revalidateQuizPage();
    } catch (error) {
      toast.error("Không thể xóa câu hỏi.");
    }
  }, [quiz]);

  const duplicateQuestion = useCallback(async (question) => {
    try {
      setDuplicatingQuestionId(question.id);

      const result = await duplicateQuestions(quiz.id, { question_id: question.id });

      setQuiz((prevQuiz) => ({
        ...prevQuiz,
        questions: [...prevQuiz.questions, ...result.data],
        questions_count: (prevQuiz.questions_count || 0) + 1
      }));

      toast.success("Sao chép câu hỏi thành công");

      await revalidateQuizPage();

      setTimeout(() => {
        const flyBox = document.getElementById('fly-box_last-qlist');
        if (flyBox) {
          flyBox.scrollIntoView({ behavior: 'smooth', block: 'end' });
        }
      }, 100);
    } catch (error) {
      toast.error("Không thể sao chép câu hỏi. Vui lòng thử lại");
    } finally {
      setDuplicatingQuestionId(null);
    }
  }, [quiz.id]);

  const revalidateQuizPage = async () => {
    if (quiz) {
      try {
        await fetch(`/api/revalidate?path=${encodeURIComponent(`/quiz/${quiz.slug}-${quiz.id}`)}`);
        localStorageHelper.set('quiz_refresh', quiz.id);
      } catch (err) {
        console.error("Failed to revalidate quiz page", err);
      }
    }
  };

  const handleOpenDialogQuizFrom = useCallback(() => {
    setOpenDialogQuizFrom(true);
  }, []);

  const handleCloseDialogQuizFrom = useCallback(() => {
    setOpenDialogQuizFrom(false);
  }, []);

  const handleWordClick = () => {
    setFileType("word");
    setOpenDialogQuizImport(true);
  };

  const handleExcelClick = () => {
    setFileType("excel");
    setOpenDialogQuizImport(true);
  };

  const handleGoogleFormClick = () => {
    setOpenDialogGoogleForm(true);
  };

  const handleCloseDialogGoogleForm = useCallback(() => {
    setOpenDialogGoogleForm(false);
  }, []);

  const handleImportSuccess = useCallback(async (importedQuestions) => {
    // Cập nhật state quiz với câu hỏi mới được import
    setQuiz((prevQuiz) => ({
      ...prevQuiz,
      questions: [...(prevQuiz.questions || []), ...importedQuestions],
      questions_count: (prevQuiz.questions_count || 0) + importedQuestions.length
    }));

    // Revalidate cache của trang quiz
    await revalidateQuizPage();

    setTimeout(() => {
      const flyBox = document.getElementById('fly-box_last-qlist');
      if (flyBox) {
        flyBox.scrollIntoView({ behavior: 'smooth', block: 'end' });
      }
    }, 100);
  }, [revalidateQuizPage]);

  const handleCloseDialogQuizImport = useCallback(() => {
    setOpenDialogQuizImport(false);
  }, []);

  return (
    <>
      <QuizEditHeader
        quiz={quiz}
        onSettingsClick={handleOpenDialogQuizFrom}
      />
      <MainContentStyled className="py-4">
        <Container maxWidth="cxl">
          <Box className="row">
            <Box className="col-lg-3">
              <ImportSection
                onImportWord={handleWordClick}
                onImportExcel={handleExcelClick}
                onImportGoogleForm={handleGoogleFormClick}
              />
            </Box>
            <Box className="col-lg-9">
              <Card className="p-3">
                <QuizSearchSidebar
                  ref={drawerRef}
                  quiz={quiz}
                  onDupQuestions={handleDupQuestions}
                />
              </Card>
              <StickyQuizAppBar
                questionCount={quiz.questions.length || 0}
                createQuestion={createQuestion}
                onDrawer={onDrawer}
                quiz={quiz}
              />
              {quiz?.questions.length > 0 ?
                quiz.questions.map((question, index) => (
                  <Card key={question.id} className="p-3 pb-0 mt-4">
                    <div className="d-flex flex-wrap align-items-center justify-content-between mb-2">
                      <span className="fw-normal fs-14 my-1 border py-0-5 px-1-5 rounded-1">
                        {index + 1}. {t(question.type)}
                      </span>
                      {question.content_json && (
                        <div className="my-1">
                          <span className="fw-normal fs-14 my-1 text-black-50 border py-0-5 px-1-5 rounded-1 me-2">
                            {question.content_json?.points || 1} điểm
                          </span>
                          <span className="fw-normal fs-14 my-1 text-black-50 border py-0-5 px-1-5 rounded-1 d-none d-md-inline">
                            {question.content_json.timeLimit ? formatDuration(question.content_json.timeLimit) : 'Không giới hạn'}
                          </span>
                        </div>
                      )}
                      <div className="my-1">
                        <Tooltip title="Nhân đôi câu hỏi này" arrow>
                          <button
                            onClick={() => duplicateQuestion(question)}
                            type="button"
                            className="btn btn-default btn-sm me-2"
                            style={{padding: "1px 10px"}}
                            disabled={duplicatingQuestionId === question.id}
                          >
                            {duplicatingQuestionId === question.id ? (
                              <CircularProgress size={14} color="inherit"/>
                            ) : (
                              <ContentCopyIcon style={{fontSize: '14px'}}/>
                            )}
                          </button>
                        </Tooltip>
                        <Tooltip title="Chỉnh sửa câu hỏi này" arrow>
                          <button
                            onClick={() => editQuestion(question)}
                            type="button"
                            className="btn btn-default btn-sm me-2"
                            style={{padding: "1px 10px"}}
                          >
                            <i className="bi bi-pencil-square me-1"></i>
                            <span className="d-none d-md-inline">Chỉnh sửa</span>
                          </button>
                        </Tooltip>
                        <DeleteButtonPopper
                          onDelete={() => deleteQuestion(question.id)}
                          title="Xóa câu hỏi này"
                          mesage="Bạn có chắc chắn muốn xóa câu hỏi này?"
                        />
                      </div>
                    </div>
                    <CardContent className="p-0">
                      <QuestionCard question={question} showAnswer={true}/>
                    </CardContent>
                  </Card>
                )) : <NoDataOverlay message="Danh sách câu hỏi trống!"/>
              }
              { quiz?.questions?.length > 0 && (
                <div className="add-question-actions z-10 transition-all duration-400 d-block py-3">
                  <div className="d-flex align-items-center gap-2 justify-content-center" id="add-question-toolbar">
                    <QuestionTypeDialog createQuestion={createQuestion} quiz={quiz}/>
                  </div>
                </div>
              )}
              <div className="fly-box" id="fly-box_last-qlist"></div>
              <QuestionFormDialog
                quizId={quiz.id}
                question={questionSelected}
                open={openQuestionFormDialog}
                onClose={handleCloseQuestionFormDialog}
                updatedQuestions={updatedQuestions}
              />
              <DialogQuizForm
                quiz={quiz}
                open={openDialogQuizFrom}
                onClose={handleCloseDialogQuizFrom}
              />
              <DialogQuizImport
                quiz={quiz}
                open={openDialogQuizImport}
                onClose={handleCloseDialogQuizImport}
                onImportSuccess={handleImportSuccess}
                fileType={fileType}
              />
              <DialogGoogleFormImport
                quiz={quiz}
                open={openDialogGoogleForm}
                onClose={handleCloseDialogGoogleForm}
                onImportSuccess={handleImportSuccess}
              />
            </Box>
          </Box>
        </Container>
      </MainContentStyled>
    </>
  );
};

export default QuizEdit;
