"use client"

import React from "react";

import Link from "next/link";

import Card from "@mui/material/Card";
import CardContent from "@mui/material/CardContent";
import CardMedia from "@mui/material/CardMedia";
import CardActionArea from "@mui/material/CardActionArea";
import CardActions from "@mui/material/CardActions";

import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation } from "swiper/modules";
import "swiper/css";
import "swiper/css/navigation";

import NextImage from "@/components/NextImage";

const QuizRelatedSideBar = ({ quiz }) => {
  console.log("QuizRelatedSideBar");

  return (
    <div className="my-4">
      <div className="d-flex align-items-center justify-content-between">
        <h4 className="h5 fw-bold">
          <i className="bi bi-star-fill me-2 text-warning"></i>
          <PERSON><PERSON><PERSON> ý cho b<PERSON>n
        </h4>
        <Link
          href={ quiz?.related_url || '/'}
          className="see-all-link"
        >
          <PERSON>em tất cả
          <i className="bi bi-arrow-right-short ms-1"></i>
        </Link>
      </div>
      <Swiper
        slidesPerView={5}
        spaceBetween={20}
        navigation={true}
        modules={[Navigation]}
        breakpoints={{
          0: {
            slidesPerView: 1.3,
          },
          450: {
            slidesPerView: 2,
          },
          639: {
            slidesPerView: 2.5,
          },
          900: {
            slidesPerView: 3.5,
          },
          1200: {
            slidesPerView: 4.2,
          },
          1400: {
            slidesPerView: 5,
          },
        }}
        className="mySwiper"
      >
        {quiz.related.map((relatedQuiz) => (
          <SwiperSlide className="h-auto" key={relatedQuiz.id}>
            <Card className="h-100 shadow">
              <CardActionArea component={Link} href={`/quiz/${relatedQuiz.slug}-${relatedQuiz.id}`}>
                <CardMedia className="p-1">
                  <NextImage
                    src={relatedQuiz.banner}
                    alt={relatedQuiz.title}
                  />
                </CardMedia>
                <CardContent sx={{ padding: "10px" }}>
                  <div className="d-flex align-items-center justify-content-between mb-2">
                    <span className="badge bg-light text-dark">{ relatedQuiz.type_text }</span>
                    <span className="text-black-50 fs-12 text-limit text-center" style={{'--lines': 1}}>
                      {relatedQuiz.subject?.title || ''} - {relatedQuiz.grade?.title || ''}
                    </span>
                  </div>
                  <h4 className="quiz-name text-limit">
                    {relatedQuiz.title || "__"}
                  </h4>
                </CardContent>
              </CardActionArea>
              <CardActions
                className="justify-content-between text-black-50 fs-14"
                sx={{ padding: "5px 10px" }}
              >
                <span>{relatedQuiz.questions_count} câu hỏi</span>
                <span>{relatedQuiz.view || 0} lượt thi</span>
              </CardActions>
            </Card>
          </SwiperSlide>
        ))}
      </Swiper>
    </div>
  );
};

export default React.memo(QuizRelatedSideBar);
