import React, { useEffect, useState } from 'react';
import LinearProgress from '@mui/material/LinearProgress';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';

const QuizGameTimer = ({ counter, initValue = 60, onEnd }) => {
  console.log('QuizTimer');
  const [progress, setProgress] = useState(initValue); // giây

  useEffect(() => {
    let currentProgress = initValue;
    setProgress(initValue); // reset progress

    const timer = setInterval(() => {
      currentProgress = Math.max(currentProgress - 0.1, 0);
      setProgress(currentProgress);

      if (currentProgress <= 0) {
        clearInterval(timer);
        onEnd?.();
      }
    }, 100);

    return () => clearInterval(timer);
  }, [initValue]);

  return (
    <Box sx={{ display: 'flex', alignItems: 'center', width: '350px' }}>
      <Box sx={{ width: '100%', mr: 1 }}>
        <LinearProgress variant="determinate" value={(progress / initValue) * 100} />
      </Box>
      <Box sx={{ minWidth: 35 }}>
        <Typography variant="body2" color="text.secondary">
          {Math.ceil(progress)}s
        </Typography>
      </Box>
    </Box>
  );
};

export default React.memo(QuizGameTimer);
