"use client";

import React from "react";

const QrDownload = ({ qrContainerRef }) => {
  const handleDownload = () => {
    if (qrContainerRef?.current) {
      const canvas = qrContainerRef.current.querySelector('canvas');
      if (canvas) {
        const pngUrl = canvas.toDataURL("image/png");
        const downloadLink = document.createElement('a');
        downloadLink.href = pngUrl;
        downloadLink.download = 'qr-code.png';
        document.body.appendChild(downloadLink);
        downloadLink.click();
        document.body.removeChild(downloadLink);
      }
    }
  };

  return (
    <button
      className="btn btn-link custom-style-shareq"
      style={{ textDecoration: 'none' }}
      onClick={handleDownload}
    >
      <i className="bi bi-arrow-down-square me-2"></i> T<PERSON>i về máy
    </button>
  );
};

export default QrDownload;
