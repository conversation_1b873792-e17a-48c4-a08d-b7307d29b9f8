import React from "react";
import NextImage from "@/components/NextImage";

const QuizMedia = ({ quiz, active = false, onQuizSelected, bannerWidth = "150px", bannerHeight = "150px" }) => {
  return (
    <div className={`card p-2 border rounded ${active ? 'active' : ''}`}
         onClick={() => onQuizSelected && onQuizSelected(quiz)}
         style={{
           maxWidth: '100%',
           overflow: 'hidden'
         }}>
      <div className="row g-0 align-items-center">
          <div className="col-3 text-center">
            <NextImage
              imgStyle={{
                objectFit: 'contain',
                objectPosition: 'center',
              }}
              objectFit="contain"
              src={quiz.banner}
              alt={quiz.title}
            />
          </div>
          <div className="col-9">
            <div className="card-body py-2 px-3">
              <span className="badge bg-light text-dark mb-2">{ quiz.type_text }</span>
              <h5 className="quiz-name text-limit" style={{'--lines': 3, maxWidth: '100%' }}>{quiz.title}</h5>
              <p className="text-black-50 mb-2 fs-13 text-truncate">
                <i className="bi bi-person me-2"></i>
                { quiz?.editor?.name || '_' }
              </p>
              <div className="text-black-50 mb-0 fs-13">
                <div className="d-flex flex-wrap">
                  <span className="text-black-50 me-3 mb-1 d-inline-block">
                    <i className="bi bi-patch-question me-2"></i>
                    { quiz.questions_count } câu hỏi
                  </span>
                  <span className="text-black-50 me-3 mb-1 d-inline-block">
                    <i className="bi bi-file-earmark-medical me-2"></i>
                    { quiz?.subject?.title || '_' }
                  </span>
                  <span className="text-black-50 me-3 mb-1 d-inline-block">
                    <i className="bi bi-mortarboard-fill me-2"></i>
                    { quiz?.grade?.title || '_' }
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
    </div>
  );
};

export default QuizMedia;
