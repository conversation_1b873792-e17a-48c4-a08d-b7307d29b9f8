import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import AutoProgressBar from '../AutoProgressBar';

// Mock timers
jest.useFakeTimers();

describe('AutoProgressBar', () => {
  const mockOnComplete = jest.fn();
  const mockOnSkip = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    jest.clearAllTimers();
  });

  afterEach(() => {
    jest.runOnlyPendingTimers();
    jest.useRealTimers();
    jest.useFakeTimers();
  });

  test('should not render when isVisible is false', () => {
    render(
      <AutoProgressBar
        isVisible={false}
        isCorrect={true}
        onComplete={mockOnComplete}
        onSkip={mockOnSkip}
      />
    );

    expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
  });

  test('should render with correct styling for correct answer', () => {
    render(
      <AutoProgressBar
        isVisible={true}
        isCorrect={true}
        onComplete={mockOnComplete}
        onSkip={mockOnSkip}
        duration={5000}
      />
    );

    const progressBar = screen.getByRole('progressbar');
    expect(progressBar).toBeInTheDocument();
    expect(progressBar).toHaveClass('auto-progress-container', 'auto-progress-correct');
    
    expect(screen.getByText(/Chuyển câu tiếp theo trong/)).toBeInTheDocument();
    expect(screen.getByText('5')).toBeInTheDocument(); // Initial countdown
    expect(screen.getByText('Tiếp tục (Space)')).toBeInTheDocument();
  });

  test('should render with correct styling for wrong answer', () => {
    render(
      <AutoProgressBar
        isVisible={true}
        isCorrect={false}
        onComplete={mockOnComplete}
        onSkip={mockOnSkip}
        duration={5000}
      />
    );

    const progressBar = screen.getByRole('progressbar');
    expect(progressBar).toHaveClass('auto-progress-container', 'auto-progress-wrong');
  });

  test('should update countdown timer', async () => {
    render(
      <AutoProgressBar
        isVisible={true}
        isCorrect={true}
        onComplete={mockOnComplete}
        onSkip={mockOnSkip}
        duration={5000}
      />
    );

    // Initial state
    expect(screen.getByText('5')).toBeInTheDocument();

    // Advance timer by 1 second
    act(() => {
      jest.advanceTimersByTime(1000);
    });

    await waitFor(() => {
      expect(screen.getByText('4')).toBeInTheDocument();
    });

    // Advance timer by another second
    act(() => {
      jest.advanceTimersByTime(1000);
    });

    await waitFor(() => {
      expect(screen.getByText('3')).toBeInTheDocument();
    });
  });

  test('should call onComplete after duration', async () => {
    render(
      <AutoProgressBar
        isVisible={true}
        isCorrect={true}
        onComplete={mockOnComplete}
        onSkip={mockOnSkip}
        duration={5000}
      />
    );

    // Advance timer to completion
    act(() => {
      jest.advanceTimersByTime(5000);
    });

    await waitFor(() => {
      expect(mockOnComplete).toHaveBeenCalledTimes(1);
    });
  });

  test('should call onSkip when skip button is clicked', () => {
    render(
      <AutoProgressBar
        isVisible={true}
        isCorrect={true}
        onComplete={mockOnComplete}
        onSkip={mockOnSkip}
        duration={5000}
      />
    );

    const skipButton = screen.getByText('Tiếp tục (Space)');
    fireEvent.click(skipButton);

    expect(mockOnSkip).toHaveBeenCalledTimes(1);
  });

  test('should call onSkip when Space key is pressed', () => {
    render(
      <AutoProgressBar
        isVisible={true}
        isCorrect={true}
        onComplete={mockOnComplete}
        onSkip={mockOnSkip}
        duration={5000}
      />
    );

    fireEvent.keyDown(window, { code: 'Space' });

    expect(mockOnSkip).toHaveBeenCalledTimes(1);
  });

  test('should not call onSkip when other keys are pressed', () => {
    render(
      <AutoProgressBar
        isVisible={true}
        isCorrect={true}
        onComplete={mockOnComplete}
        onSkip={mockOnSkip}
        duration={5000}
      />
    );

    fireEvent.keyDown(window, { code: 'Enter' });
    fireEvent.keyDown(window, { code: 'Escape' });

    expect(mockOnSkip).not.toHaveBeenCalled();
  });

  test('should update progress value over time', async () => {
    render(
      <AutoProgressBar
        isVisible={true}
        isCorrect={true}
        onComplete={mockOnComplete}
        onSkip={mockOnSkip}
        duration={1000} // Shorter duration for testing
      />
    );

    const progressBar = screen.getByRole('progressbar');
    
    // Initial progress should be 0
    expect(progressBar).toHaveAttribute('aria-valuenow', '0');

    // Advance timer by half duration
    act(() => {
      jest.advanceTimersByTime(500);
    });

    await waitFor(() => {
      const currentProgress = parseInt(progressBar.getAttribute('aria-valuenow'));
      expect(currentProgress).toBeGreaterThan(0);
      expect(currentProgress).toBeLessThan(100);
    });

    // Advance to completion
    act(() => {
      jest.advanceTimersByTime(500);
    });

    await waitFor(() => {
      expect(mockOnComplete).toHaveBeenCalledTimes(1);
    });
  });

  test('should have proper accessibility attributes', () => {
    render(
      <AutoProgressBar
        isVisible={true}
        isCorrect={true}
        onComplete={mockOnComplete}
        onSkip={mockOnSkip}
        duration={5000}
      />
    );

    const progressBar = screen.getByRole('progressbar');
    expect(progressBar).toHaveAttribute('aria-valuenow', '0');
    expect(progressBar).toHaveAttribute('aria-valuemin', '0');
    expect(progressBar).toHaveAttribute('aria-valuemax', '100');
    expect(progressBar).toHaveAttribute('aria-label', 'Chuyển câu tiếp theo trong 5 giây');
  });

  test('should cleanup timers when component unmounts', () => {
    const { unmount } = render(
      <AutoProgressBar
        isVisible={true}
        isCorrect={true}
        onComplete={mockOnComplete}
        onSkip={mockOnSkip}
        duration={5000}
      />
    );

    // Verify timers are running
    expect(jest.getTimerCount()).toBeGreaterThan(0);

    // Unmount component
    unmount();

    // Advance timers - onComplete should not be called after unmount
    act(() => {
      jest.advanceTimersByTime(5000);
    });

    expect(mockOnComplete).not.toHaveBeenCalled();
  });

  test('should work with custom duration', async () => {
    render(
      <AutoProgressBar
        isVisible={true}
        isCorrect={true}
        onComplete={mockOnComplete}
        onSkip={mockOnSkip}
        duration={3000} // 3 seconds
      />
    );

    // Should show 3 seconds initially
    expect(screen.getByText('3')).toBeInTheDocument();

    // Should complete after 3 seconds
    act(() => {
      jest.advanceTimersByTime(3000);
    });

    await waitFor(() => {
      expect(mockOnComplete).toHaveBeenCalledTimes(1);
    });
  });
});
