import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import QuizGame from '../QuizGame';

// Mock các dependencies
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    back: jest.fn(),
  }),
  useSearchParams: () => ({
    get: jest.fn(() => 'solo'),
  }),
}));

jest.mock('@/hooks/useSound', () => ({
  __esModule: true,
  default: () => [jest.fn(), { stop: jest.fn() }],
}));

jest.mock('@/hooks/useQuizTiming', () => ({
  __esModule: true,
  default: () => ({
    startQuiz: jest.fn(),
    startQuestion: jest.fn(),
    endQuestion: jest.fn(() => 1000),
    endQuiz: jest.fn(() => 5000),
    getQuestionDuration: jest.fn(),
    totalDuration: 0,
    isQuizActive: true,
  }),
}));

// Mock actions
jest.mock('@/actions/quizResultAction', () => ({
  soloProceed: jest.fn(),
  soloEnd: jest.fn(),
  soloJoin: jest.fn(),
}));

const mockStore = configureStore({
  reducer: {
    auth: (state = { user: { name: 'Test User', avatar: '' } }) => state,
    noti: (state = {}) => state,
  },
});

const mockQuizInfo = {
  id: 1,
  quiz_id: 1,
  assignment_id: null,
  status: 0,
  quiz: {
    id: 1,
    slug: 'test-quiz',
    questions: [
      {
        id: 1,
        type: 'QUIZX',
        content_json: {
          kind: 'MCQ',
          content: '<p>Câu hỏi trắc nghiệm test?</p>',
          options: [
            { content: '<p>Đáp án A</p>' },
            { content: '<p>Đáp án B</p>' },
            { content: '<p>Đáp án C</p>' },
            { content: '<p>Đáp án D</p>' },
          ],
          answer: [1], // Đáp án đúng là B (index 1)
        },
      },
    ],
  },
};

describe('QuizGame MCQ Auto Submit', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should auto submit when selecting an MCQ answer', async () => {
    const { soloProceed } = require('@/actions/quizResultAction');
    soloProceed.mockResolvedValue({
      data: {
        question: { answer: [1] },
        isCorrect: true,
      },
    });

    render(
      <Provider store={mockStore}>
        <QuizGame initQuizInfo={mockQuizInfo} />
      </Provider>
    );

    // Bắt đầu game
    const startButton = screen.getByText(/bắt đầu/i);
    fireEvent.click(startButton);

    // Đợi game load
    await waitFor(() => {
      expect(screen.getByText('Câu hỏi trắc nghiệm test?')).toBeInTheDocument();
    });

    // Click vào đáp án B
    const optionB = screen.getByText('Đáp án B');
    fireEvent.click(optionB);

    // Kiểm tra trạng thái submitting xuất hiện
    await waitFor(() => {
      expect(screen.getByText('Đang gửi câu trả lời...')).toBeInTheDocument();
    });

    // Kiểm tra soloProceed được gọi với đáp án đúng
    await waitFor(() => {
      expect(soloProceed).toHaveBeenCalledWith(1, {
        questionId: 1,
        answer: [1], // Index của đáp án B
        timer: 0,
        question_duration: 1000,
      });
    });
  });

  test('should not allow clicking options while submitting', async () => {
    const { soloProceed } = require('@/actions/quizResultAction');

    // Mock delay để test trạng thái submitting
    soloProceed.mockImplementation(() =>
      new Promise(resolve =>
        setTimeout(() => resolve({
          data: {
            question: { answer: [1] },
            isCorrect: true,
          },
        }), 1000)
      )
    );

    render(
      <Provider store={mockStore}>
        <QuizGame initQuizInfo={mockQuizInfo} />
      </Provider>
    );

    // Bắt đầu game
    const startButton = screen.getByText(/bắt đầu/i);
    fireEvent.click(startButton);

    await waitFor(() => {
      expect(screen.getByText('Câu hỏi trắc nghiệm test?')).toBeInTheDocument();
    });

    // Click vào đáp án A
    const optionA = screen.getByText('Đáp án A');
    fireEvent.click(optionA);

    // Kiểm tra trạng thái submitting
    await waitFor(() => {
      expect(screen.getByText('Đang gửi câu trả lời...')).toBeInTheDocument();
    });

    // Thử click vào đáp án khác trong khi đang submit
    const optionC = screen.getByText('Đáp án C');
    fireEvent.click(optionC);

    // soloProceed chỉ được gọi 1 lần với đáp án A
    expect(soloProceed).toHaveBeenCalledTimes(1);
    expect(soloProceed).toHaveBeenCalledWith(1, {
      questionId: 1,
      answer: [0], // Index của đáp án A
      timer: 0,
      question_duration: 1000,
    });
  });

  test('should show loading state on submit button', async () => {
    const { soloProceed } = require('@/actions/quizResultAction');

    soloProceed.mockImplementation(() =>
      new Promise(resolve =>
        setTimeout(() => resolve({
          data: {
            question: { answer: [1] },
            isCorrect: true,
          },
        }), 500)
      )
    );

    render(
      <Provider store={mockStore}>
        <QuizGame initQuizInfo={mockQuizInfo} />
      </Provider>
    );

    // Bắt đầu game
    const startButton = screen.getByText(/bắt đầu/i);
    fireEvent.click(startButton);

    await waitFor(() => {
      expect(screen.getByText('Câu hỏi trắc nghiệm test?')).toBeInTheDocument();
    });

    // Click vào đáp án
    const optionA = screen.getByText('Đáp án A');
    fireEvent.click(optionA);

    // Kiểm tra nút submit hiển thị trạng thái loading
    await waitFor(() => {
      expect(screen.getByText('ĐANG GỬI...')).toBeInTheDocument();
    });

    // Đợi submit hoàn thành
    await waitFor(() => {
      expect(screen.queryByText('ĐANG GỬI...')).not.toBeInTheDocument();
    });
  });

  test('should show auto progress bar after successful submit', async () => {
    const { soloProceed } = require('@/actions/quizResultAction');
    soloProceed.mockResolvedValue({
      data: {
        question: { answer: [1] },
        isCorrect: true,
      },
    });

    render(
      <Provider store={mockStore}>
        <QuizGame initQuizInfo={mockQuizInfo} />
      </Provider>
    );

    // Bắt đầu game
    const startButton = screen.getByText(/bắt đầu/i);
    fireEvent.click(startButton);

    await waitFor(() => {
      expect(screen.getByText('Câu hỏi trắc nghiệm test?')).toBeInTheDocument();
    });

    // Click vào đáp án
    const optionA = screen.getByText('Đáp án A');
    fireEvent.click(optionA);

    // Đợi submit hoàn thành và thanh tiến trình xuất hiện
    await waitFor(() => {
      expect(screen.getByText(/Chuyển câu tiếp theo trong/)).toBeInTheDocument();
      expect(screen.getByText('Tiếp tục (Space)')).toBeInTheDocument();
    });

    // Kiểm tra thanh tiến trình có màu xanh (correct)
    const progressContainer = screen.getByRole('progressbar');
    expect(progressContainer).toHaveClass('auto-progress-correct');
  });

  test('should show red progress bar for wrong answer', async () => {
    const { soloProceed } = require('@/actions/quizResultAction');
    soloProceed.mockResolvedValue({
      data: {
        question: { answer: [1] },
        isCorrect: false, // Wrong answer
      },
    });

    render(
      <Provider store={mockStore}>
        <QuizGame initQuizInfo={mockQuizInfo} />
      </Provider>
    );

    // Bắt đầu game
    const startButton = screen.getByText(/bắt đầu/i);
    fireEvent.click(startButton);

    await waitFor(() => {
      expect(screen.getByText('Câu hỏi trắc nghiệm test?')).toBeInTheDocument();
    });

    // Click vào đáp án sai
    const optionA = screen.getByText('Đáp án A');
    fireEvent.click(optionA);

    // Đợi submit hoàn thành và thanh tiến trình xuất hiện
    await waitFor(() => {
      expect(screen.getByText(/Chuyển câu tiếp theo trong/)).toBeInTheDocument();
    });

    // Kiểm tra thanh tiến trình có màu đỏ (wrong)
    const progressContainer = screen.getByRole('progressbar');
    expect(progressContainer).toHaveClass('auto-progress-wrong');
  });
});
