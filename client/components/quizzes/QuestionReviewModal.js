import React, { useState, useEffect } from 'react';

import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import IconButton from '@mui/material/IconButton';
import Button from '@mui/material/Button';
import Chip from '@mui/material/Chip';
import FormControlLabel from '@mui/material/FormControlLabel';
import Checkbox from '@mui/material/Checkbox';
import Slide from '@mui/material/Slide';
import Paper from '@mui/material/Paper';
import Divider from '@mui/material/Divider';

import CloseIcon from '@mui/icons-material/Close';
import PrevIcon from '@mui/icons-material/NavigateBefore';
import NextIcon from '@mui/icons-material/NavigateNext';
import CheckIcon from '@mui/icons-material/CheckCircle';
import CancelIcon from '@mui/icons-material/Cancel';
import TimeIcon from '@mui/icons-material/AccessTime';
import ShowIcon from '@mui/icons-material/CheckBox';
import HideIcon from '@mui/icons-material/CheckBoxOutlineBlank';

import HtmlContent from '../HtmlContent';
import { optionBackgrounds } from '@/constant';
import { formatDuration } from '@/utils/helpers';

const Transition = React.forwardRef(function Transition(props, ref) {
  return <Slide direction="up" ref={ref} {...props} />;
});

const QuestionReviewModal = ({
  open,
  onClose,
  questions = [],
  currentIndex = 0,
  onNavigate
}) => {
  const [showCorrectAnswers, setShowCorrectAnswers] = useState(true);
  const [showExplanations, setShowExplanations] = useState(false);
  const [activeIndex, setActiveIndex] = useState(currentIndex || 0);

  useEffect(() => {
    if (currentIndex !== null && currentIndex !== undefined) {
      setActiveIndex(currentIndex);
    }
  }, [currentIndex]);

  const currentQuestion = questions[activeIndex];

  if (!currentQuestion) return null;

  // Get option class name for styling
  const getOptionClassName = (optionIndex) => {
    const isUserAnswer = currentQuestion.userAnswer?.includes(optionIndex);
    const isCorrectAnswer = currentQuestion.content_json?.answer?.includes(optionIndex);

    if (showCorrectAnswers) {
      if (isUserAnswer && isCorrectAnswer) {
        return 'correct-selected'; // User selected correct answer
      } else if (isUserAnswer && !isCorrectAnswer) {
        return 'wrong-selected'; // User selected the wrong answer
      } else if (!isUserAnswer && isCorrectAnswer) {
        return 'correct-not-selected'; // Correct answer is not selected by user
      }
    } else {
      if (isUserAnswer) {
        return 'user-selected'; // Just show user selection
      }
    }
    return 'default';
  };

  const handlePrevious = () => {
    const newIndex = activeIndex > 0 ? activeIndex - 1 : questions.length - 1;
    setActiveIndex(newIndex);
    onNavigate?.(newIndex);
  };

  const handleNext = () => {
    const newIndex = activeIndex < questions.length - 1 ? activeIndex + 1 : 0;
    setActiveIndex(newIndex);
    onNavigate?.(newIndex);
  };

  const getOptionStyle = (optionIndex) => {
    const className = getOptionClassName(optionIndex);
    const baseStyle = {
      paddingX: 2,
      paddingY: 1,
      mt: 3,
      cursor: 'default',
      transition: 'all 0.2s',
      borderRadius: 1,
      position: 'relative',
      backgroundColor: optionBackgrounds[optionIndex] || '#f5f5f5'
    };

    switch (className) {
      case 'correct-selected':
        return {
          ...baseStyle,
          borderColor: '#7bcd7f',
          backgroundColor: 'rgba(158,232,162,0.19)',
        };
      case 'wrong-selected':
        return {
          ...baseStyle,
          borderColor: '#f44336',
          backgroundColor: 'rgba(237,176,172,0.2)',
        };
      case 'correct-not-selected':
        return {
          ...baseStyle,
          borderColor: '#4caf50',
          backgroundColor: 'rgba(158,232,162,0.23)',
        };
      case 'user-selected':
        return {
          ...baseStyle,
          borderColor: '#2196f3',
          backgroundColor: 'rgba(165,205,237,0.2)'
        };
      default:
        return {
          ...baseStyle,
          borderColor: 'gray',
          borderWidth: 1,
          backgroundColor: '#f2f2f2',
        };
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      TransitionComponent={Transition}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 3,
          maxHeight: '90vh',
          m: 2,
          p: 0,
          width: { xs: '100%', sm: 'calc(100% - 48px)' },
        }
      }}
      slotProps={{
        container: {
          className: 'custom-dialog-container',
        },
      }}
    >
      <DialogContent sx={{ p: 0, overflow: 'hidden' }}>
        {/* Header */}
        <Box sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          paddingX: 2,
          paddingY: 1,
          boxShadow: '0 4px 4px #0003'
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Typography variant="h5" sx={{ fontWeight: 'bold' }} className="mb-0">
              Câu hỏi {activeIndex + 1}
            </Typography>
            {currentQuestion.isCorrect == null ? (
              <Chip
                label="Chưa làm"
                color="secondary"
                size="small"
              />
            ) : (
              <Chip
                icon={currentQuestion.isCorrect ? <CheckIcon /> : <CancelIcon />}
                label={currentQuestion.isCorrect ? 'Đúng' : 'Sai'}
                color={currentQuestion.isCorrect ? 'success' : 'error'}
                size="small"
              />
            )}
            {currentQuestion.duration > 0 && (
              <Chip
                icon={<TimeIcon />}
                label={formatDuration(currentQuestion.duration)}
                variant="outlined"
                size="small"
              />
            )}
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Typography variant="body2" sx={{ color: 'text.secondary', fontWeight: 'bold' }}>
              {activeIndex + 1} / {questions.length}
            </Typography>
            <IconButton onClick={onClose}>
              <CloseIcon />
            </IconButton>
          </Box>
        </Box>

        {/* Question Content */}
        <Box sx={{ p: 2, maxHeight: { xs: '520px', sm: '600px' }, overflowY: 'auto' }}>
          <HtmlContent html={currentQuestion.content_json?.content || ''} />

          {/* Options */}
          <Box sx={{ mt: 3, mb: 4 }} className="options-container">
            {currentQuestion.content_json?.options?.map((option, index) => {
              const isUserAnswer = currentQuestion.userAnswer?.includes(index);
              const isCorrectAnswer = currentQuestion.content_json?.answer?.includes(index);

              return (
                <Paper
                  key={index}
                  elevation={0}
                  sx={getOptionStyle(index)}
                  className={getOptionClassName(index)}
                  position="relative"
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Box sx={{
                      width: 20,
                      height: 20,
                      borderRadius: '50%',
                      backgroundColor: 'rgba(225,221,221,0.86)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      fontWeight: 'bold',
                      fontSize: '14px'
                    }}>
                      {showCorrectAnswers && isCorrectAnswer && <CheckIcon color="success" />}
                      {showCorrectAnswers && !isCorrectAnswer && isUserAnswer && <CancelIcon color="error" />}
                    </Box>
                    <Box sx={{ flex: 1 }} className="box-content">
                      <HtmlContent html={option.content} />
                    </Box>
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      {showCorrectAnswers && isCorrectAnswer && (
                        <Chip
                          label="Đáp án đúng"
                          size="small"
                          color="success"
                        />
                      )}
                    </Box>
                  </Box>
                </Paper>
              );
            })}
          </Box>

          {showExplanations && currentQuestion?.explain && (
            <Paper>
              <Divider sx={{ my: 2 }} />
              <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mb: 1 }}>
                💡 Giải thích:
              </Typography>
              <HtmlContent html={currentQuestion.explain} />
            </Paper>
          )}
        </Box>

        {/* Navigation Footer */}
        <Box sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          paddingX: { xs: 1, sm: 2 },
          paddingY: 1,
          borderTop: 1,
          borderColor: 'divider',
          backgroundColor: '#f2f2f2'
        }}>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
            <FormControlLabel
              control={
                <Checkbox
                  checked={showCorrectAnswers}
                  onChange={(e) => setShowCorrectAnswers(e.target.checked)}
                  icon={<HideIcon />}
                  checkedIcon={<ShowIcon />}
                  color="secondary"
                  sx={{ p: 0 }}
                />
              }
              label="Hiển thị đáp án"
              sx={{ backgroundColor: '#fff', borderRadius: 1, p: '4px 6px', height: '32px', m: 0, gap: 0.5, width: '152px' }}
            />
            { currentQuestion?.explain &&
              <FormControlLabel
                control={
                  <Checkbox
                    checked={showExplanations}
                    onChange={(e) => setShowExplanations(e.target.checked)}
                    icon={<HideIcon />}
                    checkedIcon={<ShowIcon />}
                    color="secondary"
                    sx={{ p: 0 }}
                  />
                }
                label="Hiển thị giải thích"
                sx={{ backgroundColor: '#fff', borderRadius: 1, p: '4px 6px', height: '32px', m: 0, gap: 0.5, width: '152px' }}
              />
            }
          </Box>

          <Box sx={{ display: 'flex', gap: 1 }}>
            <Button
              className="bg-white"
              startIcon={<PrevIcon />}
              onClick={handlePrevious}
              variant="outlined"
              color="secondary"
              disabled={questions.length <= 1}
              sx={{
                px: { xs: 0.5, sm: 1, md: 3 },
                '&:hover': {
                  transform: 'translateY(-1px)',
                },
                transition: 'all 0.2s ease',
              }}
            >
              PREV
            </Button>
            <Button
              endIcon={<NextIcon />}
              onClick={handleNext}
              variant="contained"
              color="secondary"
              disabled={questions.length <= 1}
              sx={{
                px: { xs: 0.5, sm: 1, md: 3 },
                '&:hover': {
                  transform: 'translateY(-1px)',
                },
                transition: 'all 0.2s ease'
              }}
            >
              NEXT
            </Button>
          </Box>
        </Box>
      </DialogContent>
    </Dialog>
  );
};

export default QuestionReviewModal;
