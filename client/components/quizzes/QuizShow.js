import React from "react";

import Container from '@mui/material/Container';

import QuizRelatedSideBar from "./QuizRelatedSidebar";
import ActionButtons from "./ActionButtons";
import QuestionList from "../questions/QuestionList";
import NoDataOverlay from "@/components/NoDataOverlay";

export default async function QuizShow({ quiz }) {
  return (
    <Container maxWidth="cxl">
      <ActionButtons quiz={quiz} />
      {quiz?.questions?.length > 0 ? <QuestionList questions={quiz.questions} /> : <NoDataOverlay message="Danh sách câu hỏi trống!" />}
      {quiz?.related?.length > 0 && <QuizRelatedSideBar quiz={quiz} />}
    </Container>
  );
}
