"use client";

import React, { useState, useEffect } from 'react'
import dynamic from 'next/dynamic'

// Dynamic import QuizPrint với ssr: false để tránh SSR conflicts
const QuizPrint = dynamic(() => import('./QuizPrint'), {
  ssr: false,
  loading: () => (
    <div className="d-flex justify-content-center align-items-center p-5">
      <div className="text-center text-muted fs-4">Đang tải giao diện in...</div>
    </div>
  )
})

export default function QuizPrintWrapper({ quiz }) {
  const [isMounted, setIsMounted] = useState(false)

  useEffect(() => {
    setIsMounted(true)
  }, [])

  // Hiển thị loading state khi chưa mount
  if (!isMounted) {
    return (
      <div className="d-flex justify-content-center align-items-center p-5">
        <div className="text-center text-muted fs-4"><PERSON><PERSON> khởi tạo...</div>
      </div>
    )
  }

  // Render QuizPrint component sau khi đã mount
  return (
    <QuizPrint quiz={quiz}/>
  )
}
