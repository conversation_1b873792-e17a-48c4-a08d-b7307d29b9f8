
import React, {useMemo, useState} from 'react';
import {useSelector} from "react-redux";
import Link from "next/link";
import { useRouter } from "next/navigation";

import Card from "@mui/material/Card";
import CardHeader from "@mui/material/CardHeader";
import CardContent from "@mui/material/CardContent";
import Typography from "@mui/material/Typography";
import Avatar from "@mui/material/Avatar";
import Button from "@mui/material/Button";
import Box from "@mui/material/Box";
import Chip from "@mui/material/Chip";
import Divider from "@mui/material/Divider";
import Paper from "@mui/material/Paper";

import StoreIcon from '@mui/icons-material/Store';
import CheckIcon from '@mui/icons-material/Check';
import CancelIcon from '@mui/icons-material/Cancel';
import EmojiEventsIcon from '@mui/icons-material/EmojiEvents';
import LeaderboardIcon from '@mui/icons-material/Leaderboard';

import QuestionReviewModal from "@/components/quizzes/QuestionReviewModal";
import AccuracyBar from "@/components/AccuracyBar";

import HtmlContent from '../HtmlContent';
import {formatDuration} from "@/utils/helpers";

const QuizResult = ({
  quizResult,
  questions,
  gameType,
  onPlayAgain
}) => {
  const { user } = useSelector((state) => state.auth);
  const [selectedQuestionIndex, setSelectedQuestionIndex] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const router = useRouter();

  // Parse data_log to get question data
  const questionTimingData = useMemo(() => {
    if (!quizResult?.data_log) return [];

    let dataLog = quizResult.data_log;
    if (typeof dataLog === 'string') {
      try {
        dataLog = JSON.parse(dataLog);
      } catch (e) {
        console.warn("Error parsing data_log:", e);
        return [];
      }
    }

    return questions.map((question, index) => {
      const questionLog = dataLog[question.id] || {};
      return {
        ...question,
        questionNumber: index + 1,
        userAnswer: questionLog.answer || [],
        isCorrect: questionLog.isCorrect === undefined ? null : questionLog.isCorrect,
        duration: questionLog.question_duration || null,
        answeredAt: questionLog.answered_at || null
      };
    });
  }, [quizResult, questions]);

  // Calculate statistics
  const stats = useMemo(() => {
    const accuracy = quizResult?.total > 0 ? Math.round((quizResult.total_correct * 100) / quizResult.total) : 0;
    const averageTime = quizResult?.total_duration > 0 ? Math.round(quizResult.total_duration / quizResult.total) : 0;
    const totalScore = (quizResult?.total_correct || 0) * 100; // Assuming 100 points per correct answer

    // Calculate rank (mock calculation - in real app this would come from backend)
    const rank = 1;

    return {
      accuracy,
      averageTime,
      totalScore,
      rank,
      correctAnswers: quizResult?.total_correct || 0,
      wrongAnswers: quizResult?.total_wrong || 0,
      totalQuestions: quizResult?.total || 0,
      totalDuration: quizResult?.total_duration || 0
    };
  }, [quizResult]);

  // Get a congratulatory message based on performance
  const getCongratulationMessage = () => {
    if (stats.accuracy >= 90) return "Xuất sắc! Bạn đã làm rất tốt!";
    if (stats.accuracy >= 80) return "Chiến thắng đáng chú ý, hãy tiếp tục phát huy!";
    if (stats.accuracy >= 70) return "Kết quả tốt! Hãy cố gắng hơn nữa!";
    return "Đừng nản lòng, hãy thử lại nhé!";
  };

  const handleQuestionClick = (index) => {
    setSelectedQuestionIndex(index);
    setShowModal(true);
  };

  const handleCloseModal = () => {
    setShowModal(false);
    setSelectedQuestionIndex(null);
  };

  return (
    <div className="screen-container px-2 flex-grow-1">
      <div className="screen-summary rounded-xl" style={{ backgroundColor: gameType === 'flashcard' ? '#2f1a2d' : '' }}>
        <div className="main-section-title text-center">
          <p className="fs-18 mb-0">Bản tóm tắt</p>
        </div>
        <div className="w-full text-center d-flex justify-content-center">
          <div className="game-type-title">
            <i className="bi bi-person-workspace me-2"></i>{ gameType === 'flashcard' ? 'Flashcard' : 'Quiz' }
          </div>
        </div>
        <div className="main-section">
          <div className="top-section-wrapper">
            <Typography variant="h5" sx={{ fontWeight: 'bold', textAlign: 'center', paddingTop: '0.5rem', paddingBottom: '1.5rem' }}>
              {getCongratulationMessage()}
            </Typography>

            <Card sx={{ mt: 4, mb: 3, background: '#000'}}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', flexWrap: 'wrap', gap: 2 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Avatar
                      src={user?.avatar}
                      alt={user?.name}
                      sx={{ width: 50, height: 50 }}
                    >
                      {user?.name?.charAt(0)}
                    </Avatar>
                    <Box>
                      <Typography variant="h6" sx={{ fontWeight: 'bold', color: '#fff' }}>
                        {user?.name || 'Người chơi'}
                      </Typography>
                      <Typography variant="body2" color="#fff">
                        Điểm số hiện tại: {stats.totalScore}
                      </Typography>
                    </Box>
                  </Box>
                  {/*<Button
                    variant="contained"
                    startIcon={<StoreIcon />}
                    sx={{
                      background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
                      fontWeight: 'bold',
                      px: 3
                    }}
                  >
                    Go to shop
                  </Button>*/}
                </Box>
              </CardContent>
            </Card>

            <AccuracyBar
              accuracy={stats.accuracy}
              current={quizResult.total_correct}
              total={quizResult.total}
            />

            {/* Score & Rank */}
            <Box mb={4} display="flex" justifyContent="space-between">
              <Box display="flex" alignItems="center" gap={1} flexGrow={1} sx={{ backgroundColor: '#000', padding: 2, borderRadius: '8px' }}>
                <LeaderboardIcon color="secondary" fontSize="small" />
                <Typography>Rank: {stats.rank} / 1</Typography>
              </Box>
              <Box display="flex" alignItems="center" gap={1} flexGrow={1} sx={{ backgroundColor: '#000', padding: 2, ml: 1, borderRadius: '8px' }}>
                <EmojiEventsIcon color="success" fontSize="small" />
                <Typography>Score: {stats.correctAnswers}</Typography>
              </Box>
            </Box>
          </div>

          <div className="middle-section-wrapper animated fadeInUp anim-400-duration">
            <div className="middle-section mb-4">
            <div className="text-center fs-16 my-3">Số liệu thống kê hiệu suất</div>
              <div className="row">
                <div className="col-6">
                  <div className="stat-box">
                    <span className="value">
                      {formatDuration(stats.averageTime || 0)}
                    </span>
                    <span className="label">Thời gian / câu</span>
                  </div>
                </div>
                <div className="col-6">
                  <div className="stat-box">
                    <span className="value">
                      { quizResult.total }
                    </span>
                    <span className="label">Câu</span>
                  </div>
                </div>
                <div className="col-4">
                  <div className="stat-box correct-box">
                    <span className="value">
                      { quizResult.total_correct }
                    </span>
                    <span className="label">{ gameType === 'flashcard' ? 'Câu đã làm' : 'Câu đúng' }</span>
                  </div>
                </div>
                <div className="col-4">
                  <div className="stat-box incorrect-box">
                    <span className="value">
                      { quizResult.total_wrong }
                    </span>
                    <span className="label">Câu sai</span>
                  </div>
                </div>
                <div className="col-4">
                  <div className="stat-box notdo-box">
                    <span className="value">
                      { quizResult.total - quizResult.total_correct - quizResult.total_wrong }
                    </span>
                    <span className="label">Câu chưa làm</span>
                  </div>
                </div>
              </div>
              <div className="mt-4">
                <button
                   className="btn btn-primary2 d-block btn-lg fw-bold w-100"
                   onClick={onPlayAgain}
                >
                  <i className="bi bi-play-circle me-2"></i> Chơi lại
                </button>
                <Link href="/search" className="btn btn-light d-block btn-lg fw-bold w-100 mt-3">
                  <i className="bi bi-search me-2"></i> Tìm quiz mới
                </Link>
              </div>
            </div>
          </div>
        </div>

        <div className="review-section-wrapper">
          {/* Question Review Section */}
          <Card className="review-section" sx={{ background: '#000', borderRadius: '12px', color: '#fff' }}>
            <CardHeader
               title={
                 <Box>
                   <Typography sx={{ fontSize: '18px', color: '#fff' }}>
                     📝 Xem lại câu hỏi
                   </Typography>
                   <Typography sx={{ fontSize: '14px', color: '#ffffffa8', pt: 1 }}>
                     Nhấp vào câu hỏi để xem câu trả lời
                   </Typography>
                 </Box>
               }
               action={
                 <Button
                   size="small"
                   variant="contained"
                   className="review-flashcard-btn mr-0"
                   onClick={() => router.push(`/join/quiz/${quizResult.quiz_id}/start?gameType=flashcard`)}
                 >
                   Flashcard
                 </Button>
               }
               classes={{
                 action: 'flashcards-cta'
               }}
            />
            <CardContent className="question-review-list" sx={{ paddingTop: 0 }}>
              {questionTimingData.length > 0 ? (
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                  {questionTimingData.map((question, index) => (
                     <Box
                        key={question.id}
                        onClick={() => handleQuestionClick(index)}
                        sx={{
                          alignItems: 'center',
                          p: 2,
                          borderRadius: 2,
                          borderLeft: 8,
                          borderLeftColor: question.isCorrect ? '#4caf50' : '#f44336',
                          backgroundColor: '#f2f2f2',
                          color: '#292a3a',
                          cursor: 'pointer',
                          transition: 'all 0.2s',
                        }}
                     >
                       <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                         <Box sx={{ fontWeight: 'bold', mb: 1}}>
                           Câu {question.questionNumber}
                           {question.isCorrect == null ? (
                              <Chip
                                label="Chưa làm"
                                color="secondary"
                                size="small"
                                sx={{ ml: 1 }}
                              />
                           ) : (
                              <Chip
                                icon={question.isCorrect ? <CheckIcon /> : <CancelIcon />}
                                label={question.isCorrect ? 'Đúng' : 'Sai'}
                                color={question.isCorrect ? 'success' : 'error'}
                                size="small"
                                sx={{ ml: 1 }}
                              />
                           )}
                         </Box>
                         {question.duration > 0 && (
                           <Chip
                              size="small"
                              label={formatDuration(question.duration || 0)}
                              sx={{ ml: 1, mb: 1 }}
                           />
                         )}
                       </Box>
                       <Box sx={{ flex: 1 }} className="box-content">
                         <HtmlContent html={question.content_json?.content || ''} />
                       </Box>
                       <Divider sx={{ my: 1 }} />

                       {/* Options */}
                       <Box className="options-container">
                         {question.content_json?.options?.map((option, index) => {

                           return (
                             <Paper
                               key={index}
                               elevation={0}
                               sx={{
                                 p: 1,
                                 cursor: 'default',
                                 transition: 'all 0.2s',
                                 borderRadius: 1,
                                 position: 'relative',
                                 backgroundColor: '#f2f2f2',
                               }}
                             >
                               <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                                 <Box sx={{
                                   width: 20,
                                   height: 20,
                                   borderRadius: '50%',
                                   backgroundColor: 'rgba(225,221,221,0.86)',
                                   display: 'flex',
                                   alignItems: 'center',
                                   justifyContent: 'center',
                                   fontWeight: 'bold',
                                   fontSize: '14px'
                                 }}>
                                 </Box>
                                 <Box sx={{ flex: 1 }} className="box-content">
                                   <HtmlContent html={option.content} />
                                 </Box>
                               </Box>
                             </Paper>
                           );
                         })}
                       </Box>

                     </Box>
                  ))}
                </Box>
              ) : (
                <Box>
                  <Typography variant="body1" sx={{ fontWeight: 'bold', mb: 1}}>
                    Bạn chưa thực hiện câu hỏi nào!
                  </Typography>
                </Box>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Question Review Modal */}
      <QuestionReviewModal
        open={showModal}
        onClose={handleCloseModal}
        questions={questionTimingData}
        currentIndex={selectedQuestionIndex}
        onNavigate={setSelectedQuestionIndex}
      />
    </div>
  );
};

export default QuizResult;
