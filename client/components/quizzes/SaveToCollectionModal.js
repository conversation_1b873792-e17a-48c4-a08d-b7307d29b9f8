"use client";

import React, { useState, useEffect, useRef } from 'react';

import { pink } from '@mui/material/colors';
import Modal from '@mui/material/Modal';
import Avatar from '@mui/material/Avatar';
import Checkbox from '@mui/material/Checkbox';
import FormControlLabel from '@mui/material/FormControlLabel';
import Divider from '@mui/material/Divider';
import CloseIcon from '@mui/icons-material/Close';
import IconButton from '@mui/material/IconButton';
import FolderIcon from '@mui/icons-material/Folder';
import CircularProgress from '@mui/material/CircularProgress';
import Alert from '@mui/material/Alert';
import Paper from "@mui/material/Paper";
import InputBase from "@mui/material/InputBase";
import Button from "@mui/material/Button";
import Typography from "@mui/material/Typography";

import { toast } from "react-hot-toast";
import {
  fetchCollections,
  storeCollection,
  addQuizToCollection,
  removeQuizFromCollection
} from '@/actions/collectionAction';

const SaveToCollectionModal = ({ open, handleClose, quiz, onSuccess }) => {
  const [collections, setCollections] = useState(null);
  const [loadingArr, setLoadingArr] = useState([]);
  const [loading, setLoading] = useState(false);
  const [value, setValue] = useState("");
  const [error, setError] = useState('');

  const scrollRef = useRef(null);

  useEffect(() => {
    if (collections) return;

    const controller = new AbortController();
    const { signal } = controller;

    const fetchData = async () => {
      try {
        const { data: collectionsData } = await fetchCollections(
          { quizId: quiz.id },
          signal
        );

        if (collectionsData) {
          setCollections(collectionsData);
        } else {
          throw new Error("Đã xảy ra lỗi.");
        }
      } catch (err) {
        if (err?.message !== "canceled") {
          toast.error("Không thể tải bộ sưu tập. Vui lòng thử lại sau.");
        }
      }
    };

    fetchData();

    return () => controller.abort();
  }, [collections, quiz.id]);

  const handleCollectionToggle = async (event, collection) => {
    const checked = event.target.checked;
    const newCollection = {
      ...collection,
      table_contents_count: checked ? 1 : 0
    };

    setLoadingArr(prev => [...prev, collection.id]);

    try {
      if (checked) {
        await addQuizToCollection(collection.id, quiz.id);
      } else {
        await removeQuizFromCollection(collection.id, quiz.id);
      }

      setCollections(prev =>
        prev.map(c =>
          c.id === newCollection.id ? newCollection : c
        )
      );

      if (onSuccess) onSuccess();

      await new Promise(resolve => setTimeout(resolve, 700));
    } catch (error) {
      toast.error("Không thể thực hiện thao tác. Vui lòng thử lại.");
    } finally {
      setLoadingArr(prev => prev.filter(id => id !== collection.id));
    }
  };

  const handleCreateCollection = async () => {
    const title = value.trim();

    if (title.length < 3) {
      setError("Không được để trống và ít nhất 3 ký tự");
      return;
    }

    if (title.length > 50) {
      setError("Không được quá 50 ký tự");
      return;
    }

    setError("");
    setLoading(true);

    try {
      const {data: newCollection } = await storeCollection({ title });

      if (newCollection) {
        setCollections(prev => [...prev, newCollection]);
        setValue('');

        setTimeout(() => {
          if (scrollRef.current) {
            scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
          }
        }, 100);

        if (quiz) {
          await handleCollectionToggle({'target': {'checked': true}}, newCollection);
        }

        if (onSuccess) onSuccess();
      } else {
        throw new Error("Đã xảy ra lỗi.");
      }
    } catch (err) {
      setError("Đã xảy ra lỗi. Vui lòng thử lại sau.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      open={open}
      onClose={handleClose}
      aria-labelledby="modal-save-collection"
      disableScrollLock={false}
      className="d-flex align-items-center justify-content-center"
    >
      <div className="bg-white rounded shadow"
        style={{
          width: '90%',
          maxWidth: '500px',
          maxHeight: '90vh',
          overflow: 'hidden',
          display: 'flex',
          flexDirection: 'column'
        }}
      >
        <div className="d-flex align-items-center p-3 pb-2 gap-2"
          style={{ backgroundColor: '#f9f5ff' }}
        >
          <Avatar sx={{ bgcolor: pink[50] }}>
            <FolderIcon sx={{ color: '#ffa100' }}/>
          </Avatar>
          <h6 className="m-0 flex-grow-1 fw-normal" id="modal-save-collection">
            Thêm "{quiz.title}" đến bộ sưu tập
          </h6>
          <IconButton
            onClick={handleClose}
            size="small"
            className="text-secondary"
          >
            <CloseIcon className="fs-18"/>
          </IconButton>
        </div>
        <div className="p-2 overflow-auto bg-light" style={{ height: '16rem' }} ref={scrollRef}>
          {collections === null ? (
            <div className="text-center p-4">
              <CircularProgress size={40} />
            </div>
          ) : collections.length ? (
            collections.map((collection) => (
              <div
                key={collection.id}
              >
                <FormControlLabel
                  control={
                    loadingArr.includes(collection.id) ? (
                      <CircularProgress size={20} sx={{ color: '#8250c8', margin: '10px' }} />
                    ) : (
                      <Checkbox
                        checked={collection?.table_contents_count > 0}
                        onChange={(event) => handleCollectionToggle(event, collection)}
                        sx={{
                          color: '#8250c8',
                          '&.Mui-checked': {
                            color: '#8250c8',
                          },
                        }}
                      />
                    )
                  }
                  label={
                    <span className="fs-16">
                      {collection.title}
                    </span>
                  }
                  className="w-100 m-0"
                />
              </div>
            ))
          ) : (
            <Alert severity="info" className="mb-2">
              Bạn chưa có bộ sưu tập nào. Hãy tạo bộ sưu tập mới.
            </Alert>
          )}
        </div>
        <div className="p-3">
          <h6 className="fw-normal mb-2">Tạo bộ sưu tập mới</h6>
          <Paper
            className="mb-1 mx-auto"
            component="form"
            sx={{
              p: "2px 4px",
              display: "flex",
              alignItems: "center",
              border: error ? "1px solid red" : "1px solid #e5e5e5",
              width: "100%",
              borderRadius: '3px',
            }}
            onSubmit={(e) => {
              e.preventDefault();
              handleCreateCollection();
            }}
          >
            <InputBase
              className="input-medium"
              sx={{ ml: 1, flex: 1 }}
              placeholder="ví dụ: Yêu thích, Câu đố/bài quiz Đại số, v.v."
              inputProps={{ "aria-label": "Tạo bộ sưu tập" }}
              value={value}
              onChange={(e) => {
                setValue(e.target.value);
                if (error) setError("");
              }}
            />
            <Button
              color="secondary"
              variant="contained"
              size="medium"
              type="submit"
              sx={{ m: "5px" }}
              disabled={loading}
            >
              Thêm
            </Button>
          </Paper>
          {error && (
            <Typography color="error" variant="caption">
              {error}
            </Typography>
          )}
        </div>
      </div>
    </Modal>
  );
};

export default SaveToCollectionModal;
