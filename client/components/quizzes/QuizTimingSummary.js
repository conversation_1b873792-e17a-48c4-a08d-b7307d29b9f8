import React from 'react';
import { Box, Typography, Chip } from '@mui/material';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import SpeedIcon from '@mui/icons-material/Speed';
import AvTimerIcon from '@mui/icons-material/AvTimer';
import { formatDuration } from "@/utils/helpers";

/**
 * Simple timing summary component for quiz results
 */
const QuizTimingSummary = ({ quizResult }) => {
  // Calculate average time per question
  const getAverageTimePerQuestion = () => {
    if (!quizResult?.total_duration || !quizResult?.total) return 0;
    return Math.round(quizResult.total_duration / quizResult.total);
  };

  const totalDuration = quizResult?.total_duration || 0;
  const averageTime = getAverageTimePerQuestion();

  if (totalDuration === 0) {
    return null;
  }

  return (
    <Box sx={{ mt: 2, display: 'flex', flexWrap: 'wrap', gap: 1, alignItems: 'center' }}>
      <Typography variant="body2" sx={{ mr: 1 }}>
        <AccessTimeIcon sx={{ fontSize: 16, mr: 0.5, verticalAlign: 'middle' }} />
        Thời gian:
      </Typography>
      <Chip
        size="small"
        icon={<AvTimerIcon />}
        label={`Tổng thời gian: ${formatDuration(totalDuration)}`}
        color="primary"
        variant="outlined"
      />
      {averageTime > 0 && (
        <Chip
          size="small"
          icon={<SpeedIcon />}
          label={`Trung bình: ${formatDuration(averageTime)}/câu`}
          color="secondary"
          variant="outlined"
        />
      )}
    </Box>
  );
};

export default QuizTimingSummary;
