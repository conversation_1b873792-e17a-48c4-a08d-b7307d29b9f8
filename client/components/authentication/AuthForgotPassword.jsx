"use client";

import { useState } from "react";
import Link from "next/link";
import toast from "react-hot-toast";

// material-ui
import { useTheme } from "@mui/material/styles";
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import FormControl from "@mui/material/FormControl";
import FormHelperText from "@mui/material/FormHelperText";
import Grid from "@mui/material/Grid";
import InputLabel from "@mui/material/InputLabel";
import OutlinedInput from "@mui/material/OutlinedInput";
import Typography from "@mui/material/Typography";

// third party
import * as Yup from "yup";
import { Formik } from "formik";

// project imports
import AuthWrapper from "@/components/authentication/AuthWrapper";
import AuthCardWrapper from "@/components/authentication/AuthCardWrapper";
import AnimateButton from "@/components/extended/AnimateButton";
import Logo from "@/components/Logo";

import { axios, getCsrfToken } from "@/lib/axios";
import { apiUrl } from "@/lib/fetcher";

const AuthForgotPassword = () => {
  const theme = useTheme();
  const [isSubmitted, setIsSubmitted] = useState(false);

  return (
    <AuthWrapper>
      <Grid container direction="column" sx={{ justifyContent: 'flex-end', minHeight: '100vh' }}>
        <Grid size={12}>
          <Grid container sx={{ justifyContent: 'center', minHeight: 'calc(100vh - 10px)' }}>
            <Grid sx={{ m: { xs: 1, sm: 3 }, mb: 0 }}>
              <AuthCardWrapper>
                <Grid container spacing={2} sx={{ alignItems: 'center', justifyContent: 'center' }}>
                  <Grid sx={{ mb: 3 }}>
                    <Link href="/">
                      <Logo />
                    </Link>
                  </Grid>
                  <Grid size={12}>
                    <Grid container direction="column" sx={{ alignItems: 'center', justifyContent: 'center' }}>
                      <Typography gutterBottom variant="h5" sx={{ color: 'secondary.main' }}>
                        Quên mật khẩu?
                      </Typography>
                      <Typography variant="caption" sx={{ fontSize: '16px', textAlign: 'center', mb: 2 }}>
                        {isSubmitted
                          ? "Chúng tôi đã gửi link đặt lại mật khẩu đến email của bạn."
                          : "Nhập email của bạn và chúng tôi sẽ gửi link đặt lại mật khẩu."
                        }
                      </Typography>
                    </Grid>
                  </Grid>

                  {!isSubmitted ? (
                    <Grid size={12}>
                      <Formik
                        initialValues={{ email: "" }}
                        validationSchema={Yup.object().shape({
                          email: Yup.string()
                            .email("Email không hợp lệ")
                            .max(255)
                            .required("Email là bắt buộc"),
                        })}
                        onSubmit={async (values, { setErrors, setStatus, setSubmitting }) => {
                          try {
                            await getCsrfToken();

                            const response = await axios.post(apiUrl("/api/forgot-password"), values);

                            if (response.success) {
                              setIsSubmitted(true);
                              toast.success(response.message, {position: 'top-right'});
                            } else {
                              toast.error(response.message, {position: 'top-right'});
                            }
                          } catch (error) {
                            if (error.status === 422) {
                              setErrors(error.errors || {});
                            } else {
                              toast.error(error.message || "Có lỗi xảy ra. Vui lòng thử lại.", {position: 'top-right'});
                            }
                            setStatus({ success: false });
                          } finally {
                            setSubmitting(false);
                          }
                        }}
                      >
                        {({ errors, handleBlur, handleChange, handleSubmit, isSubmitting, touched, values }) => (
                          <form noValidate onSubmit={handleSubmit}>
                            <FormControl
                              fullWidth
                              error={Boolean(touched.email && errors.email)}
                              sx={{ ...theme.typography.customInput }}
                            >
                              <InputLabel htmlFor="outlined-adornment-email-forgot">
                                Email
                              </InputLabel>
                              <OutlinedInput
                                id="outlined-adornment-email-forgot"
                                type="email"
                                value={values.email}
                                name="email"
                                onBlur={handleBlur}
                                onChange={handleChange}
                                label="Email"
                                disabled={isSubmitting}
                              />
                              {touched.email && errors.email && (
                                <FormHelperText error>
                                  {errors.email}
                                </FormHelperText>
                              )}
                            </FormControl>

                            <Box sx={{ mt: 2 }}>
                              <AnimateButton>
                                <Button
                                  disableElevation
                                  disabled={isSubmitting}
                                  fullWidth
                                  size="large"
                                  type="submit"
                                  variant="contained"
                                  color="secondary"
                                >
                                  Gửi link đặt lại mật khẩu
                                </Button>
                              </AnimateButton>
                            </Box>
                          </form>
                        )}
                      </Formik>
                    </Grid>
                  ) : (
                    <Grid size={12}>
                      <Box sx={{ textAlign: 'center' }}>
                        <Button
                          component={Link}
                          href="/login"
                          variant="outlined"
                          color="secondary"
                          sx={{ mt: 2 }}
                        >
                          <i className="bi bi-arrow-left me-2"></i>
                          Quay lại đăng nhập
                        </Button>
                      </Box>
                    </Grid>
                  )}

                  {!isSubmitted && (
                    <Grid size={12}>
                      <Grid container direction="column" sx={{ alignItems: 'center' }}>
                        <Link href="/login">
                          ← Quay lại đăng nhập
                        </Link>
                      </Grid>
                    </Grid>
                  )}
                </Grid>
              </AuthCardWrapper>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </AuthWrapper>
  );
};

export default AuthForgotPassword;
