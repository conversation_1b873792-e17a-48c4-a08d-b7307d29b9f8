// eslint-disable-next-line react-hooks/exhaustive-deps
"use client";

import { useEffect, useState, useCallback } from "react";
import Link from "next/link";
import Image from "next/image";
import toast from "react-hot-toast";

// material-ui
import { useTheme } from "@mui/material/styles";
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import Checkbox from "@mui/material/Checkbox";
import Divider from "@mui/material/Divider";
import FormControl from "@mui/material/FormControl";
import FormControlLabel from "@mui/material/FormControlLabel";
import FormHelperText from "@mui/material/FormHelperText";
import Grid from "@mui/material/Grid";
import IconButton from "@mui/material/IconButton";
import InputAdornment from "@mui/material/InputAdornment";
import InputLabel from "@mui/material/InputLabel";
import OutlinedInput from "@mui/material/OutlinedInput";
import Typography from "@mui/material/Typography";
import Stack from "@mui/material/Stack";

import CircularProgress from "@mui/material/CircularProgress";

import { useDispatch, useSelector } from "react-redux";
import { fetchUser, login, logout, clearUser } from "@/slices/authSlice";
import { clearNoti } from '@/slices/notiSlice';
import { useRouter, useSearchParams } from "next/navigation";

// third party
import * as Yup from "yup";
import { Formik } from "formik";

// project imports
import AnimateButton from "../extended/AnimateButton";

// assets
import Visibility from "@mui/icons-material/Visibility";
import VisibilityOff from "@mui/icons-material/VisibilityOff";
import { fetchGoogleUrl } from "@/actions/userAction";

import AuthWrapper from "@/components/authentication/AuthWrapper";
import AuthCardWrapper from "@/components/authentication/AuthCardWrapper";
import Logo from "@/components/Logo";

import localStorageHelper from "@/utils/localStorageHelper";
import {keyStorageIsLoginGoogle} from "@/constant";

// ============================|| FIREBASE - LOGIN ||============================ //

const AuthLogin = ({ ...others }) => {
  console.log("AuthLogin");
  const theme = useTheme();
  const dispatch = useDispatch();
  const router = useRouter();
  const searchParams = useSearchParams();
  const customization = useSelector((state) => state.customization);

  const [showPassword, setShowPassword] = useState(false);
  const [checked, setChecked] = useState(true);

  const action = searchParams.get("action");
  const backUrl = searchParams.get("backUrl") || "/dashboard";

  const handleLogout = async () => {
    try {
      await dispatch(logout()).unwrap();
    } catch (error) {
      console.error("Error during logout:", error);
      dispatch(clearUser());
    } finally {
      router.push(`/login?backUrl=${encodeURIComponent(backUrl)}`);
    }
  };

  const handleCheckUser = async () => {
    try {
      const fetchedUser = await dispatch(fetchUser({ refreshToken: true })).unwrap();
      if (fetchedUser?.id) {
        router.push(backUrl);
      } else {
        throw new Error("Không có dữ liệu người dùng.");
      }
    } catch (error) {
      console.error("Error during checkUser:", error);
      handleLogout();
    }
  };

  useEffect(() => {
    dispatch(clearNoti());

    if (action == 0) {
      handleLogout();
    }

    if (action == 1) { // không có cookie token
      handleCheckUser();
    }
  }, [action]);

  const googleHandler = async () => {
    localStorageHelper.set(keyStorageIsLoginGoogle, true);

    try {
      const response = await fetchGoogleUrl(backUrl);
      if (response.url) {
        window.location.href = response.url;
      } else {
        toast.error("Không thể lấy được đường dẫn đăng nhập Google.");
      }
    } catch (error) {
      toast.error("Đã xảy ra lỗi khi đăng nhập Google. Vui lòng thử lại.");
    }
  };

  const handleClickShowPassword = () => setShowPassword((prev) => !prev);

  const handleMouseDownPassword = (event) => {
    event.preventDefault();
  };

  return (
    <AuthWrapper>
      <Grid container direction="column" sx={{ justifyContent: 'flex-end', minHeight: '100vh' }}>
        <Grid size={12}>
          <Grid container sx={{ justifyContent: 'center', minHeight: 'calc(100vh - 10px)' }}>
            <Grid sx={{ m: { xs: 1, sm: 3 }, mb: 0 }}>
              <AuthCardWrapper>
                <Grid container spacing={2} sx={{ alignItems: 'center', justifyContent: 'center' }}>
                  <Grid sx={{ mb: 3 }}>
                    <Link href="/">
                      <Logo />
                    </Link>
                  </Grid>
                  <Grid size={12}>
                    <Grid container direction={{ xs: 'column-reverse', md: 'row' }} sx={{ alignItems: 'center', justifyContent: 'center' }}>
                      <Grid>
                        <Stack spacing={1} sx={{ alignItems: 'center', justifyContent: 'center' }}>
                          <Typography gutterBottom variant="h5" sx={{ color: 'secondary.main' }}>
                            Chào mừng bạn!
                          </Typography>
                          <Typography variant="caption" sx={{ fontSize: '16px', textAlign: { xs: 'center', md: 'inherit' } }}>
                            Đăng nhập để bắt đầu sử dụng dịch vụ của chúng tôi.
                          </Typography>
                        </Stack>
                      </Grid>
                    </Grid>
                  </Grid>
                  <Grid size={12}>
                    <Grid container direction="column" spacing={2} sx={{ justifyContent: 'center' }}>
                      <Grid>
                        <AnimateButton>
                          <Button
                            disableElevation
                            fullWidth
                            onClick={googleHandler}
                            size="large"
                            variant="outlined"
                            sx={{
                              color: "grey.700",
                              backgroundColor: theme.palette.grey[50],
                              borderColor: theme.palette.grey[100],
                            }}
                          >
                            <Box sx={{ mr: { xs: 1, sm: 2, width: 20 } }}>
                              <Image
                                src="/assets/images/icons/social-google.svg"
                                width={16}
                                height={16}
                                alt="Picture of the author"
                                style={{ marginRight: 8 }}
                              />
                            </Box>
                            Đăng nhập bằng Google
                          </Button>
                        </AnimateButton>
                      </Grid>
                      <Grid>
                        <Box
                          sx={{
                            alignItems: "center",
                            display: "flex",
                          }}
                        >
                          <Divider sx={{ flexGrow: 1 }} orientation="horizontal" />

                          <Button
                            variant="outlined"
                            sx={{
                              cursor: "unset",
                              m: 2,
                              py: 0.5,
                              px: 7,
                              borderColor: `${theme.palette.grey[100]} !important`,
                              color: `${theme.palette.grey[900]}!important`,
                              fontWeight: 500,
                              borderRadius: `${customization.borderRadius}px`,
                            }}
                            disableRipple
                            disabled
                          >
                            hoặc Email
                          </Button>

                          <Divider sx={{ flexGrow: 1 }} orientation="horizontal" />
                        </Box>
                      </Grid>
                    </Grid>
                    <Formik
                      initialValues={{
                        email: "",
                        password: "",
                      }}
                      validationSchema={Yup.object().shape({
                        email: Yup.string()
                          .email("Must be a valid email")
                          .max(255)
                          .required("Email is required"),
                        password: Yup.string().max(255).required("Password is required"),
                      })}
                      onSubmit={async (values, { setErrors, setStatus, setSubmitting }) => {
                        router.refresh();

                        dispatch(login(values))
                          .unwrap()
                          .then(() => {
                            router.push(backUrl);
                          })
                          .catch((err) => {
                            if (err.message == 'Network Error') {
                              return router.replace(`/login?action=1&backUrl=${encodeURIComponent(backUrl)}`);
                            }

                            setErrors(err);
                            setStatus({ success: false });
                          })
                          .finally(() => {
                            setSubmitting(false);
                          });
                      }}
                    >
                      {({
                        errors,
                        handleBlur,
                        handleChange,
                        handleSubmit,
                        isSubmitting,
                        touched,
                        values,
                      }) => (
                        <form noValidate onSubmit={handleSubmit}>
                          <FormControl
                            fullWidth
                            error={Boolean(touched.email && errors.email)}
                            sx={{ ...theme.typography.customInput }}
                          >
                            <InputLabel htmlFor="outlined-adornment-email-login">
                              Email
                            </InputLabel>
                            <OutlinedInput
                              id="outlined-adornment-email-login"
                              type="email"
                              value={values.email}
                              name="email"
                              autoComplete="email"
                              onBlur={handleBlur}
                              onChange={handleChange}
                              label="Email"
                              disabled={isSubmitting}
                            />
                            {touched.email && errors.email && (
                              <FormHelperText
                                error
                                id="standard-weight-helper-text-email-login"
                              >
                                {errors.email}
                              </FormHelperText>
                            )}
                          </FormControl>

                          <FormControl
                            fullWidth
                            error={Boolean(touched.password && errors.password)}
                            sx={{ ...theme.typography.customInput }}
                          >
                            <InputLabel htmlFor="outlined-adornment-password-login">
                              Password
                            </InputLabel>
                            <OutlinedInput
                              id="outlined-adornment-password-login"
                              type={showPassword ? "text" : "password"}
                              value={values.password}
                              name="password"
                              autoComplete="password"
                              onBlur={handleBlur}
                              onChange={handleChange}
                              endAdornment={
                                <InputAdornment position="end">
                                  <IconButton
                                    aria-label="toggle password visibility"
                                    onClick={handleClickShowPassword}
                                    onMouseDown={handleMouseDownPassword}
                                    edge="end"
                                    size="large"
                                  >
                                    {showPassword ? <Visibility /> : <VisibilityOff />}
                                  </IconButton>
                                </InputAdornment>
                              }
                              label="Password"
                              disabled={isSubmitting}
                            />
                            {touched.password && errors.password && (
                              <FormHelperText
                                error
                                id="standard-weight-helper-text-password-login"
                              >
                                {errors.password}
                              </FormHelperText>
                            )}
                          </FormControl>
                          <Stack
                            direction="row"
                            sx={{ alignItems: 'center', justifyContent: 'space-between' }}
                            spacing={1}
                          >
                            <FormControlLabel
                              control={
                                <Checkbox
                                  checked={checked}
                                  onChange={(event) => setChecked(event.target.checked)}
                                  name="checked"
                                  color="primary"
                                />
                              }
                              label="Remember me"
                            />
                            <Typography
                              variant="subtitle1"
                              color="secondary"
                              sx={{ textDecoration: "none", cursor: "pointer" }}
                              component={Link}
                              href="/forgot-password"
                            >
                              Forgot Password?
                            </Typography>
                          </Stack>
                          {errors.message && (
                            <Box sx={{ mt: 3 }}>
                              <FormHelperText error>{errors.message}</FormHelperText>
                            </Box>
                          )}
                          <Box sx={{ mt: 2 }}>
                            <AnimateButton>
                              <Button
                                disableElevation
                                disabled={isSubmitting || action == 1}
                                fullWidth
                                size="large"
                                type="submit"
                                variant="contained"
                                color="secondary"
                                loading={isSubmitting}
                                loadingPosition="start"
                              >
                                Đăng nhập
                              </Button>
                            </AnimateButton>
                          </Box>
                        </form>
                      )}
                    </Formik>
                  </Grid>
                  <Grid size={12}>
                    <Divider />
                  </Grid>
                  <Grid size={12}>
                    <Grid container direction="column" sx={{ alignItems: 'center' }} size={12}>
                      <Link href="/register">
                        <strong>Bạn chưa có tài khoản?</strong> Đăng ký tài
                        khoản tại đây.
                      </Link>
                    </Grid>
                  </Grid>
                </Grid>
              </AuthCardWrapper>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </AuthWrapper>
  );
};

export default AuthLogin;
