"use client";

import Link from "next/link";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";

// material-ui
import { useTheme } from "@mui/material/styles";
import Stack from "@mui/material/Stack";
import AuthWrapper from "@/components/authentication/AuthWrapper";
import AuthCardWrapper from "@/components/authentication/AuthCardWrapper";
import Logo from "@/components/Logo";
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import Divider from "@mui/material/Divider";
import FormControl from "@mui/material/FormControl";
import FormHelperText from "@mui/material/FormHelperText";
import Grid from "@mui/material/Grid";
import IconButton from "@mui/material/IconButton";
import InputAdornment from "@mui/material/InputAdornment";
import InputLabel from "@mui/material/InputLabel";
import OutlinedInput from "@mui/material/OutlinedInput";
import Typography from "@mui/material/Typography";

import { register } from "@/slices/authSlice";

// third party
import * as Yup from "yup";
import { Formik } from "formik";
import toast from "react-hot-toast";

// project imports
import AnimateButton from "@/components/extended/AnimateButton";
import { strengthColor, strengthIndicator } from "@/utils/password-strength";

// assets
import Visibility from "@mui/icons-material/Visibility";
import VisibilityOff from "@mui/icons-material/VisibilityOff";
import {fetchGoogleUrl} from "@/actions/userAction";

// ===========================|| FIREBASE - REGISTER ||=========================== //

const AuthRegister = ({ ...others }) => {
  const theme = useTheme();
  const dispatch = useDispatch();
  const router = useRouter();
  const customization = useSelector((state) => state.customization);
  const [showPassword, setShowPassword] = useState(false);

  const [strength, setStrength] = useState(0);
  const [level, setLevel] = useState();

  const googleHandler = async () => {
    try {
      const response = await fetchGoogleUrl();
      if (response.url) {
        window.location.href = response.url;
      } else {
        toast.error("Đã xảy ra lỗi. Vui lòng thử lại");
      }
    } catch (error) {
      toast.error("Đã xảy ra lỗi. Vui lòng thử lại");
    }
  };

  const handleClickShowPassword = () => {
    setShowPassword(!showPassword);
  };

  const handleMouseDownPassword = (event) => {
    event.preventDefault();
  };

  const changePassword = (value) => {
    const temp = strengthIndicator(value);
    setStrength(temp);
    setLevel(strengthColor(temp));
  };

  useEffect(() => {
    changePassword("123456");
  }, []);

  return (
    <AuthWrapper>
      <Grid container direction="column" sx={{ justifyContent: 'flex-end', minHeight: '100vh' }}>
        <Grid size={12}>
          <Grid container sx={{ justifyContent: 'center', minHeight: 'calc(100vh - 10px)' }}>
            <Grid sx={{ m: { xs: 1, sm: 3 }, mb: 0 }}>
              <AuthCardWrapper>
                <Grid container spacing={2} sx={{ alignItems: 'center', justifyContent: 'center' }}>
                  <Grid sx={{ mb: 3 }}>
                    <Link href="/">
                      <Logo />
                    </Link>
                  </Grid>
                   <Grid size={12}>
                    <Grid container direction={{ xs: 'column-reverse', md: 'row' }} sx={{ alignItems: 'center', justifyContent: 'center' }}>
                      <Grid>
                        <Stack
                          sx={{ alignItems: 'center', justifyContent: 'center' }}
                          spacing={1}
                        >
                          {/* <Typography
                            color="secondary.main"
                            gutterBottom
                            variant="h4"
                          >
                            Đăng ký
                          </Typography> */}
                          {/* <Typography
                            variant="caption"
                            fontSize="16px"
                            textAlign={{ xs: "center" }}
                          >
                            Bằng cách đăng ký, bạn đã đồng ý với Điều khoản sử
                            dụng của chúng tôi.
                          </Typography> */}
                        </Stack>
                      </Grid>
                    </Grid>
                  </Grid>
                  <Grid size={12}>
                    <Grid container direction="column" spacing={2} sx={{ justifyContent: 'center' }}>
                      <Grid>
                        <AnimateButton>
                          <Button
                            variant="outlined"
                            fullWidth
                            onClick={googleHandler}
                            size="large"
                            sx={{
                              color: "grey.700",
                              backgroundColor: theme.palette.grey[50],
                              borderColor: theme.palette.grey[100],
                            }}
                          >
                            <Box sx={{ mr: { xs: 1, sm: 2, width: 20 } }}>
                              <Image
                                src="/assets/images/icons/social-google.svg"
                                width={16}
                                height={16}
                                alt="Picture of the author"
                                style={{ marginRight: 8 }}
                              />
                            </Box>
                            Đăng nhập bằng Google
                          </Button>
                        </AnimateButton>
                      </Grid>
                      <Grid>
                        <Box sx={{ alignItems: "center", display: "flex" }}>
                          <Divider sx={{ flexGrow: 1 }} orientation="horizontal" />
                          <Button
                            variant="outlined"
                            sx={{
                              cursor: "unset",
                              m: 2,
                              py: 0.5,
                              px: 7,
                              borderColor: `${theme.palette.grey[100]} !important`,
                              color: `${theme.palette.grey[900]}!important`,
                              fontWeight: 500,
                              borderRadius: `${customization.borderRadius}px`,
                            }}
                            disableRipple
                            disabled
                          >
                            hoặc Email
                          </Button>
                          <Divider sx={{ flexGrow: 1 }} orientation="horizontal" />
                        </Box>
                      </Grid>
                    </Grid>

                    <Formik
                      initialValues={{
                        name: "",
                        email: "",
                        password: "",
                        password_confirmation: "",
                      }}
                      validationSchema={Yup.object().shape({
                        name: Yup.string().min(3).max(30).required("Name is required"),
                        email: Yup.string()
                          .email("Must be a valid email")
                          .max(255)
                          .required("Email is required"),
                        password: Yup.string().max(255).required("Password is required"),
                        password_confirmation: Yup.string()
                          .required("Confirm password is required")
                          .oneOf([Yup.ref("password"), null], "Passwords must match"),
                      })}
                      onSubmit={async (values, { setErrors, setStatus, setSubmitting }) => {
                        router.refresh();

                        dispatch(register(values))
                          .unwrap()
                          .then(() => {
                            router.push("/");
                          })
                          .catch((err) => {
                            setErrors(err);
                            setStatus({ success: false });
                          })
                          .finally(() => {
                            setSubmitting(false);
                          });
                      }}
                    >
                      {({
                        errors,
                        handleBlur,
                        handleChange,
                        handleSubmit,
                        isSubmitting,
                        touched,
                        values,
                      }) => {
                        return (
                          <form noValidate onSubmit={handleSubmit} {...others}>
                            <FormControl
                              fullWidth
                              error={Boolean(touched.name && errors.name)}
                              sx={{ ...theme.typography.customInput }}
                            >
                              <InputLabel htmlFor="outlined-adornment-name-register">
                                Họ và Tên
                              </InputLabel>
                              <OutlinedInput
                                id="outlined-adornment-name-register"
                                type="text"
                                value={values.name}
                                name="name"
                                onBlur={handleBlur}
                                onChange={handleChange}
                                inputProps={{}}
                              />
                              {touched.name && errors.name && (
                                <FormHelperText
                                  error
                                  id="standard-weight-helper-text-name-register"
                                >
                                  {errors.name}
                                </FormHelperText>
                              )}
                            </FormControl>
                            <FormControl
                              fullWidth
                              error={Boolean(touched.email && errors.email)}
                              sx={{ ...theme.typography.customInput }}
                            >
                              <InputLabel htmlFor="outlined-adornment-email-register">
                                Email
                              </InputLabel>
                              <OutlinedInput
                                id="outlined-adornment-email-register"
                                type="email"
                                value={values.email}
                                name="email"
                                onBlur={handleBlur}
                                onChange={handleChange}
                              />
                              {touched.email && errors.email && (
                                <FormHelperText
                                  error
                                  id="standard-weight-helper-text-email-register"
                                >
                                  {errors.email}
                                </FormHelperText>
                              )}
                            </FormControl>

                            <FormControl
                              fullWidth
                              error={Boolean(touched.password && errors.password)}
                              sx={{ ...theme.typography.customInput }}
                            >
                              <InputLabel htmlFor="outlined-adornment-password-register">
                                Password
                              </InputLabel>
                              <OutlinedInput
                                id="outlined-adornment-password-register"
                                type={showPassword ? "text" : "password"}
                                value={values.password}
                                name="password"
                                label="Password"
                                onBlur={handleBlur}
                                onChange={(e) => {
                                  handleChange(e);
                                  changePassword(e.target.value);
                                }}
                                endAdornment={
                                  <InputAdornment position="end">
                                    <IconButton
                                      aria-label="toggle password visibility"
                                      onClick={handleClickShowPassword}
                                      onMouseDown={handleMouseDownPassword}
                                      edge="end"
                                      size="large"
                                    >
                                      {showPassword ? <Visibility /> : <VisibilityOff />}
                                    </IconButton>
                                  </InputAdornment>
                                }
                              />
                              {touched.password && errors.password && (
                                <FormHelperText
                                  error
                                  id="standard-weight-helper-text-password-register"
                                >
                                  {errors.password}
                                </FormHelperText>
                              )}
                            </FormControl>

                            {strength !== 0 && (
                              <FormControl fullWidth>
                                <Box sx={{ mb: 2 }}>
                                  <Grid container spacing={2} alignItems="center">
                                    <Grid item>
                                      <Box
                                        style={{ backgroundColor: level?.color }}
                                        sx={{ width: 85, height: 8, borderRadius: "7px" }}
                                      />
                                    </Grid>
                                    <Grid item>
                                      <Typography variant="subtitle1" fontSize="0.75rem">
                                        {level?.label}
                                      </Typography>
                                    </Grid>
                                  </Grid>
                                </Box>
                              </FormControl>
                            )}

                            <FormControl
                              fullWidth
                              error={Boolean(
                                touched.password_confirmation && errors.password_confirmation
                              )}
                              sx={{ ...theme.typography.customInput }}
                            >
                              <InputLabel htmlFor="outlined-adornment-password_confirmation-login">
                                Password confirmation
                              </InputLabel>
                              <OutlinedInput
                                id="outlined-adornment-password_confirmation-login"
                                type={showPassword ? "text" : "password"}
                                value={values.password_confirmation}
                                name="password_confirmation"
                                onBlur={handleBlur}
                                onChange={handleChange}
                              />
                              {touched.password_confirmation &&
                                errors.password_confirmation && (
                                  <FormHelperText
                                    error
                                    id="standard-weight-helper-text-password_confirmation-login"
                                  >
                                    {errors.password_confirmation}
                                  </FormHelperText>
                                )}
                            </FormControl>
                            {errors.message && (
                              <Box sx={{ mt: 3 }}>
                                <FormHelperText error>{errors.message}</FormHelperText>
                              </Box>
                            )}
                            <Box sx={{ mt: 2 }}>
                              <AnimateButton>
                                <Button
                                  disableElevation
                                  disabled={isSubmitting}
                                  fullWidth
                                  size="large"
                                  type="submit"
                                  variant="contained"
                                  color="secondary"
                                >
                                  Đăng ký
                                </Button>
                              </AnimateButton>
                            </Box>
                          </form>
                        );
                      }}
                    </Formik>
                  </Grid>
                  <Grid size={12}>
                    <Divider />
                  </Grid>
                  <Grid size={12}>
                    <Grid container direction="column" sx={{ alignItems: 'center' }} size={12}>
                      <Link href="/login">
                        <strong>Bạn đã có tài khoản?</strong> Đăng nhập ngay.
                      </Link>
                    </Grid>
                  </Grid>
                </Grid>
              </AuthCardWrapper>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </AuthWrapper>
  );
};

export default AuthRegister;
