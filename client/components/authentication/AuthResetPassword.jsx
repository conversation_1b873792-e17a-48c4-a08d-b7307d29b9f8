"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import toast from "react-hot-toast";

// material-ui
import { useTheme } from "@mui/material/styles";
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import FormControl from "@mui/material/FormControl";
import FormHelperText from "@mui/material/FormHelperText";
import Grid from "@mui/material/Grid";
import IconButton from "@mui/material/IconButton";
import InputAdornment from "@mui/material/InputAdornment";
import InputLabel from "@mui/material/InputLabel";
import OutlinedInput from "@mui/material/OutlinedInput";
import Typography from "@mui/material/Typography";

// third party
import * as Yup from "yup";
import { Formik } from "formik";

// project imports
import AuthWrapper from "@/components/authentication/AuthWrapper";
import AuthCardWrapper from "@/components/authentication/AuthCardWrapper";
import AnimateButton from "@/components/extended/AnimateButton";
import Logo from "@/components/Logo";

// assets
import Visibility from "@mui/icons-material/Visibility";
import VisibilityOff from "@mui/icons-material/VisibilityOff";

import { axios, getCsrfToken } from "@/lib/axios";
import { apiUrl } from "@/lib/fetcher";

const AuthResetPassword = ({ params }) => {
  const theme = useTheme();
  const router = useRouter();
  const searchParams = useSearchParams();

  const [token, setToken] = useState("");
  const [email, setEmail] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  useEffect(() => {
    const getParams = async () => {
      const resolvedParams = await params;
      setToken(resolvedParams.token || "");
      setEmail(searchParams.get("email") || "");
    };
    getParams();
  }, [params, searchParams]);

  const handleClickShowPassword = () => {
    setShowPassword(!showPassword);
  };

  const handleClickShowConfirmPassword = () => {
    setShowConfirmPassword(!showConfirmPassword);
  };

  const handleMouseDownPassword = (event) => {
    event.preventDefault();
  };

  return (
    <AuthWrapper>
      <Grid container direction="column" sx={{ justifyContent: 'flex-end', minHeight: '100vh' }}>
        <Grid size={12}>
          <Grid container sx={{ justifyContent: 'center', minHeight: 'calc(100vh - 10px)' }}>
            <Grid sx={{ m: { xs: 1, sm: 3 }, mb: 0 }}>
              <AuthCardWrapper>
                <Grid container spacing={2} sx={{ alignItems: 'center', justifyContent: 'center' }}>
                  <Grid sx={{ mb: 3 }}>
                    <Link href="/">
                      <Logo />
                    </Link>
                  </Grid>
                  <Grid size={12}>
                    <Grid container direction="column" sx={{ alignItems: 'center', justifyContent: 'center' }}>
                      <Typography gutterBottom variant="h5" sx={{ color: 'secondary.main' }}>
                        Đặt lại mật khẩu
                      </Typography>
                      <Typography variant="caption" sx={{ fontSize: '16px', textAlign: 'center', mb: 2 }}>
                        Nhập mật khẩu mới cho tài khoản của bạn.
                      </Typography>
                    </Grid>
                  </Grid>

                  <Grid size={12}>
                    <Formik
                      initialValues={{
                        email: email,
                        password: "",
                        password_confirmation: "",
                      }}
                      enableReinitialize={true}
                      validationSchema={Yup.object().shape({
                        email: Yup.string()
                          .email("Email không hợp lệ")
                          .max(255)
                          .required("Email là bắt buộc"),
                        password: Yup.string()
                          .min(8, "Mật khẩu phải có ít nhất 8 ký tự")
                          .max(255)
                          .required("Mật khẩu là bắt buộc"),
                        password_confirmation: Yup.string()
                          .oneOf([Yup.ref('password'), null], "Xác nhận mật khẩu không khớp")
                          .required("Xác nhận mật khẩu là bắt buộc"),
                      })}
                      onSubmit={async (values, { setErrors, setStatus, setSubmitting }) => {
                        try {
                          await getCsrfToken();

                          const response = await axios.post(apiUrl("/api/reset-password"), {
                            token: token,
                            email: values.email,
                            password: values.password,
                            password_confirmation: values.password_confirmation,
                          });

                          if (response.success) {
                            toast.success(response.message, {position: "top-right"});
                            setTimeout(() => {
                              router.push("/login?reset=success");
                            }, 1500);
                          } else {
                            toast.error(response.message, {position: "top-right"});
                          }
                        } catch (error) {
                          if (error.status === 422) {
                            setErrors(error.errors || {});
                          } else {
                            toast.error(error.message || "Có lỗi xảy ra. Vui lòng thử lại.", {position: "top-right"});
                          }
                          setStatus({ success: false });
                        } finally {
                          setSubmitting(false);
                        }
                      }}
                    >
                      {({ errors, handleBlur, handleChange, handleSubmit, isSubmitting, touched, values }) => (
                        <form noValidate onSubmit={handleSubmit}>
                          <FormControl
                            fullWidth
                            error={Boolean(touched.email && errors.email)}
                            sx={{ ...theme.typography.customInput }}
                          >
                            <InputLabel htmlFor="outlined-adornment-email-reset">
                              Email
                            </InputLabel>
                            <OutlinedInput
                              id="outlined-adornment-email-reset"
                              type="email"
                              value={values.email}
                              name="email"
                              onBlur={handleBlur}
                              onChange={handleChange}
                              label="Email"
                              disabled={isSubmitting}
                              readOnly={!!email}
                            />
                            {touched.email && errors.email && (
                              <FormHelperText error>
                                {errors.email}
                              </FormHelperText>
                            )}
                          </FormControl>

                          <FormControl
                            fullWidth
                            error={Boolean(touched.password && errors.password)}
                            sx={{ ...theme.typography.customInput }}
                          >
                            <InputLabel htmlFor="outlined-adornment-password-reset">
                              Mật khẩu mới
                            </InputLabel>
                            <OutlinedInput
                              id="outlined-adornment-password-reset"
                              type={showPassword ? "text" : "password"}
                              value={values.password}
                              name="password"
                              onBlur={handleBlur}
                              onChange={handleChange}
                              endAdornment={
                                <InputAdornment position="end">
                                  <IconButton
                                    aria-label="toggle password visibility"
                                    onClick={handleClickShowPassword}
                                    onMouseDown={handleMouseDownPassword}
                                    edge="end"
                                    size="large"
                                  >
                                    {showPassword ? <Visibility /> : <VisibilityOff />}
                                  </IconButton>
                                </InputAdornment>
                              }
                              label="Mật khẩu mới"
                              disabled={isSubmitting}
                            />
                            {touched.password && errors.password && (
                              <FormHelperText error>
                                {errors.password}
                              </FormHelperText>
                            )}
                          </FormControl>

                          <FormControl
                            fullWidth
                            error={Boolean(touched.password_confirmation && errors.password_confirmation)}
                            sx={{ ...theme.typography.customInput }}
                          >
                            <InputLabel htmlFor="outlined-adornment-password-confirm-reset">
                              Xác nhận mật khẩu
                            </InputLabel>
                            <OutlinedInput
                              id="outlined-adornment-password-confirm-reset"
                              type={showConfirmPassword ? "text" : "password"}
                              value={values.password_confirmation}
                              name="password_confirmation"
                              onBlur={handleBlur}
                              onChange={handleChange}
                              endAdornment={
                                <InputAdornment position="end">
                                  <IconButton
                                    aria-label="toggle confirm password visibility"
                                    onClick={handleClickShowConfirmPassword}
                                    onMouseDown={handleMouseDownPassword}
                                    edge="end"
                                    size="large"
                                  >
                                    {showConfirmPassword ? <Visibility /> : <VisibilityOff />}
                                  </IconButton>
                                </InputAdornment>
                              }
                              label="Xác nhận mật khẩu"
                              disabled={isSubmitting}
                            />
                            {touched.password_confirmation && errors.password_confirmation && (
                              <FormHelperText error>
                                {errors.password_confirmation}
                              </FormHelperText>
                            )}
                          </FormControl>

                          <Box sx={{ mt: 2 }}>
                            <AnimateButton>
                              <Button
                                disableElevation
                                disabled={isSubmitting || !token}
                                fullWidth
                                size="large"
                                type="submit"
                                variant="contained"
                                color="secondary"
                              >
                                {isSubmitting ? "Đang xử lý..." : "Đặt lại mật khẩu"}
                              </Button>
                            </AnimateButton>
                          </Box>
                        </form>
                      )}
                    </Formik>
                  </Grid>

                  <Grid size={12}>
                    <Grid container direction="column" sx={{ alignItems: 'center' }}>
                      <Link href="/login">
                        ← Quay lại đăng nhập
                      </Link>
                    </Grid>
                  </Grid>
                </Grid>
              </AuthCardWrapper>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </AuthWrapper>
  );
};

export default AuthResetPassword;