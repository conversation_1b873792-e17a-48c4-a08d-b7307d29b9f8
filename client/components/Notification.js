"use client";

import React, { useMemo, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useSelector, useDispatch, shallowEqual } from "react-redux";
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import Button from '@mui/material/Button';
import RefreshIcon from '@mui/icons-material/Refresh';
import { styled } from '@mui/material/styles';

import { clearNoti } from '@/slices/notiSlice';
import { fetchUser } from "@/slices/authSlice";

const DialogCustom = styled(Dialog, {
  shouldForwardProp: (prop) => prop !== 'colorSatus'
})(({ theme, colorSatus }) => ({
  '& .MuiPaper-root': {
    top: '10%',
    margin: 0,
    position: 'absolute',
    borderTop: '3px solid #eee',
    borderColor: colorSatus || 'white',
    '& h2': {
      fontSize: '18px',
      fontWeight: '600',
    },
    '& .MuiDialogContent-root': {
      fontSize: '18px',
      lineHeight: '1.35',
      color: '#000',
    },
  },
}));

const Notification = () => {
  console.log('Notification');
  const router = useRouter();
  const dispatch = useDispatch();
  const { status, message } = useSelector((state) => state.noti, shallowEqual);

  const colorSatus = useMemo(() => {
    let color = '#fff';

    switch(status) {
      case 401:
      case 500:
      case 'error':
        color = '#F44336';
        break;
      case 200:
      case 'success':
        color = '#4CAF50';
        break;
      case 'warning':
        color = '#ffc107';
        break;
      case 'info':
        color = '#2196F3';
        break;
    }

    return color;
  }, [status]);

  const handleClose = useCallback((event, reason) => {
    dispatch(clearNoti());
  }, []);

  const handleCheckUser = async () => {
    try {
      const fetchedUser = await dispatch(fetchUser({ refreshToken: true })).unwrap();
      if (fetchedUser?.id) {
        router.refresh();
        return;
      }

      throw new Error("Không có dữ liệu người dùng.");
    } catch (error) {
      router.replace(`/api/auth/logout?backUrl=${encodeURIComponent(window?.location.href || '')}`);
    } finally {
      dispatch(clearNoti());
    }
  };

  const renderAction = useMemo(() => {
    let action = (
      <DialogActions sx={{ justifyContent: 'center' }}>
        <Button variant="contained" color="secondary" onClick={handleClose}>
          Đóng
        </Button>
      </DialogActions>
    );

    switch(status) {
      case 401:
        action = (
          <DialogActions sx={{ justifyContent: 'center' }}>
            <Button variant="contained" color="info" onClick={handleCheckUser}>
              <RefreshIcon /> Đã xảy ra lỗi. Thử lại
            </Button>
          </DialogActions>
        );
        break;
      case 500:
      case 'error':
        action = (
          <DialogActions sx={{ justifyContent: 'center' }}>
            <Button variant="contained" color="info" onClick={() => window?.location.reload()}>
              <RefreshIcon /> Đã xảy ra lỗi. Vui lòng tải lại trang
            </Button>
          </DialogActions>
        );
        break;
    }

    return action;
  }, [status]);

  if (!status) return null;

  return (
    <DialogCustom
      fullWidth
      maxWidth="xs"
      open={true}
      colorSatus={colorSatus}
      disableEscapeKeyDown
    >
      <DialogTitle className="h4">Ối! Đã xảy ra lỗi.</DialogTitle>
      <DialogContent>
        <p>Đã xảy ra sự cố ngoài dự kiến. Vui lòng thử tải lại trang.</p>
        <p>
          Nếu vấn đề vẫn còn, vui lòng chia sẻ ảnh chụp màn hình tới <EMAIL>
        </p>
        <p className="mb-1">Nguyên nhân:</p>
        <ul className="px-4">
          <li>{message || 'Lỗi hệ thống'}</li>
        </ul>
      </DialogContent>
      {renderAction}
    </DialogCustom>
  );
};

export default React.memo(Notification);
