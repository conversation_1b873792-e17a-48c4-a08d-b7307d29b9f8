import React, { useRef } from 'react';
import { useDispatch } from 'react-redux';
import { useTranslations } from 'next-intl';
import { Formik } from 'formik';
import * as Yup from 'yup';
import { toast } from 'react-hot-toast';
import { useRouter } from 'next/navigation';

import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import Button from '@mui/material/Button';
import TextField from '@mui/material/TextField';
import Slide from '@mui/material/Slide';
import FormControl from '@mui/material/FormControl';
import InputLabel from '@mui/material/InputLabel';
import FormHelperText from '@mui/material/FormHelperText';
import OutlinedInput from '@mui/material/OutlinedInput';
import Box from '@mui/material/Box';
import Autocomplete from '@mui/material/Autocomplete';

import { setNoti } from "@/slices/notiSlice";
import { fetchOptions } from '@/actions/onlyClientAction';
import { updateCourse, storeCourse } from '@/actions/courseAction';
import { DragDropCropImage } from "../DragDropCropImage";
import { isBase64 } from "@/utils/helpers";

const Transition = React.forwardRef(function Transition(props, ref) {
  return <Slide direction="up" ref={ref} {...props} />;
});

export default function DialogCourseForm({ open, onClose, course = {}, onSubmit, ...props }) {
  const dispatch = useDispatch();
  const t = useTranslations("Common");
  const router = useRouter();

  const { data: gradeAndSubject } = fetchOptions(
    open ? "grades,subjects" : null
  );

  const cropRef = useRef();

  return (
    <Dialog
      open={open}
      TransitionComponent={Transition}
      keepMounted
      fullWidth={true}
      maxWidth="md"
      onClose={onClose}
      {...props}
    >
      <DialogTitle className="h5">Cài đặt khóa học</DialogTitle>
      <DialogContent>
        <Formik
          initialValues={{
            title: course?.title || "",
            grade_id: course?.grade_id || "",
            subject_id: course?.subject_id || "",
          }}
          validationSchema={Yup.object({
            title: Yup.string().required("Vui lòng nhập tiêu đề"),
            grade_id: Yup.number().required("Vui lòng chọn lớp"),
            subject_id: Yup.number().required("Vui lòng chọn môn"),
          })}
          onSubmit={async (values, { setSubmitting, setErrors }) => {
            try {
              let banner_base64 = null;

              if (cropRef.current) {
                banner_base64 = cropRef.current.getImg();
              }

              let res;

              if (course?.id) {
                let newData = {
                  ...course,
                  ...values
                };

                if (isBase64(banner_base64)) {
                  newData.banner_base64 = banner_base64;
                } else if (banner_base64 === null) {
                  newData.remove_banner = 1;
                }

                res = await updateCourse(newData);
                toast.success("Cập nhật khóa học thành công");

                if (onSubmit) {
                  await onSubmit(res.data);
                }
              }

              onClose();
            } catch (err) {
              console.error(err);
              if (err?.status === 422) {
                setErrors(err.errors);
              } else {
                dispatch(setNoti(err.message || "Đã có lỗi xảy ra"));
              }
            } finally {
              setSubmitting(false);
            }
          }}
        >
          {({
            errors,
            handleBlur,
            handleChange,
            handleSubmit,
            isSubmitting,
            touched,
            values,
          }) => (
            <form noValidate onSubmit={handleSubmit}>
              <div className="row align-items-start">
                <div className="col-12 col-md-6">
                  <Box className="d-flex flex-column gap-4 py-3">
                    <FormControl
                      fullWidth
                      error={Boolean(touched.title && errors.title)}
                    >
                      <InputLabel htmlFor="outlined-adornment-title">
                        Tiêu đề
                      </InputLabel>
                      <OutlinedInput
                        id="outlined-adornment-title"
                        type="text"
                        label="Tiêu đề"
                        value={values.title}
                        name="title"
                        onBlur={handleBlur}
                        onChange={handleChange}
                        inputProps={{}}
                      />
                      {touched.title && errors.title && (
                        <FormHelperText
                          error
                          id="standard-weight-helper-text-title"
                        >
                          {errors.title}
                        </FormHelperText>
                      )}
                    </FormControl>

                    {gradeAndSubject &&
                      Object.keys(gradeAndSubject).map((key) => {
                        let key_name = "";

                        switch (key) {
                          case "subjects":
                            key_name = "subject_id";
                            break;
                          case "grades":
                            key_name = "grade_id";
                            break;
                          default:
                            key_name = key;
                        }

                        return (
                          <FormControl
                            key={key}
                            fullWidth
                            error={Boolean(
                              touched[key_name] && errors[key_name]
                            )}
                          >
                          <Autocomplete
                            id={`autocomplete-${key_name}-unique`}
                            options={gradeAndSubject[key]}
                            getOptionLabel={(option) => option.label}
                            noOptionsText="Không tìm thấy kết quả phù hợp"
                            value={values[key_name] ? gradeAndSubject[key].find((item => item.value === values[key_name])) : null}
                            onChange={(event, newValue) => {
                              handleChange({
                                target: {
                                  name: key_name,
                                  value: newValue ? newValue.value : "",
                                },
                              });
                            }}
                            renderInput={(params) => (
                              <TextField
                                {...params}
                                label={t(key)}
                              />
                            )}
                            />
                            {touched[key_name] && errors[key_name] && (
                              <FormHelperText error>
                                {errors[key_name]}
                              </FormHelperText>
                            )}
                          </FormControl>
                        );
                      })
                    }
                  </Box>
                </div>
                <div className="col-12 col-md-6">
                  {open && (
                    <DragDropCropImage
                      imgSrc={course?.banner || ""}
                      ref={cropRef}
                    />
                  )}
                </div>
              </div>
              <Box sx={{ mt: 2 }}>
                <Button
                  variant="outlined"
                  type="button"
                  onClick={onClose}
                  color="inherit"
                  size="small"
                  sx={{ mr: 2 }}
                >
                  Hủy
                </Button>
                <Button
                  disableElevation
                  disabled={isSubmitting}
                  size="small"
                  type="submit"
                  variant="contained"
                  color="secondary"
                >
                  {course?.id ? "Lưu" : "Tạo mới"}
                </Button>
              </Box>
            </form>
          )}
        </Formik>
      </DialogContent>
    </Dialog>
  );
}
