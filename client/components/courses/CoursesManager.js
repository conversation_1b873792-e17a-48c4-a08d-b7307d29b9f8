'use client'

import React, { useState, useEffect, useRef } from 'react';
import { useDispatch } from 'react-redux';
import { SortableTree } from '@/components/SortableTree/SortableTree';
import { fetchCourses, fetchCourseTocs, updatePositions, deleteCourse } from '@/actions/courseAction';
import { buildTree } from '@/components/SortableTree/utilities';
import Card from "@mui/material/Card";
import IconButton from "@mui/material/IconButton";
import MoreVertIcon from '@mui/icons-material/MoreVert';
import AppBar from '@mui/material/AppBar';
import Toolbar from '@mui/material/Toolbar';
import Box from '@mui/material/Box';
import Button from "@mui/material/Button";
import AddIcon from '@mui/icons-material/Add';
import InfiniteScroll from '@/components/InfiniteScroll';
import CircularProgress from '@mui/material/CircularProgress';
import Container from '@mui/material/Container';
import NextImage from '@/components/NextImage';
import { toast } from 'react-hot-toast';

import { flattenTree, removeItem } from '@/components/SortableTree/utilities';

import { updateQuizTitle } from '@/actions/quizAction';
import { updateTocTitle, deleteToc, createToc } from '@/actions/tocAction';
import { removeQuizFromFolder } from '@/actions/quizAction';

import Dropdown from "@/components/Dropdown";
import DialogTocForm from '@/components/courses/DialogTocForm';
import DialogQuizForm from '@/components/quizzes/DialogQuizForm';
import DialogCourseForm from './DialogCourseForm';

import { useConfirm } from "@/contexts/ConfirmContext";

export default function CoursesManager() {
  const dispatch = useDispatch();
  const confirmDelete = useConfirm();
  const containerRef = useRef(null);

  const [courses, setCourses] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedCourseId, setSelectedCourseId] = useState(null);
  const [courseContent, setCourseContent] = useState([]);
  const [contentLoading, setContentLoading] = useState(false);

  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);

  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  const [tocDialogOpen, setTocDialogOpen] = useState(false);
  const [currentParentToc, setCurrentParentToc] = useState(null);

  const [quizDialogOpen, setQuizDialogOpen] = useState(false);
  const [currentTocParent, setCurrentTocParent] = useState(null);

  const [courseDialogOpen, setCourseDialogOpen] = useState(false);

  useEffect(() => {
    loadCourses();
  }, []);

  useEffect(() => {
    if (selectedCourseId) {
      loadCourseTocs();
    }
  }, [selectedCourseId]);

  const loadCourses = async () => {
    try {
      setLoading(true);
      const response = await fetchCourses({
        page: 1,
        limit: 7,
        all: true
      });

      if (response && response.data) {
        setCourses(response.data);
        setHasMore(response.data.length === 7);

        if (response.data.length > 0 && !selectedCourseId) {
          const firstCourse = response.data[0];
          setSelectedCourseId(firstCourse.id);
        }
      }
    } catch (error) {
      toast.error("Không thể tải danh sách khóa học");
    } finally {
      setLoading(false);
    }
  };

  const loadCourseTocs = async () => {
    try {
      setContentLoading(true);
      const response = await fetchCourseTocs(selectedCourseId);

      if (response && response.data) {
        const treeData = buildTreeFromTocs(response.data);
        setCourseContent(treeData);
      }
    } catch (error) {
      toast.error("Không thể tải nội dung khóa học");
    } finally {
      setContentLoading(false);
    }
  };

  const fetchMore = async () => {
    if (loadingMore) return;

    try {
      setLoadingMore(true);
      const nextPage = page + 1;

      const response = await fetchCourses({
        page: nextPage,
        limit: 7,
        all: true
      });

      if (response && response.data) {
        setCourses(prev => [...prev, ...response.data]);
        setHasMore(response.data.length === 7);
        setPage(nextPage);
      }
    } catch (error) {
      toast.error("Không thể tải thêm khóa học");
    } finally {
      setLoadingMore(false);
    }
  };

  const handleItemsChanged = (items, reason) => {
    setCourseContent(items);

    if (reason.type === 'dropped') {
      setHasUnsavedChanges(true);
    }
  };

  const handleAddChapterClick = () => {
    setCurrentParentToc(null);
    setTocDialogOpen(true);
  };

  const handleAddSubToc = (parentToc) => {
    setCurrentParentToc(parentToc);
    setTocDialogOpen(true);
  };

  const handleTocFormSubmit = async ({ title, parentToc }) => {
    if (!selectedCourseId) return;

    try {
      const tocData = {
        title,
        course_id: selectedCourseId,
        toc_id: parentToc ? parentToc.id : null
      };

      const response = await createToc(tocData);

      if (response.data) {
        const newToc = {
          id: String(response.data.id),
          title: response.data.title,
          children: [],
          type: 0,
          status: response.data.status,
          index: response.data.index
        };

        setTocDialogOpen(false);

        let shouldScrollToElement = true;

        if (parentToc) {
          const updatedContent = [...courseContent];

          const findAndAddChild = (items) => {
            for (let i = 0; i < items.length; i++) {
              if (items[i].id === parentToc.id) {
                if (!items[i].children) {
                  items[i].children = [];
                }
                items[i].children.push(newToc);

                if (items[i].collapsed) {
                  items[i].collapsed = false;
                }

                return true;
              }

              if (items[i].children && items[i].children.length > 0) {
                if (findAndAddChild(items[i].children)) {
                  return true;
                }
              }
            }
            return false;
          };

          if (findAndAddChild(updatedContent)) {
            setCourseContent(updatedContent);
          } else {
            await loadCourseTocs();
            shouldScrollToElement = false;
          }
        } else {
          setCourseContent([...courseContent, newToc]);
        }

        toast.success('Đã thêm thư mục mới');

        if (shouldScrollToElement) {
          setTimeout(() => {
            const newElement = document.querySelector(`[data-id="${newToc.id}"]`);
            if (newElement) {
              newElement.scrollIntoView({ behavior: 'smooth', block: 'center' });

              newElement.classList.add('highlight-new-item');
              setTimeout(() => {
                newElement.classList.remove('highlight-new-item');
              }, 3000);
            }
          }, 100);
        }
      }
    } catch (error) {
      toast.error(error.message || 'Không thể tạo thư mục');
    }
  };

  const handleSavePositions = async () => {
    if (!selectedCourseId || !courseContent.length) return;

    try {
      const flatItems = flattenTree(courseContent);

      const positions = flatItems.map(item => ({
        id: item.id,
        toc_id: item.parentId,
        index: item.index
      }));

      await updatePositions(selectedCourseId, positions);
      toast.success('Lưu vị trí thành công!');
      setHasUnsavedChanges(false);
    } catch (error) {
      toast.error('Không thể lưu vị trí!');
    }
  };

  const selectedCourse = courses.find(course => course.id === selectedCourseId) || null;

  const buildTreeFromTocs = (courseData) => {
    const { tocs, tableContents } = courseData;

    if (!tocs || tocs.length === 0) {
      if (tableContents && tableContents.length > 0) {
        return tableContents.map(content => ({
          id: `q-${content.id}`,
          title: content.title || 'Không có tiêu đề',
          type: content.type || 1,
          children: [],
          tableContent: content,
          index: content.index || 0
        }));
      }
      return [];
    }

    const allTocs = [...tocs];
    const rootTocs = allTocs.filter(toc => !toc.parent_id);

    const buildNodeTree = (node) => {
      const children = allTocs.filter(toc => toc.parent_id === node.id);
      const tocTableContents = node.tableContents || [];

      const result = {
        id: String(node.id),
        title: node.title || 'Không có tiêu đề',
        type: 0,
        course_id: node.course_id,
        children: [],
        index: node.index || 0
      };

      const allChildrenNodes = [];

      if (children.length > 0) {
        const tocNodes = children.map(buildNodeTree);
        allChildrenNodes.push(...tocNodes);
      }

      if (tocTableContents.length > 0) {
        const tableContentNodes = tocTableContents.map(content => ({
          id: `q-${content.id}`,
          title: content.title || 'Không có tiêu đề',
          type: content.type || 1,
          children: [],
          tableContent: content,
          index: content.index || 0
        }));
        allChildrenNodes.push(...tableContentNodes);
      }

      allChildrenNodes.sort((a, b) => (a.index || 0) - (b.index || 0));

      result.children = allChildrenNodes;
      return result;
    };

    const rootNodes = rootTocs.map(buildNodeTree);

    if (tableContents && tableContents.length > 0) {
      const orphanTableContents = tableContents.filter(content =>
        !content.toc_id || !tocs.some(toc => toc.id === content.toc_id)
      );

      if (orphanTableContents.length > 0) {
        const orphanNodes = orphanTableContents.map(content => ({
          id: `q-${content.id}`,
          title: content.title || 'Không có tiêu đề',
          type: content.type || 1,
          children: [],
          tableContent: content,
          index: content.index || 0
        }));

        rootNodes.push(...orphanNodes);
      }
    }

    rootNodes.sort((a, b) => (a.index || 0) - (b.index || 0));

    return rootNodes;
  };

  const handleTitleUpdate = async (item, value) => {
    try {
      let response;
      if (String(item.id).startsWith('q-')) {
        const id = item.id.replace('q-', '');
        response = await updateQuizTitle(id, value);
      } else {
        response = await updateTocTitle(item.id, value);
      }

      toast.success('Cập nhật tiêu đề thành công');
      return true;
    } catch (error) {
      const errorMessage = error.message || 'Không thể cập nhật tiêu đề';
      toast.error(errorMessage);
      return false;
    }
  };

  // Xóa TOC
  const handleDeleteToc = async (item) => {
    if (hasUnsavedChanges) {
      toast.error("Bạn cần lưu vị trí hiện tại trước khi xóa");
      return;
    }

    const confirm = await confirmDelete(
      `Xóa thư mục: ${item.title}?`,
      "Tất cả bài quiz bên trong sẽ được giữ lại nhưng không còn thuộc thư mục này nữa."
    );

    if (confirm) {
      try {
        await deleteToc(item.id);

        const newItems = removeItem(courseContent, item.id);
        setCourseContent(newItems);

        toast.success('Đã xóa thư mục thành công');
      } catch (error) {
        toast.error('Không thể xóa thư mục');
      }
    }
  };


  const handleRemoveQuizFromFolder = async (item) => {
    if (hasUnsavedChanges) {
      toast.error("Bạn cần lưu vị trí hiện tại trước khi xóa");
      return;
    }

    const confirm = await confirmDelete(
      `Xóa quiz khỏi thư mục: ${item.title}?`,
      "Bài quiz sẽ vẫn tồn tại trong thư viện của bạn."
    );

    if (confirm) {
      try {
        const id = item.id.replace('q-', '');
        await removeQuizFromFolder(id);

        const newItems = [...courseContent];
        const removeQuizFromItems = (items) => {
          for (let i = 0; i < items.length; i++) {
            if (items[i].children && items[i].children.length > 0) {
              items[i].children = items[i].children.filter(child => child.id !== item.id);
              removeQuizFromItems(items[i].children);
            }
          }
        };
        removeQuizFromItems(newItems);
        setCourseContent(newItems);

        toast.success('Đã xóa quiz khỏi thư mục');
      } catch (error) {
        toast.error('Không thể xóa quiz khỏi thư mục');
      }
    }
  };

  const handleItemRemove = (item, type) => {
    if (type === 'toc') {
      handleDeleteToc(item);
    } else if (type === 'quiz') {
      handleRemoveQuizFromFolder(item);
    }
  };

  const handleAddQuiz = (parentToc) => {
    setCurrentTocParent(parentToc);
    setQuizDialogOpen(true);
  };

  const handleQuizFormSubmit = async (response) => {
    try {
      if (response && response.data) {
        const newQuiz = {
          id: `q-${response.data.id}`,
          title: response.data.title,
          type: response.data.type,
          children: []
        };

        let updatedContent = [...courseContent];
        let addedToParent = false;

        if (currentTocParent) {
          const findAndAddChild = (items) => {
            for (let i = 0; i < items.length; i++) {
              if (items[i].id === currentTocParent.id) {
                if (!items[i].children) {
                  items[i].children = [];
                }
                items[i].children.push(newQuiz);
                return true;
              }

              if (items[i].children && items[i].children.length > 0) {
                if (findAndAddChild(items[i].children)) {
                  return true;
                }
              }
            }
            return false;
          };

          addedToParent = findAndAddChild(updatedContent);
          if (addedToParent) {
            setCourseContent(updatedContent);
          } else {
            await loadCourseTocs();
          }
        } else {
          updatedContent.push(newQuiz);
          setCourseContent(updatedContent);
          addedToParent = true;
        }

        if (addedToParent) {
          setTimeout(() => {
            const quizElement = document.querySelector(`[data-id="${newQuiz.id}"]`);
            quizElement.scrollIntoView({ behavior: 'smooth', block: 'center' });

            quizElement.classList.add('highlight-new-item');

            setTimeout(() => {
              quizElement.classList.remove('highlight-new-item');
            }, 3000);
          }, 100);
        }
      }
    } catch (error) {
      toast.error(error.message || 'Không thể tạo quiz');
    }
  };

  const handleEditCourse = () => {
    setCourseDialogOpen(true);
  };

  const handleCourseFormSubmit = (updatedCourse) => {
    setCourses(prevCourses =>
      prevCourses.map(course =>
        course.id === updatedCourse.id
          ? {...course,
             title: updatedCourse.title,
             grade_id: updatedCourse.grade_id,
             subject_id: updatedCourse.subject_id,
             banner: updatedCourse.banner,
             grade: updatedCourse.grade,
             subject: updatedCourse.subject
            }
          : course
      )
    );
  };

  const handleDeleteCourse = async () => {
    if (!selectedCourse) return;

    const confirmed = await confirmDelete(
      `Xóa khóa học: ${selectedCourse.title}?`,
      "Tất cả thư mục sẽ bị xóa, các bài quiz sẽ được chuyển về thư viện chung."
    );

    if (!confirmed) return;

    try {
      await deleteCourse(selectedCourse.id);

      setCourses(prevCourses => prevCourses.filter(course => course.id !== selectedCourse.id));

      if (courses.length > 1) {
        const nextCourse = courses.find(course => course.id !== selectedCourse.id);
        if (nextCourse) {
          setSelectedCourseId(nextCourse.id);
        } else {
          setSelectedCourseId(null);
        }
      } else {
        setSelectedCourseId(null);
        setCourseContent([]);
      }

      toast.success('Đã xóa khóa học thành công');
    } catch (error) {
      toast.error(error.message || 'Không thể xóa khóa học');
    }
  };

  return (
    <div className="position-relative">
      <Container maxWidth={false}>
        <h2 className="h3 mb-4">Quản lý khóa học</h2>
        <div className="row">
          <div className="col-lg-3">
            <div className="sticky-top" style={{maxHeight: 'calc(100vh - 120px)', overflowY: 'auto' }}>
              {loading && selectedCourseId ? (
                <div className="text-center py-3">
                  <CircularProgress size={24} />
                </div>
              ) : (
                <div className="media-group media-lg-group d-flex flex-nowrap flex-lg-wrap overflow-auto hide-scrollbar" style={{ gap: '10px' }}>
                  <InfiniteScroll
                    fetchMore={fetchMore}
                    hasMore={hasMore}
                    className="d-flex flex-nowrap flex-lg-wrap w-100"
                    style={{ gap: '10px' }}
                    loader={
                      <div className="d-flex justify-content-center align-items-center w-100 py-3">
                        <CircularProgress size={24} />
                      </div>
                    }
                  >
                    {courses.map((course) => {
                      return (
                        <div
                          key={course.id}
                          className={`card p-2 border rounded flex-shrink-0 ${selectedCourseId === course.id ? 'active' : ''}`}
                          style={{ width: 'calc(100% - 10px)', maxWidth: '400px' }}
                          onClick={() => setSelectedCourseId(course.id)}
                        >
                          <div className="row g-0 align-items-center">
                            <div className="col-3">
                              <NextImage
                                imgStyle={{
                                objectFit: 'contain',
                                objectPosition: 'center',
                              }}
                                objectFit="contain"
                                src={course.banner}
                                alt={course.title}
                                height={110}
                              />
                            </div>
                            <div className="col-9">
                              <div className="card-body px-3 py-2">
                                <h5
                                  className="mt-0 quiz-name"
                                  style={{
                                    display: '-webkit-box',
                                    WebkitLineClamp: 3,
                                    WebkitBoxOrient: 'vertical',
                                    overflow: 'hidden',
                                    textOverflow: 'ellipsis',
                                    maxWidth: '100%'
                                  }}
                                >
                                  {course.title}
                                </h5>
                                <p className="text-black-50 mb-2 fs-13 text-truncate">
                                  <i className="bi bi-person me-2"></i>
                                  {course.editor?.name || course.editor?.email || '_'}
                                </p>
                                <div className="text-black-50 mb-0 fs-13">
                                  <div className="d-flex flex-wrap">
                                    <span className="text-black-50 me-3 mb-1 d-inline-block">
                                      <i className="bi bi-file-earmark-medical me-2"></i>
                                      {course.subject?.title || '_'}
                                    </span>
                                    <span className="text-black-50 me-3 mb-1 d-inline-block">
                                      <i className="bi bi-mortarboard-fill me-2"></i>
                                      {course.grade?.title || '_'}
                                    </span>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </InfiniteScroll>
                </div>
              )}
            </div>
          </div>

          <div ref={containerRef} className="col-lg-9">
            {selectedCourse && (
              <>
                <AppBar
                  position="sticky"
                  color="default"
                  elevation={1}
                  sx={{
                    backgroundColor: 'white',
                    top: '65px',
                    zIndex: 10,
                    marginBottom: '0',
                    border: '1px solid #8854c0',
                    borderBottom: '0',
                  }}
                >
                  <Toolbar className="p-3">
                    <div style={{
                      maxWidth: 'calc(100% - 200px)',
                      overflow: 'hidden'
                    }}>
                      <h5 className="fw-bold mb-1" style={{
                        overflow: 'hidden',
                        lineHeight: '1.4',
                        wordBreak: 'break-word'
                      }}>
                        {selectedCourse.title}
                      </h5>
                      <p className="text-muted mb-0">
                        {selectedCourse.subject?.title || '_'} · {selectedCourse.grade?.title || '_'}
                      </p>
                    </div>
                    <Box sx={{ flexGrow: 1 }} />
                    <Dropdown
                      renderToggle={({ onClick }) => (
                        <button
                          type="button"
                          onClick={onClick}
                          className="btn btn-link btn-dropdown-small dropdown-toggle"
                        >
                          <MoreVertIcon />
                        </button>
                      )}
                      placement="bottom-end"
                      renderMenu={() => (
                        <div className="dropdown-menu">
                          <button
                            type="button"
                            className="dropdown-item fs-14"
                            onClick={handleEditCourse}
                          >
                            <i className="bi bi-pencil me-2"></i>
                            Chỉnh sửa
                          </button>
                          <button
                            type="button"
                            className="dropdown-item fs-14"
                            onClick={handleDeleteCourse}
                          >
                            <i className="bi bi-trash me-2"></i>
                            Xóa khóa học
                          </button>
                        </div>
                      )}
                    />
                    <Button
                      variant="outlined"
                      className="btn-primary4 btn-sm ms-2"
                      onClick={handleSavePositions}
                      startIcon={<i className="bi bi-save fs-14"></i>}
                      disabled={!hasUnsavedChanges}
                    >
                      Lưu vị trí
                    </Button>

                    <Button
                      variant="outlined"
                      className="btn-primary2 btn-sm ms-2"
                      startIcon={<i className="bi bi-plus-circle fs-14"></i>}
                      onClick={handleAddChapterClick}
                    >
                      Thêm chương
                    </Button>
                  </Toolbar>
                </AppBar>

                <Card
                  className="d-flex flex-column"
                  sx={{
                    border: '1px solid #8854c0',
                    borderTop: 'none',
                    borderRadius: '0 0 8px 8px',
                    marginTop: '-1px',
                    overflow: 'hidden',
                    height: 'calc(100vh - 200px)'
                  }}
                >
                  <div className="p-3">
                    {contentLoading ? (
                      <div className="d-flex justify-content-center align-items-center py-5">
                        <CircularProgress size={30} />
                      </div>
                    ) : courseContent.length === 0 ? (
                      <div className="text-center py-5 text-muted">
                        <i className="bi bi-folder fs-1 d-block mb-3"></i>
                        Không có thư mục nào
                      </div>
                    ) : (
                      <SortableTree
                        items={courseContent}
                        onItemsChanged={handleItemsChanged}
                        onTitleUpdate={handleTitleUpdate}
                        onRemove={handleItemRemove}
                        onAddSubToc={handleAddSubToc}
                        onAddQuiz={handleAddQuiz}
                        courseId={selectedCourse.id}
                        indicator={false}
                        collapsible
                        removable
                        indentationWidth={50}
                        customStyles={{
                          title: {
                            color: '#8854c0',
                            fontWeight: 'bold'
                          }
                        }}
                        canDrop={({ dragItem, dropTarget }) => {
                          if (dragItem.type !== 0 && dropTarget.type !== 0) {
                            return false;
                          }
                          return true;
                        }}
                        renderItem={(props) => {
                          return <SortableTree.Item {...props} />;
                        }}
                      />
                    )}
                  </div>
                </Card>
              </>
            )}
          </div>
        </div>
      </Container>
      <DialogTocForm
        open={tocDialogOpen}
        onClose={() => setTocDialogOpen(false)}
        parentToc={currentParentToc}
        onSubmit={handleTocFormSubmit}
      />
      {quizDialogOpen && (
        <DialogQuizForm
          open={quizDialogOpen}
          onClose={() => setQuizDialogOpen(false)}
          quiz={{
            grade_id: selectedCourse?.grade?.id,
            subject_id: selectedCourse?.subject?.id,
          }}
          courseId={selectedCourse?.id}
          tocId={currentTocParent?.id}
          onSubmit={handleQuizFormSubmit}
        />
      )}
      {courseDialogOpen && (
        <DialogCourseForm
          open={courseDialogOpen}
          onClose={() => setCourseDialogOpen(false)}
          course={selectedCourse}
          onSubmit={handleCourseFormSubmit}
        />
      )}
    </div>
  );
}
