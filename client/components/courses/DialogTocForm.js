"use client";

import React, { forwardRef, memo } from "react";
import { toast } from "react-hot-toast";

import Box from "@mui/material/Box";
import Dialog from "@mui/material/Dialog";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import FormControl from "@mui/material/FormControl";
import FormHelperText from "@mui/material/FormHelperText";
import InputLabel from "@mui/material/InputLabel";
import OutlinedInput from "@mui/material/OutlinedInput";
import Button from "@mui/material/Button";
import Slide from "@mui/material/Slide";

import * as Yup from "yup";
import { Formik } from "formik";

const Transition = forwardRef(function Transition(props, ref) {
  return <Slide direction="up" ref={ref} {...props} />;
});

const DialogTocForm = ({ open, onClose, parentToc = null, onSubmit }) => {
  return (
    <>
      <Dialog
        open={open}
        TransitionComponent={Transition}
        keepMounted
        fullWidth={true}
        maxWidth="sm"
      >
        <DialogTitle className="h5">
          {parentToc ? 'Thêm thư mục con' : 'Thêm chương mới'}
        </DialogTitle>
        <DialogContent>
          <Formik
            initialValues={{
              title: "Thư mục không có tiêu đề",
            }}
            validationSchema={Yup.object().shape({
              title: Yup.string()
                .min(3, "Tiêu đề phải có ít nhất 3 ký tự")
                .max(500, "Tiêu đề không được vượt quá 500 ký tự")
                .required("Tiêu đề không được để trống"),
            })}
            onSubmit={async (values, { setErrors, setSubmitting }) => {
              setTimeout(async () => {
                try {
                  await onSubmit({
                    ...values,
                    parentToc
                  });

                  onClose();
                } catch (err) {
                  console.log(err);
                  if (err?.status == 422) {
                    setErrors(err.errors);
                  } else {
                    toast.error(err.message || "Có lỗi xảy ra");
                  }
                } finally {
                  setSubmitting(false);
                }
              }, 400);
            }}
          >
            {({
              errors,
              handleBlur,
              handleChange,
              handleSubmit,
              isSubmitting,
              touched,
              values,
            }) => (
              <form noValidate onSubmit={handleSubmit}>
                <Box className="d-flex flex-column gap-4 py-3">
                  <FormControl
                    fullWidth
                    error={Boolean(touched.title && errors.title)}
                  >
                    <InputLabel htmlFor="outlined-adornment-title">
                      Tiêu đề
                    </InputLabel>
                    <OutlinedInput
                      id="outlined-adornment-title"
                      type="text"
                      label="Tiêu đề"
                      value={values.title}
                      name="title"
                      onBlur={handleBlur}
                      onChange={handleChange}
                      autoFocus
                      inputProps={{}}
                    />
                    {touched.title && errors.title && (
                      <FormHelperText
                        error
                        id="standard-weight-helper-text-title"
                      >
                        {errors.title}
                      </FormHelperText>
                    )}
                  </FormControl>
                </Box>
                <Box sx={{ mt: 2 }}>
                  <Button
                    variant="outlined"
                    type="button"
                    onClick={onClose}
                    color="inherit"
                    size="small"
                    sx={{ mr: 2 }}
                  >
                    Hủy
                  </Button>
                  <Button
                    disableElevation
                    disabled={isSubmitting}
                    size="small"
                    type="submit"
                    variant="contained"
                    color="secondary"
                  >
                    Lưu
                  </Button>
                </Box>
              </form>
            )}
          </Formik>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default memo(DialogTocForm);
