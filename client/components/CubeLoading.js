import 'assets/scss/cubeLoading.scss';

const CubeLoading = ({ loadingIs = true }) => {
    if (!loadingIs) return true;

    return (
        <div className="cube-container">
            <div className="cube">
                <div className="sides">
                    <div className="top"></div>
                    <div className="right"></div>
                    <div className="bottom"></div>
                    <div className="left"></div>
                    <div className="front"></div>
                    <div className="back"></div>
                </div>
            </div>
            <div className="text">Loading...</div>
        </div>
    );
};

export default CubeLoading;
