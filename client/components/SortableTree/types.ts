import type { MutableRefObject } from "react";
import type { UniqueIdentifier } from "@dnd-kit/core";

export interface TreeItem {
  id: UniqueIdentifier;
  title: string;
  children: TreeItem[];
  type?: number;
  /*
  Default: false.
   */
  collapsed?: boolean;
  /*
  If false, doesn't allow to drag&drop nodes so that they become children of current node.
  If you are showing files&directories it makes sense to set this to `true` for folders, and `false` for files.
  Default: true.
   */
  canHaveChildren?: boolean | ((dragItem: FlattenedItem) => boolean);
}

export type TreeItems = TreeItem[];

export interface FlattenedItem extends TreeItem {
  parentId: UniqueIdentifier | null;
  depth: number;
  index: number;
  isLast: boolean;
  parent: FlattenedItem | null;
}

export type SensorContext = MutableRefObject<{
  items: FlattenedItem[];
  offset: number;
}>;

export interface ItemChangedReason {
  type: "removed" | "dropped";
  id?: UniqueIdentifier;
  sortedItems?: TreeItems;
}
