.Wrapper {
  list-style: none;
  box-sizing: border-box;
  padding-left: var(--spacing);
  margin-bottom: 0;
  display: flex;
  flex-direction: row;
  padding: 0;
  margin: 0;
  position: relative;
  margin-top: -1px;

  .dnd_line {
    width: 50px;
    align-self: stretch;
    background: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg'><line stroke='black' style='stroke-width: 1px;' x1='50%' y1='0' x2='50%' y2='100%'/></svg>");
  }

  .dnd_line-last {
    width: 50px;
    align-self: stretch;
  }

  .dnd_line-to_self {
    width: 50px;
    align-self: stretch;
    background: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg'><line stroke='black' style='stroke-width: 1px;' x1='50%' y1='0' x2='50%' y2='100%'/><line stroke='black' style='stroke-width: 1px;' x1='50%' y1='50%' x2='100%' y2='50%'/></svg>");
  }

  .dnd_line-to_self-last {
    width: 50px;
    align-self: stretch;
    background: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg'><line stroke='black' style='stroke-width: 1px;' x1='50%' y1='0' x2='50%' y2='50%'/><line stroke='black' style='stroke-width: 1px;' x1='50%' y1='50%' x2='100%' y2='50%'/></svg>");
  }

  &.clone {
    display: inline-block;
    pointer-events: none;
    padding: 0;
    padding-left: 10px;
    padding-top: 5px;

    .TreeItem {
      --vertical-padding: 5px;

      padding-right: 24px;
      border-radius: 4px;
      box-shadow: 0px 15px 15px 0 rgba(34, 33, 81, 0.1);
      position: relative;
    }
  }

  &.ghost {
    &.indicator {
      opacity: 1;
      position: relative;
      z-index: 1;
      margin-bottom: -1px;

      .TreeItem {
        position: relative;
        padding: 0;
        height: 8px;
        border-color: #2389ff;
        background-color: #56a1f8;

        &:before {
          position: absolute;
          left: -8px;
          top: -4px;
          display: block;
          content: '';
          width: 12px;
          height: 12px;
          border-radius: 50%;
          border: 1px solid #2389ff;
          background-color: #ffffff;
        }

        > * {
          /* Items are hidden using height and opacity to retain focus */
          opacity: 0;
          height: 0;
        }
      }
    }

    &:not(.indicator) {
      opacity: 0.5;
    }

    .TreeItem > * {
      box-shadow: none;
      background-color: transparent;
    }
  }
}

.TreeItem {
  --vertical-padding: 10px;

  position: relative;
  padding: 10px;
  border: 1px solid #e0e0e0;
  border-radius: 0;
  margin-bottom: 0;
  background-color: white;
  display: flex;
  align-items: center;
  flex: 1;
  margin: 0;

  // Hover effect
  &:hover {
    background-color: #f9f9f9;
  }
}

.Text {
  flex-grow: 1;
  padding-left: 0.5rem;
  // white-space: nowrap;
  // text-overflow: ellipsis;
  // overflow: hidden;
}

.Count {
  position: absolute;
  top: -10px;
  right: -10px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #2389ff;
  font-size: 0.8rem;
  font-weight: 600;
  color: #fff;
}

.disableInteraction {
  pointer-events: none;
}

.disableSelection,
.clone {
  .Text,
  .Count {
    user-select: none;
    -webkit-user-select: none;
  }
}

.Collapse {
  svg {
    transition: transform 250ms ease;
  }

  &.collapsed svg {
    transform: rotate(-90deg);
  }
}

.t-icon_t {
  margin-right: 8px;
  display: inline-flex;
  align-items: center;

  i {
    font-size: 16px;
  }
}

// Đảm bảo mục cuối cùng có border-radius phía dưới
.Wrapper:last-child .TreeItem {
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
}

// Đảm bảo mục đầu tiên có border-radius phía trên
.Wrapper:first-child .TreeItem {
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
}

// Mục đang được kéo
.clone .TreeItem {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  border-color: #8854c0;
}

.Wrapper:first-child {
  margin-top: 0;
}

.highlight-new-item {
  animation: highlight 3s ease-out;
}

@keyframes highlight {
  0% {
    background-color: rgba(136, 84, 192, 0.3);
  }
  100% {
    background-color: transparent;
  }
}
