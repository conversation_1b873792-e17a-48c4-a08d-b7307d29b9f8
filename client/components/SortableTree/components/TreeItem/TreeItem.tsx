import React, { forwardRef, HTMLAttributes, useCallback } from 'react';
import Link from 'next/link';
import classNames from 'classnames';

import type { FlattenedItem, TreeItem as TreeItemProp } from "../../types";

import { Action } from '../Action';
import { Handle } from '../Handle';
import { Remove } from '../Remove';

import './TreeItem.scss';

import Dropdown from "@/components/Dropdown";

const useNoti = () => (message: any) => {};

const tablecontents_types = {
  0: {
    title: "<PERSON><PERSON><PERSON> lục",
    icon: '<i class="bi bi-folder2-open me-2"></i>',
  },
  1: {
    title: "Đề thi",
    icon: '<i class="bi bi-calendar-check me-2 text-danger"></i>',
  },
  2: {
    title: "<PERSON><PERSON> thuyết",
    icon: '<i class="bi bi-file-earmark-medical me-2 text-info"></i>',
  },
};


import EditableText from '../../../EditableText';

const updateTableContent = async (courseId: number, data: any) => {
  return { success: true, message: 'Cập nhật thành công' };
};

export interface Props extends Omit<HTMLAttributes<HTMLLIElement>, 'id'> {
  item: TreeItemProp;
  parent: FlattenedItem | null;
  /*
  Total number of children (including nested children)
   */
  childCount?: number;
  /*
  Ghost and Clone are two properties that are set to True for an item that is being dragged.
  Item that is being dragged is shown in 2 places:
  - as an overlay item (for which clone=true, ghost=false)
  - as an item within a tree (for which ghost=true, clone=false)
   */
  clone?: boolean;
  ghost?: boolean;
  /*
  True if item has children which are not shown (collapsed)
   */
  collapsed?: boolean;
  /*
  The level of depth current item is at. Should be used to calculate paddingLeft for an item
  (by using depth * indentationWidth)
   */
  depth: number;
  /*
  While dragging it makes sense to disable selection/interaction for all other items
  (to prevent unneeded text selection).
  So, it's true for all nodes that are NOT dragged (if some other is being dragged)
   */
  disableInteraction?: boolean;
  disableSelection?: boolean;
  /*
  True if the item is the last one among it's parent children.
  Might be important to show correct images.
   */
  isLast: boolean;

  handleProps?: any;
  indicator?: boolean;
  indentationWidth: number;
  onCollapse?(): void;
  onRemove?(): void;
  wrapperRef?(node: HTMLLIElement): void;
  onTitleUpdate?(item: TreeItemProp, value: string): Promise<boolean>;
  onAddSubToc?(item: TreeItemProp): void;
  onAddQuiz?(item: TreeItemProp, type: number): void;
}

function flattenParents(
  parent: FlattenedItem | null
): FlattenedItem[] {
  if (!parent) return [];
  return [...flattenParents(parent.parent), parent];
}

export const TreeItem = forwardRef<HTMLDivElement, Props>(
  (
    {
      item,
      childCount,
      clone,
      depth,
      disableSelection,
      disableInteraction,
      ghost,
      handleProps,
      indentationWidth,
      indicator,
      collapsed,
      onCollapse,
      onRemove,
      style,
      wrapperRef,
      isLast,
      parent,
      onTitleUpdate,
      onAddSubToc,
      onAddQuiz,
      ...props
    },
    ref
  ) => {
    const notiResponse = useNoti();
    const flattenedParents = flattenParents(parent);

    const handleTitleUpdate = useCallback(async (value) => {
      if (onTitleUpdate) {
        return await onTitleUpdate(item, value);
      }
      return false;
    }, [item, onTitleUpdate]);

    const depthOffset = depth * indentationWidth;

    const handleRemove = useCallback(() => {
      if (onRemove) {
        const type = String(item.id).startsWith('q-') ? 'quiz' : 'toc';
        onRemove(item, type);
      }
    }, [item, onRemove]);

    return (
      <li
        className={classNames(
          'Wrapper',
          clone && 'clone',
          ghost && 'ghost',
          indicator && 'indicator',
          disableSelection && 'disableSelection',
          disableInteraction && 'disableInteraction'
        )}
        ref={wrapperRef}
        {...props}
      >
        <div className="TreeItem" ref={ref} style={{
          ...style,
          marginLeft: depthOffset ? `${depthOffset}px` : undefined
        }} data-id={item.id}>
          <Handle {...handleProps} />
          {onCollapse && (
            <Action
              onClick={onCollapse}
              className={classNames(
                'Collapse',
                collapsed && 'collapsed'
              )}
            >
              {collapseIcon}
            </Action>
          )}
          <div className="Text">
            <div className='d-flex'>
              <span
                className="t-icon_t"
                data-type={item.type as number}
                dangerouslySetInnerHTML={{
                  __html: tablecontents_types[item.type as number]?.icon || '+'
                }}
              />
              <EditableText
                className='my-1 fs-16'
                textVariant="div"
                value={item.title || ''}
                emptyInit={true}
                onBlur={handleTitleUpdate}
              />
            </div>
            { item.type === 0 && (
              <Dropdown
                renderToggle={({ onClick }) => (
                  <button
                    type="button"
                    onClick={onClick}
                    className="dropdown-toggle btn btn-link btn-sm rounded-0 px-0"
                  >
                    <i className="bi bi-plus-circle me-2 ms-0"></i>
                    Tạo phần
                  </button>
                )}
                placement="bottom-end"
                renderMenu={() => (
                  <div className="dropdown-menu">
                    <a
                      className="dropdown-item"
                      onClick={() => onAddSubToc && onAddSubToc(item)}
                      style={{ cursor: 'pointer' }}
                    >
                      Mục lục
                    </a>
                    <hr className="dropdown-divider" />
                    <a
                      className="dropdown-item"
                      onClick={() => onAddQuiz && onAddQuiz(item, 1)}
                      style={{ cursor: 'pointer' }}
                    >
                      Đề thi
                    </a>
                    <a
                      className="dropdown-item"
                      onClick={() => onAddQuiz && onAddQuiz(item, 2)}
                      style={{ cursor: 'pointer' }}
                    >
                      Lý thuyết
                    </a>
                  </div>
                )}
              />
            ) }
            { item.type !== 0 && (
              <Link href={`/dashboard/quiz/${String(item.id).replace('q-', '')}/edit`} className="btn btn-outline-primary border-0 btn-sm mt-2" style={{padding: '2px 7px'}}>
                <i className="bi bi-pencil-square me-1"></i> Nhập nội dung
              </Link>
            ) }
          </div>
          {!clone && onRemove && <Remove onClick={handleRemove} />}
          {clone && childCount && childCount > 1 ? (
            <span className="Count">{childCount}</span>
          ) : null}
        </div>
      </li>
    );
  }
);

const collapseIcon = (
  <svg width="10" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 70 41">
    <path d="M30.76 39.2402C31.885 40.3638 33.41 40.995 35 40.995C36.59 40.995 38.115 40.3638 39.24 39.2402L68.24 10.2402C69.2998 9.10284 69.8768 7.59846 69.8494 6.04406C69.822 4.48965 69.1923 3.00657 68.093 1.90726C66.9937 0.807959 65.5106 0.178263 63.9562 0.150837C62.4018 0.123411 60.8974 0.700397 59.76 1.76024L35 26.5102L10.24 1.76024C9.10259 0.700397 7.59822 0.123411 6.04381 0.150837C4.4894 0.178263 3.00632 0.807959 1.90702 1.90726C0.807714 3.00657 0.178019 4.48965 0.150593 6.04406C0.123167 7.59846 0.700153 9.10284 1.75999 10.2402L30.76 39.2402Z" />
  </svg>
);
