"use client";

import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { setNoti } from '@/slices/notiSlice';
import { assignUserRole } from '@/actions/userAction';
import { fetchSubjects } from '@/actions/subjectAction';
import { http } from '@/lib/axios';
import { apiUrl } from '@/lib/fetcher';

import RoleSelectionDialog from './RoleSelectionDialog';
import RoleDialogFactory from './RoleDialogFactory';
import { roleMessages } from '@/constant';

const WelcomeDialogSystem = ({
  user,
  onComplete,
  availableRoles = ['student', 'teacher']
}) => {
  const dispatch = useDispatch();
  const [currentStep, setCurrentStep] = useState('role-selection');
  const [selectedRole, setSelectedRole] = useState(null);
  const [loading, setLoading] = useState(false);
  const [subjects, setSubjects] = useState([]);
  const [grades, setGrades] = useState([]);

  // Load subjects and grades when component mounts
  useEffect(() => {
    const loadData = async () => {
      try {
        const [subjectsResponse, gradesResponse] = await Promise.all([
          fetchSubjects({ page: 1, limit: 100 }),
          http({
            url: apiUrl('/api/grades'),
            method: 'GET'
          })
        ]);

        if (subjectsResponse?.data) {
          setSubjects(subjectsResponse.data);
        }
        if (gradesResponse?.data) {
          setGrades(gradesResponse.data);
        }
      } catch (error) {
        console.error('Error loading subjects and grades:', error);
      }
    };

    loadData();
  }, []);

  const handleRoleSelection = (role) => {
    setSelectedRole(role);
    setCurrentStep('role-onboarding');
  };

  const handleRoleOnboardingComplete = async (roleData) => {
    setLoading(true);

    try {
      await assignUserRole(roleData);

      dispatch(setNoti({
        type: 'success',
        message: roleMessages[roleData.role] || 'Chào mừng bạn đến với 2048.vn!'
      }));

      onComplete();
    } catch (error) {
      dispatch(setNoti({
        type: 'error',
        message: error?.message || 'Đã xảy ra lỗi khi thiết lập tài khoản.'
      }));
    } finally {
      setLoading(false);
    }
  };

  const handleBack = () => {
    if (currentStep === 'role-onboarding') {
      setCurrentStep('role-selection');
      setSelectedRole(null);
    }
  };

  return (
    <>
      <RoleSelectionDialog
        open={currentStep === 'role-selection'}
        onRoleSelect={handleRoleSelection}
        availableRoles={availableRoles}
        loading={loading}
      />

      <RoleDialogFactory
        role={selectedRole}
        open={currentStep === 'role-onboarding'}
        user={user}
        subjects={subjects}
        grades={grades}
        onComplete={handleRoleOnboardingComplete}
        onBack={handleBack}
        loading={loading}
      />
    </>
  );
};

export default WelcomeDialogSystem;
