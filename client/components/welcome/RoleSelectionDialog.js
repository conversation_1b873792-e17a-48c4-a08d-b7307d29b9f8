"use client";

import React from 'react';

import { useTheme } from '@mui/material/styles';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import CardActionArea from '@mui/material/CardActionArea';
import CircularProgress from '@mui/material/CircularProgress';
import Avatar from '@mui/material/Avatar';

const RoleSelectionDialog = ({
   open,
   onRoleSelect,
   loading,
   availableRoles = ['student', 'teacher']
}) => {
  const theme = useTheme();

  const allRoleOptions = {
    teacher: {
      id: 'teacher',
      title: 'Giáo viên',
      description: 'Tạo và quản lý nội dung giáo dục',
      icon: <Avatar src="/assets/images/roles/signup-teacher.png" alt="Student icon" sx={{
        width: { xs: 80, sm: 100 },
        height: { xs: 80, sm: 100 },
        mx: 'auto',
        mb: 1,
        background: 'none'
      }}
      ></Avatar>,
      color: theme.palette.secondary.main
    },
    student: {
      id: 'student',
      title: 'Học sinh',
      description: 'Tham gia các bài kiểm tra và học tập',
      icon: <Avatar src="/assets/images/roles/signup-student.png" alt="Student icon" sx={{
          width: { xs: 80, sm: 100 },
          height: { xs: 80, sm: 100 },
          mx: 'auto',
          mb: 1,
          background: 'none'
        }}
      ></Avatar>,
      color: theme.palette.primary.main
    },
    trainer: {
      id: 'trainer',
      title: 'Trainer',
      description: 'Đào tạo và phát triển kỹ năng',
      icon: <Avatar src="/assets/images/roles/signup-admin.png" alt="Trainer icon" sx={{
        width: { xs: 80, sm: 100 },
        height: { xs: 80, sm: 100 },
        mx: 'auto',
        mb: 1,
        background: 'none'
      }}
      ></Avatar>,
      color: theme.palette.warning.main
    },
  };

  // Filter available roles based on props
  const roleOptions = availableRoles.map(roleId => allRoleOptions[roleId]).filter(Boolean);

  const handleRoleSelect = (roleId) => {
    if (!loading) {
      onRoleSelect(roleId);
    }
  };

  return (
    <Dialog
      open={open}
      maxWidth="sm"
      fullWidth
      disableEscapeKeyDown
      PaperProps={{
        sx: {
          borderRadius: 2,
          p: 2
        }
      }}
    >
      <DialogContent sx={{ px: { xs: 0.5, sm: 2, md: 3 } }}>
        <Box textAlign="center" mb={4}>
          <Typography variant="h4" component="h4" gutterBottom fontWeight="bold">
            Chào mừng đến với 2048.vn
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Hãy chọn vai trò của bạn để bắt đầu
          </Typography>
        </Box>

        <Box
          sx={{
            width: '100%',
            display: 'flex',
            gap: 2,
            alignItems: 'stretch',
            flexWrap: 'nowrap',
            flexDirection: { xs: 'column', sm: 'row' },
            '&::-webkit-scrollbar': {
              display: 'none'
            }
          }}
        >
          {roleOptions.map((role) => (
            <Box key={role.id} className="w-100" sx={{ borderBottom: '2px solid #09090933', borderRadius: 2 }}>
              <Card
                sx={{
                  height: '100%',
                  border: `2px solid transparent`,
                  transition: 'all 0.3s ease',
                  backgroundColor: 'rgba(241, 241, 241, 1)',
                  '&:hover': {
                    border: `2px solid ${role.color}`,
                    boxShadow: theme.shadows[8]
                  }
                }}
              >
                <CardActionArea
                  onClick={() => handleRoleSelect(role.id)}
                  disabled={loading}
                  sx={{
                    height: '100%',
                    p: 2,
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    minHeight: 200
                  }}
                >
                  <CardContent sx={{ textAlign: 'center', p: 0 }}>
                    <Box mb={2}>
                      {role.icon}
                    </Box>
                    <Typography variant="h5" component="h6" gutterBottom fontWeight="bold">
                      {role.title}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" fontSize="12px">
                      {role.description}
                    </Typography>
                  </CardContent>
                </CardActionArea>
              </Card>
            </Box>
          ))}
        </Box>

        {loading && (
          <Box display="flex" justifyContent="center" mt={3}>
            <CircularProgress size={24} />
          </Box>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default RoleSelectionDialog;
