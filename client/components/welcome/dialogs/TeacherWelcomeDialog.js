"use client";

import React, { useState, useEffect } from 'react';

import { useTheme } from '@mui/material/styles';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import Avatar from '@mui/material/Avatar';
import FormControl from '@mui/material/FormControl';
import InputLabel from '@mui/material/InputLabel';
import Select from '@mui/material/Select';
import MenuItem from '@mui/material/MenuItem';
import Chip from '@mui/material/Chip';
import OutlinedInput from '@mui/material/OutlinedInput';
import CircularProgress from '@mui/material/CircularProgress';
import Alert from '@mui/material/Alert';

import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import PersonIcon from '@mui/icons-material/ManageAccounts';

const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
      width: 250,
    },
  },
};

const TeacherWelcomeDialog = ({
  open,
  user,
  subjects = [],
  grades = [],
  onComplete,
  onBack,
  loading
}) => {
  const theme = useTheme();
  const [currentStep, setCurrentStep] = useState(0);
  const [progress, setProgress] = useState(0);
  const [autoAdvancing, setAutoAdvancing] = useState(false);
  const [selectedSubjects, setSelectedSubjects] = useState([]);
  const [selectedGrades, setSelectedGrades] = useState([]);
  const [errors, setErrors] = useState({});

  const steps = ['Chào mừng', 'Thiết lập thông tin'];

  // Auto-advance logic for welcome step
  useEffect(() => {
    if (open && currentStep === 0) {
      setProgress(0);
      setAutoAdvancing(true);

      const timer = setInterval(() => {
        setProgress((prevProgress) => {
          const newProgress = prevProgress + (100 / 20); // 2 seconds = 20 intervals of 100ms

          if (newProgress >= 100) {
            clearInterval(timer);
            setAutoAdvancing(false);
            setTimeout(() => {
              setCurrentStep(1);
            }, 200);
            return 100;
          }

          return newProgress;
        });
      }, 100);

      return () => {
        clearInterval(timer);
        setAutoAdvancing(false);
      };
    }
  }, [open, currentStep]);

  const handleSubjectChange = (event) => {
    const value = event.target.value;
    setSelectedSubjects(typeof value === 'string' ? value.split(',') : value);
    if (errors.subjects) {
      setErrors(prev => ({ ...prev, subjects: null }));
    }
  };

  const handleGradeChange = (event) => {
    const value = event.target.value;
    setSelectedGrades(typeof value === 'string' ? value.split(',') : value);
    if (errors.grades) {
      setErrors(prev => ({ ...prev, grades: null }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (selectedSubjects.length === 0) {
      newErrors.subjects = 'Vui lòng chọn ít nhất một môn học';
    }

    if (selectedGrades.length === 0) {
      newErrors.grades = 'Vui lòng chọn ít nhất một lớp';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (currentStep === 0 && !autoAdvancing) {
      setCurrentStep(1);
    }
  };

  const handleBack = () => {
    if (currentStep === 0) {
      onBack();
    } else if (currentStep === 1) {
      setCurrentStep(0);
    }
  };

  const handleComplete = () => {
    if (validateForm() && !loading) {
      onComplete({
        role: 'teacher',
        subject_ids: selectedSubjects.map(id => parseInt(id)),
        grade_ids: selectedGrades.map(id => parseInt(id))
      });
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <Box textAlign="center">
            <Box mb={3}>
              <Avatar
                src="/assets/images/roles/onboarding-hello.webp"
                alt={user?.name}
                sx={{
                  width: 200,
                  height: 200,
                  mx: 'auto',
                  mb: 2,
                  border: 'none', background: 'none'
                }}
              >
              </Avatar>
            </Box>

            <Typography variant="h4" component="h5" gutterBottom fontWeight="bold">
              Chào mừng đến với 2048.vn,
            </Typography>
            <Typography variant="h6" color="text.secondary" mb={4}>
              {user?.name}
            </Typography>
            <Typography variant="body1" color="text.secondary" mb={4}>
              Hãy cùng cá nhân hóa trải nghiệm trên 2048.vn của bạn
            </Typography>

            {autoAdvancing && (
              <Typography variant="caption" color="text.secondary" mt={1} display="block">
                Tự động chuyển sau {Math.ceil((100 - progress) / (100 / 2))} giây...
              </Typography>
            )}
          </Box>
        );

      case 1:
        return (
          <>
            <Box mb={4} sx={{ display: 'flex' }}>
              <Box mb={1}>
                <PersonIcon sx={{ fontSize: 42, color: theme.palette.secondary.main, background: 'rgba(246, 240, 255, 1)', borderRadius: '50%', p: 0.75 }} />
              </Box>
              <Box ml={2}>
                <Typography variant="h6" gutterBottom fontWeight="bold" className="mb-1">
                  Thiết lập thông tin giảng dạy
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Hãy cho chúng tôi biết các môn học và lớp bạn thường giảng dạy
                </Typography>
              </Box>
            </Box>

            <Box mb={3}>
              <FormControl fullWidth error={!!errors.subjects}>
                <InputLabel id="subjects-select-label">Môn học *</InputLabel>
                <Select
                  labelId="subjects-select-label"
                  multiple
                  value={selectedSubjects}
                  onChange={handleSubjectChange}
                  input={<OutlinedInput label="Môn học *" />}
                  renderValue={(selected) => (
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                      {selected.map((value) => {
                        const subject = subjects.find(s => s.id.toString() === value.toString());
                        return (
                          <Chip
                            key={value}
                            label={subject?.title || value}
                            size="small"
                            color="primary"
                            variant="outlined"
                          />
                        );
                      })}
                    </Box>
                  )}
                  MenuProps={MenuProps}
                  disabled={loading}
                >
                  {subjects.map((subject) => (
                    <MenuItem key={subject.id} value={subject.id}>
                      {subject.title}
                    </MenuItem>
                  ))}
                </Select>
                {errors.subjects && (
                  <Typography variant="caption" color="error" sx={{ mt: 0.5, ml: 1 }}>
                    {errors.subjects}
                  </Typography>
                )}
              </FormControl>
            </Box>

            <Box mb={3}>
              <FormControl fullWidth error={!!errors.grades}>
                <InputLabel id="grades-select-label">Lớp *</InputLabel>
                <Select
                  labelId="grades-select-label"
                  multiple
                  value={selectedGrades}
                  onChange={handleGradeChange}
                  input={<OutlinedInput label="Lớp *" />}
                  renderValue={(selected) => (
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                      {selected.map((value) => {
                        const grade = grades.find(g => g.id.toString() === value.toString());
                        return (
                          <Chip
                            key={value}
                            label={grade?.title || value}
                            size="small"
                            color="secondary"
                            variant="outlined"
                          />
                        );
                      })}
                    </Box>
                  )}
                  MenuProps={MenuProps}
                  disabled={loading}
                >
                  {grades.map((grade) => (
                    <MenuItem key={grade.id} value={grade.id}>
                      {grade.title}
                    </MenuItem>
                  ))}
                </Select>
                {errors.grades && (
                  <Typography variant="caption" color="error" sx={{ mt: 0.5, ml: 1 }}>
                    {errors.grades}
                  </Typography>
                )}
              </FormControl>
            </Box>

            <Alert severity="info" sx={{ mb: 3 }}>
              Bạn có thể thay đổi thông tin này sau trong cài đặt tài khoản
            </Alert>

            {loading && (
              <Box display="flex" justifyContent="center" mb={2}>
                <CircularProgress size={24} />
              </Box>
            )}
          </>
        );

      default:
        return null;
    }
  };

  const renderActions = () => {
    return (
      <DialogActions sx={{ px: 1, pt: 0 }}>
        { currentStep === 1 && (
          <Button
            onClick={handleBack}
            disabled={loading || (currentStep === 0 && autoAdvancing)}
            startIcon={<ArrowBackIcon />}
            color="inherit"
          >
            Quay lại
          </Button>
        )}

        {currentStep === 1 && (
          <Button
            onClick={handleComplete}
            disabled={loading || selectedSubjects.length === 0 || selectedGrades.length === 0}
            variant="contained"
            color="secondary"
            startIcon={loading ? <CircularProgress size={16} /> : <CheckCircleIcon />}
          >
            {loading ? 'Đang xử lý...' : 'Hoàn thành'}
          </Button>
        )}
      </DialogActions>
    );
  };

  return (
    <Dialog
      open={open}
      maxWidth="sm"
      fullWidth
      disableEscapeKeyDown
      PaperProps={{
        sx: {
          borderRadius: 2,
          px: { xs: 0.5, sm: 2 }
        }
      }}
    >
      <DialogContent sx={{ px: 1 }}>
        {renderStepContent()}
      </DialogContent>

      {renderActions()}
    </Dialog>
  );
};

export default TeacherWelcomeDialog;
