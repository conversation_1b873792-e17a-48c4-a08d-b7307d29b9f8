"use client";

import React from 'react';

import { useTheme } from '@mui/material/styles';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import Stepper from '@mui/material/Stepper';
import Step from '@mui/material/Step';
import StepLabel from '@mui/material/StepLabel';

import ArrowBackIcon from '@mui/icons-material/ArrowBack';

/**
 * Base component for role-specific welcome dialogs
 * Provides common structure and functionality that can be extended
 */
const BaseRoleWelcomeDialog = ({
  open,
  title,
  subtitle,
  icon,
  steps = [],
  currentStep = 0,
  onBack,
  loading = false,
  children,
  actions,
  maxWidth = "sm",
  ...props
}) => {
  const theme = useTheme();

  const defaultActions = (
    <DialogActions sx={{ p: 3, pt: 0 }}>
      <Button
        onClick={onBack}
        disabled={loading}
        startIcon={<ArrowBackIcon />}
        color="inherit"
      >
        Quay lại
      </Button>
    </DialogActions>
  );

  return (
    <Dialog
      open={open}
      maxWidth={maxWidth}
      fullWidth
      disableEscapeKeyDown
      PaperProps={{
        sx: {
          borderRadius: 2,
          px: { xs: 0.5, sm: 2 }
        }
      }}
      {...props}
    >
      <DialogContent sx={{ px: 1 }}>
        {/* Step indicator */}
        {steps.length > 0 && (
          <Box mb={3}>
            <Stepper activeStep={currentStep} alternativeLabel>
              {steps.map((label) => (
                <Step key={label}>
                  <StepLabel>{label}</StepLabel>
                </Step>
              ))}
            </Stepper>
          </Box>
        )}
        {/* Header with title and icon */}
        {(title || icon) && (
          <Box mb={4} sx={{ display: 'flex' }}>
            {icon && (
              <Box mb={1}>
                {icon}
              </Box>
            )}
            {title && (
              <Box ml={2}>
                <Typography variant="h6" gutterBottom fontWeight="bold" className="mb-1">
                  {title}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {subtitle}
                </Typography>
              </Box>
            )}
          </Box>
        )}

        {/* Main content */}
        {children}
      </DialogContent>

      {/* Actions */}
      {actions || defaultActions}
    </Dialog>
  );
};

export default BaseRoleWelcomeDialog;
