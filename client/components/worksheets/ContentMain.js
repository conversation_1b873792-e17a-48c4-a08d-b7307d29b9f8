"use client";

import { useState } from 'react';
import { Link } from './Link';
import { usePathname } from 'next/navigation';

import { useTheme } from "@mui/material/styles";
import useMediaQuery from "@mui/material/useMediaQuery";
import Card from "@mui/material/Card";
import CardActionArea from "@mui/material/CardActionArea";
import CardActions from "@mui/material/CardActions";
import CardContent from "@mui/material/CardContent";
import CardMedia from "@mui/material/CardMedia";

import NextImage from "@/components/NextImage";
import QuizPreviewModal from '@/components/quizzes/QuizPreviewModal';

export default function ContentMain({ page = 1, ...other }) {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));
  const [selectedQuiz, setSelectedQuiz] = useState(null);
  const [modalOpen, setModalOpen] = useState(false);
  const pathname = usePathname();

  const handleQuizClick = (quiz, e) => {
    if (!isMobile) {
      e.preventDefault();
      setSelectedQuiz(quiz);
      setModalOpen(true);
    }
  };

  const handleCloseModal = () => {
    setModalOpen(false);
  };

  const grade = other?.grade || other?.course?.grade || other?.toc?.grade || null
  const subject = other?.subject || other?.course?.subject || other?.toc?.subject || null
  const course = other?.course || other?.toc?.course || null

  return (
    <>
      { (grade || subject || course || other?.toc) ? (
        <ul className="list-tag list-unstyled d-flex flex-wrap gap-2 mt-0">
          {grade && (
            <li>
              <Link className="btn btn-sm bg-white border fw-medium text-dark" href={`/${grade.slug}`}>
                {grade.title}
              </Link>
            </li>
          )}
          {subject && (
            <li>
              <Link className="btn btn-sm bg-white border fw-medium text-dark" href={grade ? `/${grade.slug}/${subject.slug}` : `/worksheets/${subject.slug}`}>
                {subject.title}
              </Link>
            </li>
          )}
          {course && (
            <li>
              <Link className="btn btn-sm bg-white border fw-medium text-dark" href={`/${course.slug}-c${course.id}`}>
                {course.title}
              </Link>
            </li>
          )}
          {other?.toc && (
            <li>
              <Link className="btn btn-sm bg-white border fw-medium text-dark" href={`/${other.toc.slug}-t${other.toc.id}`}>
                {other.toc.title}
              </Link>
            </li>
          )}
        </ul>
      ) : null }

      { (other?.toc || other?.course) ? (
        <div className="rounded-2 p-3 mb-1">
          <>
            <h2 className="h5 text-dark mb-0">
              { other?.toc?.title || '' }
              { other?.course?.title || '' }
            </h2>
            { other?.toc && other.toc?.children?.length > 0 &&(
              <ul className="list-unstyled topics pb-2 m-0 mt-3">
                <li className="text-muted topic">
                  Chủ đề:
                </li>
                { other.toc.children.map((tocItem) => (
                  <li key={tocItem.id}>
                    <Link className="btn btn-sm btn-default m-1 fw-medium topic active4" href={`/${tocItem.slug}-t${tocItem.id}`}>
                      { tocItem.title }
                    </Link>
                  </li>
                )) }
              </ul>
            ) }
            { other?.course && other.course?.tocs?.length > 0 && (
              <ul className="list-unstyled topics pb-2 m-0 mt-3">
                <li className="text-muted topic">
                  Chủ đề:
                </li>
                { other.course.tocs.map((tocItem) => (
                  <li key={tocItem.id}>
                    <Link className="btn btn-sm btn-default m-1 fw-medium topic active4" href={`/${tocItem.slug}-t${tocItem.id}`}>
                      { tocItem.title }
                    </Link>
                  </li>
                )) }
              </ul>
            ) }
          </>
        </div>
      ) : (
        other?.subjects?.length > 0 && (
          other.subjects.map((subjectItem) => {
            return (
              <div className="bg-white rounded-2 p-3 mb-3" key={subjectItem.id}>
                <div className="d-flex flex-wrap gap-2 align-items-center justify-content-between">
                  <h2 className="h5 text-dark m-0">
                    { subjectItem.title }
                  </h2>
                  <Link
                    href={other?.grade ? `/${other.grade.slug}/${subjectItem.slug}` : `/worksheets/${subjectItem.slug}`}
                    className={`btn btn-primary4 btn-sm d-block active5 ${other?.quizzes?.length ? 'd-none' : ''}`}
                  >
                    Xem tất cả
                    <i className="bi bi-arrow-right-short"></i>
                  </Link>
                </div>
                { subjectItem?.courses?.length == 1 && subjectItem.courses[0]?.tocs?.length > 0 && (
                  <ul className="list-unstyled topics pb-2 m-0 mt-3">
                    <li className="text-muted topic">
                      Chủ đề:
                    </li>
                    { subjectItem.courses[0].tocs.map((tocItem) => (
                      <li key={tocItem.id}>
                        <Link className="btn btn-sm btn-default m-1 fw-medium topic" href={`/${tocItem.slug}-t${tocItem.id}`}>
                          { tocItem.title }
                        </Link>
                      </li>
                    )) }
                  </ul>
                ) }
                { subjectItem?.courses?.length > 1 && (
                  <ul className="list-unstyled topics pb-2 m-0 mt-3">
                    <li className="text-muted topic">
                      Chủ đề:
                    </li>
                    { subjectItem.courses.map((courseItem) => (
                      <li key={courseItem.id}>
                        <Link className="btn btn-sm btn-default m-1 fw-medium topic" href={`/${courseItem.slug}-c${courseItem.id}`}>
                          { courseItem.title }
                        </Link>
                      </li>
                    )) }
                  </ul>
                ) }
                {subjectItem?.quiz?.length > 0 && (
                  <div className="quiz-grid-column pb-2 mt-3">
                    { subjectItem.quiz.map((quizItem) => (
                      <Card
                        className="h-100 shadow"
                        key={quizItem.id}
                      >
                        <CardActionArea
                          component="a"
                          href={`/quiz/${quizItem.slug}-${quizItem.id}`}
                          onClick={(e) => handleQuizClick(quizItem, e)}
                        >
                          <CardMedia className="p-1">
                            <NextImage
                              src={quizItem.banner}
                              alt={quizItem.title}
                            />
                          </CardMedia>
                          <CardContent sx={{ padding: "10px" }}>
                            <div className="d-flex align-items-center justify-content-between mb-2">
                              <span className="badge bg-light text-dark">{ quizItem.type_text }</span>
                              <span className="text-black-50 fs-12 text-limit text-center" style={{'--lines': 1}}>
                                {quizItem.subject?.title || ''} - {quizItem.grade?.title || ''}
                              </span>
                            </div>
                            <h4 className="quiz-name text-limit">
                              {quizItem.title || "__"}
                            </h4>
                          </CardContent>
                        </CardActionArea>
                        <CardActions
                          className="justify-content-between text-black-50 fs-14"
                          sx={{ padding: "5px 10px" }}
                        >
                          <span>{quizItem.questions_count} câu hỏi</span>
                          <span>{quizItem.view} lượt thi</span>
                        </CardActions>
                      </Card>
                    )) }
                  </div>
                )}
              </div>
            )
          })
        )
      ) }
      { other?.quizzes?.length > 0 && (
        <>
          <div className="quiz-grid pb-2 mt-3">
            { other.quizzes.map((quizItem) => (
              <Card
                className="h-100 shadow"
                key={quizItem.id}
              >
                <CardActionArea
                  component="a"
                  href={`/quiz/${quizItem.slug}-${quizItem.id}`}
                  onClick={(e) => handleQuizClick(quizItem, e)}
                >
                  <CardMedia className="p-1">
                    <NextImage
                      src={quizItem.banner}
                      alt={quizItem.title}
                    />
                  </CardMedia>
                  <CardContent sx={{ padding: "10px" }}>
                    <div className="d-flex align-items-center justify-content-between mb-2">
                      <span className="badge bg-light text-dark">{ quizItem.type_text }</span>
                      <span className="text-black-50 fs-12 text-limit text-center" style={{'--lines': 1}}>
                        {quizItem.subject?.title || ''} - {quizItem.grade?.title || ''}
                      </span>
                    </div>
                    <h4 className="quiz-name text-limit">
                      {quizItem.title || "__"}
                    </h4>
                  </CardContent>
                </CardActionArea>
                <CardActions
                  className="justify-content-between text-black-50 fs-14"
                  sx={{ padding: "5px 10px" }}
                >
                  <span>{quizItem.questions_count} câu hỏi</span>
                  <span>{quizItem.view} lượt thi</span>
                </CardActions>
              </Card>
            )) }
          </div>
          <div className="d-flex justify-content-between align-items-center p-4">
            <span>
              {page > 1 && (
                <Link href={`${pathname}?page=${parseInt(page) - 1}`} className="btn btn-primary4 btn-sm d-block active5">
                  <i className="bi bi-arrow-left-short"></i> Prev
                </Link>
              )}
            </span>
            <span>
              {other?.quizzes?.length === 24 && (
                <Link href={`${pathname}?page=${parseInt(page) + 1}`} className="btn btn-primary4 btn-sm d-block active5">
                  Next <i className="bi bi-arrow-right-short"></i>
                </Link>
              )}
            </span>
          </div>
        </>
      ) }

      { selectedQuiz &&
        <QuizPreviewModal
          open={modalOpen}
          onClose={handleCloseModal}
          quiz={selectedQuiz}
        />
      }
    </>
  );
}
