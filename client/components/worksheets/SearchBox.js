"use client";

import React, { useRef } from "react";

import Paper from "@mui/material/Paper";
import InputBase from "@mui/material/InputBase";
import Button from "@mui/material/Button";

import NavigateNextIcon from "@mui/icons-material/NavigateNext";

const SearchBox = () => {
  const inputSearchRef = useRef(null);

  const submitSearch = () => {
    const keyword = inputSearchRef.current.value;

    if (keyword.trim()) {
      alert(keyword);
    }
  };

  return (
    <Paper
      className="mx-auto mb-md-5"
      component="form"
      sx={{
        p: "2px 4px",
        display: "flex",
        alignItems: "center",
        border: "1px solid #e5e5e5",
        width: "100%",
        maxWidth: "600px",
      }}
    >
      <InputBase
        className="input-large"
        sx={{ ml: 1, flex: 1 }}
        placeholder="Tìm kiếm quiz về bất kỳ chủ đề nào"
        inputProps={{ "aria-label": "Tìm kiếm quiz về bất kỳ chủ đề nào" }}
        inputRef={inputSearchRef}
        onKeyDown={(ev) => {
          if (ev.key === "Enter") {
            ev.preventDefault();

            submitSearch();
          }
        }}
      />
      <Button
        variant="text"
        size="large"
        type="button"
        color="inherit"
        aria-label="search"
        onClick={submitSearch}
      >
        <NavigateNextIcon fontSize="large" />
      </Button>
    </Paper>
  );
};

export default SearchBox;
