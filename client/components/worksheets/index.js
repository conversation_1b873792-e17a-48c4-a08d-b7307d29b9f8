import * as React from 'react';

import AppBar from '@mui/material/AppBar';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import Container from '@mui/material/Container';

import Toc from './Toc';
import DrawerMobile from './DrawerMobile';

export const dDrawerWidth = 300;

export default function Worksheets({
  currentPath = null,
  grades = [],
  subjects = [],
  grade = null,
  subject = null,
  course = null,
  toc = null,
  children,
}) {
  return (
    <Container maxWidth={false}>
      <Box className="worksheets d-flex gap-4">
        <Box
          className="mb-3"
          sx={{
            width: dDrawerWidth,
            flexShrink: 0,
            display: { xs: 'none', md: 'block' },
            position: 'sticky',
            top: 70,
            alignSelf: 'flex-start',
          }}
        >
          <Toc
            currentPath={currentPath}
            grades={grades}
            subjects={subjects}
            grade={grade}
            subject={subject}
            course={course}
            toc={toc}
          />
        </Box>
        <Box
          sx={{ flexGrow: 1, width: { xs: '100%', md: `calc(100% - ${dDrawerWidth}px)` } }}
        >
          <DrawerMobile>
            <Toc
              currentPath={currentPath}
              grades={grades}
              subjects={subjects}
              grade={grade}
              subject={subject}
              course={course}
              toc={toc}
            />
          </DrawerMobile>
          {children}
        </Box>
      </Box>
    </Container>
  );
}
