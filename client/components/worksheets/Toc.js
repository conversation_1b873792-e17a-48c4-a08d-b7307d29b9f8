import React, { useCallback } from 'react';

import Box from '@mui/material/Box';
import List from '@mui/material/List';
import Divider from '@mui/material/Divider';

import PersistScroll from './PersistScroll';
import TocNavItem from './TocNavItem';
import { Link } from './Link';

import { buildTree } from '@/utils/helpers';

function renderNavItems({ pages, depth }) {
  return (
    <List sx={{ paddingTop: 0, paddingBottom: 0 }}>
      {pages.reduce((items, page) => reduceChildRoutes({ items, page, depth }), [])}
    </List>
  );
}

function reduceChildRoutes({ items, page, depth }) {
  if (page.inSideNav === false) return items;

  const title = page.subheader || page.title;

  if (page.children && page.children.length >= 1) {
    let firstChild = page;
    if (!firstChild.pathname) {
      firstChild = page.children[0];
      if (firstChild.subheader && firstChild.children) firstChild = firstChild.children[0];
    }
    const subheader = Boolean(page.subheader);
    const [path, hash] = firstChild.pathname.split('#');
    items.push(
      <TocNavItem
        key={page.id || title}
        title={title}
        depth={depth}
        href={{ pathname: path, ...(firstChild.query && { query: firstChild.query }), ...(hash && { hash }) }}
        {...page}
        subheader={subheader}
        initiallyExpanded={page.expanded || false}
        expandable={!subheader}
      >
        {renderNavItems({ pages: page.children, depth: subheader ? depth : depth + 1 })}
      </TocNavItem>
    );
  } else {
    const [path, hash] = page.pathname.split('#');
    items.push(
      <TocNavItem
        key={page.id || title}
        title={title}
        depth={depth}
        href={{ pathname: path, ...(page.query && { query: page.query }), ...(hash && { hash }) }}
        {...page}
        subheader={Boolean(page.subheader)}
      />
    );
  }
  return items;
}

function buildTocsTree(tocs, currentPath) {
  return buildTree(tocs, {
    idKey: 'id',
    parentKey: 'parent_id',
    rootId: null,
    transform: item => ({
      ...item,
      pathname: `/${item.slug}-t${item.id}`,
    }),
    isActive: node => currentPath && node.pathname === currentPath,
    sortFn: (a, b) => {
      if (a.index !== b.index) {
        return a.index - b.index;
      }

      return a.id - b.id;
    },
  });
}

export default function Toc({ currentPath = null, grades, subjects, grade, subject, course, toc }) {
  console.log('Toc');

  const tocTree = useCallback((tocs) => {
    const pages = buildTocsTree(tocs, currentPath);

    return renderNavItems({ pages, depth: 0 });
  }, [currentPath]);

  return (grades.length || subjects.length)  ? (
    <Box sx={{ overflowY: 'auto', border: '1px solid #e3e8ef' }}>
      <PersistScroll slot="side" enabled>
        <div className="bg-white rounded-2 p-3">
          <h3 className="fs-18 text-uppercase fw-normal mb-3 text-primary">Lớp</h3>
          {grades.map((gradeItem) => (
            <Link key={gradeItem.id} className={`btn btn-sm btn-default m-1 fw-medium ${(grade?.id || course?.grade_id || toc?.grade_id) == gradeItem.id ? 'active1' : ''}`} href={subject ? `/${gradeItem.slug}/${subject.slug}` : `/${gradeItem.slug}`}>
              { gradeItem.title }
            </Link>
          ))}
          <Divider className="my-4" />
          <h3 className="fs-18 text-uppercase fw-normal mb-3 text-primary">Môn</h3>
          { subjects.map((subjectItem) => (
            <ul className="list-unstyled" key={subjectItem.id}>
              <li className="mb-2">
                <Link className={`bg-body-secondary rounded d-block p-2 fw-light ${(subject?.id || course?.subject_id || toc?.subject_id) == subjectItem.id ? 'active2' : ''}`} href={grade ? `/${grade.slug}/${subjectItem.slug}` : `/worksheets/${subjectItem.slug}`}>
                  <i className="bi bi-caret-right-fill small me-1"></i> { subjectItem.title }
                </Link>
                { subjectItem?.courses?.length && (
                  subjectItem.courses.length == 1 ? tocTree(subjectItem.courses[0]?.tocs || []) : (
                    subjectItem.courses.map((courseItem) => (
                      <div className="ms-2 mt-2" key={courseItem.id}>
                        <Link className={`d-block p-1 fw-light ${(course?.id || toc?.course_id) == courseItem.id ? 'active3' : ''}`} href={`/${courseItem.slug}-c${courseItem.id}`}>
                          <i className="bi bi-caret-right-fill small me-1"></i> { courseItem.title }
                        </Link>
                        { courseItem?.tocs?.length && tocTree(courseItem.tocs) }
                      </div>
                    ))
                  )
                ) }
              </li>
            </ul>
          ))}
        </div>
      </PersistScroll>
    </Box>
  ) : null;
}
