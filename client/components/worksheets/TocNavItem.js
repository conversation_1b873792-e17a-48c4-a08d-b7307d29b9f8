"use client"

import * as React from 'react';
import PropTypes from 'prop-types';

import { alpha, styled } from '@mui/material/styles';
import Collapse from '@mui/material/Collapse';
import Box from '@mui/material/Box';
import Chip from '@mui/material/Chip';

import KeyboardArrowRightRoundedIcon from '@mui/icons-material/KeyboardArrowRightRounded';

import { Link } from './Link';

function samePageLinkNavigation(event) {
  return (
    event.defaultPrevented ||
    event.button !== 0 || // only left click
    event.metaKey ||
    event.ctrlKey ||
    event.altKey ||
    event.shiftKey
  );
}

function ItemBase({ component: Component = 'div', ...props }) {
  return <Component {...props} />;
}

const Item = styled(ItemBase, {
  shouldForwardProp: (prop) =>
    prop !== 'depth' &&
    prop !== 'hasIcon' &&
    prop !== 'subheader' &&
    prop !== 'expandable',
})(({ theme }) => {
  return [
    {
      ...theme.typography.body2,
      position: 'relative',
      display: 'flex',
      alignItems: 'center',
      borderRadius: 6,
      color: `var(--_color, ${theme.palette.text.secondary})`,
      outline: 0,
      width: '100%',
      padding: 6,
      justifyContent: 'flex-start',
      fontWeight: theme.typography.fontWeightBold,
      transition: theme.transitions.create(['color', 'background-color'], {
        duration: theme.transitions.duration.shortest,
      }),
      fontSize: theme.typography.pxToRem(14),
      textDecoration: 'none',
      paddingLeft: `calc(10px + (var(--_depth) + 1) * 13px - (var(--_expandable) * 21px))`,
      '&::before': {
        content: '""',
        display: 'block',
        position: 'absolute',
        zIndex: 1,
        left: 9.5,
        height: '100%',
        width: 1,
        opacity: 0,
        background: theme.palette.grey[200],
      },
      variants: [
        {
          props: ({ depth }) => depth === 0,
          style: {
            '--_color': theme.palette.text.primary,
          },
        },
        {
          props: ({ depth }) => depth !== 0,
          style: {
            fontWeight: theme.typography.fontWeightMedium,
            '&::before': {
              opacity: 1,
            },
          },
        },
        {
          props: ({ depth }) => depth > 1,
          style: {
            fontWeight: theme.typography.fontWeightRegular,
          },
        },
        {
          props: ({ subheader }) => !subheader,
          style: [
            {
              '&:hover': {
                color: theme.palette.common.black,
                backgroundColor: theme.palette.grey[50],
                '@media (hover: none)': {
                  color: 'var(--_color)',
                  backgroundColor: 'transparent',
                },
              },
            },
          ],
        },
        {
          props: ({ subheader }) => !!subheader,
          style: [
            {
              '--_color': theme.palette.text.tertiary,
              marginTop: theme.spacing(1),
              textTransform: 'uppercase',
              letterSpacing: '.1rem',
              fontWeight: theme.typography.fontWeightBold,
              fontSize: theme.typography.pxToRem(11),
              '&::before': {
                content: '""',
                display: 'block',
                position: 'absolute',
                zIndex: 1,
                left: 9.5,
                height: '55%',
                top: 16,
                width: 1,
                opacity: 0,
                background: theme.palette.grey[100],
              },
              '&::after': {
                content: '""',
                display: 'block',
                position: 'absolute',
                zIndex: 5,
                left: 6,
                height: 8,
                width: 8,
                borderRadius: 2,
                opacity: 0,
                background: alpha(theme.palette.grey[50], 0.5),
                border: '1px solid',
                borderColor: theme.palette.grey[300],
              },
            },
          ],
        },
        {
          props: ({ depth, subheader }) => depth !== 0 && subheader,
          style: {
            '&::after': {
              opacity: 1,
            },
            '&::before': {
              opacity: 1,
            },
          },
        },
        {
          props: ({ depth, expandable }) => depth == 0 && !expandable,
          style: {
            '&::after': {
              content: '""',
              display: 'block',
              position: 'absolute',
              zIndex: 5,
              left: 6,
              height: 8,
              width: 8,
              borderRadius: 2,
              opacity: 1,
              background: alpha(theme.palette.grey[50], 0.5),
              border: '1px solid',
              borderColor: theme.palette.grey[300],
            },
          },
        },
        {
          props: ({ hasIcon }) => !!hasIcon,
          style: {
            paddingLeft: 0,
          },
        },
      ],
      '&.app-drawer-active': {
        // To match browserUrlPreviewMarge
        scrollMarginBottom: 120,
        color: theme.palette.primary[700],
        backgroundColor: theme.palette.primary[100],
        '&:hover': {
          backgroundColor: alpha(theme.palette.primary[100], 0.8),
          color: theme.palette.primary[800],
          '@media (hover: none)': {
            backgroundColor: alpha(theme.palette.primary.main, theme.palette.action.selectedOpacity),
          },
        },
        '&::before': {
          background: theme.palette.primary[400],
        },
      },
      '& .MuiChip-root': {
        marginTop: '2px',
      },
      [theme.breakpoints.up('md')]: {
        paddingTop: 4,
        paddingBottom: 4,
      },
      '& .ItemButtonIcon': {
        marginRight: '6px',
        color: theme.palette.primary.main,
      },
      '&:hover .ItemButtonIcon': {
        color: theme.palette.primary[400],
        '@media (hover: none)': {
          color: theme.palette.primary.main,
        },
      },
    },
  ];
});

const ItemButtonIcon = styled(KeyboardArrowRightRoundedIcon, {
  shouldForwardProp: (prop) => prop !== 'open',
})({
  fontSize: '1rem',
  '&&:last-child': {
    // overrides https://github.com/mui/material-ui/blob/ca7c5c63e64b6a7f55255981f1836a565927b56c/docs/src/modules/brandingTheme.ts#L757-L759
    marginLeft: 0,
  },
  variants: [
    {
      props: { open: true },
      style: {
        transform: 'rotate(90deg)',
      },
    },
  ],
});

const StyledLi = styled('li', {
  shouldForwardProp: (prop) => prop !== 'depth',
})(({ theme }) => ({
  display: 'block',
  variants: [
    {
      props: {
        depth: 0,
      },
      style: {
        padding: theme.spacing(1, '10px', 0, '10px'),
      },
    },
    {
      props: ({ depth }) => depth !== 0,
      style: {
        padding: 0,
      },
    },
  ],
}));

export const sxChip = (color) => [
  (theme) => ({
    ml: 1,
    fontSize: theme.typography.pxToRem(10),
    fontWeight: 'semiBold',
    textTransform: 'uppercase',
    letterSpacing: '.04rem',
    height: '16px',
    border: 1,
    borderColor: theme.palette[color][400],
    bgcolor: alpha(theme.palette[color][200], 0.5),
    color: theme.palette[color][800],
    '&:hover': {
      bgcolor: alpha(theme.palette[color][200], 0.5),
    },
    '& .MuiChip-label': {
      px: '4px',
    },
  }),
];

function DeadLink(props) {
  const { activeClassName, href, noLinkStyle, prefetch, ...other } = props;
  return <div {...other} />;
}

export default function TocNavItem(props) {
  const {
    beta,
    children,
    deprecated,
    depth,
    href,
    icon,
    legacy,
    newFeature,
    planned,
    unstable,
    linkProps,
    onClick,
    initiallyExpanded = false,
    expandable = false,
    plan = 'community',
    subheader,
    title,
    ...other
  } = props;

  const [open, setOpen] = React.useState(initiallyExpanded);

  const handleClick = (event) => {
    if (samePageLinkNavigation(event)) return;
    if (onClick) onClick(event);
    if (expandable) {
      event.preventDefault();
      setOpen((prev) => !prev);
    }
  };

  const hasIcon = !!icon;
  const iconElement = hasIcon ? (
    <Box
      component="span"
      sx={{ '& svg': { fontSize: (theme) => theme.typography.pxToRem(16.5) }, display: 'flex', alignItems: 'center', height: '100%', marginRight: '6px' }}
    >
      {icon}
    </Box>
  ) : null;

  return (
    <StyledLi depth={depth}>
      <Item
        component={subheader ? DeadLink : Link}
        depth={depth}
        hasIcon={hasIcon}
        href={href}
        prefetch={false}
        subheader={subheader}
        expandable={expandable}
        activeClassName={initiallyExpanded ? null : 'app-drawer-active'}
        className={null}
        onClick={handleClick}
        {...linkProps}
        style={{
          ...(linkProps?.style ?? {}),
          '--_depth': depth,
          '--_expandable': expandable ? 1 : 0,
        }}
      >
        {iconElement}
        {expandable && <ItemButtonIcon className="ItemButtonIcon" open={open} />}
        {title}
        {plan === 'pro' && <span className="plan-pro" title="Pro plan" />}
        {plan === 'premium' && <span className="plan-premium" title="Premium plan" />}
        {legacy && <Chip label="Legacy" sx={sxChip('warning')} />}
        {newFeature && <Chip label="New" sx={sxChip('success')} />}
        {planned && <Chip label="Planned" sx={sxChip('grey')} />}
        {unstable && <Chip label="Preview" sx={sxChip('primary')} />}
        {beta && <Chip label="Beta" sx={sxChip('primary')} />}
        {deprecated && <Chip label="Deprecated" sx={sxChip('warning')} />}
      </Item>
      {expandable ? (
        <Collapse in={open} timeout="auto" unmountOnExit>
          {children}
        </Collapse>
      ) : (
        children
      )}
    </StyledLi>
  );
}

TocNavItem.propTypes = {
  beta: PropTypes.bool,
  children: PropTypes.node,
  deprecated: PropTypes.bool,
  depth: PropTypes.number.isRequired,
  expandable: PropTypes.bool,
  href: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
  icon: PropTypes.elementType,
  initiallyExpanded: PropTypes.bool,
  legacy: PropTypes.bool,
  linkProps: PropTypes.object,
  newFeature: PropTypes.bool,
  onClick: PropTypes.func,
  plan: PropTypes.oneOf(['community', 'pro', 'premium']),
  planned: PropTypes.bool,
  subheader: PropTypes.bool.isRequired,
  title: PropTypes.string.isRequired,
  unstable: PropTypes.bool,
};
