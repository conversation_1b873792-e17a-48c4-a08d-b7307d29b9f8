"use client";

import React, { useRef } from 'react';
import { unstable_useEnhancedEffect as useEnhancedEffect } from '@mui/utils';

const savedScrollTop = {};
const BROWSER_URL_PREVIEW_MARGIN = 120;

export default function PersistScroll({ slot, enabled, children }) {
  const rootRef = useRef(null);

  useEnhancedEffect(() => {
    const scrollContainer = rootRef.current?.parentElement;
    const activeDrawerLink = scrollContainer?.querySelector('.app-drawer-active');

    if (
      !enabled ||
      !scrollContainer ||
      !activeDrawerLink ||
      typeof activeDrawerLink.scrollIntoView !== 'function'
    ) {
      return undefined;
    }

    // Khôi phục vị trí scroll lần trước
    scrollContainer.scrollTop = savedScrollTop[slot] || 0;

    const activeBox = activeDrawerLink.getBoundingClientRect();

    // Nếu item đang active nằm ngoài vùng nhìn thấy, cuộn tới nó
    if (
      activeBox.top < 0 ||
      activeBox.bottom + BROWSER_URL_PREVIEW_MARGIN > window.innerHeight
    ) {
      activeDrawerLink.scrollIntoView({ block: 'nearest' });
    }

    return () => {
      // Lưu lại vị trí scroll khi unmount
      savedScrollTop[slot] = scrollContainer.scrollTop;
    };
  }, [enabled, slot]);

  return <div ref={rootRef}>{children}</div>;
}
