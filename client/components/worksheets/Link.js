"use client"

import * as React from 'react';
import clsx from 'clsx';
import { usePathname } from 'next/navigation'
import NextLink from 'next/link';
import MuiLink from '@mui/material/Link';

export const NextLinkComposed = React.forwardRef(function NextLinkComposed(props, ref) {
  const { to, linkAs, ...other } = props;
  return <NextLink href={to} as={linkAs} ref={ref} {...other} prefetch={false} />;
});

export const Link = React.forwardRef(function Link(props, ref) {
  const {
    activeClassName = 'active',
    as,
    className: classNameProps,
    href,
    linkAs: linkAsProp,
    noLinkStyle,
    ...other
  } = props;

  const pathname = typeof href === 'string' ? href : href?.pathname;
  const routerPathname = usePathname();

  const className = clsx(classNameProps, {
    [activeClassName]: routerPathname === pathname && activeClassName,
  });

  const linkAs = linkAsProp || as || href;

  const nextjsProps = {
    to: href,
    linkAs,
  };

  if (noLinkStyle) {
    return <NextLinkComposed className={className} ref={ref} {...nextjsProps} {...other} />;
  }

  return (
    <MuiLink
      component={NextLinkComposed}
      className={className}
      ref={ref}
      {...nextjsProps}
      {...other}
    />
  );
});
