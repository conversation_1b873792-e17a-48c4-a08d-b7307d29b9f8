import React, { useEffect, useRef } from "react";

const ShutterAnimated = ({ className = "", children, open = false, onEnd }) => {
  const wrapperRef = useRef(null);

  useEffect(() => {
    const current = wrapperRef.current;
    if (current) {
      if (open) {
        current.classList.add("hover-state");
      } else {
        current.classList.remove("hover-state");
      }
    }
  }, [open]);

  return (
    <div
      ref={wrapperRef}
      className={`shutter-wrapper ${className}`}
      onTransitionEnd={onEnd}
    >
      <div className="stripe" />
      <div className="stripe" />
      {children}
    </div>
  );
};

export default React.memo(ShutterAnimated);
