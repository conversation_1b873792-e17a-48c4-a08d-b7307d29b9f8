import React, { useState, useEffect, useRef, useCallback } from 'react';
import Box from '@mui/material/Box';
import Popper from '@mui/material/Popper';
import Fade from '@mui/material/Fade';
import Paper from '@mui/material/Paper';
import ClickAwayListener from '@mui/material/ClickAwayListener';

const Dropdown = React.memo(function Dropdown({
  className = '',
  renderToggle,
  renderMenu,
  placement = 'bottom-end',
}) {
  const [anchorEl, setAnchorEl] = useState(null);

  const handleToggle = useCallback((event) => {
    setAnchorEl((prev) => (prev && prev === event.currentTarget ? null : event.currentTarget));
  }, []);

  const handleClose = useCallback(() => {
    setAnchorEl(null);
  }, []);

  const open = Boolean(anchorEl);

  const menuContent =
    typeof renderMenu === 'function' ? renderMenu() : renderMenu;

  return (
    <ClickAwayListener onClickAway={handleClose}>
      <Box className={className} display="inline-block" position="relative">
        {renderToggle({
          onClick: handleToggle,
        })}
        <Popper
          // Note: The following zIndex style is specifically for documentation purposes and may not be necessary in your application.
          sx={{ zIndex: 1200 }}
          open={open}
          anchorEl={anchorEl}
          placement={placement}
          transition
        >
          {({ TransitionProps }) => (
            <Fade {...TransitionProps} timeout={350}>
              <Paper>
                {menuContent}
              </Paper>
            </Fade>
          )}
        </Popper>
      </Box>
    </ClickAwayListener>
  );
});

export default Dropdown;
