import React, { memo, useMemo } from 'react';
import parse, { domToReact } from 'html-react-parser';

import AutoResizeInput from '@/components/AutoResizeInput';

import useMathJax from '@/hooks/useMathJax';

const HtmlContent = memo(({ html, type }) => {
  const reactTree = useMemo(() => {
    if (type === 'BLANK') {
      return parse(html, {
        replace: (node) => {
          if (
            node.type === 'tag' &&
            node.name === 'span' &&
            node.attribs?.class?.split(' ').includes('flag-el')
          ) {
            const { id } = node.attribs;

            return (
              <AutoResizeInput
                key={id}
                defaultValue=""
                value=""
                placeholder=""
              />
            );
          }
        }
      });
    }

    return parse(html);
  }, [html, type]);

  useMathJax([reactTree]);

  return <div>{reactTree}</div>;
});

HtmlContent.displayName = "HtmlContent";

export default HtmlContent;
