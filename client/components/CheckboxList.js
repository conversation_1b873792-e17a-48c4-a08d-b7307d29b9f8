import React, { useState, useCallback, memo } from 'react';
import FormControlLabel from "@mui/material/FormControlLabel";
import Checkbox from "@mui/material/Checkbox";
import Button from "@mui/material/Button";
import Box from "@mui/material/Box";

const CheckboxList = ({ options = [], selectedValues = [], onChange, defaultCount = 3, className="col-md-4" }) => {
    console.log('CheckboxList');
    const [showMore, setShowMore] = useState(false);

    const handleToggleShowMore = useCallback(() => {
        setShowMore((prev) => !prev);
    }, []);

    const displayedOptions = showMore ? options : options.slice(0, defaultCount);

    const handleCheckboxChange = (value) => (event) => {
        onChange(value, event.target.checked);
    };

    return (
        <>
            <Box className="row">
                {displayedOptions.map((option) => (
                    <div key={option.value} className={className}>
                        <FormControlLabel
                            control={
                                <Checkbox
                                  checked={selectedValues.includes(option.value)}
                                  onChange={handleCheckboxChange(option.value)}
                                  size="small"
                                />
                            }
                            label={option.label}
                        />
                    </div>
                ))}
            </Box>
            {options.length > defaultCount && (
            <Box sx={{ mt: 1 }}>
                <Button variant="text" onClick={handleToggleShowMore}>
                    {showMore ? (
                        <small>
                            Thu gọn
                            <i className="bi bi-chevron-up ms-2"></i>
                        </small>
                    ) : (
                        <small>
                            Xem thêm
                            <i className="bi bi-chevron-down ms-2"></i>
                        </small>
                    )}
                </Button>
            </Box>
            )}
        </>
    );
};

export default memo(CheckboxList);
