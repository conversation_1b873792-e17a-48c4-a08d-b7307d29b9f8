"use client";

import React from "react";
import { styled } from "@mui/material/styles";
import Paper from "@mui/material/Paper";
import Typography from "@mui/material/Typography";
import CloudUploadIcon from "@mui/icons-material/CloudUpload";
import ExcelIcon from "@mui/icons-material/TableView";
import FileIcon from "@mui/icons-material/Description";

const VisuallyHiddenInput = styled("input")({
  clip: "rect(0 0 0 0)",
  clipPath: "inset(50%)",
  height: 1,
  overflow: "hidden",
  position: "absolute",
  bottom: 0,
  left: 0,
  whiteSpace: "nowrap",
  width: 1,
});

const FileUploadBox = styled(Paper, {
  shouldForwardProp: (prop) => prop !== 'isDragOver',
})(({ theme, isDragOver }) => ({
  border: `2px dashed ${isDragOver ? theme.palette.primary.main : "#e0e0e0"}`,
  borderRadius: theme.spacing(1),
  padding: "2rem 1rem",
  textAlign: "center",
  cursor: "pointer",
  transition: "border-color 0.3s ease",
  backgroundColor: isDragOver ? theme.palette.action.hover : "transparent",
  "&:hover": {
    borderColor: theme.palette.primary.main,
    backgroundColor: theme.palette.action.hover,
  },
}));

/**
 * Reusable file upload area component with drag and drop support
 * @param {Object} props - Component props
 * @param {boolean} props.isDragOver - Whether drag is currently over the area
 * @param {Function} props.onDrop - Drop event handler
 * @param {Function} props.onDragOver - Drag over event handler
 * @param {Function} props.onDragLeave - Drag leave event handler
 * @param {Function} props.onClick - Click event handler
 * @param {Function} props.onFileInputChange - File input change handler
 * @param {React.RefObject} props.fileInputRef - Ref for the file input
 * @param {Object} props.fileConfig - File configuration object
 * @param {string} props.title - Custom title text
 * @param {string} props.subtitle - Custom subtitle text
 * @param {React.ReactNode} props.icon - Custom icon component
 * @param {Object} props.sx - Additional styling
 * @returns {React.Component} FileUploadArea component
 */
const FileUploadArea = ({
  isDragOver = false,
  onDrop,
  onDragOver,
  onDragLeave,
  onClick,
  onFileInputChange,
  fileInputRef,
  fileConfig,
  title,
  subtitle,
  icon,
  sx = {},
  ...props
}) => {
  const defaultTitle = fileConfig?.fileType === 'word'
    ? "Kéo thả file Docx hoặc click để chọn file"
    : "Kéo thả file Excel hoặc click để chọn file";

  const defaultSubtitle = `${fileConfig?.acceptMessage || "Chỉ chấp nhận file"} (tối đa ${fileConfig?.maxSizeMB || 10}MB)`;

  return (
    <FileUploadBox
      isDragOver={isDragOver}
      onDrop={onDrop}
      onDragOver={onDragOver}
      onDragLeave={onDragLeave}
      onClick={onClick}
      sx={sx}
      {...props}
    >
      {icon || <CloudUploadIcon sx={{fontSize: 40, color: "text.secondary", mb: 2}}/>}

      <Typography variant="h6" gutterBottom>
        {title || defaultTitle}
      </Typography>

      <Typography variant="caption" color="text.secondary">
        {/*{subtitle || defaultSubtitle}*/}
        Các định dạng được hỗ trợ: {fileConfig?.extensions || ".xlsx,.xls"}
        <br />
        <span className="text-ds-dark-500-20">Giới hạn kích thước tệp: {fileConfig?.maxSizeMB || 10}MB, không dài quá 30 trang</span>
      </Typography>

      <VisuallyHiddenInput
        ref={fileInputRef}
        type="file"
        accept={fileConfig?.extensions || ".xlsx,.xls"}
        onChange={onFileInputChange}
      />
    </FileUploadBox>
  );
};

export default FileUploadArea;
