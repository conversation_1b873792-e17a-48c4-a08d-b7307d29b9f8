"use client";

import React from "react";
import { styled } from "@mui/material/styles";
import Paper from "@mui/material/Paper";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import Chip from "@mui/material/Chip";
import IconButton from "@mui/material/IconButton";
import DeleteIcon from "@mui/icons-material/Delete";
import { formatFileSize } from "@/utils/importHelper";

const SelectedFileBox = styled(Paper)(({ theme }) => ({
  border: `2px solid ${theme.palette.primary.main}`,
  borderRadius: theme.spacing(1),
  padding: theme.spacing(2),
  backgroundColor: theme.palette.primary.light + "10",
  display: "flex",
  alignItems: "center",
  justifyContent: "space-between",
  marginBottom: theme.spacing(2),
  minHeight: "100px",
}));

/**
 * Reusable selected file display component
 * @param {Object} props - Component props
 * @param {File} props.file - Selected file object
 * @param {Function} props.onRemove - Remove file handler
 * @param {Object} props.fileConfig - File configuration object
 * @param {boolean} props.showRemoveButton - Whether to show remove button
 * @param {string} props.chipLabel - Custom chip label
 * @param {string} props.chipColor - Chip color
 * @param {string} props.fileType - File type (for icon)
 * @param {Object} props.sx - Additional styling
 * @returns {React.Component} SelectedFileDisplay component
 */
const SelectedFileDisplay = ({
  file,
  onRemove,
  fileConfig,
  showRemoveButton = true,
  fileType = "excel",
  chipLabel,
  chipColor,
  sx = {},
  ...props
}) => {
  if (!file) return null;

  const displayChipLabel = chipLabel || fileConfig?.chipLabel || "File";
  const displayChipColor = chipColor || fileConfig?.chipColor || "success";
  const displayFileIcon = fileConfig?.icon || "bi-file-earmark";

  return (
    <SelectedFileBox sx={sx} {...props}>
      <Box sx={{ display: 'flex', alignItems: 'center', flex: 1 }}>
        <Box sx={{ mr: 2 }}>
          <Typography variant="subtitle1" fontWeight="medium">
            <i className={`bi ${displayFileIcon} me-2 fs-20 text-${displayChipColor}`}></i>
            {file.name}
          </Typography>
          <Typography variant="body2" color="text.secondary" className="ms-4">
            {formatFileSize(file.size)}
          </Typography>
        </Box>
        <Chip
          label={displayChipLabel}
          color={displayChipColor}
          size="small"
          variant="outlined"
        />
      </Box>

      {showRemoveButton && onRemove && (
        <IconButton
          onClick={onRemove}
          color="error"
          size="small"
          aria-label="Xóa file đã chọn"
        >
          <DeleteIcon fontSize="small" />
        </IconButton>
      )}
    </SelectedFileBox>
  );
};

export default SelectedFileDisplay;
