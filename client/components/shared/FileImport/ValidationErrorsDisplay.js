"use client";

import React from "react";
import Box from "@mui/material/Box";
import Alert from "@mui/material/Alert";
import Typography from "@mui/material/Typography";
import List from "@mui/material/List";
import ListItem from "@mui/material/ListItem";
import ListItemText from "@mui/material/ListItemText";

/**
 * Reusable validation errors display component
 * @param {Object} props - Component props
 * @param {Array} props.errors - Array of validation errors
 * @param {string} props.title - Custom title for errors section
 * @param {number} props.maxHeight - Maximum height for scrollable error list
 * @param {Object} props.sx - Additional styling
 * @returns {React.Component} ValidationErrorsDisplay component
 */
const ValidationErrorsDisplay = ({
  errors = [],
  title,
  maxHeight = 150,
  sx = {},
  ...props
}) => {
  if (!errors || errors.length === 0) return null;

  const defaultTitle = `Có ${errors.length} lỗi cần kh<PERSON>c phục:`;

  return (
    <Box mt={2} mb={2} sx={sx} {...props}>
      <Alert severity="error" sx={{ mb: 2 }}>
        <Typography variant="subtitle2" className="mb-0" gutterBottom>
          {title || defaultTitle}
        </Typography>
      </Alert>

      <List
        dense
        sx={{
          maxHeight,
          overflow: 'auto',
          border: '2px solid #eee',
          borderRadius: 1
        }}
      >
        {errors.map((error, index) => {
          // Handle different error formats
          let errorText = '';

          if (typeof error === 'string') {
            errorText = error;
          } else if (error.name && error.errors && error.row) {
            // Format for import validation errors
            errorText = `${error.name}: ${error.errors.join(", ")} (Dòng ${error.row})`;
          } else if (error.message) {
            errorText = error.message;
          } else {
            errorText = JSON.stringify(error);
          }

          return (
            <ListItem key={index} sx={{ py: 0.5 }}>
              <ListItemText
                primary={errorText}
                primaryTypographyProps={{
                  variant: "body2",
                  color: "error"
                }}
              />
            </ListItem>
          );
        })}
      </List>
    </Box>
  );
};

export default ValidationErrorsDisplay;
