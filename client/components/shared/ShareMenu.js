"use client";

import React, { useEffect, useState } from "react";
import toast from "react-hot-toast";
import dynamic from "next/dynamic";

import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import Popover from '@mui/material/Popover';
import Box from '@mui/material/Box';
import QrDownload from "@/components/quizzes/QrDownload";

const QRCodeCanvas = dynamic(
  () => import("qrcode.react").then(m => m.QRCodeCanvas),
  { ssr: false }
);

const ShareMenu = ({
  quiz,
  qrContainerRef,
  widthClass = "",
  positionToast = "top-center",
  customUrl = null
}) => {
  const [anchorEl, setAnchorEl] = useState(null);
  const [qrAnchorEl, setQrAnchorEl] = useState(null);
  const [shareUrl, setShareUrl] = useState("");

  useEffect(() => {
    if (customUrl) {
      setShareUrl(customUrl);
    } else if (typeof window !== "undefined") {
      setShareUrl(window.location.href);
    }
  }, [customUrl]);

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
    setQrAnchorEl(null);
  };

  const handleQrClick = (event) => {
    setQrAnchorEl(event.currentTarget);
  };

  const handleCopyLink = () => {
    if (navigator?.clipboard && shareUrl) {
      navigator.clipboard
        .writeText(shareUrl)
        .then(() => {
          toast.success('Liên kết đã được sao chép', {
            position: positionToast,
            icon: '👏',
            style: {
              fontSize: '14px',
              borderRadius: '20px',
              backgroundColor: '#9a42926e',
              color: '#fff'
            }
          });
          handleClose();
        })
        .catch((err) => {
          console.error("Lỗi copy:", err);
          toast.error('Không thể sao chép liên kết');
        });
    } else {
      toast.error('Trình duyệt của bạn không hỗ trợ copy.');
    }
  };

  return (
    <>
      <button
        type="button"
        onClick={handleClick}
        className={`btn btn-default ${widthClass}`}
        title="Chia sẻ"
      >
        <i className="bi bi-share me-2"></i>
        <span className="fw-medium">Chia sẻ</span>
      </button>

      <Menu
        id="share-menu"
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
      >
        <MenuItem onClick={handleCopyLink} className="share-item">
          <i className="bi bi-link-45deg me-2"></i>
          Sao chép đường dẫn
        </MenuItem>
        <MenuItem onClick={handleQrClick} className="share-item">
          <i className="bi bi-qr-code me-2"></i>
          Quét mã QR
        </MenuItem>
      </Menu>

      <Popover
        id="qr-code-popover"
        open={Boolean(qrAnchorEl)}
        anchorEl={qrAnchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
      >
        <Box textAlign="center" ref={qrContainerRef}>
          <div className="d-flex flex-column align-items-center">
            {shareUrl && (
              <QRCodeCanvas
                value={shareUrl}
                size={200}
                title={quiz?.title || "Mã QR chia sẻ"}
                includeMargin
              />
            )}
            <QrDownload qrContainerRef={qrContainerRef} />
          </div>
        </Box>
      </Popover>
    </>
  );
};

export default ShareMenu;
