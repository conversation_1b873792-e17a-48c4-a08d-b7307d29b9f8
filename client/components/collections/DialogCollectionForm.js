"use client";

import { forwardRef, useRef, useEffect } from "react";
import { useDispatch } from "react-redux";
import toast from "react-hot-toast";

import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import CircularProgress from "@mui/material/CircularProgress";
import Dialog from "@mui/material/Dialog";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import FormControl from "@mui/material/FormControl";
import InputLabel from "@mui/material/InputLabel";
import OutlinedInput from "@mui/material/OutlinedInput";
import FormHelperText from "@mui/material/FormHelperText";
import Slide from "@mui/material/Slide";

import * as Yup from "yup";
import { Formik } from "formik";

import { storeCollection, updateCollection } from "@/actions/collectionAction";
import { setNoti } from "@/slices/notiSlice";

const Transition = forwardRef(function Transition(props, ref) {
  return <Slide direction="up" ref={ref} {...props} />;
});

const DialogCollectionForm = ({ open, onClose, onSubmitSuccess, collection }) => {
  const dispatch = useDispatch();
  const formikRef = useRef(null);

  useEffect(() => {
    if (open && formikRef.current) {
      formikRef.current.resetForm();

      if (collection?.id) {
        formikRef.current.setFieldValue('title', collection?.title || '');
      }
    }
  }, [open, collection]);

  return (
    <Dialog
      open={open}
      onClose={onClose}
      aria-labelledby="collection-dialog-title"
      maxWidth="sm"
      fullWidth
      TransitionComponent={Transition}
      keepMounted
    >
      <DialogTitle className="h5">
        {collection?.id ? "Chỉnh sửa bộ sưu tập" : "Tạo bộ sưu tập mới"}
      </DialogTitle>
      <DialogContent>
        <Formik
          innerRef={formikRef}
          initialValues={{
            title: collection?.title || "",
          }}
          validationSchema={Yup.object().shape({
            title: Yup.string()
              .max(50, "Tên bộ sưu tập không được quá 50 ký tự")
              .required("Tên bộ sưu tập không được để trống"),
          })}
          onSubmit={async (values, { setErrors, setSubmitting, resetForm }) => {
            try {
              setSubmitting(true);

              const {data: newCollection } = collection?.id
                ? await updateCollection(collection.id, {
                  title: values.title.trim()
                })
                : await storeCollection({
                  title: values.title.trim()
                });

              if (newCollection) {
                onSubmitSuccess(newCollection, collection?.id ? true : false);
                resetForm();
                onClose();
              } else {
                throw new Error("Đã xảy ra lỗi.");
              }
            } catch (error) {
              if (error?.status === 422) {
                setErrors(error.errors);
              } else {
                dispatch(setNoti(error));
              }
            } finally {
              setSubmitting(false);
            }
          }}
        >
          {({
            errors,
            handleBlur,
            handleChange,
            handleSubmit,
            isSubmitting,
            touched,
            values,
          }) => (
            <form noValidate onSubmit={handleSubmit}>
              <Box className="d-flex flex-column gap-4 py-3">
                <FormControl
                  fullWidth
                  error={Boolean(touched.title && errors.title)}
                >
                  <InputLabel htmlFor="outlined-adornment-title">
                    Tên bộ sưu tập
                  </InputLabel>
                  <OutlinedInput
                    id="outlined-adornment-title"
                    type="text"
                    label="Tên bộ sưu tập"
                    value={values.title}
                    name="title"
                    onBlur={handleBlur}
                    onChange={handleChange}
                    autoFocus
                  />
                  {touched.title && errors.title && (
                    <FormHelperText
                      error
                      id="standard-weight-helper-text-title"
                    >
                      {errors.title}
                    </FormHelperText>
                  )}
                </FormControl>
              </Box>
              <Box className="text-end" sx={{ mt: 2 }}>
                <Button
                  variant="outlined"
                  type="button"
                  onClick={onClose}
                  color="inherit"
                  size="small"
                  sx={{ mr: 2 }}
                >
                  Hủy
                </Button>
                <Button
                  disableElevation
                  disabled={isSubmitting}
                  size="small"
                  type="submit"
                  variant="contained"
                  color="secondary"
                >
                  {isSubmitting ? <CircularProgress size={24} /> : "Lưu"}
                </Button>
              </Box>
            </form>
          )}
        </Formik>
      </DialogContent>
    </Dialog>
  );
};

export default DialogCollectionForm;
