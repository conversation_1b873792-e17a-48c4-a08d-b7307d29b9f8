"use client";

import { useState, useEffect } from "react";

import { styled } from "@mui/material/styles";
import Paper from "@mui/material/Paper";
import InputBase from "@mui/material/InputBase";
import IconButton from "@mui/material/IconButton";

import SearchIcon from "@mui/icons-material/Search";

import { useDebounce } from "@/hooks/useDebounce";

const GroupInputSearch = styled(Paper)(() => {
  return {
    padding: "2px 4px",
    display: "flex",
    alignItems: "center",
    border: "1px solid #e5e5e5",
    width: "100%",
    height: "39px",
    maxWidth: "300px",
    borderRadius: "8px",
  };
});

const SearchInput = ({ onSearch, delay = 500, ...others }) => {
  console.log('SearchInput');
  const [value, setValue] = useState("");
  const debouncedValue = useDebounce(value, delay);

  useEffect(() => {
    onSearch(debouncedValue);
  }, [debouncedValue, onSearch]);

  return (
    <GroupInputSearch>
      <IconButton
        size="small"
        color="inherit"
        aria-label="search"
        sx={{ minWidth: "20px" }}
        disabled
      >
        <SearchIcon />
      </IconButton>
      <InputBase
        {...others}
        value={value}
        onChange={(e) => setValue(e.target.value)}
      />
    </GroupInputSearch>
  );
};

export default SearchInput;
