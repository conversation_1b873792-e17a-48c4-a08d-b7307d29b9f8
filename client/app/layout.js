import { <PERSON><PERSON> } from "next/font/google";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ScripsClient } from "./Provider";

import { AppRouterCacheProvider } from "@mui/material-nextjs/v15-appRouter";

import { NextIntlClientProvider } from "next-intl";
import { getLocale, getMessages } from "next-intl/server";

// import Script from "next/script";

import "@/styles/global.css";
import "@/styles/style.scss";

import { siteMetadata } from "@/constant";

const roboto = Roboto({
  weight: ["400", "500", "600", "700"],
  subsets: ["vietnamese"],
});

export const metadata = {
  metadataBase: siteMetadata.siteUrl,
  title: siteMetadata.title,
  description: siteMetadata.description,
  robots: {
    follow: true,
    index: true,
  },
  alternates: {
    canonical: '/',
  },
  openGraph: {
    type: 'website',
    title: siteMetadata.title,
    description: siteMetadata.description,
    siteName: siteMetadata.siteName,
  },
  twitter: {
    card: 'summary_large_image',
    title: siteMetadata.title,
    description: siteMetadata.description,
    images: [`/twitter-image.png`],
  },
};

export default async function RootLayout({ children }) {
  const locale = await getLocale();
  const messages = await getMessages();

  return (
    <StoreProvider>
      <html lang={locale}>
        <head>
          <link
            rel="stylesheet"
            href="/css/custom.css"
          />
        </head>
        <body className={roboto.className}>
          <AppRouterCacheProvider options={{ key: "css", enableCssLayer: true }}>
            <NextIntlClientProvider messages={messages}>
              <AppProvider>
                {children}
              </AppProvider>
            </NextIntlClientProvider>
          </AppRouterCacheProvider>
          <ScripsClient />
        </body>
      </html>
    </StoreProvider>
  );
}
