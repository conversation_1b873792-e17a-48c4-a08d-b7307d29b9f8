'use server'

import { cookies } from 'next/headers'
import { decodeBase64Cookies } from "@/utils/helpers";
import { userCookieName } from "@/constant";
 
export async function getDataUserFromCookie(key = 'token') {
    try {
        const userCookie = (await cookies()).get(userCookieName)?.value;

        if (!userCookie) return null;

        const decoded = decodeBase64Cookies(userCookie);

        console.debug('[SSR userCookie decoded]', JSON.stringify(decoded));

        return decoded?.[key] ?? null;
    } catch {
        return null;
    }
}
