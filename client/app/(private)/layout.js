import { redirect } from 'next/navigation';
import ProtectedLayout from "@/layout/ProtectedLayout";

import { getDataUserFromCookie } from '@/app/actions';

export const dynamic = 'force-dynamic';
// export const fetchCache = 'force-no-store';
// export const revalidate = 0;

export default async function Private({ children }) {
  const user = await getDataUserFromCookie('user');

  if (!user) {
    redirect('/login?action=1');
  }

  return <ProtectedLayout initialUser={user}>{children}</ProtectedLayout>;
}
