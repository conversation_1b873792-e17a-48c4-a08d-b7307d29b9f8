import MainLayout from "@/layout/MainLayout";
import { notFound } from "next/navigation";

import { fetchClassroom } from "@/actions/classroomAction";

import Error from "@/app/error";
import ClassroomShow from "@/components/classrooms/ClassroomShow";
import InitNoti from "@/components/InitNoti";

import { getDataUserFromCookie } from '@/app/actions';

export async function generateMetadata({ params }, parent) {
  const { id } = await params;
  const token = await getDataUserFromCookie('token');
  const classroom = await fetchClassroom(id, token);

  if (classroom?.status === 404) {
    return { title: "Classroom Not Found" };
  }

  if (classroom?.status === 403) {
    return { title: "Unauthorized Access" };
  }

  return {
    title: classroom?.data?.title || "Something went wrong!",
    alternates: {
      canonical: `/dashboard/classroom/${id}`,
    },
  };
}

export default async function Page({ params }) {
  console.log("Classroom Detail");
  const { id } = await params;
  const token = await getDataUserFromCookie('token');
  const classroom = await fetchClassroom(id, token);

  if (classroom?.status == 403) {
    return <Error statusCode={classroom.status} />;
  }

  if (classroom?.status == 404) {
    return notFound();
  }

  return (
    <MainLayout>
      { classroom?.data ? <ClassroomShow initClassroom={classroom.data} /> : <InitNoti initValue={classroom} /> }
    </MainLayout>
  );
}
