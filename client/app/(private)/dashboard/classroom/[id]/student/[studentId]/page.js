import MainLayout from "@/layout/MainLayout";

import StudentReport from "@/components/reports/StudentReport";

export async function generateMetadata({ params }, parent) {
  const { id, studentId } = await params;

  return {
    title: `<PERSON> tiết học sinh - <PERSON><PERSON><PERSON> học ${id}`,
    alternates: {
      canonical: `/dashboard/classroom/${id}/student/${studentId}`,
    },
  };
}

export default async function Page({ params }) {
  const { id, studentId } = await params;

  return (
    <MainLayout>
      <StudentReport
        studentId={studentId}
        classroomId={id}
      />
    </MainLayout>
  );
}
