import dynamic from "next/dynamic";
import MainLayout from "@/layout/MainLayout";
import SearchBox from "@/components/dashboard/SearchBox";
import InitNoti from "@/components/InitNoti";
import Container from '@mui/material/Container';
import DashboardClient from "@/components/dashboard/DashboardClient";

import { getDataUserFromCookie } from '@/app/actions';
import { fetchRecentActivity } from "@/actions/userAction";
import { fetchSubjects } from "@/actions/subjectAction";

const DisplayUserData = dynamic(
  () => import("@/components/dashboard/DisplayUserData"),
  {
    loading: () => <p className="text-center">Loading...</p>,
  }
);

const ListSubjects = dynamic(
  () => import("@/components/dashboard/ListSubjects"),
  {
    loading: () => <p className="text-center">Loading...</p>,
  }
);

const DisplaySubjects = dynamic(
  () => import("@/components/dashboard/DisplaySubjects"),
  {
    loading: () => <p className="text-center">Loading...</p>,
  }
);

export default async function Dashboard() {
  const token = await getDataUserFromCookie('token');
  const recentActivity = await fetchRecentActivity({ token });
  const [subjectsResponse, subjectsWithQuizResponse] = await Promise.all([
    fetchSubjects({ page: 1, t: Date.now() }),
    fetchSubjects({ page: 1, quiz: true, t: Date.now() }),
  ]);

  return (
    <MainLayout>
      <DashboardClient>
        <Container maxWidth="cxl">
          <SearchBox />
          {subjectsResponse?.data && <ListSubjects subjects={subjectsResponse.data} />}
          {recentActivity?.data ? <DisplayUserData initialDatas={recentActivity.data} /> : <InitNoti initValue={recentActivity} />}
          {subjectsWithQuizResponse?.data && (
            <DisplaySubjects initialDatas={subjectsWithQuizResponse.data} />
          )}
        </Container>
      </DashboardClient>
    </MainLayout>
  );
}
