import MainLayout from "@/layout/MainLayout";
import { notFound } from "next/navigation";

import {fetchQuiz, quizInfo} from "@/actions/quizAction";
import QuizCreate from "@/components/quizzes/QuizCreate";
import InitNoti from "@/components/InitNoti";

import { getDataUserFromCookie } from '@/app/actions';

export const metadata = {
  title: 'Start Homework Game',
}

export default async function Page({ params }) {
  console.log("Quiz Homework");
  const { id } = await params;
  const token = await getDataUserFromCookie('token');
  const quiz = await quizInfo(id, token);

  if (quiz?.status == 404) {
    return notFound();
  }

  return (
    <MainLayout>
      { quiz?.data ? <QuizCreate quiz={quiz.data} /> : <InitNoti initValue={quiz} /> }
    </MainLayout>
  );
}
