import MinimalLayout from "@/layout/MinimalLayout";
import AuthLogin from "@/components/authentication/AuthLogin";

export const metadata = {
  title: '<PERSON>ăng nhập vào hệ thống',
  alternates: {
    canonical: '/login',
  },
}

export const dynamic = 'force-dynamic';
export const revalidate = 0;

const Login = async () => {
  return (
    <MinimalLayout>
      <AuthLogin />
    </MinimalLayout>
  );
};

export default Login;
