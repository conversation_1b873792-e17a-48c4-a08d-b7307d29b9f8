import MinimalLayout from "@/layout/MinimalLayout";
import AuthRegister from "@/components/authentication/AuthRegister";

export const metadata = {
  title: '<PERSON>ăng ký tài kho<PERSON>n',
  alternates: {
    canonical: '/register',
  },
}

export const dynamic = 'force-dynamic';
export const revalidate = 0;

const Register = () => {
  return (
    <MinimalLayout>
      <AuthRegister />
    </MinimalLayout>
  );
};

export default Register;
