import MinimalLayout from "@/layout/MinimalLayout";
import AuthResetPassword from "@/components/authentication/AuthResetPassword";

export const metadata = {
  title: 'Đặt lại mật khẩu',
  alternates: {
    canonical: '/reset-password',
  },
}

export const dynamic = 'force-dynamic';
export const revalidate = 0;

const ResetPassword = ({ params }) => {
  return (
    <MinimalLayout>
      <AuthResetPassword params={params} />
    </MinimalLayout>
  );
};

export default ResetPassword;
