import MinimalLayout from "@/layout/MinimalLayout";
import AuthForgotPassword from "@/components/authentication/AuthForgotPassword";

export const metadata = {
  title: 'Quên mật khẩu',
  alternates: {
    canonical: '/forgot-password',
  },
}

export const dynamic = 'force-dynamic';
export const revalidate = 0;

const ForgotPassword = () => {
  return (
    <MinimalLayout>
      <AuthForgotPassword />
    </MinimalLayout>
  );
};

export default ForgotPassword;
