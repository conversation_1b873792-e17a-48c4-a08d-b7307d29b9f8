"use client";

import { useState, useEffect } from 'react';

export default function StateTestPage() {
  const [showAutoProgress, setShowAutoProgress] = useState(false);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [isSubmit, setIsSubmit] = useState(false);

  // Debug: Log showAutoProgress changes
  useEffect(() => {
    console.log('🔍 showAutoProgress changed:', showAutoProgress);
  }, [showAutoProgress]);

  // Simulate the problematic useEffect
  useEffect(() => {
    console.log('🔄 Question useEffect triggered:', { 
      currentQuestionIndex,
      showAutoProgress 
    });
    
    // This would reset showAutoProgress
    // setShowAutoProgress(false);
    setIsSubmit(false);
  }, [currentQuestionIndex]);

  const simulateSubmit = () => {
    console.log('🔥 Simulating submit...');
    setIsSubmit(true);
    setShowAutoProgress(true);
    console.log('🔥 Auto progress should be true now');
  };

  const simulateNextQuestion = () => {
    console.log('➡️ Moving to next question...');
    setCurrentQuestionIndex(prev => prev + 1);
  };

  const resetTest = () => {
    setShowAutoProgress(false);
    setCurrentQuestionIndex(0);
    setIsSubmit(false);
  };

  return (
    <div style={{ 
      padding: '20px', 
      fontFamily: 'Arial, sans-serif',
      backgroundColor: '#f0f0f0',
      minHeight: '100vh'
    }}>
      <h1>State Management Test</h1>
      
      <div style={{ 
        padding: '20px', 
        backgroundColor: '#fff', 
        borderRadius: '8px',
        marginBottom: '20px'
      }}>
        <h3>Current State:</h3>
        <p><strong>showAutoProgress:</strong> {showAutoProgress ? 'true' : 'false'}</p>
        <p><strong>currentQuestionIndex:</strong> {currentQuestionIndex}</p>
        <p><strong>isSubmit:</strong> {isSubmit ? 'true' : 'false'}</p>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <button 
          onClick={simulateSubmit}
          style={{
            padding: '10px 20px',
            margin: '5px',
            backgroundColor: '#2ecc71',
            color: '#fff',
            border: 'none',
            borderRadius: '5px',
            cursor: 'pointer'
          }}
        >
          Simulate Submit (should set showAutoProgress = true)
        </button>
        
        <button 
          onClick={simulateNextQuestion}
          style={{
            padding: '10px 20px',
            margin: '5px',
            backgroundColor: '#3498db',
            color: '#fff',
            border: 'none',
            borderRadius: '5px',
            cursor: 'pointer'
          }}
        >
          Simulate Next Question (currentQuestionIndex++)
        </button>
        
        <button 
          onClick={resetTest}
          style={{
            padding: '10px 20px',
            margin: '5px',
            backgroundColor: '#95a5a6',
            color: '#fff',
            border: 'none',
            borderRadius: '5px',
            cursor: 'pointer'
          }}
        >
          Reset Test
        </button>
      </div>

      <div style={{ 
        padding: '20px', 
        backgroundColor: '#fff', 
        borderRadius: '8px'
      }}>
        <h3>Test Scenario:</h3>
        <ol>
          <li>Click "Simulate Submit" → showAutoProgress should become true</li>
          <li>Click "Simulate Next Question" → currentQuestionIndex increases</li>
          <li>Check if showAutoProgress remains true or gets reset to false</li>
          <li>Open console to see detailed logs</li>
        </ol>
        
        <div style={{
          marginTop: '20px',
          padding: '10px',
          backgroundColor: showAutoProgress ? '#d5f4e6' : '#ffeaa7',
          borderRadius: '5px',
          border: `2px solid ${showAutoProgress ? '#2ecc71' : '#fdcb6e'}`
        }}>
          <strong>Auto Progress Status:</strong> {showAutoProgress ? '✅ VISIBLE' : '❌ HIDDEN'}
        </div>
      </div>
    </div>
  );
}

export const metadata = {
  title: 'State Management Test',
  description: 'Test page for debugging state management issues',
};
