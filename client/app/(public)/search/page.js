import { Suspense } from "react";
import MinimalLayout from "@/layout/MinimalLayout";
import SearchApp from "@/components/search";
import Loader from '@/components/Loader'

export const dynamic = 'force-dynamic';
export const revalidate = 0;
export const fetchCache = 'default-no-store';

export const metadata = {
  title: 'Tìm kiếm đề thi',
  alternates: {
    canonical: `/search`,
  },
}

export default function Page() {
  return (
    <MinimalLayout>
      <Suspense fallback={<Loader />}>
        <SearchApp />
      </Suspense> 
    </MinimalLayout>
  );
}
