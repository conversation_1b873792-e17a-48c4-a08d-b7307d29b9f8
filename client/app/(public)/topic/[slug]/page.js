import { notFound, redirect } from "next/navigation";
import dynamic from "next/dynamic";
import MainLayout from "@/layout/MainLayout";
import Container from '@mui/material/Container';

import { decodeSlug } from "@/utils/helpers";

import { fetchQuizsOfSubject, fetchSubjectBySlug } from "@/actions/subjectAction";
import SearchBox from "@/components/dashboard/SearchBox";

const DisplayQuizsOfSubject = dynamic(
  () => import("@/components/subjects/DisplayQuizs"),
  {
    loading: () => <p className="text-center">Loading...</p>,
  }
);

export async function generateMetadata({ params }, parent) {
  const { slug } = await params;
  const tmpParams = decodeSlug(decodeURI(slug));

  if (!tmpParams.id) {
    return { title: "Subject Not Found" };
  }

  const subject = await fetchSubjectBySlug(tmpParams.slug);

  if (subject?.status === 404) {
    return { title: "Subject Not Found" };
  }

  return {
    title: subject?.data?.title || "Something went wrong!",
    alternates: {
      canonical: `/topic/${subject.data.slug}-${subject.data.id}`,
    },
    openGraph: {
      type: 'article',
      title: subject?.data?.title || "Something went wrong!",
    },
    twitter: {
      title: subject?.data?.title || "Something went wrong!",
    },
  };
}

export default async function Page({ params }) {
  const { slug } = await params;
  const tmpParams = decodeSlug(decodeURI(slug));

  if (!tmpParams.id) {
    return notFound();
  }

  const { data } = await fetchQuizsOfSubject(tmpParams.id, { page: 1, limit: 10 });

  if (!data || !data?.subject) {
    return notFound();
  }

  if (data?.subject?.slug !== tmpParams.slug) {
    redirect(`/topic/${data.subject.slug}-${data.subject.id}`);
  }

  return (
    <MainLayout sidebar="2">
      <Container maxWidth="cxl">
        <SearchBox />
        <DisplayQuizsOfSubject
          initialQuizzes={data?.quizzes || []}
          subject={data.subject}
        />
      </Container>
    </MainLayout>
  );
}
