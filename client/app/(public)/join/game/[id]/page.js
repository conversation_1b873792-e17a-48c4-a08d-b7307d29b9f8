import { cookies } from "next/headers";
import MinimalLayout from "@/layout/MinimalLayout";
import { notFound, redirect } from "next/navigation";
import QuizGame from "@/components/quizzes/QuizGame";

import { gameInfo } from '@/actions/quizResultAction';
import { getDataUserFromCookie } from '@/app/actions';

import InitNoti from "@/components/InitNoti";

export async function generateMetadata({ params }, parent) {
  const { id } = await params;
  const token = await getDataUserFromCookie('token');
  const game = await gameInfo(id, token);

  if (game?.status === 404 || !game?.data?.id) {
    return { title: "Quiz Not Found" };
  }

  const title = `${ (game?.data?.type || 'Game').toUpperCase() }: ${ game?.data?.quiz?.title || "New Quiz" }`;

  return {
    title,
    alternates: {
      canonical: `/join/game/${game.data.id}?gameType=${game.data.type}`,
    },
    openGraph: {
      type: 'article',
      title,
    },
    twitter: {
      title,
    },
  };
}

export default async function Page({ params }) {
  const { id } = await params;
  const token = await getDataUserFromCookie('token');
  const game = await gameInfo(id, token);

  if (game?.status == 404) {
    return notFound();
  }

  if (!game?.data?.id) {
    return (
      <MinimalLayout>
        <InitNoti initValue={game} />
      </MinimalLayout>
    );
  }

  if (game.data.id != id) {
    redirect(`/join/game/${game.data.id}?gameType=${game.data.type}`);
  }

  return (
    <MinimalLayout>
      <QuizGame initQuizInfo={game.data} />
    </MinimalLayout>
  );
}
