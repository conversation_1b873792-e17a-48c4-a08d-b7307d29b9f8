import MinimalLayout from "@/layout/MinimalLayout";
import { notFound } from "next/navigation";
import QuizStart from "@/components/quizzes/QuizStart";
import { quizInfo } from "@/actions/quizAction";

import InitNoti from "@/components/InitNoti";

export async function generateMetadata({ params }, parent) {
  const { id } = await params;
  const quiz = await quizInfo(id);

  if (quiz?.status === 404) {
    return { title: "Quiz Not Found" };
  }

  const title = `Thi online: ${ quiz?.data?.title || "New Quiz" }`;

  return {
    title,
    alternates: {
      canonical: `/join/quiz/${id}/start`,
    },
    openGraph: {
      type: 'article',
      title,
    },
    twitter: {
      title,
    },
  };
}

export default async function Page({ params }) {
  const { id } = await params;
  const quiz = await quizInfo(id);

  if (quiz?.status == 404) {
    return notFound();
  }

  return (
    <MinimalLayout>
      { quiz?.data ? <QuizStart initQuiz={quiz.data} /> : <InitNoti initValue={quiz} /> }
    </MinimalLayout>
  );
}
