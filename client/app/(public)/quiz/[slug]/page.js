import MainLayout from "@/layout/MainLayout";
import { decodeSlug } from "@/utils/helpers";
import { notFound, redirect } from "next/navigation";
import { fetchQuizzes, quizWithQuestions } from "@/actions/quizAction";

import QuizShow from "@/components/quizzes/QuizShow";

// Next.js will invalidate the cache when a
// request comes in, at most once every 3600 seconds.
export const revalidate = 3600 // invalidate every hour

// We'll prerender only the params from `generateStaticParams` at build time.
// If a request comes in for a path that hasn't been generated,
// Next.js will server-render the page on-demand.
export const dynamicParams = true // or false, to 404 on unknown paths

// Tạo các đường dẫn tĩnh cho tất cả các quiz trong quá trình build.
export async function generateStaticParams() {
  // try {
  //   const batchSize = 1000;
  //   let page = 1;
  //   let allParams = [];
  //   let hasMore = true;

  //   console.log(`Fetching quizzes in batches of ${batchSize}`);

  //   while (hasMore) {
  //     console.log(`Fetching batch ${page}...`);

  //     const { data: quizzes } = await fetchQuizzes({
  //       page: page,
  //       limit: batchSize
  //     });

  //     if (quizzes && quizzes.length > 0) {
  //       console.log(`Batch ${page}: Fetched ${quizzes.length} quizzes`);

  //       const batchParams = quizzes.map((quiz) => ({
  //         slug: `${quiz.slug}-${quiz.id}`,
  //       }));

  //       allParams.push(...batchParams);

  //       hasMore = quizzes.length === batchSize;
  //       page++;
  //     } else {
  //       hasMore = false;
  //     }
  //   }

  //   console.log(`Total: ${allParams.length} params for static generation`);

  //   return allParams;
  // } catch (error) {
  //   console.error("Error generating static params:", error);
  //   return [];
  // }

  return [];
}

async function getQuizData(slug) {
  const tmpParams = decodeSlug(decodeURI(slug));

  if (!tmpParams.id) {
    return null;
  }

  const res = await quizWithQuestions(tmpParams.id);

  if (!res.data) {
    return null;
  }

  return res.data;
}

export async function generateMetadata({ params }, parent) {
  const { slug } = await params;
  const quiz = await getQuizData(slug);

  if (!quiz) {
    return { title: "Quiz Not Found" };
  }

  const publishedAt = new Date(quiz.created_at).toISOString();
  const modifiedAt = new Date(quiz.updated_at || quiz.created_at).toISOString();

  return {
    title: quiz.title,
    description: quiz.description || quiz.title,
    alternates: {
      canonical: `/quiz/${quiz.slug}-${quiz.id}`,
    },
    openGraph: {
      title: quiz.title,
      description: quiz.description || quiz.title,
      type: "article",
      publishedTime: publishedAt,
      modifiedTime: modifiedAt,
    },
    twitter: {
      title: quiz.title,
    },
  };
}

export default async function QuizPage({ params }) {
  const { slug } = await params;
  const quiz = await getQuizData(slug);

  if (!quiz) {
    return notFound();
  }

  const correctSlug = `${quiz.slug}-${quiz.id}`;

  if (slug !== correctSlug) {
    redirect(`/quiz/${correctSlug}`);
  }

  return (
    <MainLayout sidebar="2">
      <QuizShow quiz={quiz} />
    </MainLayout>
  );
}
