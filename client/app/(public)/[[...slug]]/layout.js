import { notFound, redirect } from "next/navigation";
import MainLayout from "@/layout/MainLayout";
import Worksheets from "@/components/worksheets";

import { fetchGrades, fetchGradeBySlug } from "@/actions/gradeAction";
import { fetchSubjectBySlug } from "@/actions/subjectAction";
import { fetchCourses, fetchCourse } from "@/actions/courseAction";
import { fetchToc } from "@/actions/tocAction";
import { fetchTreeWorksheets } from "@/actions/commonAction";

// export const dynamic = 'force-static'; // gặp lỗi trên k8s khi có 2 pod lần đầu sẽ bị 404
export const revalidate = 3600;
// Cho phép fallback blocking cho các route ngoài generateStaticParams
export const dynamicParams = true;

export async function generateStaticParams() {
  // const [gradesRes, coursesRes] = await Promise.all([
  //   fetchGrades(),
  //   fetchCourses({
  //     vip: 1,
  //     all: 1
  //   }),
  // ]);

  // const params = [];
  // params.push({ slug: [] });

  // const grades = gradesRes?.data || [];
  // const courses = coursesRes?.data || [];
  // const subjectIds = new Set();

  // for (const grade of grades) {
  //   params.push({ slug: [grade.slug] });

  //   for (const subject of grade.subjects) {
  //     params.push({ slug: [grade.slug, subject.slug] });

  //     if (subjectIds.has(subject.id)) continue;

  //     params.push({ slug: ['worksheets', subject.slug] });
  //   }
  // }

  // for (const course of courses) {
  //   params.push({ slug: [`${course.slug}-c${course.id}`] });
  // }

  // return params;

  return [];
}

export async function generateMetadata({ params }, parent) {
  let { slug } = await params;

  if (Array.isArray(slug)) {
    let page = 1;
    let title = "Not Found!";
    let canonical = null;

    if (slug.length > 0 && /^\d+$/.test(slug.at(-1))) {
      page = Number(slug.at(-1));
      slug = slug.slice(0, -1);
    }

    if (slug.length === 1) {
      const raw = slug[0];
      const tocMatch    = raw.match(/^(.*)-t(\d+)$/);
      const courseMatch = raw.match(/^(.*)-c(\d+)$/);

      if (tocMatch) {
        const { data: toc } = await fetchToc(tocMatch[2]);
        
        if (toc) {
          title = toc.title;
          canonical = `/${toc.slug}-t${toc.id}`;
        }
      } else if (courseMatch) {
        const { data: course } = await fetchCourse(courseMatch[2]);

        if (course) {
          title = `Chủ đề: ${course.title}`;
          canonical = `/${course.slug}-c${course.id}`;
        }
      } else {
        const { data: grade } = await fetchGradeBySlug(raw);

        if (grade) {
          title = grade.title;
          canonical = `/${grade.slug}`;
        }
      }
    }

    if (slug.length === 2) {
      if (slug[0] === 'worksheets') {
        const { data: subject } = await fetchSubjectBySlug(slug[1]);

        if (subject) {
          title = subject.title;
          canonical = `/worksheets/${subject.slug}`;
        }
      } else {
        const [gradeRes, subjectRes] = await Promise.all([
          fetchGradeBySlug(slug[0]),
          fetchSubjectBySlug(slug[1]),
        ]);

        if (gradeRes?.data && subjectRes?.data) {
          title = `${subjectRes.data.title} ${gradeRes.data.title}`
          canonical = `/${gradeRes.data.slug}/${subjectRes.data.slug}`;
        };
      }
    }

    if (page > 1) {
      title += ` - Trang ${page}`;
    }

    const meta = { title };

    if (canonical) {
      meta.alternates = { canonical };
      meta.openGraph  = { type: "article", title };
      meta.twitter    = { title };
    }

    return meta;
  }
}

export default async function Layout({ params, children }) {
  let { slug } = await params;
  if (!Array.isArray(slug)) slug = [];
  const queryParams = {};
  const extraProps = {
    currentPath: '/'
  };

  let pageNum = null;

  if (slug.length > 0 && /^\d+$/.test(slug.at(-1))) {
    pageNum = Number(slug.at(-1));
    slug = slug.slice(0, -1);
    queryParams.page = pageNum;
  }

  if (slug.length > 2) {
    return notFound();
  }

  let layoutSlug = slug;

  if (slug.length === 3 && /^\d+$/.test(slug[2])) {
    layoutSlug = slug.slice(0, 2);
  }

  if (slug.length === 1) {
    const raw = slug[0];
    const tocMatch    = raw.match(/^(.*)-t(\d+)$/);
    const courseMatch = raw.match(/^(.*)-c(\d+)$/);

    if (tocMatch) {
      const { data: toc } = await fetchToc(tocMatch[2]);

      if (!toc) return notFound();

      if (toc.slug !== tocMatch[1]) {
        let exactTocPath = `/${toc.slug}-t${toc.id}`;
        if (pageNum) exactTocPath += `?page=${pageNum}`;
        redirect(exactTocPath);
      }

      queryParams.grade = toc.grade_id;
      queryParams.subject = toc.subject_id;
      queryParams.course = toc.course_id;
      extraProps.toc = toc;
    } else if (courseMatch) {
      const { data: course } = await fetchCourse(courseMatch[2]);

      if (!course) return notFound();

      if (course.slug !== courseMatch[1]) {
        let exactCoursePath = `/${course.slug}-c${course.id}`;
        if (pageNum) exactCoursePath += `?page=${pageNum}`;
        redirect(exactCoursePath);
      }

      queryParams.grade = course.grade_id;
      queryParams.subject = course.subject_id;
      queryParams.course = course.id;
      extraProps.course = course;
    } else {
      const { data: grade } = await fetchGradeBySlug(raw);

      if (!grade) return notFound();

      extraProps.grade = grade;
      queryParams.grade = grade.id;
    }
  }

  if (slug.length === 2) {
    if (slug[0] === 'worksheets') {
      const subjectSlug = slug[1];
      const { data: subject } = await fetchSubjectBySlug(subjectSlug);

      if (!subject) return notFound();

      extraProps.subject = subject;
      queryParams.subject = subject.id;
    } else {
      const [gradeSlug, subjectSlug] = slug;
      const [gradeRes, subjectRes] = await Promise.all([
        fetchGradeBySlug(gradeSlug),
        fetchSubjectBySlug(subjectSlug),
      ]);

      if (!gradeRes?.data || !subjectRes?.data) return notFound();

      extraProps.grade = gradeRes.data;
      extraProps.subject = subjectRes.data;
      queryParams.grade = gradeRes.data.id;
      queryParams.subject = subjectRes.data.id;
    }
  }

  extraProps.currentPath = '/' + slug.join('/');

  const { data } = await fetchTreeWorksheets(queryParams);

  extraProps.grades = data?.grades || [];
  extraProps.subjects = data?.subjects || [];

  return (
    <MainLayout sidebar="0">
      <Worksheets {...extraProps}>
        {children}
      </Worksheets>
    </MainLayout>
  );
}
