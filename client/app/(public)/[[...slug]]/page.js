import { notFound } from "next/navigation";
import ContentMain from "@/components/worksheets/ContentMain";

import { searchWorksheets } from "@/actions/commonAction";

export default async function Page({ params }) {
  let { slug } = await params;

  if (!Array.isArray(slug)) slug = [];

  let page = 1;

  if (slug.length > 0 && /^\d+$/.test(slug.at(-1))) {
    page = Number(slug.at(-1));
    slug = slug.slice(0, -1);
  }

  const queryParams = { page };

  if (slug.length === 1) {
    const raw = slug[0];
    const tocMatch    = raw.match(/^(.*)-t(\d+)$/);
    const courseMatch = raw.match(/^(.*)-c(\d+)$/);

    if (tocMatch) {
      queryParams.toc = tocMatch[2];
    } else if (courseMatch) {
      queryParams.course = courseMatch[2];
    } else {
      queryParams.sgrade = raw;
    }
  }

  if (slug.length === 2) {
    if (slug[0] === 'worksheets') {
      queryParams.ssubject = slug[1];
    } else {
      queryParams.sgrade = slug[0];
      queryParams.ssubject = slug[1];
    }
  }

  const { data } = await searchWorksheets(queryParams);

  return <ContentMain page={page} {...data} />;
}
