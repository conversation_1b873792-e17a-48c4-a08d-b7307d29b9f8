import { revalidatePath, revalidateTag } from "next/cache";

export async function GET(request) {
  const { searchParams } = request.nextUrl;
  const now = Date.now();
  const paths = searchParams.getAll('path');
  const tags = searchParams.getAll('tag');

  const uniquePaths = Array.from(new Set(paths.filter(Boolean)));
  const uniqueTags  = Array.from(new Set(tags.filter(Boolean)));

  if (uniquePaths.length > 0 || uniqueTags.length > 0) {
    uniquePaths.forEach((path) => revalidatePath(path));
    uniqueTags.forEach((tag)   => revalidateTag(tag));

    return Response.json({
      revalidated: true,
      now,
      message: `Revalidated paths: ${uniquePaths.join(", ")}, tags: ${uniqueTags.join(", ")}`,
    });
  }

  return Response.json({
    revalidated: false,
    now,
    message: 'Missing path or tag to revalidate',
  });
}
