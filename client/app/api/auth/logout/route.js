import { NextResponse } from 'next/server';
import { userCookieName } from '@/constant';

const dynamic = 'force-dynamic'
export const revalidate = 0;
export const fetchCache = 'force-no-store';

export async function GET(request) {
  const baseUrl = process.env.NEXT_PUBLIC_HOST_URL || new URL(request.url).origin;
  const redirectUrl = new URL("/login" + request.nextUrl.search, baseUrl);
  const response = NextResponse.redirect(redirectUrl);

  const userCookie = request.cookies.get(userCookieName);

  if (userCookie) {
    const base = {
      name: userCookieName,
      value: '',
      path: '/',
      httpOnly: true,
      sameSite: 'lax',
      secure: request.headers.get('x-forwarded-proto') === 'https',
      maxAge: 0,
      expires: new Date(0),
      domain: process.env.SESSION_DOMAIN,
    };
    response.cookies.set(base);
  }

  response.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate');

  return response;
}
