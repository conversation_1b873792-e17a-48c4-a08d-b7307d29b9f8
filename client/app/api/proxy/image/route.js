import { NextResponse } from "next/server";

export async function GET(request) {
  const { searchParams } = new URL(request.url);
  const imageUrl = searchParams.get("url");

  if (!imageUrl) {
    return NextResponse.json({ error: "<PERSON>hiếu tham số URL" }, { status: 400 });
  }

  try {
    const response = await fetch(imageUrl);

    if (!response.ok) {
      return NextResponse.json(
        { error: "Không tìm thấy hình ảnh" },
        { status: 404 }
      );
    }

    const contentType =
      response.headers.get("Content-Type") || "application/octet-stream";
    const buffer = await response.arrayBuffer();

    return new NextResponse(buffer, {
      status: 200,
      headers: {
        "Content-Type": contentType,
        "Content-Disposition": "inline",
      },
    });
  } catch (error) {
    console.error(error);
    return NextResponse.json(
      { error: "Lỗi khi tải hình ảnh" },
      { status: 500 }
    );
  }
}
