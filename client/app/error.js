"use client";

import { useEffect } from "react";
import Button from '@mui/material/Button';
import Container from '@mui/material/Container';

export default function Error({ error, reset, statusCode = 500 }) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error(error);
  }, [error]);

  return (
    <Container className="text-center">
        {statusCode === 403 ? (
        <>
          <h1>403 - Forbidden</h1>
          <p>Bạn không có quyền truy cập vào tài nguyên này.</p>
        </>
      ) : (
        <>
          <h1>
            Woops! <br />
            Something went wrong :(
          </h1>
          <p>Have you tried turning it off and on again?</p>
        </>
      )}
      <Button variant="contained" color="error" onClick={() => reset()}>
        Try Again
      </Button>
    </Container>
  );
}
