version: '3.7'

services:
  backend:
    build:
      context: .
      dockerfile: .docker/backend.dev.Dockerfile
    working_dir: /app/server
    volumes:
      - ./server/:/app/server
      - ./server/node_modules:/app/server/node_modules
      - ./server/vendor:/app/server/vendor
      - ./.docker/php-fpm/custom.ini:/usr/local/etc/php/conf.d/local.ini
    expose:
      - "9000"
    networks:
      - app_network
    depends_on:
      - db_mysql
    shm_size: 1gb
    tmpfs:
      - /dev/shm:size=1g

  backend_nginx:
    image: nginx:alpine
    ports:
      - "8001:80"
      - "4431:443"
    volumes:
      - ./server/:/app/server
      - ./.docker/nginx/vhostdev.conf/:/etc/nginx/conf.d/default.conf
    networks:
      - app_network
    depends_on:
      - backend

  frontend:
    build:
      context: .
      dockerfile: .docker/frontend.dev.Dockerfile
    working_dir: /app/client
    volumes:
      - ./client:/app/client
      - ./client/node_modules:/app/client/node_modules
      - ./client/.next:/app/client/.next
    ports:
      - "3001:3000"
    networks:
      - app_network

  db_mysql:
    image: mysql:8.0
    ports:
      - "3307:3306"
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: my_database
      # MYSQL_USER: your_database_user
      # MYSQL_PASSWORD: your_database_password
    volumes:
      - mysql_data:/var/lib/mysql
      - ./.docker/mysql/my.cnf:/etc/mysql/my.cnf
    networks:
      - app_network

networks:
  app_network:
    driver: bridge

volumes:
  mysql_data:
    driver: local
