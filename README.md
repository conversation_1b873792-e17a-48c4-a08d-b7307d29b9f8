<p align="center">
    <h1><PERSON><PERSON>, Next.js</h1>
</p>

## Installation

```bash
# clone the repo
$ git clone <project>
```

# Server Side

## Server Installation

```bash
# go into app's directory
$ cd <project-name>/server

# install app's dependencies
$ composer install

# copy .env.example to .env
$ cp .env.example .env

```

### If you choice to use MySQL

in file ".env" complete this database configuration:

- DB_CONNECTION=mysql
- DB_HOST=127.0.0.1
- DB_PORT=3306
- DB_DATABASE=laravel
- DB_USERNAME=root
- DB_PASSWORD=

> **_NOTE:_** laravel sanctum sử dụng session cho SPA cần đảm bảo server và client chạy cùng trên một domain cấp cao (server: api.example.com / client: example.com) để đảm bảo cors và tránh xss. vì cookie chưa session_id server trả về chỉ có cjia sẻ được ở domain đó (tính cả các subdomain).

### Next step

```bash
# in your app directory
# generate laravel APP_KEY
$ php artisan key:generate

# run database migration and seed
$ php artisan migrate:fresh --seed
```

## Usage

```bash
# start local server
$ php artisan serve
```

# Client Side

## Client Installation

```bash

# go into app's directory
$ cd <project-name>/client

# copy .env.example to .env
$ cp .env.example .env

# install dependencies
npm install
# or
yarn install
```

then, you can run the development server:

```bash
npm run dev
# or
yarn dev
```

> **_NOTE:_** please make sure server is running.

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

# Other

```bash
# Tạo ảnh snapshot cho quiz
npm install -g puppeteer
sudo apt-get update
sudo apt-get install -y chromium-browser
sudo ln -s /usr/bin/chromium-browser /usr/bin/chromium
# và cần cài php-imagick
```

# Docker

- build images prod:
```bash
# backend
docker build -t myapp-backend:backend -f .docker/backend.prod.Dockerfile .
# cấu hình .env
docker run -d --name myapp-backend \
  -p 8001:8001 \
  --env-file server/.env \
  -v app_storage:/var/www/html/storage \
  myapp-backend:backend

# frontend
docker build -t myapp-frontend:frontend -f .docker/frontend.prod.Dockerfile .
# nếu test bằng host cần 2 dòng --add-host
docker run -d --name myapp-frontend \
  -p 3001:3000 \
  --env-file client/.env \
  --add-host myapp.test:host-gateway \
  --add-host api.myapp.test:host-gateway \
  myapp-frontend:frontend
```

# Gitlab Container registry

- push to container registry:
```bash
# sau khi build docker image cần đổi tên image cho khớp với tên repository Gitlab
# tag image vừa build bằng tên mới
docker tag myapp-backend:backend registry.gitlab.com/<username_của_bạn>/<tên repo>
# push image với tên mới lên container registry
docker push registry.gitlab.com/<username_của_bạn>/<tên repo>
# nếu khi push cần login  thì dùng lệnh:
docker login registry.gitlab.com
# sau đó thì nhập email và password rồi push lại
```

# Deploy

- Dùng k3s để triển khai (triển khai nhanh vì k3s đa cài sẵn các thành phần cần thiết để triển khai 1 cụm k8s)
- Nếu server đang chạy các dịch vụ web khác dùng nginx hay 1 service gì dùng port 80/443 thì dùng k3s với traefik sẽ bị chiếm mất port và web khác sẽ bị 404
- Giải pháp: Giữ Nginx host trên :80/:443, đẩy Traefik sang cổng khác (8080/8443) và để Nginx host reverse-proxy vào Traefik

+ Đổi Traefik Service sang 8080/8443 (k3s native), Tạo file cấu hình HelmChartConfig để override chart Traefik mặc định của k3s:
```bash
sudo tee /var/lib/rancher/k3s/server/manifests/traefik-config.yaml >/dev/null <<'YAML'
apiVersion: helm.cattle.io/v1
kind: HelmChartConfig
metadata:
  name: traefik
  namespace: kube-system
spec:
  valuesContent: |-
    service:
      type: NodePort
    ports:
      web:
        # giữ mặc định expose/exposedPort của chart, chỉ đặt nodePort
        nodePort: 32080
      websecure:
        nodePort: 32443
    # Quan trọng: tin tưởng header từ Nginx (127.0.0.1) để giữ https
    additionalArguments:
      - "--entryPoints.web.forwardedHeaders.trustedIPs=127.0.0.1/32"
      # Nếu Nginx proxy từ IP khác, thêm vào đây, ví dụ IP public của host:
      # - "--entryPoints.web.forwardedHeaders.trustedIPs=*************/32,127.0.0.1/32"
      # hoặc nhanh gọn: trust mọi nguồn (ít an toàn hơn)
      # - "--entryPoints.web.forwardedHeaders.insecure=true"
YAML
# K3s sẽ tự reconcile. Kiểm tra:
kubectl -n kube-system get svc traefik -o wide
kubectl -n kube-system get ds -l app=svclb-traefik -o wide   # klipper-lb sẽ listen 8080/8443
ss -ltnp | egrep ':8080|:8443'  
# Nếu đã cài Traefik riêng bằng Helm, chỉ cần helm upgrade với --set service.ports.web.port=8080,service.ports.websecure.port=8443
```

+ Nginx host reverse-proxy theo hostname vào Traefik

```
server {
  listen 80;
  server_name app.myapp.test api.myapp.test;
  return 301 https://$host$request_uri;
}

server {
  listen 443 ssl http2;
  server_name app.myapp.test;

  ssl_certificate     /etc/nginx/ssl/app.fullchain.pem;
  ssl_certificate_key /etc/nginx/ssl/app.privkey.pem;

  location / {
    proxy_set_header Host $host;
    proxy_set_header X-Forwarded-Proto https;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_pass http://127.0.0.1:8080;
  }
}

server {
  listen 443 ssl http2;
  server_name api.myapp.test;

  ssl_certificate     /etc/nginx/ssl/api.fullchain.pem;
  ssl_certificate_key /etc/nginx/ssl/api.privkey.pem;

  location / {
    proxy_set_header Host $host;
    proxy_set_header X-Forwarded-Proto https;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_pass http://127.0.0.1:8080;
  }
}
```

+ Triển khai k3s:
```bash
# Cài k3s (native)
curl -sfL https://get.k3s.io | sh -
# test cài đặt
sudo kubectl get node -owide
# nếu lỗi: "Unhandled Error" err="couldn't get current server API group list: Get \"http://localhost:8080/api?timeout=32s\": dial tcp 127.0.0.1:8080: connect: connection refused"
# Trỏ kubeconfig cho user hiện tại (không dùng sudo cho kubectl)
mkdir -p ~/.kube
sudo cp /etc/rancher/k3s/k3s.yaml ~/.kube/config
sudo chown "$USER":"$USER" ~/.kube/config
# Nếu master KHÔNG nằm trên máy hiện tại (VD: VM khác), mở ~/.kube/config và sửa 127.0.0.1 thành IP của master (ví dụ https://********:6443).
# Test lại
kubectl cluster-info
kubectl get nodes -o wide
# Cài cert-manager cho TLS local (self-signed):
kubectl apply -f https://github.com/cert-manager/cert-manager/releases/latest/download/cert-manager.yaml
# Setup domain trong ingress: cp và update domain
cp .k8s/backend-ingress.yaml.example .k8s/backend-ingress.yaml
cp .k8s/frontend-ingress.yaml.example .k8s/frontend-ingress.yaml
# set biến env:cp và update các biến env
cp .k8s/secrets-backend.yaml.example .k8s/secrets-backend.yaml
cp .k8s/secrets-frontend.yaml.example .k8s/secrets-frontend.yaml
kubectl apply -f .k8s/namespace.yaml
kubectl apply -f .k8s/clusterissuer-selfsigned.yaml # chạy trên local cần cái này để tự ký ssl
# Lấy ci-deploy-token: https://kubernetes.io/docs/tasks/configure-pod-container/pull-image-private-registry/
# Gitlab vào: User Settings/Access tokens và tạo 1 token cấp quyền k8s-proxy, read_registry
GITLAB_USER="your-ci-username" GITLAB_TOKEN="your-ci-deploy-token" .k8s/secrets-regcred.sh
kubectl apply -f .k8s/secrets-backend.yaml
kubectl apply -f .k8s/secrets-frontend.yaml
kubectl apply -f .k8s/redis.yaml
kubectl -n 2048-app rollout status sts/redis-master
kubectl apply -f .k8s/backend.yaml
# kubectl apply -f .k8s/backend-migrate.yaml # nếu cần khởi tạo database
# kubectl apply -f .k8s/backend-queue.yaml
# kubectl apply -f .k8s/backend-schedule.yaml
kubectl -n 2048-app rollout status deploy/backend # theo dõi tiến trình deploy và chờ cho tới khi rollout hoàn tất
kubectl apply -f .k8s/frontend.yaml
kubectl -n 2048-app rollout status deploy/frontend
kubectl apply -f .k8s/hpa.yaml
kubectl apply -f .k8s/pdb.yaml
# Kiểm tra
kubectl -n 2048-app get ingress,svc,pods
```

- khi có bản image mới:
```bash
# dùng tag => cập nhật tag image mới trong file .yaml và apply lại
# dùng latest (apply lại file .yaml sẽ không kéo bản mới vì ko có thay đổi gì nên cần tạo Pod mới)
# Restart để recreate Pod -> kéo lại image :latest
kubectl -n 2048-app rollout restart deploy/backend
kubectl -n 2048-app rollout restart deploy/frontend
# Theo dõi quá trình
kubectl -n 2048-app rollout status deploy/backend
kubectl -n 2048-app rollout status deploy/frontend
# Kiểm tra image mới đã chạy chưa
kubectl -n 2048-app get pods -l app=frontend -o jsonpath='{range .items[*]}{.metadata.name}{"  "}{.status.containerStatuses[0].imageID}{"\n"}{end}'
kubectl -n 2048-app get pods -l app=backend  -o jsonpath='{range .items[*]}{.metadata.name}{"  "}{.status.containerStatuses[0].imageID}{"\n"}{end}'
```

- Nếu bản latest mới lỗi cần rollout
```bash
# Clear cache nếu đổi .env
kubectl -n 2048-app exec deploy/backend -- php artisan config:clear
kubectl -n 2048-app exec deploy/backend -- php artisan route:clear
kubectl -n 2048-app exec deploy/backend -- php artisan cache:clear
# rollout về SHA tốt gần nhất:
kubectl -n 2048-app set image deploy/backend backend=<registry>/<path>/backend:<OLD_SHA> --record
kubectl -n 2048-app set image deploy/frontend frontend=<registry>/<path>/frontend:<OLD_SHA> --record
kubectl -n 2048-app rollout status deploy/backend
kubectl -n 2048-app rollout status deploy/frontend
# khi dùng --record trước đó bạn có thể xem lịch sử và rollout về bản lịch sử đó
kubectl -n 2048-app rollout history deploy/backend
kubectl -n 2048-app rollout undo deploy/backend --to-revision=<REV>
```

- Một số lệnh check:
```bash
kubectl -n 2048-app get pods -l app=frontend -o wide
# hiện ra các pod rồi xem log pod bằng lệnh: frontend-5856475b48-4cdzk là name của pod 1
kubectl -n 2048-app describe pod frontend-5856475b48-4cdzk | sed -n '/Events:/,$p'
# hoặc
kubectl -n 2048-app logs <tên-pod-frontend-1>
# check trạng thái
sudo journalctl -u k3s -f
kubectl -n kube-system get deploy,svc -l app.kubernetes.io/name=traefik -o wide
```

# Một số khái niệm

- Namespace = setup “môi trường” tách biệt (prod, staging…).
- StatefulSet = chạy dịch vụ có trạng thái/persistence (MySQL, Redis), gắn PVC.
- Deployment = chạy app stateless (FE/BE web, queue worker).
- Service = IP ổn định để các Pod trong cluster gọi nhau (discovery).
- Ingress = cổng vào từ Internet → ánh xạ host/path → Service (TLS, route).
- Job/CronJob = lệnh 1 lần / theo lịch.
- Secret/Config = cấu hình & bí mật; dùng 12-factor.
