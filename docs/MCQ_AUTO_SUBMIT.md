# MCQ Auto Submit Feature

## Tổng quan

Tính năng tự động submit cho câu hỏi trắc nghiệm (MCQ - Multiple Choice Question) được thiết kế để cải thiện trải nghiệm người dùng bằng cách tự động gửi câu trả lời ngay khi người dùng chọn một đáp án.

## Tính năng chính

### 1. Radio Button Behavior
- Chỉ cho phép chọn một đáp án duy nhất tại một thời điểm
- Khi chọn đáp án mới, đáp án cũ sẽ tự động bị bỏ chọn
- Người dùng có thể bỏ chọn đáp án hiện tại bằng cách click lại vào nó

### 2. Auto Submit
- Sau khi chọn đáp án, hệ thống tự động gọi `submitAnswer()` sau 300ms delay
- Delay ngắn giúp người dùng thấy được đáp án đã được chọn trước khi submit
- Chỉ áp dụng cho MCQ, không áp dụng cho MSQ (Multiple Select Question)

### 3. Loading State
- Hiển thị trạng thái "Đang gửi câu trả lời..." khi đang submit
- Disable tất cả các option trong khi đang submit để tránh click nhiều lần
- Nút submit hiển thị "ĐANG GỬI..." với icon loading
- Visual feedback với opacity và animation

## Cấu trúc Code

### Component Changes

#### MultipleChoiceQuestion
```javascript
const MultipleChoiceQuestion = memo(({ 
  questionData, 
  answer, 
  onAnswer, 
  isSubmit = false, 
  onSubmitAnswer,     // Callback mới để submit
  isSubmitting = false, // State mới cho loading
  ...props 
}) => {
  // Logic xử lý auto submit cho MCQ
  const activeOption = (index) => {
    if (!isSubmit && !isSubmitting && typeof onAnswer === 'function') {
      let newAnswer = [];

      if (questionData.kind === 'MCQ') {
        // Chỉ cho phép chọn 1 đáp án
        if (answer.includes(index)) {
          newAnswer = [];
        } else {
          newAnswer = [index];
        }
        
        // Tự động submit cho MCQ
        onAnswer(newAnswer);
        if (newAnswer.length > 0 && typeof onSubmitAnswer === 'function') {
          setTimeout(() => {
            onSubmitAnswer(newAnswer);
          }, 300);
        }
      } else {
        // MSQ logic không thay đổi
        // ...
      }
    }
  };
});
```

#### QuizGame Component
```javascript
const QuizGame = ({ initQuizInfo }) => {
  // State mới
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Cập nhật submitAnswer function
  const submitAnswer = async (answerOfUser) => {
    if (
      activeQuestion?.user_answer ||
      isSubmit ||
      isSubmitting ||  // Kiểm tra thêm isSubmitting
      (gameType !== 'flashcard' && answerOfUser.length == 0)
    ) return;

    setIsSubmitting(true);
    
    try {
      // Submit logic...
      setIsSubmit(true);
    } catch (errors) {
      // Error handling...
    } finally {
      setIsSubmitting(false);
    }
  };

  // Truyền props mới cho MultipleChoiceQuestion
  return (
    <MultipleChoiceQuestion
      // ... existing props
      isSubmitting={isSubmitting}
      onSubmitAnswer={(answered) => 
        activeQuestion.user_answer ? undefined : submitAnswer(answered)
      }
    />
  );
};
```

### CSS Styles

```scss
// MCQ Submitting styles
.question-box.submitting {
  .q-option-item {
    transition: opacity 0.3s ease, transform 0.3s ease;
    
    &.disabled {
      cursor: not-allowed !important;
      transform: scale(0.98);
    }
  }
}

.mcq-submitting-text {
  animation: pulse 1.5s ease-in-out infinite;
  
  i {
    animation: spin 1s linear infinite;
  }
}
```

## Luồng hoạt động

1. **Người dùng click vào một đáp án MCQ**
2. **Component cập nhật state `answer`** với đáp án được chọn
3. **Sau 300ms delay, `onSubmitAnswer` được gọi**
4. **`isSubmitting` được set thành `true`**
5. **UI hiển thị loading state:**
   - Text "Đang gửi câu trả lời..."
   - Disable tất cả options
   - Nút submit hiển thị "ĐANG GỬI..."
6. **API call được thực hiện**
7. **Sau khi hoàn thành:**
   - `isSubmitting` được set thành `false`
   - `isSubmit` được set thành `true`
   - Hiển thị kết quả (đúng/sai)

## Testing

### Unit Tests
- Test auto submit khi chọn đáp án MCQ
- Test không cho phép click khi đang submit
- Test hiển thị loading state
- Test chỉ submit một lần dù click nhiều lần

### Manual Testing
1. Tạo quiz với câu hỏi MCQ
2. Chọn một đáp án
3. Kiểm tra auto submit sau 300ms
4. Kiểm tra loading state hiển thị đúng
5. Kiểm tra không thể click option khác khi đang submit

## Lưu ý quan trọng

1. **Chỉ áp dụng cho MCQ**: MSQ vẫn giữ nguyên behavior cũ (cần click nút submit)
2. **Delay 300ms**: Đủ thời gian để user thấy đáp án được chọn
3. **Prevent multiple submissions**: Kiểm tra `isSubmitting` để tránh submit nhiều lần
4. **Graceful error handling**: Luôn reset `isSubmitting` trong finally block
5. **Accessibility**: Vẫn giữ keyboard navigation và screen reader support

## Auto Progress Bar Feature (NEW)

### Tổng quan
Sau khi submit câu trả lời thành công, hệ thống sẽ hiển thị thanh tiến trình tự động chuyển câu hỏi tiếp theo.

### Tính năng chính
1. **Xuất hiện sau khi nhận kết quả**: Thanh tiến trình hiển thị ngay sau khi server trả về kết quả đúng/sai
2. **Animation 5 giây**: Chạy từ 0% đến 100% trong 5 giây với animation mượt mà (cập nhật mỗi 50ms)
3. **Thiết kế tối giản**: Chỉ có LinearProgress component, không có text hay button
4. **Tự động chuyển câu**: Sau 5 giây tự động gọi `nextQuestion()`
5. **Màu sắc theo kết quả**: Xanh lá nếu đúng (#2ecc71), đỏ nếu sai (#e74c3c)
6. **Styling cụ thể**: Width 30px, background trắng, border radius 16px, căn giữa
7. **Chỉ cho game thường**: Không áp dụng cho flashcard mode

### Component Structure

#### AutoProgressBar.js (Simplified)
```javascript
const AutoProgressBar = memo(({
  isVisible,        // Boolean - hiển thị thanh tiến trình
  isCorrect,        // Boolean - kết quả đúng/sai để chọn màu
  onComplete,       // Function - callback khi hoàn thành 5 giây
  duration = 5000  // Number - thời gian chạy (ms)
}) => {
  // Chỉ có logic progress animation và auto complete
  // Không có countdown text, skip button, keyboard events
});
```

### Styling
- **Width**: 30px (chiều rộng cố định)
- **Background**: #fff (màu nền trắng)
- **Border radius**: 1rem (16px bo góc)
- **Position**: Căn giữa với flexbox
- **Progress color**: Xanh (#2ecc71) nếu đúng, đỏ (#e74c3c) nếu sai
- **Animation**: Smooth transition với interval 50ms

### Simplified Design
- Không có text countdown
- Không có skip button
- Không có keyboard events
- Không có responsive breakpoints
- Chỉ focus vào core functionality

### Integration với QuizGame
```javascript
// States
const [showAutoProgress, setShowAutoProgress] = useState(false);
const [lastAnswerCorrect, setLastAnswerCorrect] = useState(null);

// Trong submitAnswer()
if (gameType !== 'flashcard') {
  const isCorrect = res.data.isCorrect;
  setLastAnswerCorrect(isCorrect);
  setShowAutoProgress(true);
}

// Handlers
const handleAutoProgressComplete = () => {
  setShowAutoProgress(false);
  nextQuestion(currentQuestionIndex);
};

const handleAutoProgressSkip = () => {
  setShowAutoProgress(false);
  nextQuestion(currentQuestionIndex);
};
```

## Tương lai

Có thể mở rộng thêm:
- Cấu hình delay time từ admin
- Animation transition khi chuyển câu hỏi
- Sound effect khi auto submit
- Analytics tracking cho auto submit behavior
- **Cấu hình thời gian thanh tiến trình từ admin**
- **Pause/resume thanh tiến trình khi user inactive**
- **Custom animation effects cho thanh tiến trình**
