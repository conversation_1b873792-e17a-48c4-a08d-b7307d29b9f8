FROM node:22-alpine

# Set the working directory
WORKDIR /app/client

# Copy package.json and package-lock.json
COPY ./client/package*.json ./

# sinh lock từ package.json
RUN npm install --package-lock-only --no-audit --no-fund

# Install dependencies
RUN npm ci --no-audit --no-fund

# Copy all files
COPY ./client .

EXPOSE 3000

# nếu muốn chạy luôn thì dùng: CMD npm run dev
CMD ["tail", "-f", "/dev/null"]
