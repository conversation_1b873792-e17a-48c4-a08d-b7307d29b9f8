FROM php:8.3-fpm-alpine

RUN apk add --no-cache \
    bash \
    curl \
    nodejs \
    npm \
    chromium \
    nss \
    freetype \
    harfbuzz \
    ttf-freefont \
    libjpeg-turbo-dev \
    libpng-dev \
    libwebp-dev \
    libzip-dev \
    freetype-dev \
    imagemagick-dev \
    autoconf \
    build-base \
    libtool \
    make \
    jpegoptim \
    optipng \
    pngquant \
    gifsicle \
    sqlite \
    sqlite-dev \
    pandoc \
    && docker-php-ext-configure gd \
        --with-freetype=/usr/include/ \
        --with-jpeg=/usr/include/ \
    && docker-php-ext-install \
        pdo \
        pdo_mysql \
        gd \
        exif \
        zip \
    && pecl install imagick \
    && docker-php-ext-enable imagick

RUN curl -sS https://getcomposer.org/installer \
    | php -- --install-dir=/usr/local/bin --filename=composer

# Tell Puppeteer to skip installing Chrome. We'll be using the installed package.
# https://gist.github.com/spaceemotion/01a9c94519eaf57fecba4279a8d83cc2
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true \
    PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser

RUN npm install -g puppeteer

WORKDIR /app/server

# Copy the application code
COPY ./server .

# Clear cache
RUN rm -rf /var/lib/apt/lists/*

EXPOSE 8000

# Run the PHP server
CMD ["php-fpm"]
