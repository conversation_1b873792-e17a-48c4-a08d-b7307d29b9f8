FROM node:22-alpine AS assets

WORKDIR /assets
COPY ./server/package*.json ./
RUN npm ci --legacy-peer-deps
COPY ./server ./
RUN npm run build

FROM php:8.3-fpm-alpine AS backend
WORKDIR /var/www/html
RUN apk add --no-cache \
    bash curl nginx supervisor ca-certificates \
    chromium nss freetype harfbuzz ttf-freefont ttf-dejavu ttf-liberation \
    imagemagick libjpeg-turbo libpng libwebp libzip sqlite \
    nodejs npm \
    && apk add --no-cache --virtual .build-deps \
      autoconf build-base libtool \
      freetype-dev libjpeg-turbo-dev libpng-dev libwebp-dev libzip-dev imagemagick-dev sqlite-dev \
    && docker-php-ext-configure gd --with-freetype --with-jpeg --with-webp \
    && docker-php-ext-install pdo pdo_mysql gd exif zip \
    && pecl install imagick \
    && docker-php-ext-enable imagick \
    && apk del .build-deps \
    && rm -rf /tmp/*

# Tell Puppeteer to skip installing Chrome. We'll be using the installed package.
# https://gist.github.com/spaceemotion/01a9c94519eaf57fecba4279a8d83cc2
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true \
    PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser
RUN npm install -g puppeteer

COPY --from=composer:2 /usr/bin/composer /usr/bin/composer

COPY ./server/composer.json ./server/composer.lock ./
RUN composer install \
    --no-dev --prefer-dist --no-interaction --no-progress \
    --no-scripts --no-autoloader

COPY ./server ./
COPY --from=assets /assets/public/build ./public/build
# Symlink storage nếu thiếu
RUN mkdir -p storage/app/public \
 && [ -L public/storage ] || ln -s ../storage/app/public public/storage

RUN composer dump-autoload --classmap-authoritative --optimize --no-dev

COPY .docker/php-fpm/custom.ini /usr/local/etc/php/conf.d/
COPY .docker/nginx/vhost.conf /etc/nginx/http.d/app.conf
COPY .docker/supervisor/supervisord.conf /etc/supervisord.conf

RUN mkdir -p /run/nginx /var/cache/nginx /var/log/nginx \
             /var/lib/nginx/logs /var/lib/nginx/tmp \
    && chown -R www-data:www-data /run/nginx /var/cache/nginx /var/log/nginx /var/lib/nginx \
    # Xoá 'user nginx;'
    && sed -ri 's/^\s*user\s+nginx\s*;.*$//g' /etc/nginx/nginx.conf \
    && sed -ri 's#^\s*error_log\s+.*;#error_log /dev/stderr warn;#g' /etc/nginx/nginx.conf \
    && chown -R www-data:www-data storage bootstrap/cache \
    && chmod -R ug+rwX storage bootstrap/cache

USER www-data

EXPOSE 8001

CMD ["supervisord","-c","/etc/supervisord.conf"]
