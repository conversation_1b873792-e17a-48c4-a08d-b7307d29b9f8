server {
    listen 8001;
    server_name _;

    root /var/www/html/public;

    index index.php index.html;

    # Log ra stdout/stderr để thân thiện container
    access_log /dev/stdout;
    error_log  /dev/stderr warn;

    error_page 404 /index.php;

    # Static & cache headers nhẹ
    location ~* \.(?:css|js|jpg|jpeg|png|gif|ico|webp|svg|ttf|otf|woff|woff2)$ {
        expires 7d;
        add_header Cache-Control "public, max-age=604800, immutable";
        try_files $uri =404;
    }

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        try_files $uri =404;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_pass 127.0.0.1:9000;
        fastcgi_read_timeout 120s;
    }

    location ~ /\.ht {
        deny all;
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }

    client_max_body_size 1024m;

    location /healthz {
        return 200 'ok';
        add_header Content-Type text/plain;
        access_log off;
    }
}
