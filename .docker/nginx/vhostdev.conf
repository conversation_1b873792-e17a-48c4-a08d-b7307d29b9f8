server {
    listen 80;
    server_name _;

    root /app/server/public;
    index index.php index.html;

    server_tokens off;

    error_log  /var/log/nginx/error.log;
    access_log /var/log/nginx/access.log;

    sendfile on;
    keepalive_timeout 120;

    client_max_body_size 2G;
    client_header_timeout 300;
    client_body_timeout 300;

    gzip                on;
    gzip_min_length     1024;
    gzip_proxied        any;
    gzip_types          text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss;
    gzip_comp_level     5;

    location / {
        try_files $uri $uri/ /index.php?$query_string;

        # Headers
        add_header Access-Control-Allow-Headers 'Origin,Range,Accept-Encoding,Referer,Cache-Control';
        add_header Access-Control-Expose-Headers 'Server,Content-Length,Content-Range,Date';
        add_header Access-Control-Allow-Methods 'GET, HEAD, OPTIONS';
        add_header Access-Control-Allow-Origin '*';
    }

    location ~ \.php$ {
        try_files $uri =404;
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        # backend phải trùng với tên container_name trong docker-compose.yml
        fastcgi_pass backend:9000;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_param PATH_INFO $fastcgi_path_info;
        fastcgi_read_timeout 300;
    }

    location ~* \.(js|css|png|jpg|jpeg|gif|svg|woff2?)$ {
        expires 30d;
        add_header Cache-Control "public, immutable";
    }
}
