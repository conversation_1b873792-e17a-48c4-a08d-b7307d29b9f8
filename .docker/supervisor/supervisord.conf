[supervisord]
nodaemon=true
logfile=/dev/null
pidfile=/tmp/supervisord.pid
childlogdir=/tmp
# socket không cần khi không dùng supervisorctl
serverurl=unix:///tmp/supervisor.sock

[program:php-fpm]
command=php-fpm -F
priority=10
autostart=true
autorestart=true
stdout_logfile=/dev/stdout
stderr_logfile=/dev/stderr
stdout_logfile_maxbytes=0
stderr_logfile_maxbytes=0

[program:nginx]
command=nginx -g "daemon off;"
priority=20
autostart=true
autorestart=true
stdout_logfile=/dev/stdout
stderr_logfile=/dev/stderr
stdout_logfile_maxbytes=0
stderr_logfile_maxbytes=0
