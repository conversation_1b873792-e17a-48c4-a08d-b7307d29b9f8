FROM node:22-alpine AS deps
WORKDIR /app
COPY ./client/package*.json ./
RUN npm ci --no-audit --no-fund

FROM node:22-alpine AS builder
WORKDIR /app
COPY ./client .
COPY --from=deps /app/node_modules ./node_modules
COPY ./.docker/.env .env
# cần config trong next.config.js: output: 'standalone'
RUN npm run build          

FROM node:22-alpine AS app
WORKDIR /app
ENV NODE_ENV=production
# Uncomment the following line in case you want to disable telemetry during runtime.
# ENV NEXT_TELEMETRY_DISABLED=1
ENV PORT=3000
ENV HOSTNAME="0.0.0.0"

EXPOSE 3000

# tạo user non-root
ENV USER=nextjs
ENV GROUP=nodejs
RUN addgroup -g 1001 ${GROUP} && adduser -D -u 1001 -G ${GROUP} ${USER}

COPY --from=builder --chown=${USER}:${GROUP} /app/.next/standalone ./
COPY --from=builder --chown=${USER}:${GROUP} /app/.next/static ./.next/static
COPY --from=builder --chown=${USER}:${GROUP} /app/public ./public

# đảm bảo .next và /tmp/next ghi được
RUN mkdir -p /app/.next/cache /app/.next/server/app \
 && chown -R ${USER}:${GROUP} /app/.next

COPY --chown=${USER}:${GROUP} ./.docker/entrypoint.sh ./entrypoint.sh
RUN chmod +x ./entrypoint.sh

# RUN echo "Checking built files..." && \
#     ls -al /app/.next && \
#     ls -al /app/public && \
#     cat /app/package.json && \
#     cat /app/next.config.js

USER ${USER}

ENTRYPOINT ["./entrypoint.sh"]

CMD ["node", "server.js"]
